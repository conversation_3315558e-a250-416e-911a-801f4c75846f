{"name": "corp-credit-products-ui", "license": "MIT", "version": "1.2.348", "description": "Corporate Credit Products UI", "withMobileVersion": true, "alfa:meta": {"name": "corp-credit-products"}, "packageManager": "yarn@4.9.1", "engines": {"node": "18.20.8"}, "scripts": {"analyze": "arui-scripts bundle-analyze", "predocker-build": "yarn install --no-progress --ignore-optional --frozen-lockfile --unsafe-perm", "prearchive-build": "yarn install --no-progress --ignore-optional --frozen-lockfile --unsafe-perm", "docker-build": "arui-scripts docker-build", "archive-build": "arui-scripts archive-build", "start": "cross-env NODE_TLS_REJECT_UNAUTHORIZED=0 NODE_OPTIONS=\"--max-old-space-size=4096 UV_THREADPOOL_SIZE=12\" arui-scripts start", "start:dev": "cross-env GATEWAY_HOST=corp-gateway-dev HOST=api.kuberdev.alfaintra.net AUTH_DEV_ACCESS=1 CONFIG_SERVER_URL=http://api.kuberdev.alfaintra.net/corp-credit-products-config-server CONFIG_PROJECT_NAME=corp-credit-products-ui CONFIG_LABEL=master CONFIG_PROFILE=development yarn start", "start:int": "cross-env GATEWAY_HOST=corp-gateway-test HOST=api.kubernib.alfaintra.net AUTH_DEV_ACCESS=1 CONFIG_SERVER_URL=http://api.kubernib.alfaintra.net/corp-credit-products-config-server CONFIG_PROJECT_NAME=corp-credit-products-ui CONFIG_LABEL=master CONFIG_PROFILE=integration yarn start", "start:mocks": "cross-env APP_MOCKS=1 AUTH_DEV_ACCESS=1 yarn start", "start:cypress": "yarn start:mocks", "build": "arui-scripts build", "lint:css": "arui-presets-lint styles", "lint:scripts": "arui-presets-lint scripts", "lint": "yarn lint:css && yarn lint:scripts && yarn format:check", "lint:fix": "yarn lint:css --fix && yarn lint:scripts --fix && yarn format", "format": "arui-presets-lint format", "format:check": "arui-presets-lint format:check", "jest": "arui-scripts test", "cy:open": "cypress open", "cy:run": "cypress run --headless", "cy:parallel": "cypress-parallel -s cy:run -t 4 -d 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}'", "cy:coverage": "yarn nyc report --reporter=text-summary", "coverage": "cp coverage/cypress/coverage-final.json coverage/cypress.json && cp coverage/jest/coverage-final.json coverage/jest.json && nyc merge coverage coverage/full/coverage.json && nyc report --temp-dir=coverage/full --reporter=lcov --report-dir=coverage", "posttest:e2e:run": "yarn coverage", "test": "npm run lint && jest --json --outputFile=coverage/jest/jest.json", "test:e2e:run": "cross-env DISABLE_SOURCEMAPS=1 USE_ISTANBUL=enabled start-server-and-test start:cypress http://localhost:8080/healthmonitor cy:parallel && yarn cy:coverage", "test:e2e:open": "cross-env USE_ISTANBUL=enabled start-server-and-test start:cypress http://localhost:8080/healthmonitor cy:open && yarn cy:coverage", "check-dep-versions": "npm outdated || true", "release-patch": "npm version patch -m 'chore(*) patch version'", "release-minor": "npm version minor -m 'chore(*) minor version'", "release-major": "npm version major -m 'chore(*) major version'", "version": "git fetch --tags", "postversion": "git push origin master --force && git push --tags", "collect-methods": "curl http://localhost:3000/api/methods-list > endpoints.json", "collect-methods-list": "start-server-and-test start:cypress http://localhost:3000/api/methods-list collect-methods", "find-unused": "node scripts/find-unused-files/index.js --verbose", "find-unused:delete": "node scripts/find-unused-files/index.js --delete"}, "dependencies": {"@alfalab/core-components": "48.25.1", "@alfalab/data": "1.8.0", "@alfalab/hooks": "1.12.9", "@alfalab/icons-classic": "2.5.0", "@alfalab/icons-glyph": "2.210.0", "@alfalab/scripts-modules": "1.6.2", "@alfalab/utils": "1.15.0", "@corp-front/client-entrypoint": "2.0.2", "@corp-front/client-event-bus": "3.1.0", "@corp-front/client-event-bus-implementation": "6.0.2", "@corp-front/cloud-config": "^1.5.25", "@corp-front/holding-controls": "3.0.1", "@corp-front/module-loader": "1.8.0", "@corp-front/server-side-render": "18.0.0", "@hapi/hapi": "21.3.10", "@testing-library/jest-dom": "6.6.3", "arui-private": "70.4.0", "bem-react-classname": "1.2.1", "config": "1.30.0", "connected-react-router": "6.9.3", "corp-accounts-api-typescript-services": "0.18.0", "corp-core-credit-products-api-typescript-services": "0.10.2", "corp-credit-document-circulation-api-typescript-services": "0.2.1", "corp-credit-products-api-typescript-services": "0.17.0", "corp-credit-request-api-typescript-services": "0.8.0", "corp-customers-api-typescript-services": "0.34.0", "corp-early-repayment-api-typescript-services": "0.5.0", "corp-global-features-api-typescript-services": "0.27.0", "corp-loan-statements-api-typescript-services": "0.1.0", "corp-new-credit-api-typescript-services": "0.60.0", "corp-proxies-api-typescript-services": "0.11.0", "corp-role-model-mks-permissions-api-typescript-services": "1.4.0", "corp-roles-api-typescript-services": "0.3.0", "corp-users-api-typescript-services": "0.18.0", "corporate-blocking": "3.4.13", "corporate-logger": "4.1.0", "corporate-services": "45.17.0", "crmmb-status-model-core-api-typescript-services": "0.1.0", "date-fns": "2.29.3", "date-fns-tz": "1.3.7", "history": "4.10.1", "js-cookie": "^3.0.1", "lodash": "4.17.21", "qs": "6.11.2", "react": "18.3.1", "react-dom": "18.3.1", "react-redux": "8.1.2", "react-router": "5.3.4", "redux": "4.1.2", "redux-saga": "1.1.3", "regenerator-runtime": "0.13.9", "reselect": "4.1.5", "resolve": "1.22.0", "source-map-support": "0.5.21", "thrift-services": "0.2.4084"}, "devDependencies": {"@babel/core": "7.17.8", "@babel/preset-env": "7.16.11", "@cypress/code-coverage": "3.12.22", "@testing-library/cypress": "10.0.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@types/config": "0.0.41", "@types/jest": "27.4.1", "@types/js-cookie": "3.0.1", "@types/lodash": "4.14.181", "@types/node": "14.18.0", "@types/react": "18.2.39", "@types/react-dom": "18.3.5", "@types/react-redux": "7.1.23", "@types/react-router": "5.1.20", "@types/request-promise-native": "1.0.18", "@types/webpack-env": "1.16.3", "arui-presets-lint": "8.6.0", "arui-scripts": "19.0.5", "arui-scripts-corporate-presets": "4.0.0", "babel-jest": "26.6.3", "babel-loader": "8.2.4", "babel-plugin-istanbul": "6.1.1", "cross-env": "7.0.3", "cypress": "13.13.0", "cypress-multi-reporters": "^1.6.3", "cypress-network-idle": "1.15.0", "cypress-parallel": "0.15.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "nyc": "17.1.0", "redux-devtools": "3.6.1", "redux-mock-store": "1.5.4", "redux-saga-test-plan": "4.0.5", "resize-observer": "1.0.4", "start-server-and-test": "2.0.3", "ts-loader": "9.5.1", "typescript": "4.9.3"}, "resolutions": {"tough-cookie": ">=4.1.3", "loader-utils": "2.0.4", "swiper": ">=6.5.1", "xml2js": ">=0.5.0", "@sideway/formula": ">=3.0.1", "jsonwebtoken": ">=9.0.0", "follow-redirects": ">=1.15.4", "node-fetch": ">=2.6.1", "fstream": ">=1.0.12", "hawk": ">=9.0.1", "json5": ">=2.2.2", "glob-parent": ">=5.1.2", "cryptiles": ">=4.1.2", "joi": "17.x.x", "stylelint": "15.11.0", "@types/react": "18.2.37", "@types/react-dom": "18.3.5"}, "commitlint": {"extends": "./node_modules/arui-presets-lint/commitlint"}, "stylelint": {"extends": "./node_modules/arui-presets-lint/stylelint", "rules": {"no-duplicate-selectors": null}}, "prettier": "./node_modules/arui-presets-lint/prettier", "jest": {"preset": "arui-scripts", "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost/"}, "testRegex": "src/.*\\.test\\.(jsx?|tsx?)$", "collectCoverage": true, "coverageDirectory": "<rootDir>/coverage/jest", "moduleNameMapper": {"#/src/(.*)$": "<rootDir>/src/$1", "#/package.json$": "<rootDir>/package.json"}, "collectCoverageFrom": ["**/src/sagas/workers/*.ts", "**/src/ducks/**/selectors.ts", "**/src/utils/**/*.ts"], "coveragePathIgnorePatterns": ["/node_modules/", "/cypress/", "/src/constants.ts"], "globalSetup": "<rootDir>/jest/global-setup.ts", "setupFilesAfterEnv": ["<rootDir>/jest/setup-env.ts"], "transformIgnorePatterns": ["!node_modules/"], "coverageReporters": ["lcov", "json", "text-summary"]}, "nyc": {"report-dir": "coverage/cypress", "all": true, "include": ["src/**/*.{ts,tsx}"], "exclude": ["**/*.{test,tests}.{ts,tsx}"]}, "corpPlatformSettings": {"isE2EForDevelopActive": true, "shouldCollectEndpoints": true, "jenkinsAgentLabel": "nodejs-18", "isE2EInactive": false}}