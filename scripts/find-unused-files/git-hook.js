#!/usr/bin/env node

const { findUnusedFiles } = require('./index.js');

/**
 * Git hook версия скрипта для поиска неиспользуемых файлов
 * Выводит краткое уведомление, если найдены неиспользуемые файлы
 */

function runGitHookCheck() {
    try {
        // Перехватываем вывод основного скрипта
        const originalLog = console.log;
        let capturedOutput = '';
        console.log = (...args) => {
            capturedOutput += args.join(' ') + '\n';
        };

        const result = findUnusedFiles({
            srcDir: './src',
            deleteFiles: false,
            verbose: false
        });

        // Восстанавливаем console.log
        console.log = originalLog;

        const { unusedFiles, relatedTestFiles } = result;
        const totalUnusedFiles = unusedFiles.length + relatedTestFiles.length;

        if (totalUnusedFiles > 0) {
            console.log('\n⚠️  ВНИМАНИЕ: Найдены неиспользуемые файлы!');
            console.log(`📁 Неиспользуемых файлов: ${unusedFiles.length}`);
            if (relatedTestFiles.length > 0) {
                console.log(`🧪 Связанных тестовых файлов: ${relatedTestFiles.length}`);
            }
            console.log(`📊 Всего файлов для очистки: ${totalUnusedFiles}`);

            console.log('\n💡 Для просмотра списка файлов выполните:');
            console.log('   npm run find-unused');

            console.log('\n🧹 Для автоматического удаления выполните:');
            console.log('   npm run find-unused:delete');

            console.log('\n📖 Подробная документация: scripts/find-unused-files/README.md');
            console.log('─'.repeat(60));

            // Не блокируем коммит, просто уведомляем
            return 0;
        }

        // Если неиспользуемых файлов нет, ничего не выводим
        return 0;
    } catch (error) {
        console.error('❌ Ошибка при проверке неиспользуемых файлов:', error.message);
        // Не блокируем коммит при ошибке
        return 0;
    }
}

// Запускаем только если файл вызван напрямую
if (require.main === module) {
    process.exit(runGitHookCheck());
}

module.exports = { runGitHookCheck };
