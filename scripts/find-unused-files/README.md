# Скрипты для анализа проекта

## find-unused-files.js

Скрипт для поиска и удаления неиспользуемых файлов в проекте.

### Описание

Скрипт анализирует все TypeScript/JavaScript файлы в проекте и находит те, которые нигде не импортируются. Это помогает очистить кодовую базу от мертвого кода и уменьшить размер проекта.

### Возможности

-   ✅ Поиск неиспользуемых файлов в указанной директории
-   ✅ Анализ различных способов импорта (абсолютные, относительные пути)
-   ✅ **Умный анализ экспортируемых компонентов** - находит использование компонентов по их именам
-   ✅ Поддержка многострочных импортов и деструктуризации
-   ✅ **Автоматический поиск связанных тестовых файлов** - находит и удаляет тесты для неиспользуемых файлов
-   ✅ Защита важных файлов от удаления (точки входа, конфигурация)
-   ✅ Отдельная категоризация компонентов
-   ✅ Безопасное удаление найденных файлов
-   ✅ Подробный отчет с командами для ручного удаления

### Использование

#### Через npm scripts (рекомендуется):

```bash
# Найти неиспользуемые файлы
npm run find-unused

# Найти с подробным выводом
npm run find-unused:verbose

# Найти и удалить неиспользуемые файлы
npm run clean-unused

# Краткая проверка (для git hooks)
npm run find-unused:check
```

#### Прямой запуск:

```bash
# Базовый анализ
node scripts/find-unused-files.js

# С подробным выводом
node scripts/find-unused-files.js --verbose

# Удалить найденные файлы
node scripts/find-unused-files.js --delete

# Анализ конкретной директории
node scripts/find-unused-files.js --src=./src/components

# Краткая проверка для git hooks
node scripts/find-unused-files/index.js --brief
```

### Интеграция с Git Hooks

Скрипт автоматически запускается при каждом коммите через Lefthook и показывает уведомление, если найдены неиспользуемые файлы.

#### Настройка в lefthook.yml:

```yaml
pre-commit:
    commands:
        unused-files:
            run: npm run find-unused:check
```

#### Пример вывода при коммите:

```
⚠️  ВНИМАНИЕ: Найдены неиспользуемые файлы!
📁 Неиспользуемых файлов: 3
🧪 Связанных тестовых файлов: 2
📊 Всего файлов для очистки: 5

💡 Для просмотра списка файлов выполните:
   npm run find-unused

🧹 Для автоматического удаления выполните:
   npm run find-unused:delete
```

### Параметры командной строки

-   `--delete` - Автоматически удалить найденные неиспользуемые файлы
-   `--verbose` - Подробный вывод процесса анализа
-   `--brief` - Краткий вывод (для git hooks) - показывает только уведомление при наличии неиспользуемых файлов
-   `--src=<path>` - Указать директорию для анализа (по умолчанию: `./src`)

### Защищенные файлы и директории

Скрипт автоматически исключает из анализа:

**Файлы:**

-   `src/index.tsx` - основная точка входа
-   `src/mobile-index.tsx` - точка входа для мобильной версии
-   `src/configure-store.ts` - конфигурация store
-   `src/custom.d.ts` - TypeScript декларации

**Директории:**

-   `src/types/` - типы TypeScript
-   `src/constants/` - константы
-   `src/ducks/` - Redux логика
-   `src/sagas/` - Redux-saga
-   `src/middlewares/` - middleware
-   `src/server/` - серверная логика

**Автоматически исключаются:**

-   Тестовые файлы (`*.test.*`, `*.spec.*`)
-   Папки `__tests__`
-   `node_modules`

### Пример вывода

```
============================================================
АНАЛИЗ НЕИСПОЛЬЗУЕМЫХ ФАЙЛОВ
============================================================
Директория: ./src
Всего файлов проанализировано: 1015
Неиспользуемых файлов найдено: 4
Связанных тестовых файлов: 2
Неиспользуемых компонентов: 2

============================================================
НЕИСПОЛЬЗУЕМЫЕ КОМПОНЕНТЫ:
============================================================
❌ src/components/unused-component/unused-component.tsx
❌ src/components/old-modal/old-modal.tsx

============================================================
СВЯЗАННЫЕ ТЕСТОВЫЕ ФАЙЛЫ (будут удалены вместе с основными):
============================================================
🧪 src/components/unused-component/__tests__/unused-component.test.tsx
🧪 src/utils/__tests__/unused-helper.test.ts

============================================================
КОМАНДЫ ДЛЯ УДАЛЕНИЯ:
============================================================
# Удалить все неиспользуемые файлы:
rm "src/components/unused-component/unused-component.tsx"
rm "src/components/old-modal/old-modal.tsx"

# Удалить связанные тестовые файлы:
rm "src/components/unused-component/__tests__/unused-component.test.tsx"
rm "src/utils/__tests__/unused-helper.test.ts"

# Или запустить скрипт с флагом --delete:
node scripts/find-unused-files/index.js --delete
```

### Безопасность

-   Скрипт никогда не удаляет защищенные файлы
-   Перед удалением показывает список файлов для подтверждения
-   Можно запустить в режиме анализа без удаления для проверки
-   Все удаления логируются в консоль

### Рекомендации

1. **Перед удалением** всегда запускайте анализ без флага `--delete`
2. **Проверяйте результаты** - убедитесь, что файлы действительно не нужны
3. **Делайте коммит** перед массовым удалением для возможности отката
4. **Запускайте тесты** после удаления файлов
5. **Используйте регулярно** для поддержания чистоты кодовой базы

### Ограничения

-   Не анализирует динамические импорты через переменные
-   Не учитывает импорты в комментариях
-   Может не найти некоторые сложные случаи использования файлов
-   Работает только с TypeScript/JavaScript файлами
