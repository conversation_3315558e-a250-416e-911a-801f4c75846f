#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Скрипт для поиска неиспользуемых файлов в проекте
 * Анализирует импорты и находит файлы, которые нигде не используются
 */

// Функция для получения всех файлов в директории рекурсивно
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
        } else {
            arrayOfFiles.push(fullPath);
        }
    });

    return arrayOfFiles;
}

// Функция для извлечения экспортируемых компонентов из файла
function getExportedComponents(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const exports = [];

        // Паттерны для поиска экспортов
        const exportPatterns = [
            /export\s+const\s+(\w+)/g,
            /export\s+function\s+(\w+)/g,
            /export\s+class\s+(\w+)/g,
            /export\s+interface\s+(\w+)/g,
            /export\s+type\s+(\w+)/g,
            /export\s+enum\s+(\w+)/g,
            /export\s*{\s*([^}]+)\s*}/g,
            /export\s+default\s+(\w+)/g,
        ];

        exportPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                if (pattern.source.includes('{')) {
                    // Обработка export { ... }
                    const exportList = match[1].split(',').map(item =>
                        item.trim().split(/\s+as\s+/)[0].trim()
                    );
                    exports.push(...exportList);
                } else {
                    exports.push(match[1]);
                }
            }
        });

        return [...new Set(exports)]; // Убираем дубликаты
    } catch (error) {
        return [];
    }
}

// Функция для поиска связанных тестовых файлов
function findRelatedTestFiles(filePath, allFiles) {
    const fileName = path.basename(filePath, path.extname(filePath));
    const relatedTests = [];

    // Паттерны для поиска тестовых файлов
    const testPatterns = [
        `${fileName}.test.ts`,
        `${fileName}.test.tsx`,
        `${fileName}.spec.ts`,
        `${fileName}.spec.tsx`,
    ];

    allFiles.forEach(file => {
        const testFileName = path.basename(file);
        if (testPatterns.includes(testFileName)) {
            relatedTests.push(file);
        }
    });

    return relatedTests;
}

// Функция для поиска импортов файла
function isFileImported(filePath, allFiles) {
    const fileName = path.basename(filePath, path.extname(filePath));
    const relativePath = filePath.replace('./src/', '');

    // Получаем экспортируемые компоненты из файла
    const exportedComponents = getExportedComponents(filePath);

    // Исключаем сам файл из поиска
    const otherFiles = allFiles.filter(f => f !== filePath);

    for (const file of otherFiles) {
        if (file.includes('node_modules') || file.includes('.git')) continue;

        try {
            const content = fs.readFileSync(file, 'utf8');

            // Проверяем импорты по имени файла
            const filePatterns = [
                // Абсолютные импорты
                new RegExp(`from ['"]#/src/${relativePath.replace(/\.(tsx?|jsx?)$/, '')}['"]`, 'g'),
                new RegExp(`from ['"]#/src/${relativePath}['"]`, 'g'),
                // Относительные импорты
                new RegExp(`from ['"]\\.\\.?/?.*${fileName}['"]`, 'g'),
                new RegExp(`from ['"]\\.\\.?/?.*${fileName}/`, 'g'),
                // Импорты без кавычек
                new RegExp(`import.*${fileName}`, 'g'),
                // Require
                new RegExp(`require\\(['"].*${fileName}`, 'g'),
                // Динамические импорты
                new RegExp(`import\\(['"].*${fileName}`, 'g'),
            ];

            // Проверяем импорты экспортируемых компонентов
            const componentPatterns = exportedComponents.map(component => [
                new RegExp(`import.*${component}`, 'g'),
                new RegExp(`\\b${component}\\b`, 'g'), // Поиск компонента как отдельного слова
            ]).flat();

            const allPatterns = [...filePatterns, ...componentPatterns];

            for (const pattern of allPatterns) {
                if (pattern.test(content)) {
                    return true;
                }
            }
        } catch (error) {
            // Игнорируем ошибки чтения файлов
        }
    }

    return false;
}

// Основная функция анализа
function findUnusedFiles(options = {}) {
    const {
        srcDir = './src',
        deleteFiles = false,
        verbose = false,
        excludePatterns = []
    } = options;

    const srcFiles = getAllFiles(srcDir)
        .filter(file => file.match(/\.(tsx?|jsx?)$/))
        .filter(file => !file.includes('node_modules'))
        .filter(file => !file.includes('.test.'))
        .filter(file => !file.includes('.spec.'))
        .filter(file => !file.includes('__tests__'))
        .filter(file => !excludePatterns.some(pattern => file.includes(pattern)));

    // Файлы, которые нельзя удалять (точки входа, конфигурация, типы)
    const protectedFiles = [
        'src/index.tsx',
        'src/mobile-index.tsx',
        'src/configure-store.ts',
        'src/custom.d.ts',
    ];

    // Папки, которые нужно защитить от удаления
    const protectedDirs = [
        'src/types',
        'src/constants',
        'src/ducks',
        'src/sagas',
        'src/middlewares',
        'src/server'
    ];

    if (verbose) {
        console.log(`Анализируем ${srcFiles.length} файлов в ${srcDir}...\n`);
    }

    const unusedFiles = [];
    const componentsToDelete = [];
    const relatedTestFiles = [];

    // Получаем все файлы включая тестовые для поиска связанных тестов
    const allProjectFiles = getAllFiles(srcDir)
        .filter(file => file.match(/\.(tsx?|jsx?)$/))
        .filter(file => !file.includes('node_modules'));

    srcFiles.forEach(filePath => {
        // Пропускаем защищенные файлы
        const isProtectedFile = protectedFiles.includes(filePath);
        const isInProtectedDir = protectedDirs.some(dir => filePath.startsWith(dir));

        if (isProtectedFile || isInProtectedDir) {
            return;
        }

        if (!isFileImported(filePath, srcFiles)) {
            unusedFiles.push(filePath);

            // Ищем связанные тестовые файлы
            const relatedTests = findRelatedTestFiles(filePath, allProjectFiles);
            relatedTestFiles.push(...relatedTests);

            // Если это компонент, добавляем в отдельный список
            if (filePath.includes('/components/')) {
                componentsToDelete.push(filePath);
            }
        }
    });

    // Добавляем связанные тестовые файлы к списку для удаления
    const allFilesToDelete = [...unusedFiles, ...relatedTestFiles];
    const uniqueFilesToDelete = [...new Set(allFilesToDelete)];

    // Выводим результаты
    console.log('='.repeat(60));
    console.log(`АНАЛИЗ НЕИСПОЛЬЗУЕМЫХ ФАЙЛОВ`);
    console.log('='.repeat(60));
    console.log(`Директория: ${srcDir}`);
    console.log(`Всего файлов проанализировано: ${srcFiles.length}`);
    console.log(`Неиспользуемых файлов найдено: ${unusedFiles.length}`);
    console.log(`Связанных тестовых файлов: ${relatedTestFiles.length}`);
    console.log(`Неиспользуемых компонентов: ${componentsToDelete.length}`);

    if (componentsToDelete.length > 0) {
        console.log('\n' + '='.repeat(60));
        console.log('НЕИСПОЛЬЗУЕМЫЕ КОМПОНЕНТЫ:');
        console.log('='.repeat(60));
        componentsToDelete.forEach(file => {
            console.log(`❌ ${file}`);
        });
    }

    if (relatedTestFiles.length > 0) {
        console.log('\n' + '='.repeat(60));
        console.log('СВЯЗАННЫЕ ТЕСТОВЫЕ ФАЙЛЫ (будут удалены вместе с основными):');
        console.log('='.repeat(60));
        relatedTestFiles.forEach(file => {
            console.log(`🧪 ${file}`);
        });
    }

    if (unusedFiles.length > 0 || relatedTestFiles.length > 0) {
        console.log('\n' + '='.repeat(60));
        console.log('ВСЕ НЕИСПОЛЬЗУЕМЫЕ ФАЙЛЫ:');
        console.log('='.repeat(60));
        unusedFiles.forEach(file => {
            console.log(`- ${file}`);
        });

        if (deleteFiles) {
            console.log('\n' + '='.repeat(60));
            console.log('УДАЛЕНИЕ ФАЙЛОВ...');
            console.log('='.repeat(60));

            let deletedCount = 0;

            // Удаляем основные файлы
            unusedFiles.forEach(file => {
                try {
                    fs.unlinkSync(file);
                    console.log(`✅ Удален: ${file}`);
                    deletedCount++;
                } catch (error) {
                    console.log(`❌ Ошибка удаления ${file}: ${error.message}`);
                }
            });

            // Удаляем связанные тестовые файлы
            relatedTestFiles.forEach(file => {
                try {
                    fs.unlinkSync(file);
                    console.log(`✅ Удален тест: ${file}`);
                    deletedCount++;
                } catch (error) {
                    console.log(`❌ Ошибка удаления теста ${file}: ${error.message}`);
                }
            });

            console.log(`\nУдалено файлов: ${deletedCount}/${uniqueFilesToDelete.length}`);
        } else {
            console.log('\n' + '='.repeat(60));
            console.log('КОМАНДЫ ДЛЯ УДАЛЕНИЯ:');
            console.log('='.repeat(60));
            console.log('# Удалить все неиспользуемые файлы:');
            unusedFiles.forEach(file => {
                console.log(`rm "${file}"`);
            });

            if (relatedTestFiles.length > 0) {
                console.log('\n# Удалить связанные тестовые файлы:');
                relatedTestFiles.forEach(file => {
                    console.log(`rm "${file}"`);
                });
            }

            console.log('\n# Или запустить скрипт с флагом --delete:');
            console.log('node scripts/find-unused-files/index.js --delete');
        }
    } else {
        console.log('\n✅ Все файлы используются в проекте!');
    }

    return { unusedFiles, componentsToDelete, relatedTestFiles, allFilesToDelete: uniqueFilesToDelete };
}

// CLI интерфейс
if (require.main === module) {
    const args = process.argv.slice(2);
    const deleteFiles = args.includes('--delete');
    const verbose = args.includes('--verbose');

    const srcDirArg = args.find(arg => arg.startsWith('--src='));
    const srcDir = srcDirArg ? srcDirArg.split('=')[1] : './src';

    findUnusedFiles({
        srcDir,
        deleteFiles,
        verbose: verbose || deleteFiles
    });
}

module.exports = { findUnusedFiles };
