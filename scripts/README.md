# Скрипты для анализа проекта

## find-unused-files.js

Скрипт для поиска и удаления неиспользуемых файлов в проекте.

### Описание

Скрипт анализирует все TypeScript/JavaScript файлы в проекте и находит те, которые нигде не импортируются. Это помогает очистить кодовую базу от мертвого кода и уменьшить размер проекта.

### Возможности

- ✅ Поиск неиспользуемых файлов в указанной директории
- ✅ Анализ различных способов импорта (абсолютные, относительные пути)
- ✅ Защита важных файлов от удаления (точки входа, конфигурация)
- ✅ Отдельная категоризация компонентов
- ✅ Безопасное удаление найденных файлов
- ✅ Подробный отчет с командами для ручного удаления

### Использование

#### Через npm scripts (рекомендуется):

```bash
# Найти неиспользуемые файлы
npm run find-unused

# Найти с подробным выводом
npm run find-unused:verbose

# Найти и удалить неиспользуемые файлы
npm run clean-unused
```

#### Прямой запуск:

```bash
# Базовый анализ
node scripts/find-unused-files.js

# С подробным выводом
node scripts/find-unused-files.js --verbose

# Удалить найденные файлы
node scripts/find-unused-files.js --delete

# Анализ конкретной директории
node scripts/find-unused-files.js --src=./src/components
```

### Параметры командной строки

- `--delete` - Автоматически удалить найденные неиспользуемые файлы
- `--verbose` - Подробный вывод процесса анализа
- `--src=<path>` - Указать директорию для анализа (по умолчанию: `./src`)

### Защищенные файлы и директории

Скрипт автоматически исключает из анализа:

**Файлы:**
- `src/index.tsx` - основная точка входа
- `src/mobile-index.tsx` - точка входа для мобильной версии
- `src/configure-store.ts` - конфигурация store
- `src/custom.d.ts` - TypeScript декларации

**Директории:**
- `src/types/` - типы TypeScript
- `src/constants/` - константы
- `src/ducks/` - Redux логика
- `src/sagas/` - Redux-saga
- `src/middlewares/` - middleware
- `src/server/` - серверная логика

**Автоматически исключаются:**
- Тестовые файлы (`*.test.*`, `*.spec.*`)
- Папки `__tests__`
- `node_modules`

### Пример вывода

```
============================================================
АНАЛИЗ НЕИСПОЛЬЗУЕМЫХ ФАЙЛОВ
============================================================
Директория: ./src
Всего файлов проанализировано: 1015
Неиспользуемых файлов найдено: 4
Неиспользуемых компонентов: 4

============================================================
НЕИСПОЛЬЗУЕМЫЕ КОМПОНЕНТЫ:
============================================================
❌ src/components/unused-component/unused-component.tsx
❌ src/components/old-modal/old-modal.tsx

============================================================
КОМАНДЫ ДЛЯ УДАЛЕНИЯ:
============================================================
# Удалить все неиспользуемые файлы:
rm "src/components/unused-component/unused-component.tsx"
rm "src/components/old-modal/old-modal.tsx"

# Или запустить скрипт с флагом --delete:
node scripts/find-unused-files.js --delete
```

### Безопасность

- Скрипт никогда не удаляет защищенные файлы
- Перед удалением показывает список файлов для подтверждения
- Можно запустить в режиме анализа без удаления для проверки
- Все удаления логируются в консоль

### Рекомендации

1. **Перед удалением** всегда запускайте анализ без флага `--delete`
2. **Проверяйте результаты** - убедитесь, что файлы действительно не нужны
3. **Делайте коммит** перед массовым удалением для возможности отката
4. **Запускайте тесты** после удаления файлов
5. **Используйте регулярно** для поддержания чистоты кодовой базы

### Ограничения

- Не анализирует динамические импорты через переменные
- Не учитывает импорты в комментариях
- Может не найти некоторые сложные случаи использования файлов
- Работает только с TypeScript/JavaScript файлами
