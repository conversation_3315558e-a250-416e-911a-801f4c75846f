#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Скрипт для поиска неиспользуемых файлов в проекте
 * Анализирует импорты и находит файлы, которые нигде не используются
 */

// Функция для получения всех файлов в директории рекурсивно
function getAllFiles(dirPath, arrayOfFiles = []) {
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
        const fullPath = path.join(dirPath, file);
        if (fs.statSync(fullPath).isDirectory()) {
            arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
        } else {
            arrayOfFiles.push(fullPath);
        }
    });

    return arrayOfFiles;
}

// Функция для поиска импортов файла
function isFileImported(filePath, allFiles) {
    const fileName = path.basename(filePath, path.extname(filePath));
    const relativePath = filePath.replace('./src/', '');

    // Исключаем сам файл из поиска
    const otherFiles = allFiles.filter(f => f !== filePath);

    for (const file of otherFiles) {
        if (file.includes('node_modules') || file.includes('.git')) continue;

        try {
            const content = fs.readFileSync(file, 'utf8');

            // Проверяем различные способы импорта
            const patterns = [
                // Абсолютные импорты
                new RegExp(`from ['"]#/src/${relativePath.replace(/\.(tsx?|jsx?)$/, '')}['"]`, 'g'),
                new RegExp(`from ['"]#/src/${relativePath}['"]`, 'g'),
                // Относительные импорты
                new RegExp(`from ['"]\\.\\.?/?.*${fileName}['"]`, 'g'),
                new RegExp(`from ['"]\\.\\.?/?.*${fileName}/`, 'g'),
                // Импорты без кавычек
                new RegExp(`import.*${fileName}`, 'g'),
                // Require
                new RegExp(`require\\(['"].*${fileName}`, 'g'),
                // Динамические импорты
                new RegExp(`import\\(['"].*${fileName}`, 'g'),
            ];

            for (const pattern of patterns) {
                if (pattern.test(content)) {
                    return true;
                }
            }
        } catch (error) {
            // Игнорируем ошибки чтения файлов
        }
    }

    return false;
}

// Основная функция анализа
function findUnusedFiles(options = {}) {
    const {
        srcDir = './src',
        deleteFiles = false,
        verbose = false,
        excludePatterns = []
    } = options;

    const srcFiles = getAllFiles(srcDir)
        .filter(file => file.match(/\.(tsx?|jsx?)$/))
        .filter(file => !file.includes('node_modules'))
        .filter(file => !file.includes('.test.'))
        .filter(file => !file.includes('.spec.'))
        .filter(file => !file.includes('__tests__'))
        .filter(file => !excludePatterns.some(pattern => file.includes(pattern)));

    // Файлы, которые нельзя удалять (точки входа, конфигурация, типы)
    const protectedFiles = [
        'src/index.tsx',
        'src/mobile-index.tsx',
        'src/configure-store.ts',
        'src/custom.d.ts',
    ];

    // Папки, которые нужно защитить от удаления
    const protectedDirs = [
        'src/types',
        'src/constants',
        'src/ducks',
        'src/sagas',
        'src/middlewares',
        'src/server'
    ];

    if (verbose) {
        console.log(`Анализируем ${srcFiles.length} файлов в ${srcDir}...\n`);
    }

    const unusedFiles = [];
    const componentsToDelete = [];

    srcFiles.forEach(filePath => {
        // Пропускаем защищенные файлы
        const isProtectedFile = protectedFiles.includes(filePath);
        const isInProtectedDir = protectedDirs.some(dir => filePath.startsWith(dir));

        if (isProtectedFile || isInProtectedDir) {
            return;
        }

        if (!isFileImported(filePath, srcFiles)) {
            unusedFiles.push(filePath);

            // Если это компонент, добавляем в отдельный список
            if (filePath.includes('/components/')) {
                componentsToDelete.push(filePath);
            }
        }
    });

    // Выводим результаты
    console.log('='.repeat(60));
    console.log(`АНАЛИЗ НЕИСПОЛЬЗУЕМЫХ ФАЙЛОВ`);
    console.log('='.repeat(60));
    console.log(`Директория: ${srcDir}`);
    console.log(`Всего файлов проанализировано: ${srcFiles.length}`);
    console.log(`Неиспользуемых файлов найдено: ${unusedFiles.length}`);
    console.log(`Неиспользуемых компонентов: ${componentsToDelete.length}`);

    if (componentsToDelete.length > 0) {
        console.log('\n' + '='.repeat(60));
        console.log('НЕИСПОЛЬЗУЕМЫЕ КОМПОНЕНТЫ:');
        console.log('='.repeat(60));
        componentsToDelete.forEach(file => {
            console.log(`❌ ${file}`);
        });
    }

    if (unusedFiles.length > 0) {
        console.log('\n' + '='.repeat(60));
        console.log('ВСЕ НЕИСПОЛЬЗУЕМЫЕ ФАЙЛЫ:');
        console.log('='.repeat(60));
        unusedFiles.forEach(file => {
            console.log(`- ${file}`);
        });

        if (deleteFiles) {
            console.log('\n' + '='.repeat(60));
            console.log('УДАЛЕНИЕ ФАЙЛОВ...');
            console.log('='.repeat(60));

            let deletedCount = 0;
            unusedFiles.forEach(file => {
                try {
                    fs.unlinkSync(file);
                    console.log(`✅ Удален: ${file}`);
                    deletedCount++;
                } catch (error) {
                    console.log(`❌ Ошибка удаления ${file}: ${error.message}`);
                }
            });

            console.log(`\nУдалено файлов: ${deletedCount}/${unusedFiles.length}`);
        } else {
            console.log('\n' + '='.repeat(60));
            console.log('КОМАНДЫ ДЛЯ УДАЛЕНИЯ:');
            console.log('='.repeat(60));
            console.log('# Удалить все неиспользуемые файлы:');
            unusedFiles.forEach(file => {
                console.log(`rm "${file}"`);
            });
            console.log('\n# Или запустить скрипт с флагом --delete:');
            console.log('node scripts/find-unused-files.js --delete');
        }
    } else {
        console.log('\n✅ Все файлы используются в проекте!');
    }

    return { unusedFiles, componentsToDelete };
}

// CLI интерфейс
if (require.main === module) {
    const args = process.argv.slice(2);
    const deleteFiles = args.includes('--delete');
    const verbose = args.includes('--verbose');

    const srcDirArg = args.find(arg => arg.startsWith('--src='));
    const srcDir = srcDirArg ? srcDirArg.split('=')[1] : './src';

    findUnusedFiles({
        srcDir,
        deleteFiles,
        verbose: verbose || deleteFiles
    });
}

module.exports = { findUnusedFiles };
