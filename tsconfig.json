{
    "extends": "arui-scripts/tsconfig.json",
    "compileOnSave": false,
    "compilerOptions": {
        "lib": ["ES2019", "DOM"],
        "module": "commonjs",
        "target": "ES2019",
        "baseUrl": "./",
        "paths": {
            "#/*": ["./*"]
        },
        "skipLibCheck": true,
        // strictFunctionTypes will trigger errors with decorators.
        "strictFunctionTypes": false,

        // strictNullChecks is almost impossible to turn on, it will give you countless errors
        "strictNullChecks": false,

        "noImplicitAny": false,

        // strictPropertyInitialization depends on nullChecks
        "strictPropertyInitialization": false
    },
    "include": ["**/*.ts", "**/*.tsx"],
    "exclude": ["./config", "*.js", "./coverage", "./cypress", "cypress.config.ts"]
}
