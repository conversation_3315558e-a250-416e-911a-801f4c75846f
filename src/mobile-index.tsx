import React from 'react';
import { Provider } from 'react-redux';
import { type Store } from 'redux';
import { createAppEntryPoint } from '@corp-front/client-entrypoint';
import { ConnectedRouter } from 'connected-react-router';
import { collectTimings } from 'corporate-services/lib/collect-timings';
import { type History } from 'history';

import packageJson from '../package.json';

import { type ApplicationState } from './ducks/application-state';
import { contextRootSelector } from './ducks/settings/selectors';
import { init as initClientApi } from './utils/client-api';
import { initFakeEventBus } from './utils/fake-event-bus';
import configureStore from './configure-store';
import { MobileRoot } from './mobile';

const WrappedRoot = ({ store, history }: { store: Store<ApplicationState>; history: History }) => (
    <Provider store={store}>
        <ConnectedRouter history={history}>
            <MobileRoot history={history} />
        </ConnectedRouter>
    </Provider>
);

createAppEntryPoint({
    name: packageJson.name,
    contextRootSelector,
    initMethod({ preloadedState, contextRoot, render }) {
        initClientApi(contextRoot);
        collectTimings(contextRoot);

        if (process.env.NODE_ENV !== 'production') {
            initFakeEventBus();
        }

        const { store, history } = configureStore(contextRoot || '/')(preloadedState);

        render(<WrappedRoot store={store} history={history} />);

        if (process.env.NODE_ENV !== 'production' && module.hot) {
            module.hot.accept(['./mobile', './configure-store'], () => {
                render(<WrappedRoot store={store} history={history} />);
            });
        }
    },
});
