import { type ApplicationState } from '../application-state';

const earlyPaySelector = (state: ApplicationState) => state.earlyPay;

export const isSuccessfullySignedModalOpenSelector = (state: ApplicationState) =>
    earlyPaySelector(state).isSuccessfullySignedModalOpen;

export const isSigningErrorModalOpenSelector = (state: ApplicationState) =>
    earlyPaySelector(state).isSigningErrorModalOpen;

export const isRequestAlreadyCreatedErrorModalOpenSelector = (state: ApplicationState) =>
    earlyPaySelector(state).isRequestAlreadyCreatedErrorModalOpen;

export const earlyRepaymentDataSelector = (state: ApplicationState) => earlyPaySelector(state).data;
