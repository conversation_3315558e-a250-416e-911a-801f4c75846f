import { type EarlyPayData } from '#/src/types/early-pay-sidebar';
import { type LOG_LEVEL } from '#/src/types/logger';
import { type EarlyPayErrors } from '#/src/utils/errors';

import { EARLY_PAY } from './types';

export const setSuccessfullySignedModalOpen = (isOpen: boolean) => ({
    type: EARLY_PAY.OPEN_SUCCESSFULLY_SIGNED_MODAL,
    payload: isOpen,
});

export const setSigningErrorModalOpen = (isOpen: boolean) => ({
    type: EARLY_PAY.OPEN_SIGNING_ERROR_MODAL,
    payload: isOpen,
});

export const setRequestAlreadyCreatedErrorModalOpen = (isOpen: boolean) => ({
    type: EARLY_PAY.OPEN_REQUEST_ALREADY_CREATED_ERROR_MODAL,
    payload: isOpen,
});

export const setEarlyRepaymentData = (data: EarlyPayData) => ({
    type: EARLY_PAY.SET_EARLY_REPAYMENT_DATA,
    payload: data,
});

export const sendMailKMStart = () => ({
    type: EARLY_PAY.SEND_MAIL_KM_START,
});

export const sendMailKMFinish = () => ({
    type: EARLY_PAY.SEND_MAIL_KM_FINISH,
});

export const sendMailKMError = (error: EarlyPayErrors, logLevel: LOG_LEVEL) => ({
    type: EARLY_PAY.SEND_MAIL_KM_ERROR,
    error,
    logLevel,
});
