import { CURRENCY } from 'corporate-services/lib/currency';

import { type EarlyPayData, earlyPaymentTypes } from '#/src/types/early-pay-sidebar';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { EARLY_PAY } from './types';

export type EarlyPayState = {
    isSuccessfullySignedModalOpen: boolean;
    isSigningErrorModalOpen: boolean;
    isRequestAlreadyCreatedErrorModalOpen: boolean;
    isFetching: boolean;
    data: EarlyPayData;
    error: string;
};
type Actions = ReturnType<InferValueTypes<typeof actions>>;

export const initialState: EarlyPayState = {
    isSuccessfullySignedModalOpen: false,
    isSigningErrorModalOpen: false,
    isRequestAlreadyCreatedErrorModalOpen: false,
    isFetching: false,
    data: {
        docNumber: '',
        fromDate: '',
        paymentType: earlyPaymentTypes.PARTIAL,
        paymentDate: '',
        debtToPay: '',
        interestToPay: '',
        total: '',
        selectedAccountNumber: '',
        currency: CURRENCY.RUR,
        branchNumber: '',
        currentOrganizationEqId: '',
    },
    error: '',
};

export function earlyPayReducer(
    state: EarlyPayState = initialState,
    action: Actions,
): EarlyPayState {
    switch (action.type) {
        case EARLY_PAY.OPEN_SUCCESSFULLY_SIGNED_MODAL:
            return {
                ...state,
                isSuccessfullySignedModalOpen: action.payload,
            };
        case EARLY_PAY.OPEN_SIGNING_ERROR_MODAL:
            return {
                ...state,
                isSigningErrorModalOpen: action.payload,
            };
        case EARLY_PAY.OPEN_REQUEST_ALREADY_CREATED_ERROR_MODAL:
            return {
                ...state,
                isRequestAlreadyCreatedErrorModalOpen: action.payload,
            };

        case EARLY_PAY.SET_EARLY_REPAYMENT_DATA:
            return {
                ...state,
                data: action.payload,
            };

        case EARLY_PAY.SEND_MAIL_KM_START:
            return {
                ...state,
                isFetching: true,
            };

        case EARLY_PAY.SEND_MAIL_KM_FINISH:
            return {
                ...state,
                isFetching: false,
            };

        case EARLY_PAY.SEND_MAIL_KM_ERROR:
            return {
                ...state,
                isFetching: false,
                error: action.error.message,
            };
        default:
            return state;
    }
}
