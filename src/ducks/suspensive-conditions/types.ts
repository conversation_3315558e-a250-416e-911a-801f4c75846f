import { type SuspensiveCondition } from 'corp-credit-products-api-typescript-services';

import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

export const SUSPENSIVE_CONDITIONS_TYPES = {
    GET_SUSPENSIVE_CONDITIONS_START: 'GET_SUSPENSIVE_CONDITIONS_START',
    GET_SUSPENSIVE_CONDITIONS_FINISH: 'GET_SUSPENSIVE_CONDITIONS_FINISH',
    GET_SUSPENSIVE_CONDITIONS_ERROR: 'GET_SUSPENSIVE_CONDITIONS_ERROR',
    RESET_SUSPENSIVE_CONDITIONS_STATE: 'RESET_SUSPENSIVE_CONDITIONS_STATE',
} as const;

export type TSuspensiveConditions = SuspensiveCondition;
export type TSuspensiveConditionsError = ServerResponseError;
