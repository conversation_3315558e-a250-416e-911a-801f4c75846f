import { createSelector } from 'reselect';

import { type ApplicationState } from '../application-state';
import {
    isKIBCategoryCodeSelector,
    isSBCategoryCodeSelector,
} from '../credit-products/selectors/common-credit-products.selectors';
import { isEcoOUViewDeniedSelector } from '../organization/selectors';

export const suspensiveConditionsSelector = (state: ApplicationState) => state.suspensiveConditions;

export const suspensiveConditionsListSelector = createSelector(
    suspensiveConditionsSelector,
    (suspensiveConditions) => suspensiveConditions.list || [],
);

export const pagesCountSuspensiveConditionsSelector = createSelector(
    suspensiveConditionsSelector,
    (suspensiveConditions) => suspensiveConditions.pagesCount || 0,
);

export const isSuspensiveConditionsFetchingSelector = createSelector(
    suspensiveConditionsSelector,
    (suspensiveConditions) => suspensiveConditions.isFetching || false,
);

export const isSuspensiveConditionsAvailableSelector = createSelector(
    isEcoOUViewDeniedSelector,
    isSBCategoryCodeSelector,
    isKIBCategoryCodeSelector,
    (isEcoOUViewDenied, isSBCategoryCode, isKIBCategoryCode) =>
        !isEcoOUViewDenied && (isSBCategoryCode || isKIBCategoryCode),
);

export const isSuspensiveConditionsVisibleSelector = createSelector(
    suspensiveConditionsSelector,
    (suspensiveConditions) => suspensiveConditions.isVisible || false,
);

export const suspensiveConditionsErrorSelector = createSelector(
    suspensiveConditionsSelector,
    (suspensiveConditions) => suspensiveConditions.error || '',
);
