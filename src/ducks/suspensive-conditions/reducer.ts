import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { SUSPENSIVE_CONDITIONS_TYPES, type TSuspensiveConditions } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type SuspensiveConditionsState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    isVisible: boolean;
    list: TSuspensiveConditions[];
    pagesCount: number;
};

export const initialState: SuspensiveConditionsState = {
    error: null,
    isFetching: false,
    isVisible: false,
    list: [],
    pagesCount: 0,
};

export function suspensiveConditionsReducer(state = initialState, action: ActionTypes) {
    switch (action.type) {
        case SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_START:
            return {
                ...state,
                error: null,
                isFetching: true,
            };
        case SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH:
            return {
                ...state,
                isFetching: false,
                isVisible: action.isVisible,
                list: action.list,
                pagesCount: action.pagesCount,
            };
        case SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_ERROR:
            return {
                ...state,
                isFetching: false,
                error: action.error.message,
            };
        case SUSPENSIVE_CONDITIONS_TYPES.RESET_SUSPENSIVE_CONDITIONS_STATE:
            return initialState;
        default:
            return state;
    }
}
