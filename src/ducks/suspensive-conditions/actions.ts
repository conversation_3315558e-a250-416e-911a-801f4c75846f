import { type DealType, type Status } from 'corp-credit-products-api-typescript-services';

import { type LOG_LEVEL } from '#/src/types/logger';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import { SUSPENSIVE_CONDITIONS_TYPES, type TSuspensiveConditions } from './types';

export const getSuspensiveConditionsStart = ({
    dealId,
    dealType,
    status,
    pageNumber,
    pageSize,
    docNumber,
}: {
    dealId: string;
    dealType: DealType;
    status: Status;
    pageNumber: number;
    pageSize: number;
    docNumber: string;
}) => ({
    type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_START,
    dealId,
    dealType,
    status,
    pageNumber,
    pageSize,
    docNumber,
});

export const getSuspensiveConditionsFinish = ({
    list,
    pagesCount,
    isVisible,
}: {
    list: TSuspensiveConditions[];
    pagesCount: number;
    isVisible: boolean;
}) => ({
    type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
    list,
    pagesCount,
    isVisible,
});

export const getSuspensiveConditionsError = (error: ServerResponseError, logLevel: LOG_LEVEL) => ({
    type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_ERROR,
    error,
    logLevel,
});

export const resetSuspensiveConditionsState = () => ({
    type: SUSPENSIVE_CONDITIONS_TYPES.RESET_SUSPENSIVE_CONDITIONS_STATE,
});
