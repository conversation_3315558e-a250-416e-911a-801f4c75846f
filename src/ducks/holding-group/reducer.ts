import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { HOLDING_GROUP_TYPES, type HoldingGroupState } from './types';

type ActionsType = ReturnType<InferValueTypes<typeof actions>>;

export type HoldingGroupInitialState = HoldingGroupState | null;

export const holdingGroupInitialState: HoldingGroupInitialState = null;

export function holdingGroupReducer(
    state = holdingGroupInitialState,
    action: ActionsType,
): HoldingGroupInitialState {
    switch (action.type) {
        case HOLDING_GROUP_TYPES.SET_HOLDING_GROUP:
            return { ...holdingGroupInitialState, ...action.group };
        case HOLDING_GROUP_TYPES.RESET_HOLDING_GROUP:
            return holdingGroupInitialState;
        default:
            return state;
    }
}
