import { type ApplicationState } from '../application-state';

export const holdingGroupSelector = (state: ApplicationState) => state.holdingGroup;

export const holdingGroupIdSelector = (state: ApplicationState) =>
    holdingGroupSelector(state)?.groupInfo?.groupId;

export const holdingGroupOrganizationsSelector = (state: ApplicationState) =>
    holdingGroupSelector(state)?.groupOrganizationsIds;
