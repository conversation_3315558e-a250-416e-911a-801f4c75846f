import { type TLoanPaymentV2 } from 'thrift-services/services/credit_products_v2';
import { type UnixEpoch } from 'thrift-services/utils';

import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { PAYMENT_SCHEDULE_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type PaymentScheduleState = {
    futurePayments: TLoanPaymentV2[];
    payedPayments: TLoanPaymentV2[];
    paymentsList: TLoanPaymentV2[];
    todayDate: UnixEpoch | null;
    isFetching: boolean;
    isGetPaymentScheduleError: boolean;
    isFuturePaymentsRequestError: boolean;
    isPayedPaymentsRequestError: boolean;
    docNumber: string;
    fileError?: { title: string; text: string };
    fileDownloadStart: boolean;
};

const initialState: PaymentScheduleState = {
    futurePayments: [],
    payedPayments: [],
    paymentsList: [],
    todayDate: null,
    isFetching: false,
    isGetPaymentScheduleError: false,
    isFuturePaymentsRequestError: false,
    isPayedPaymentsRequestError: false,
    docNumber: '',
    fileError: undefined,
    fileDownloadStart: false,
};

export function paymentScheduleReducer(
    state: PaymentScheduleState = initialState,
    action: ActionTypes,
): PaymentScheduleState {
    switch (action.type) {
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_START:
            return {
                ...state,
                docNumber: action.docNumber,
                isFetching: true,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_GUARANTY_PAYMENT_SCHEDULE_START:
            return {
                ...state,
                docNumber: action.docNumber,
                isFetching: true,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYED_PAYMENT_SCHEDULE_FINISH:
            return {
                ...state,
                payedPayments: action.paymentsList || [],
                todayDate: action.todayDate || null,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_FUTURE_PAYMENT_SCHEDULE_FINISH:
            return {
                ...state,
                todayDate: action.todayDate || null,
                futurePayments: action.paymentsList || [],
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FINISH:
            return {
                ...state,
                paymentsList: action.paymentsList ? action.paymentsList : state.paymentsList,
                todayDate: action.todayDate ? action.todayDate : state.todayDate,
                isFetching: false,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_ERROR:
            return {
                ...state,
                docNumber: '',
                isGetPaymentScheduleError: true,
                isFetching: false,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_FUTURE_PAYMENT_SCHEDULE_ERROR:
            return {
                ...state,
                futurePayments: [],
                isFuturePaymentsRequestError: true,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYED_PAYMENT_SCHEDULE_ERROR:
            return {
                ...state,
                payedPayments: [],
                isPayedPaymentsRequestError: true,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_START:
            return {
                ...state,
                fileError: undefined,
                fileDownloadStart: true,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_FINISH:
            return {
                ...state,
                fileDownloadStart: false,
            };
        case PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_ERROR:
            return {
                ...state,
                fileDownloadStart: false,
                fileError: action.error,
            };
        default:
            return state;
    }
}
