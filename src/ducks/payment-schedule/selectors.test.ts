import { type ApplicationState } from '../application-state';

import {
    closestFuturePaymentDateSelector,
    closestFuturePaymentSelector,
    futurePaymentsSelector,
    isFetchingPaymentsErrorSelector,
    isFetchingPaymentsSelector,
    isPaymentScheduleFetchingSelector,
    payedPaymentsSelector,
    paymentScheduleDocNumberSelector,
    paymentScheduleListSelector,
    paymentScheduleSelector,
    todayDateSelector,
} from './selectors';

describe('payment schedule selectors', () => {
    const mockState = {
        paymentSchedule: {
            docNumber: '12345',
            isFetching: false,
            todayDate: { seconds: 1700000000 },
            paymentsList: [{ paymentDate: { seconds: 1700000500 }, amount: 100 }],
            futurePayments: [{ paymentDate: { seconds: 1700000500 }, amount: 100 }],
            payedPayments: [{ paymentDate: { seconds: 1699990000 }, amount: 50 }],
            isFuturePaymentsRequestError: false,
            isPayedPaymentsRequestError: false,
            isGetPaymentScheduleError: false,
        },
        settings: {
            currentTime: '2024-11-15',
        },
        creditProducts: {
            parentTranche: {
                payDebtTillDate: '2024-12-15',
                payInterestTillDate: '2024-12-16',
            },
        },
    };

    it('should return the payment schedule', () => {
        const result = paymentScheduleSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual(mockState.paymentSchedule);
    });

    it('should return the payment schedule doc number', () => {
        const result = paymentScheduleDocNumberSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual('12345');
    });

    it('should return fetching status for payment schedule', () => {
        const result = isPaymentScheduleFetchingSelector(mockState as unknown as ApplicationState);

        expect(result).toBe(false);
    });

    it('should return today date from payment schedule', () => {
        const result = todayDateSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual({ seconds: 1700000000 });
    });

    it('should return the payment schedule list', () => {
        const result = paymentScheduleListSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual([{ paymentDate: { seconds: 1700000500 }, amount: 100 }]);
    });

    it('should return future payments', () => {
        const result = futurePaymentsSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual([{ paymentDate: { seconds: 1700000500 }, amount: 100 }]);
    });

    it('should return the closest future payment', () => {
        const result = closestFuturePaymentSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual({ paymentDate: { seconds: 1700000500 }, amount: 100 });
    });

    it('should return the closest future payment date', () => {
        const result = closestFuturePaymentDateSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual({ seconds: 1700000500 });
    });

    it('should return the paid payments', () => {
        const result = payedPaymentsSelector(mockState as unknown as ApplicationState);

        expect(result).toEqual([{ paymentDate: { seconds: 1699990000 }, amount: 50 }]);
    });

    it('should return fetching status for payments', () => {
        const result = isFetchingPaymentsSelector(mockState as unknown as ApplicationState);

        expect(result).toBe(false);
    });

    it('should return any fetching errors for payments', () => {
        const result = isFetchingPaymentsErrorSelector(mockState as unknown as ApplicationState);

        expect(result).toBe(false);
    });
});
