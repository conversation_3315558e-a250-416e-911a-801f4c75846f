import {
    type SchedulePrintData,
    type TCreditPaymentSchedule,
} from 'thrift-services/services/credit_products';

import { type LOG_LEVEL } from '../../types/logger';
import { type CreditProductErrors } from '../../utils/errors';

import { PAYMENT_SCHEDULE_TYPES } from './types';

export function getPaymentScheduleStart(docNumber: string) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_START,
        docNumber,
    };
}

export function getPayedPaymentScheduleFinish(payedCreditPaymentsSchedule: TCreditPaymentSchedule) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYED_PAYMENT_SCHEDULE_FINISH,
        paymentsList: payedCreditPaymentsSchedule.payments,
        todayDate: payedCreditPaymentsSchedule.actualityDate,
    };
}

export function getPayedPaymentScheduleError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYED_PAYMENT_SCHEDULE_ERROR,
        error,
        logLevel,
    };
}

export function getFuturePaymentScheduleFinish(
    futureCreditPaymentsSchedule: TCreditPaymentSchedule,
) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_FUTURE_PAYMENT_SCHEDULE_FINISH,
        paymentsList: futureCreditPaymentsSchedule.payments,
        todayDate: futureCreditPaymentsSchedule.actualityDate,
    };
}

export function getFuturePaymentScheduleError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_FUTURE_PAYMENT_SCHEDULE_ERROR,
        error,
        logLevel,
    };
}

export function getPaymentScheduleError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_ERROR,
        error,
        logLevel,
    };
}

export function getPaymentScheduleFinish(paymentSchedule?: TCreditPaymentSchedule) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FINISH,
        paymentsList: paymentSchedule ? paymentSchedule.payments : null,
        todayDate: paymentSchedule ? paymentSchedule.actualityDate : null,
    };
}

export function getPaymentScheduleFile(data: SchedulePrintData) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE,
        data,
    };
}

export function getPaymentScheduleFileError(
    error: { title: string; text: string },
    logLevel: LOG_LEVEL,
) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_ERROR,
        error,
        logLevel,
    };
}

export function getPaymentScheduleFileStart() {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_START,
    };
}
export function getPaymentScheduleFilFinish() {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_FINISH,
    };
}

export function getGuarantyPaymentSchedule(docNumber: string) {
    return {
        type: PAYMENT_SCHEDULE_TYPES.GET_GUARANTY_PAYMENT_SCHEDULE_START,
        docNumber,
    };
}
