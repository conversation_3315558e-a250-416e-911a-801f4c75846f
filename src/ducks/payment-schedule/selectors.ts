import isEqual from 'date-fns/isEqual';
import { createSelector } from 'reselect';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { parseDateFromAny } from '#/src/utils/date';

import { type ApplicationState } from '../application-state';
import { parentTrancheSelector } from '../credit-products/selectors/tranches.selectors';
import { currentTimeSelector } from '../settings/selectors';

export const paymentScheduleSelector = (state: ApplicationState) => state.paymentSchedule;

export const paymentScheduleDocNumberSelector = (state: ApplicationState) =>
    state.paymentSchedule.docNumber;

export const isPaymentScheduleFetchingSelector = (state: ApplicationState) =>
    state.paymentSchedule.isFetching;

export const todayDateSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.todayDate,
);

export const paymentScheduleListSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.paymentsList,
);

export const futurePaymentsSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.futurePayments,
);

export const closestFuturePaymentSelector = createSelector(
    futurePaymentsSelector,
    todayDateSelector,
    (futurePayments, todayDate) => {
        const realFuturePayments = futurePayments.filter(
            (payment) => (payment?.paymentDate?.seconds || 0) >= (todayDate?.seconds || 0),
        );

        return (realFuturePayments.length ? realFuturePayments[0] : {}) as TLoanPayment;
    },
);

export const paymentOfMainDebtAndInterestAtDifferentDatesSelector = createSelector(
    currentTimeSelector,
    parentTrancheSelector,
    (currentTime, tranche) =>
        !!tranche &&
        !isEqual(
            parseDateFromAny(currentTime, tranche.payDebtTillDate),
            parseDateFromAny(currentTime, tranche.payInterestTillDate),
        ),
);

export const closestFuturePaymentDateSelector = createSelector(
    closestFuturePaymentSelector,
    (futurePayment) => futurePayment.paymentDate,
);

export const payedPaymentsSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.payedPayments,
);

export const isFetchingPaymentsSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.isFetching,
);

export const isDownloadPaymentsFileStartSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.fileDownloadStart,
);

export const isDownloadPaymentsFileErrorSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) => paymentSchedule.fileError,
);

export const isFetchingPaymentsErrorSelector = createSelector(
    paymentScheduleSelector,
    (paymentSchedule) =>
        paymentSchedule.isFuturePaymentsRequestError ||
        paymentSchedule.isPayedPaymentsRequestError ||
        paymentSchedule.isGetPaymentScheduleError,
);
