import { type FormValues } from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-filter/types';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '#/src/ducks/credit-products-main-menu/types';
import { LOG_LEVEL } from '#/src/types/logger';

import {
    getCategoryMainMenuError,
    getCategoryMainMenuFinish,
    getCategoryMainMenuStart,
    getCountCreditProductsByTypeError,
    getCountCreditProductsByTypeFinish,
    getCountCreditProductsByTypeStart,
    getCreditProductsMainMenuError,
    getCreditProductsMainMenuFinish,
    getCreditProductsMainMenuStart,
    resetCreditProductsMainMenuState,
} from '../actions';

describe('Credit Products Main Menu Actions', () => {
    it('должен создать экшен для начала загрузки кредитных продуктов', () => {
        const filter: FormValues = {
            company: [],
            products: [],
            companiesOptions: [],
            status: [],
            sortBy: [],
            docNumber: '',
        };
        const pagination = { pageNumber: 1, pageSize: 10 };
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_START,
            filter,
            pagination,
        };

        expect(getCreditProductsMainMenuStart(filter, pagination)).toEqual(expectedAction);
    });

    it('должен создать экшен для завершения загрузки кредитных продуктов', () => {
        const response = { creditProducts: [] };
        const filter: FormValues = {
            company: [],
            products: [],
            companiesOptions: [],
            status: [],
            sortBy: [],
            docNumber: '',
        };
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_FINISH,
            response,
            filterValue: filter,
        };

        expect(getCreditProductsMainMenuFinish(response, filter)).toEqual(expectedAction);
    });

    it('должен создать экшен для ошибки загрузки кредитных продуктов', () => {
        const error = new Error('Ошибка загрузки');
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_ERROR,
            error,
        };

        expect(getCreditProductsMainMenuError(error)).toEqual(expectedAction);
    });

    it('должен создать экшен для сброса состояния кредитных продуктов', () => {
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.RESET_CREDIT_PRODUCTS_MAIN_MENU_STATE,
        };

        expect(resetCreditProductsMainMenuState()).toEqual(expectedAction);
    });

    it('должен создать экшен для начала загрузки категории', () => {
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_START,
        };

        expect(getCategoryMainMenuStart()).toEqual(expectedAction);
    });

    it('должен создать экшен для завершения загрузки категории', () => {
        const category = { sksCode: '123', sksName: 'Test Category' };
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_FINISH,
            category,
        };

        expect(getCategoryMainMenuFinish(category)).toEqual(expectedAction);
    });

    it('должен создать экшен для ошибки загрузки категории', () => {
        const error = new Error('Ошибка загрузки');
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_ERROR,
            error,
        };

        expect(getCategoryMainMenuError(error)).toEqual(expectedAction);
    });

    it('должен создать экшен для начала загрузки количества кредитных продуктов по типу', () => {
        const customerIds = ['123', '456'];
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_START,
            customerIds,
        };

        expect(getCountCreditProductsByTypeStart({ customerIds })).toEqual(expectedAction);
    });

    it('должен создать экшен для завершения загрузки количества кредитных продуктов по типу', () => {
        const productCounts = [
            { productType: 2, count: 3 },
            { productType: 5, count: 5 },
        ];
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
            productCounts,
        };

        expect(getCountCreditProductsByTypeFinish({ productCounts })).toEqual(expectedAction);
    });

    it('должен создать экшен для ошибки загрузки количества кредитных продуктов по типу', () => {
        const error = new Error('Ошибка получения количества');
        const logLevel = LOG_LEVEL.ERROR;
        const expectedAction = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR,
            error,
            logLevel,
        };

        expect(getCountCreditProductsByTypeError(error, logLevel)).toEqual(expectedAction);
    });
});
