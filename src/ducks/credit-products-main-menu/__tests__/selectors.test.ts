import { type CreditProductCount } from 'corp-credit-products-api-typescript-services';

import { type DeepPartial } from 'arui-private/types';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import { type ApplicationState } from '#/src/ducks/application-state';

import {
    categoryCodeMainMenuSelector,
    creditProductsMainMenuErrorSelector,
    creditProductsMainMenuFilterValuesSelector,
    creditProductsMainMenuPaginationSelector,
    creditProductsMainMenuSelector,
    currentOrganizationMainMenuSelector,
    isCategoryMainMenuFetchingSelector,
    isCreditProductsMainMenuEmptySelector,
    isCreditProductsMainMenuFetchingSelector,
    isHiddenGuarantyMainMenuTabSelector,
    isHiddenTrancheMainMenuTabSelector,
    isMmbCategoryMainMenuSelector,
    isNeededToCallCategorySelector,
} from '../selectors';

describe('Credit Products Main Menu Selectors', () => {
    const state: DeepPartial<ApplicationState> = {
        creditProductsMainMenu: {
            products: null,
            category: {
                isFetching: false,
                error: null,
                category: { sksCode: '', sksName: '' },
            },
            alfaCreditStatus: {
                alfaCreditStatus: {
                    checks: [
                        {
                            check: 'NOT_OK',
                            claims: undefined,
                            code: 'STATUS_CODE',
                            customerId: 'org1',
                        },
                    ],
                    generalSuccessful: false,
                },
            },
            isFetching: false,
            error: null,
            filterValue: null,
        },
        organization: {
            current: 'org1',
            list: [{ eqId: 'org1' }],
        },

        // ... другие части состояния
    };

    it('creditProductsMainMenuSelector должен возвращать состояние creditProductsMainMenu', () => {
        expect(creditProductsMainMenuSelector(state as ApplicationState)).toEqual(
            state.creditProductsMainMenu,
        );
    });

    it('isCreditProductsMainMenuFetchingSelector должен возвращать isFetching', () => {
        expect(isCreditProductsMainMenuFetchingSelector(state as ApplicationState)).toEqual(false);
    });

    it('creditProductsMainMenuErrorSelector должен возвращать error', () => {
        expect(creditProductsMainMenuErrorSelector(state as ApplicationState)).toEqual(null);
    });

    it('isCategoryMainMenuFetchingSelector должен возвращать isFetching для категории', () => {
        expect(isCategoryMainMenuFetchingSelector(state as ApplicationState)).toEqual(false);
    });

    it('categoryCodeMainMenuSelector должен возвращать sksCode категории', () => {
        expect(categoryCodeMainMenuSelector(state as ApplicationState)).toEqual('');
    });

    it('isMmbCategoryMainMenuSelector должен возвращать false для MMB категории', () => {
        expect(isMmbCategoryMainMenuSelector(state as ApplicationState)).toEqual(false);
    });

    it('currentOrganizationMainMenuSelector должен возвращать текущую организацию', () => {
        expect(currentOrganizationMainMenuSelector(state as ApplicationState)).toEqual(
            state.organization?.list?.[0],
        );
    });

    it('isNeededToCallCategorySelector должен возвращать true, если нужно вызвать категорию', () => {
        expect(isNeededToCallCategorySelector(state as ApplicationState)).toEqual(true);
    });

    it('isCreditProductsMainMenuEmptySelector должен возвращать true, если продукты пустые', () => {
        expect(isCreditProductsMainMenuEmptySelector(state as ApplicationState)).toEqual(true);
    });

    it('creditProductsMainMenuPaginationSelector должен возвращать pagination продуктов', () => {
        expect(creditProductsMainMenuPaginationSelector(state as ApplicationState)).toEqual(
            undefined,
        );
    });

    it('creditProductsMainMenuFilterValuesSelector должен возвращать filterValue продуктов', () => {
        expect(creditProductsMainMenuFilterValuesSelector(state as ApplicationState)).toEqual(null);
    });
});

describe('isHiddenTabByCount selectors', () => {
    const getStateWithProductCounts = (
        counts: CreditProductCount[] | null,
    ): DeepPartial<ApplicationState> => ({
        creditProductsMainMenu: {
            productCounts: {
                info: counts,
            },
        },
    });

    it('isHiddenTrancheMainMenuTabSelector должен вернуть true, если нет productCounts', () => {
        const state = {
            creditProductsMainMenu: {},
        } as DeepPartial<ApplicationState>;

        expect(isHiddenTrancheMainMenuTabSelector(state as ApplicationState)).toBe(true);
    });

    it('isHiddenTrancheMainMenuTabSelector должен вернуть true, если count <= 0', () => {
        const state = getStateWithProductCounts([
            { productType: Number(ECreditProductsCodes.CREDIT_LINE), count: 0 },
        ]);

        expect(isHiddenTrancheMainMenuTabSelector(state as ApplicationState)).toBe(true);
    });

    it('isHiddenTrancheMainMenuTabSelector должен вернуть false, если count > 0', () => {
        const state = getStateWithProductCounts([
            { productType: Number(ECreditProductsCodes.CREDIT_LINE), count: 3 },
        ]);

        expect(isHiddenTrancheMainMenuTabSelector(state as ApplicationState)).toBe(false);
    });

    it('isHiddenGuarantyMainMenuTabSelector должен вернуть true, если count <= 0', () => {
        const state = getStateWithProductCounts([
            { productType: Number(ECreditProductsCodes.GUARANTY_LINE), count: 0 },
        ]);

        expect(isHiddenGuarantyMainMenuTabSelector(state as ApplicationState)).toBe(true);
    });

    it('isHiddenGuarantyMainMenuTabSelector должен вернуть false, если count > 0', () => {
        const state = getStateWithProductCounts([
            { productType: Number(ECreditProductsCodes.GUARANTY_LINE), count: 1 },
        ]);

        expect(isHiddenGuarantyMainMenuTabSelector(state as ApplicationState)).toBe(false);
    });
});
