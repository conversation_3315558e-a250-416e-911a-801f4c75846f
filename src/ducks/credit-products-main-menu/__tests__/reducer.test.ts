import { type InferValueTypes } from 'corporate-blocking/common/actions-type';

import type * as actions from '../actions';
import {
    creditProductMainMenuInitialState,
    creditProductsMainMenuReducer,
    type CreditProductsMainMenuState,
} from '../reducer';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

describe('creditProductsMainMenuReducer', () => {
    it('должен возвращать начальное состояние по умолчанию', () => {
        const action = { type: 'UNKNOWN_ACTION' };
        const expectedState: CreditProductsMainMenuState = creditProductMainMenuInitialState;

        const newState = creditProductsMainMenuReducer(undefined, action as ActionTypes);

        expect(newState).toEqual(expectedState);
    });
});

describe('creditProductsMainMenuReducer — productCounts', () => {
    it('должен устанавливать isFetching в true при GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_START', () => {
        const action = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_START,
        };

        const newState = creditProductsMainMenuReducer(
            creditProductMainMenuInitialState,
            action as ActionTypes,
        );

        expect(newState.productCounts.isFetching).toBe(true);
        expect(newState.productCounts.error).toBeNull();
        expect(newState.productCounts.info).toBeNull();
    });

    it('должен устанавливать productCounts и isFetching в false при GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH', () => {
        const mockCounts = [
            { productType: 2, count: 5 },
            { productType: 5, count: 10 },
        ];

        const action = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
            productCounts: mockCounts,
        };

        const newState = creditProductsMainMenuReducer(
            {
                ...creditProductMainMenuInitialState,
                productCounts: {
                    ...creditProductMainMenuInitialState.productCounts,
                    isFetching: true,
                },
            },
            action as ActionTypes,
        );

        expect(newState.productCounts.isFetching).toBe(false);
        expect(newState.productCounts.info).toEqual(mockCounts);
        expect(newState.productCounts.error).toBeNull();
    });

    it('должен устанавливать ошибку и isFetching в false при GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR', () => {
        const errorMessage = 'Ошибка получения данных';

        const action = {
            type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR,
            error: { message: errorMessage },
        };

        const newState = creditProductsMainMenuReducer(
            {
                ...creditProductMainMenuInitialState,
                productCounts: {
                    ...creditProductMainMenuInitialState.productCounts,
                    isFetching: true,
                },
            },
            action as ActionTypes,
        );

        expect(newState.productCounts.isFetching).toBe(false);
        expect(newState.productCounts.error).toBe(errorMessage);
        expect(newState.productCounts.info).toBeNull();
    });
});
