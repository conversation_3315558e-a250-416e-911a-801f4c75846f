import { type AlfaCreditStatusResponse } from 'corp-credit-document-circulation-api-typescript-services';
import { type CreditProductCount } from 'corp-credit-products-api-typescript-services';
import { type CreditProductsResponse } from 'corp-credit-products-api-typescript-services/dist/creditProductsResponse';
import { type Page } from 'corp-credit-products-api-typescript-services/dist/page';
import { type Category } from 'corp-customers-api-typescript-services';

import { type FormValues } from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-filter/types';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '#/src/ducks/credit-products-main-menu/types';
import { type LOG_LEVEL } from '#/src/types/logger';

export function getCreditProductsMainMenuStart(filter: FormValues, pagination: Page) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_START,
        filter,
        pagination,
    };
}

export function getCreditProductsMainMenuFinish(
    response: CreditProductsResponse,
    filter: FormValues | null,
) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_FINISH,
        response,
        filterValue: filter,
    };
}

export function getCreditProductsMainMenuError(error: Error) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_ERROR,
        error,
    };
}

export function resetCreditProductsMainMenuState() {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.RESET_CREDIT_PRODUCTS_MAIN_MENU_STATE,
    };
}

export function getCategoryMainMenuStart(organizationId?: string) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_START,
        organizationId,
    };
}

export function getCategoryMainMenuFinish(category: Category) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_FINISH,
        category,
    };
}

export function getCategoryMainMenuError(error: Error) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_ERROR,
        error,
    };
}

export function getOrConnectAlfaCreditMainMenuStart({ customerIds }: { customerIds: string[] }) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_START,
        customerIds,
    };
}

export function getOrConnectAlfaCreditMainMenuFinish(
    alfaCreditStatus: AlfaCreditStatusResponse | null,
) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_FINISH,
        alfaCreditStatus,
    };
}

export function getOrConnectAlfaCreditMainMenuError(error: Error, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_ERROR,
        error,
        logLevel,
    };
}

export function getCountCreditProductsByTypeStart({ customerIds }: { customerIds: string[] }) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_START,
        customerIds,
    };
}

export function getCountCreditProductsByTypeFinish({
    productCounts,
}: {
    productCounts: CreditProductCount[];
}) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
        productCounts,
    };
}

export function getCountCreditProductsByTypeError(error: Error, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR,
        error,
        logLevel,
    };
}
