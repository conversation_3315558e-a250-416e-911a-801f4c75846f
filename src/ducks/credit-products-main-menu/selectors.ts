import { createSelector } from 'reselect';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import {
    MMB_CATEGORY_CODES,
    type OrganizationCategoryCodes,
} from '#/src/constants/organization-category-codes';
import { type ApplicationState } from '#/src/ducks/application-state';

import {
    currentOrganizationEqIdSelector,
    organizationCategoryCodeSelector,
    organizationSelector,
    organizationsListSelector,
} from '../organization/selectors';

export const creditProductsMainMenuSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu;

export const isCreditProductsMainMenuFetchingSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.isFetching;

export const creditProductsMainMenuErrorSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.error;

// category
export const isCategoryMainMenuFetchingSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.category.isFetching;

export const categoryCodeMainMenuSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.category.category?.sksCode;

export const isMmbCategoryMainMenuSelector = createSelector(
    categoryCodeMainMenuSelector,
    (code = '') => MMB_CATEGORY_CODES.includes(code as OrganizationCategoryCodes),
);

export const currentOrganizationMainMenuSelector = createSelector(
    organizationsListSelector,
    organizationSelector,
    (organizations, currentOrganization) =>
        organizations.find((organization) => organization.eqId === currentOrganization.current),
);

export const isNeededToCallCategorySelector = createSelector(
    categoryCodeMainMenuSelector,
    organizationCategoryCodeSelector,
    currentOrganizationEqIdSelector,
    organizationSelector,
    (categoryCodeMainMenu, organizationCategoryCode, currentOrganizationEqId, organization) =>
        categoryCodeMainMenu != organizationCategoryCode ||
        currentOrganizationEqId != organization.current,
);

// случай, когда ошибки нет и продукты не найдены
export const hasCreditProductMainMenuSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.products?.hasCreditProduct;

export const isCreditProductsMainMenuEmptySelector = (state: ApplicationState) =>
    !state.creditProductsMainMenu.products?.creditProducts?.length;

export const creditProductsMainMenuPaginationSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.products?.pagination;

export const creditProductsMainMenuFilterValuesSelector = createSelector(
    creditProductsMainMenuSelector,
    (state) => state?.filterValue,
);

export const alfaCreditStatusSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.alfaCreditStatus?.alfaCreditStatus || false;

export const isAlfaCreditWidgetButtonVisibleSelector = createSelector(
    alfaCreditStatusSelector,
    currentOrganizationEqIdSelector,
    (alfaCreditStatus, currentOrgId) => {
        if (!alfaCreditStatus) {
            return false;
        }

        const status = alfaCreditStatus?.checks?.find((check) => check.customerId === currentOrgId);

        return status?.check === 'OK';
    },
);

const productCountsSelector = (state: ApplicationState) =>
    state.creditProductsMainMenu.productCounts?.info;

const isHiddenTabByCount = ({ productType }: { productType: ECreditProductsCodes | number }) =>
    createSelector(productCountsSelector, (productCounts) => {
        if (!productCounts) return true;

        const count = productCounts.find(
            (item) => item.productType.toString() === productType,
        )?.count;

        return (count ?? 0) <= 0;
    });

export const isHiddenTrancheMainMenuTabSelector = isHiddenTabByCount({
    productType: ECreditProductsCodes.CREDIT_LINE,
});

export const isHiddenGuarantyMainMenuTabSelector = isHiddenTabByCount({
    productType: ECreditProductsCodes.GUARANTY_LINE,
});
