import { type AlfaCreditStatusResponse } from 'corp-credit-document-circulation-api-typescript-services';
import { type CreditProductCount } from 'corp-credit-products-api-typescript-services';
import { type CreditProductsResponse } from 'corp-credit-products-api-typescript-services/dist/creditProductsResponse';
import { type Category } from 'corp-customers-api-typescript-services';

import { type FormValues } from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-filter/types';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '#/src/ducks/credit-products-main-menu/types';
import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

type TCategoryState = {
    isFetching: boolean;
    error: null | string | EErrorMessages;
    category: Category;
};

type TAlfaCreditStatusState = {
    isFetching: boolean;
    error: null | string | EErrorMessages;
    alfaCreditStatus: AlfaCreditStatusResponse | null;
};

type TProductCountsState = {
    isFetching: boolean;
    error: null | string | EErrorMessages;
    info: CreditProductCount[] | null;
};

export type CreditProductsMainMenuState = {
    products: CreditProductsResponse | null;
    category: TCategoryState;
    alfaCreditStatus: TAlfaCreditStatusState;
    isFetching: boolean;
    error: null | string | EErrorMessages;
    filterValue: FormValues | null;
    productCounts: TProductCountsState;
};

const categoryInitialState: TCategoryState = {
    isFetching: false,
    error: null,
    category: {
        sksCode: '',
        sksName: '',
    },
};

const alfaCreditStatusInitialState: TAlfaCreditStatusState = {
    isFetching: false,
    error: null,
    alfaCreditStatus: null,
};

const productCountsInitialState: TProductCountsState = {
    isFetching: false,
    error: null,
    info: null,
};

export const creditProductMainMenuInitialState: CreditProductsMainMenuState = {
    products: null,
    category: categoryInitialState,
    alfaCreditStatus: alfaCreditStatusInitialState,
    isFetching: false,
    error: null,
    filterValue: null,
    productCounts: productCountsInitialState,
};

export const creditProductsMainMenuReducer = (
    state: CreditProductsMainMenuState = creditProductMainMenuInitialState,
    action: ActionTypes,
): CreditProductsMainMenuState => {
    switch (action.type) {
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_START:
            return {
                ...state,
                products: null,
                isFetching: true,
                error: null,
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_FINISH:
            return {
                ...state,
                products: action.response,
                isFetching: false,
                error: null,
                filterValue: action.filterValue,
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CREDIT_PRODUCTS_MAIN_MENU_ERROR:
            return {
                ...state,
                products: null,
                isFetching: false,
                error: action.error?.message,
                filterValue: null,
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.RESET_CREDIT_PRODUCTS_MAIN_MENU_STATE:
            return {
                ...creditProductMainMenuInitialState,
                alfaCreditStatus: state.alfaCreditStatus,
                productCounts: state.productCounts,
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_START:
            return {
                ...state,
                category: { ...categoryInitialState, isFetching: true },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_FINISH:
            return {
                ...state,
                category: {
                    ...categoryInitialState,
                    isFetching: false,
                    category: action.category,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_ERROR:
            return {
                ...state,
                category: {
                    ...categoryInitialState,
                    isFetching: false,
                    error: action.error?.message,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_START:
            return {
                ...state,
                alfaCreditStatus: {
                    ...state.alfaCreditStatus,
                    isFetching: true,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_FINISH:
            return {
                ...state,
                alfaCreditStatus: {
                    ...state.alfaCreditStatus,
                    isFetching: false,
                    alfaCreditStatus: action.alfaCreditStatus,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_ERROR:
            return {
                ...state,
                alfaCreditStatus: {
                    ...state.alfaCreditStatus,
                    isFetching: false,
                    error: action.error?.message,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_START:
            return {
                ...state,
                productCounts: {
                    ...state.productCounts,
                    isFetching: true,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH:
            return {
                ...state,
                productCounts: {
                    ...state.productCounts,
                    isFetching: false,
                    info: action.productCounts,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR:
            return {
                ...state,
                productCounts: {
                    ...state.productCounts,
                    isFetching: false,
                    error: action.error?.message,
                },
            };
        default:
            return state;
    }
};
