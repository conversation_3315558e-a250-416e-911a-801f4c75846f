import { type SharedUIV1Respond } from 'corporate-services/server/services/shared-ui';

export type SharedUIState = SharedUIV1Respond;

export const initialState: SharedUIState = {
    cssSourcesUrls: [],
    jsSourcesUrls: [],
    serverJsSourcesUrls: [],
    version: '',
    contextRoot: '',
    state: {},
};

export function sharedUIReducer(state: SharedUIState = initialState): SharedUIState {
    return state;
}
