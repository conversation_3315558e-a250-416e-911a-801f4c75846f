import { type RouterState } from 'connected-react-router';
import { type TBlocking as BlockingPanelState } from 'corporate-blocking/blocking-panel/reducers';

import {
    creditProductMainMenuInitialState,
    type CreditProductsMainMenuState,
} from '#/src/ducks/credit-products-main-menu/reducer';
import { type CreditWidgetStatusState } from '#/src/ducks/credit-widget-status/reducer';
import { type SignedDocumentsState } from '#/src/ducks/signed-documents/reducer';

import { type AccountsState } from './accounts/reducer';
import { type AddressesState } from './addresses/reducer';
import { type AppState, initialState as appInitialState } from './app/reducer';
import { type AttachDocumentsState } from './attach-documents/reducer';
import { type SignConfirmationState } from './correspondence/reducer';
import { type CreditCalculatorState } from './credit-calculator/reducer';
import { type CreditDocumentCirculationState } from './credit-document-circulation/reducer';
import {
    type CreditOffersState,
    initialState as creditOffersInitialState,
} from './credit-offers/reducer';
import { type CreditProcessingState } from './credit-processing/reducer';
import { type CreditProductsState } from './credit-products/reducer';
import {
    creditProductHeaderInitialState,
    type CreditProductsHeaderState,
} from './credit-products-header/reducer';
import { type CreditRequestsState } from './credit-requests/reducer';
import { type DocumentsState } from './documents/reducer';
import { type EarlyPayState } from './early-pay/reducer';
import { holdingGroupInitialState } from './holding-group/reducer';
import { type HoldingGroupState } from './holding-group/types';
import { mksPermissionsInitialState, type MksPermissionsState } from './mks-permissions/reducer';
import {
    initialState as organizationInitialState,
    type OrganizationsState,
} from './organization/reducer';
import { type PaymentScheduleState } from './payment-schedule/reducer';
import { initialState as settingsInitialState, type SettingsState } from './settings/reducer';
import { initialState as sharedUIInitialState, type SharedUIState } from './shared/reducer';
import { signModuleInitialState, type SignModuleState } from './sign-module/reducer';
import { type StatementRequestsState } from './statement-requests/reducer';
import { type SuspensiveConditionsState } from './suspensive-conditions/reducer';
import { type UfrAkCorpGatewayRestState } from './ufr-ak-corp-gateway-rest/reducer';
import { type UserState } from './user/reducer';

export type ApplicationState = Readonly<{
    app: AppState;
    settings: SettingsState;
    organization: OrganizationsState;
    sharedUI: SharedUIState;
    creditOffers: CreditOffersState;
    creditWidgetStatus: CreditWidgetStatusState;
    creditProducts: CreditProductsState;
    creditProductsMainMenu: CreditProductsMainMenuState;
    creditProductsHeader: CreditProductsHeaderState;
    paymentSchedule: PaymentScheduleState;
    creditProcessing: CreditProcessingState;
    creditDocumentCirculation: CreditDocumentCirculationState;
    creditRequests: CreditRequestsState;
    statementRequests: StatementRequestsState;
    suspensiveConditions: SuspensiveConditionsState;
    router: RouterState;
    blockings: BlockingPanelState;
    accounts: AccountsState;
    signConfirmation: SignConfirmationState;
    earlyPay: EarlyPayState;
    mksPermissions: MksPermissionsState;
    creditCalculator: CreditCalculatorState;
    attachDocuments: AttachDocumentsState;
    signedDocuments: SignedDocumentsState;
    signModule: SignModuleState;
    ufrAkCorpGatewayRest: UfrAkCorpGatewayRestState;
    documents: DocumentsState;
    user: UserState;
    addresses: AddressesState;
    holdingGroup: HoldingGroupState;
}>;

export const appDefaultState = {
    app: appInitialState,
    creditOffers: creditOffersInitialState,
    creditProductsMainMenu: creditProductMainMenuInitialState,
    creditProductsHeader: creditProductHeaderInitialState,
    creditProducts: {},
    creditDocumentCirculation: {},
    sharedUI: sharedUIInitialState,
    organization: organizationInitialState,
    settings: settingsInitialState,
    mksPermissions: mksPermissionsInitialState,
    signModule: signModuleInitialState,
    holdingGroup: holdingGroupInitialState,
};
