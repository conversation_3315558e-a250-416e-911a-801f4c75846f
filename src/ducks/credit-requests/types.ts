import { type CommonCreditRequest } from 'corp-credit-request-api-typescript-services';
import {
    type ApiGetApplicationProgressStagesResponse,
    type ProgressStageResponseDto,
    type ProgressStatusResponseDto,
} from 'crmmb-status-model-core-api-typescript-services';

export const CREDIT_REQUESTS_TYPES = {
    GET_CREDIT_REQUESTS_LIST_START: 'GET_CREDIT_REQUESTS_LIST_START',
    GET_CREDIT_REQUESTS_LIST_FINISH: 'GET_CREDIT_REQUESTS_LIST_FINISH',
    GET_CREDIT_REQUESTS_LIST_ERROR: 'GET_CREDIT_REQUESTS_LIST_ERROR',
    GET_PROGRESS_STAGES_START: 'GET_PROGRESS_STAGES_START',
    GET_PROGRESS_STAGES_FINISH: 'GET_PROGRESS_STAGES_FINISH',
    GET_PROGRESS_STAGES_ERROR: 'GET_PROGRESS_STAGES_ERROR',
    FILTERS_APPLY: 'FILTERS_APPLY',
    DELETE_WELCOME_DEAL_BY_ID_START: 'DELETE_WELCOME_DEAL_BY_ID_START',
    DELETE_WELCOME_DEAL_BY_ID_FINISH: 'DELETE_WELCOME_DEAL_BY_ID_FINISH',
    DELETE_WELCOME_DEAL_BY_ID_ERROR: 'DELETE_WELCOME_DEAL_BY_ID_ERROR',
    RESET_CREDIT_REQUESTS_STATE: 'RESET_CREDIT_REQUESTS_STATE',
} as const;

export type TCreditRequestListItem = CommonCreditRequest;
export type TCreditRequestList = TCreditRequestListItem[];
export type TCreditRequestProgressStagesResponse = ApiGetApplicationProgressStagesResponse;
export type TCreditRequestSingleProgressStageResponse = ProgressStageResponseDto;
export type TCreditRequestProgressStatusResponse = ProgressStatusResponseDto;
