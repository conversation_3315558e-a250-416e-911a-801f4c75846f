import { createSelector } from 'reselect';

import { APPROVED_STATUSES } from '#/src/constants/approved-statuses';
import {
    ECreditProductsNames,
    type ERequestStatuses,
    FILTER_STATUSES,
    PRODUCTS_OPTIONS_FILTER,
    Status,
} from '#/src/constants/credit-document-circulation';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { type ApplicationState } from '../application-state';

export const creditRequestsListSelector = (state: ApplicationState) =>
    state.creditRequests?.list || [];

export const isCreditRequestsFetchingSelector = (state: ApplicationState) =>
    state.creditRequests.isFetching;

export const creditRequestsErrorSelector = (state: ApplicationState) => state.creditRequests.error;

export const isCreditRequestsAccessErrorSelector = createSelector(
    creditRequestsErrorSelector,
    (creditRequestsError) =>
        !!creditRequestsError && creditRequestsError === EErrorMessages.ACCESS_ERROR,
);

export const isAvailableCreditRequestSelector = createSelector(
    creditRequestsListSelector,
    (creditRequestsList: TCreditRequestList) =>
        creditRequestsList.some(({ clientStatus }) =>
            APPROVED_STATUSES.includes(clientStatus as ERequestStatuses),
        ),
);

export const creditRequestsFiltersSelector = (state: ApplicationState) =>
    state.creditRequests.filter;

export const filteredCreditRequestsListSelector = createSelector(
    creditRequestsListSelector,
    creditRequestsFiltersSelector,
    (creditRequestsList: TCreditRequestList, { status, products }) => {
        const filteredStatuses = new Set(FILTER_STATUSES[status]);
        const filterByStatus = creditRequestsList.filter(({ clientStatus }) => {
            const isNullStatusActive = clientStatus === null && status === Status.Active;

            return (
                isNullStatusActive ||
                (clientStatus != null && filteredStatuses.has(clientStatus.toLocaleLowerCase()))
            );
        });

        filterByStatus.sort((a, b) => (b?.createDt ?? 0) - (a?.createDt ?? 0));

        if (!products.length) {
            return filterByStatus;
        }

        const productFilters = products.flatMap(
            (product) => PRODUCTS_OPTIONS_FILTER[product.key as ECreditProductsNames],
        );

        return filterByStatus.filter(({ productName }) => {
            const normalizedProductName = productName ?? ECreditProductsNames.CREDIT_APPLICATION;

            return productFilters.includes(normalizedProductName);
        });
    },
);

export const progressStagesSelector = (state: ApplicationState) =>
    state.creditRequests.progressStages.stages;

export const progressStagesFetchingSelector = (state: ApplicationState) =>
    state.creditRequests.progressStages.isFetching;

export const progressStagesErrorSelector = (state: ApplicationState) =>
    state.creditRequests.progressStages.error;

export const isAvailableProgressStagesSelector = createSelector(
    progressStagesSelector,
    (progressStages) => {
        if (Array.isArray(progressStages) && progressStages.length > 1) {
            return true;
        }

        return false;
    },
);

export const singleProductStageSelector = createSelector(
    progressStagesSelector,
    (progressStages) => {
        if (Array.isArray(progressStages) && progressStages.length === 1) {
            return progressStages[0];
        }

        return null;
    },
);

export const sortedProgressStagesSelector = createSelector(
    progressStagesSelector,
    isAvailableProgressStagesSelector,
    (progressStages, isAvailableProgressStages) => {
        if (!isAvailableProgressStages) {
            return [];
        }

        progressStages.sort((a, b) => (a.stageOrder ?? 0) - (b.stageOrder ?? 0));

        return progressStages;
    },
);
