import { type OptionShape } from '@alfalab/core-components/select/typings';

import { Status } from '#/src/constants/credit-document-circulation';

import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import {
    CREDIT_REQUESTS_TYPES,
    type TCreditRequestList,
    type TCreditRequestProgressStagesResponse,
} from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditRequestsState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    list: TCreditRequestList;
    filter: {
        status: Status;
        products: OptionShape[];
        company: OptionShape[] | null;
    };
    progressStages: {
        error: string | EErrorMessages | null;
        isFetching: boolean;
        stages: TCreditRequestProgressStagesResponse;
    };
};

const progressStagesInitialState = {
    isFetching: false,
    error: null,
    stages: [],
};

export const initialState: CreditRequestsState = {
    error: null,
    isFetching: false,
    list: [],
    filter: {
        status: Status.Active,
        products: [],
        company: null,
    },
    progressStages: progressStagesInitialState,
};

export function creditRequestsReducer(state = initialState, action: ActionTypes) {
    switch (action.type) {
        case CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_START:
            return {
                ...state,
                isFetching: true,
            };
        case CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_FINISH:
            return {
                ...state,
                isFetching: false,
                list: action.list,
            };
        case CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_ERROR:
            return {
                ...state,
                isFetching: false,
                error: action.error.message,
            };

        case CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_START:
            return {
                ...state,
                progressStages: {
                    ...state.progressStages,
                    isFetching: true,
                    error: null,
                    stages: [],
                },
            };
        case CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_FINISH:
            return {
                ...state,
                progressStages: {
                    ...state.progressStages,
                    isFetching: false,
                    stages: action.stages,
                },
            };
        case CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_ERROR:
            return {
                ...state,
                progressStages: {
                    ...state.progressStages,
                    isFetching: false,
                    error: action.error.message,
                    stages: [],
                },
            };

        case CREDIT_REQUESTS_TYPES.FILTERS_APPLY:
            return {
                ...state,
                filter: {
                    ...state.filter,
                    status: action.status,
                    products: action.products,
                    company: action.company,
                },
            };
        case CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_START:
            return {
                ...state,
            };
        case CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_FINISH:
            return {
                ...state,
                list: action.list,
            };
        case CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_ERROR:
            return {
                ...state,
                error: action.error.message,
            };
        case CREDIT_REQUESTS_TYPES.RESET_CREDIT_REQUESTS_STATE:
            return initialState;
        default:
            return state;
    }
}
