import { type OptionShape } from '@alfalab/core-components/select/typings';

import { type Status } from '#/src/constants/credit-document-circulation';
import { type LOG_LEVEL } from '#/src/types/logger';
import { type CreditDocumentCirculationErrors } from '#/src/utils/errors';

import {
    CREDIT_REQUESTS_TYPES,
    type TCreditRequestList,
    type TCreditRequestProgressStagesResponse,
} from './types';

export function getCreditRequestsListStart(organizationId?: string) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_START,
        organizationId,
    };
}

export function getCreditRequestsListFinish({ list }: { list: TCreditRequestList }) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_FINISH,
        list,
    };
}

export function getCreditRequestsListError(
    error: CreditDocumentCirculationErrors,
    logLevel: LOG_LEVEL,
) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_ERROR,
        error,
        logLevel,
    };
}

export function applyRequestsFilter({
    status,
    products,
    company = null,
}: {
    status: Status;
    products: OptionShape[];
    company?: OptionShape[] | null;
}) {
    return {
        type: CREDIT_REQUESTS_TYPES.FILTERS_APPLY,
        status,
        products,
        company,
    };
}

export function getProgressStagesStart(requestId: string) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_START,
        requestId,
    };
}

export function getProgressStagesFinish(stages: TCreditRequestProgressStagesResponse) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_FINISH,
        stages,
    };
}

export function getProgressStagesError(error: Error, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_REQUESTS_TYPES.GET_PROGRESS_STAGES_ERROR,
        error,
        logLevel,
    };
}

export function deleteWelcomeDealByIdStart({ welcomeDealId }: { welcomeDealId: string }) {
    return {
        type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_START,
        welcomeDealId,
    };
}

export function deleteWelcomeDealByIdFinish({ list }: { list: TCreditRequestList }) {
    return {
        type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_FINISH,
        list,
    };
}

export function deleteWelcomeDealByIdError(error: Error) {
    return {
        type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_ERROR,
        error,
    };
}

export function resetCreditRequestsState() {
    return {
        type: CREDIT_REQUESTS_TYPES.RESET_CREDIT_REQUESTS_STATE,
    };
}
