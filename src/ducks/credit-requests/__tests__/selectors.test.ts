import setIn from 'lodash/fp/set';

import {
    ERequestsStatusName,
    ERequestStatuses,
    Status,
} from '#/src/constants/credit-document-circulation';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { type ApplicationState } from '../../application-state';
import {
    creditRequestsErrorSelector,
    creditRequestsFiltersSelector,
    creditRequestsListSelector,
    filteredCreditRequestsListSelector,
    isAvailableCreditRequestSelector,
    isCreditRequestsAccessErrorSelector,
    isCreditRequestsFetchingSelector,
} from '../selectors';

describe('credit requests selectors', () => {
    const mockCreditRequestsList: TCreditRequestList = [
        {
            id: 'test-id-1',
            clientStatus: ERequestsStatusName.CALL_RESCHEDULED,
            productName: 'Овердрафт',
            createDt: 1,
        },
        {
            id: 'test-id-2',
            clientStatus: ERequestsStatusName.COMPLETED,
            productName: 'Кредит',
            createDt: 2,
        },
        {
            id: 'test-id-3',
            clientStatus: ERequestStatuses.Approved,
            productName: 'Овердрафт',
            createDt: 3,
        },
    ];

    const testState: Partial<ApplicationState> = {
        creditRequests: {
            list: mockCreditRequestsList,
            isFetching: false,
            error: null,
            filter: { status: Status.Active, products: [], company: null },
            progressStages: {
                isFetching: false,
                error: null,
                stages: [],
            },
        },
    };

    describe('creditRequestsListSelector', () => {
        it('should return the credit requests list', () => {
            const result = creditRequestsListSelector(testState as ApplicationState);

            expect(result).toEqual(mockCreditRequestsList);
        });

        it('should return an empty array if no credit requests are present', () => {
            const modifiedState = setIn('creditRequests.list', [], testState);
            const result = creditRequestsListSelector(modifiedState as ApplicationState);

            expect(result).toEqual([]);
        });
    });

    describe('isCreditRequestsFetchingSelector', () => {
        it('should return fetching status', () => {
            const result = isCreditRequestsFetchingSelector(testState as ApplicationState);

            expect(result).toEqual(false);
        });
    });

    describe('creditRequestsErrorSelector', () => {
        it('should return the error if present', () => {
            const errorState = setIn(
                'creditRequests.error',
                EErrorMessages.ACCESS_ERROR,
                testState,
            );
            const result = creditRequestsErrorSelector(errorState as ApplicationState);

            expect(result).toEqual(EErrorMessages.ACCESS_ERROR);
        });

        it('should return null if no error', () => {
            const result = creditRequestsErrorSelector(testState as ApplicationState);

            expect(result).toBeNull();
        });
    });

    describe('isCreditRequestsAccessErrorSelector', () => {
        it('should return true if access error is present', () => {
            const errorState = setIn(
                'creditRequests.error',
                EErrorMessages.ACCESS_ERROR,
                testState,
            );
            const result = isCreditRequestsAccessErrorSelector(errorState as ApplicationState);

            expect(result).toEqual(true);
        });

        it('should return false if no access error', () => {
            const errorState = setIn('creditRequests.error', null, testState);
            const result = isCreditRequestsAccessErrorSelector(
                errorState as unknown as ApplicationState,
            );

            expect(result).toEqual(false);
        });
    });

    describe('isAvailableCreditRequestSelector', () => {
        it('should return true if there are approved credit requests', () => {
            const result = isAvailableCreditRequestSelector(testState as ApplicationState);

            expect(result).toEqual(true);
        });

        it('should return false if there are no approved credit requests', () => {
            const modifiedState = setIn(
                'creditRequests.list',
                [
                    {
                        id: 'test-id-1',
                        clientStatus: 'Отклонена',
                        productName: 'Овердрафт',
                        createDt: 1,
                    },
                ],
                testState,
            );
            const result = isAvailableCreditRequestSelector(modifiedState as ApplicationState);

            expect(result).toEqual(false);
        });
    });

    describe('creditRequestsFiltersSelector', () => {
        it('should return the filters state', () => {
            const result = creditRequestsFiltersSelector(testState as ApplicationState);

            expect(result).toEqual({ status: Status.Active, products: [], company: null });
        });
    });

    describe('filteredCreditRequestsListSelector', () => {
        it('should filter credit requests based on status and return sorted list', () => {
            const result = filteredCreditRequestsListSelector(testState as ApplicationState);

            expect(result).toEqual([
                {
                    id: 'test-id-1',
                    clientStatus: ERequestsStatusName.CALL_RESCHEDULED,
                    productName: 'Овердрафт',
                    createDt: 1,
                },
            ]);
        });

        it('should filter credit requests based on product filter', () => {
            const modifiedState = setIn(
                'creditRequests.filter.products',
                [{ key: 'Овердрафт' }],
                testState,
            );
            const result = filteredCreditRequestsListSelector(modifiedState as ApplicationState);

            expect(result).toEqual([
                {
                    id: 'test-id-1',
                    clientStatus: ERequestsStatusName.CALL_RESCHEDULED,
                    productName: 'Овердрафт',
                    createDt: 1,
                },
            ]);
        });

        it('should return an empty list if no matching products', () => {
            const modifiedState = setIn(
                'creditRequests.filter.products',
                [{ key: 'unknown' }],
                testState,
            );
            const result = filteredCreditRequestsListSelector(modifiedState as ApplicationState);

            expect(result).toEqual([]);
        });
    });
});
