import { type InferValueTypes } from 'corporate-blocking/common/actions-type';

import { ERequestsStatusName } from '#/src/constants/credit-document-circulation';

import type * as actions from '../actions';
import { creditRequestsReducer, initialState } from '../reducer';
import { CREDIT_REQUESTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

describe('creditRequestsReducer', () => {
    it('should handle DELETE_WELCOME_DEAL_BY_ID_START', () => {
        const action = {
            type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_START,
        };
        const newState = creditRequestsReducer(initialState, action as ActionTypes);

        expect(newState).toEqual(initialState);
    });

    it('should handle DELETE_WELCOME_DEAL_BY_ID_FINISH', () => {
        const action = {
            type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_FINISH,
            list: [{ id: '456', clientStatus: ERequestsStatusName.WORKS }],
        };
        const newState = creditRequestsReducer(initialState, action);

        expect(newState).toEqual({
            ...initialState,
            list: action.list,
        });
    });

    it('should handle DELETE_WELCOME_DEAL_BY_ID_ERROR', () => {
        const errorMessage = 'Unexpected Error';
        const action = {
            type: CREDIT_REQUESTS_TYPES.DELETE_WELCOME_DEAL_BY_ID_ERROR,
            error: new Error(errorMessage),
        };
        const newState = creditRequestsReducer(initialState, action);

        expect(newState).toEqual({
            ...initialState,
            error: errorMessage,
        });
    });
});
