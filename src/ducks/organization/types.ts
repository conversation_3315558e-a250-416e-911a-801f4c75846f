export const ORGA<PERSON>ZATION_TYPES = {
    CHANGE_ORGANIZATION: 'CHANGE_ORGANIZATION',

    GET_CATEGORY_START: 'GET_CATEGORY_START',
    GET_CATEGORY_FINISH: 'GET_CATEGORY_FINISH',
    GET_CATEGORY_ERROR: 'GET_CATEGORY_ERROR',
    RESET_CATEGORY: 'RESET_CATEGORY',

    GET_SUBJECT_FEATURES_START: 'GET_SUBJECT_FEATURES_START',
    GET_SUBJECT_FEATURES_FINISH: 'GET_SUBJECT_FEATURES_FINISH',
    GET_SUBJECT_FEATURES_ERROR: 'GET_SUBJECT_FEATURES_ERROR',

    GET_MANAGERS_START: 'GET_MANAGERS_START',
    GET_MANAGERS_FINISH: 'GET_MANAGERS_FINISH',
    GET_MANAGERS_ERROR: 'GET_MANAGERS_ERROR',

    RESET_MANAGERS: 'RESET_MANAGERS',

    SET_CHANGE_ORGANIZATION_MODAL_VISIBLE: 'SET_CHANGE_ORGANIZATION_MODAL_VISIBLE',
} as const;
