import { type AnyAction } from 'redux';
import { type ResponseError } from 'corp-core-credit-products-api-typescript-services/dist/responseError';
import { type Manager } from 'corp-customers-api-typescript-services';
import { type Category } from 'corp-customers-api-typescript-services/dist/category';

import { type LOG_LEVEL } from '../../types/logger';

import { ORGANIZATION_TYPES } from './types';

export function changeOrganization(organizationId: string) {
    return {
        type: ORGANIZATION_TYPES.CHANGE_ORGANIZATION,
        organizationId,
    };
}

export const getCategoryStart = (organizationId?: string) => ({
    type: ORGANIZATION_TYPES.GET_CATEGORY_START,
    organizationId,
});

export const getCategoryFinish = (category: Category) => ({
    type: ORGANIZATION_TYPES.GET_CATEGORY_FINISH,
    category,
});

export const getCategoryError = (error: Error) => ({
    type: ORGANIZATION_TYPES.GET_CATEGORY_ERROR,
    error,
});

export const resetCategory = () => ({
    type: ORGANIZATION_TYPES.RESET_CATEGORY,
});

export function getManagersStart(payload: { codes: string[] }) {
    return {
        type: ORGANIZATION_TYPES.GET_MANAGERS_START,
        payload,
    };
}

export function getManagersFinish(managers: Manager[]) {
    return {
        type: ORGANIZATION_TYPES.GET_MANAGERS_FINISH,
        managers,
    };
}

export function getManagersError(responseError: ResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ORGANIZATION_TYPES.GET_MANAGERS_ERROR,
        error: responseError,
        logLevel,
    };
}

export function resetManagers() {
    return {
        type: ORGANIZATION_TYPES.RESET_MANAGERS,
    };
}

export function setChangeOrganizationModalVisible({
    isVisible,
    organizationName = '',
    organizationId = '',
    action,
}: {
    isVisible: boolean;
    organizationName?: string;
    organizationId?: string;
    action?: AnyAction;
}) {
    return {
        type: ORGANIZATION_TYPES.SET_CHANGE_ORGANIZATION_MODAL_VISIBLE,
        isVisible,
        organizationName,
        organizationId,
        action,
    };
}
