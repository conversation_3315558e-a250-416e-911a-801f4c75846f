import qs from 'qs';
import { createSelector } from 'reselect';

import {
    MMB_CATEGORY_CODES,
    type OrganizationCategoryCodes,
} from '#/src/constants/organization-category-codes';

import { type ApplicationState } from '../application-state';
import { currentPathSelector, searchParamsSelector } from '../router/selectors';

export const organizationsListSelector = (state: ApplicationState) =>
    state.organization?.list || [];

export const organizationCategoryCodeSelector = (state: ApplicationState) =>
    state.organization.category?.sksCode;

export const isFetchingCategorySelector = (state: ApplicationState) =>
    state.organization.isFetchingCategory;

export const changeOrganizationModalSelector = (state: ApplicationState) =>
    state.organization.changeOrganizationModal;

export const categoryErrorSelector = (state: ApplicationState) => state.organization.categoryError;

export const clientManagersSelector = (state: ApplicationState) => state.organization.managers;

export const isMmbCategorySelector = createSelector(organizationCategoryCodeSelector, (code = '') =>
    MMB_CATEGORY_CODES.includes(code as OrganizationCategoryCodes),
);

export const organizationSelector = (state: ApplicationState) => state.organization;

export const currentOrganizationEqIdSelector = createSelector(
    searchParamsSelector,
    organizationSelector,
    (search, organization) =>
        `${qs.parse(search, { ignoreQueryPrefix: true })?.customerId || ''}` ||
        organization?.current ||
        '',
);

export const currentOrganizationSelector = createSelector(
    organizationsListSelector,
    currentOrganizationEqIdSelector,
    (organizations, currentOrganizationEqId) =>
        organizations.find((organization) => organization.eqId === currentOrganizationEqId),
);

export const currentSharedOrganizationSelector = createSelector(
    organizationsListSelector,
    organizationSelector,
    (organizations, currentOrganization) =>
        organizations.find((organization) => organization.eqId === currentOrganization.current),
);

export const currentOrganizationTypeSelector = createSelector(
    currentOrganizationSelector,
    (currentOrganization) => currentOrganization?.type,
);

export const getCurrentOrganizationNameSelector = createSelector(
    currentOrganizationSelector,
    (currentOrganization) => currentOrganization?.name || '',
);

export const getCurrentOrganizationShortNameSelector = createSelector(
    currentOrganizationSelector,
    (currentOrganization) => currentOrganization?.organizationName.shortName || '',
);

export const getCurrentSharedOrganizationShortNameSelector = createSelector(
    currentSharedOrganizationSelector,
    (currentOrganization) => currentOrganization?.organizationName.shortName || '',
);

export const currentOrganizationFeaturesListSelector = createSelector(
    currentOrganizationSelector,
    (currentOrganization) => currentOrganization?.features ?? {},
);

export const createOrganizationFeatureSelector = (name: string) =>
    createSelector(
        currentOrganizationFeaturesListSelector,
        (organizationFeatures) => organizationFeatures[name] || false,
    );

export const isEcoCreditRequestsViewDeniedSelector = createOrganizationFeatureSelector(
    'EcoCreditRequestsViewDenied',
);
export const isEcoOUViewDeniedSelector = createOrganizationFeatureSelector('EcoOUViewDenied');

export const isOverdraftNetTurnsFeatureSelector =
    createOrganizationFeatureSelector('OverdraftNetTurns');

export const isMobileWeb = createSelector(
    organizationCategoryCodeSelector,
    currentPathSelector,
    (code, currentPath) => {
        if (MMB_CATEGORY_CODES.includes(code as OrganizationCategoryCodes)) {
            return !currentPath.includes('&desktop=true');
        }

        return false;
    },
);

export const isEcoAKDocumentCountersSelector =
    createOrganizationFeatureSelector('EcoAKDocumentCounters');
