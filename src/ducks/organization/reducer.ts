import { type AnyAction } from 'redux';
import { type ApiGetManagersResponse, type Category } from 'corp-customers-api-typescript-services';

import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type TCustomCustomer } from '../../types/organization';
import { type InferValueTypes } from '../../utils/generic-types';
import type * as mainMenuActions from '../credit-products-main-menu/actions';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '../credit-products-main-menu/types';

import type * as actions from './actions';
import { ORGANIZATION_TYPES } from './types';

export type GetManagersState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    isFinished: boolean;
    managers: ApiGetManagersResponse | null;
};

export type OrganizationsState = {
    current: string;
    list: TCustomCustomer[];
    isFetchingCategory: boolean;
    isFetchingOrganizationIdToken: boolean;
    isWrappingOrganizationIdError: boolean;
    changeOrganizationModal: {
        isVisible: boolean;
        organizationName: string;
        organizationId: string;
        action?: AnyAction;
    };
    wrappedInTokenOrganizationId: string | null;
    category: Category;
    categoryError: string | EErrorMessages | null;
    managers: GetManagersState;
};

type Actions =
    | ReturnType<InferValueTypes<typeof actions>>
    | ReturnType<InferValueTypes<typeof mainMenuActions>>;

export const managersInitialState: GetManagersState = {
    error: null,
    isFetching: false,
    isFinished: false,
    managers: null,
};

export const categoryInitialState: Category = {
    sksCode: '',
    sksName: '',
};

export const initialState: OrganizationsState = {
    current: '',
    list: [],
    isFetchingCategory: false,
    isFetchingOrganizationIdToken: false,
    isWrappingOrganizationIdError: false,
    changeOrganizationModal: {
        isVisible: false,
        organizationName: '',
        organizationId: '',
    },
    wrappedInTokenOrganizationId: null,
    category: categoryInitialState,
    categoryError: null,
    managers: managersInitialState,
};

export function organizationReducer(
    state: OrganizationsState = initialState,
    action: Actions,
): OrganizationsState {
    switch (action.type) {
        case ORGANIZATION_TYPES.CHANGE_ORGANIZATION:
            return {
                ...state,
                current: action.organizationId,
            };

        case ORGANIZATION_TYPES.GET_CATEGORY_START:
            return {
                ...state,
                isFetchingCategory: true,
                categoryError: null,
            };

        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_FINISH:
        case ORGANIZATION_TYPES.GET_CATEGORY_FINISH:
            return {
                ...state,
                category: action.category,
                isFetchingCategory: false,
                categoryError: null,
            };

        case ORGANIZATION_TYPES.GET_CATEGORY_ERROR:
            return {
                ...state,
                isFetchingCategory: false,
                categoryError: action.error?.message,
            };

        case ORGANIZATION_TYPES.RESET_CATEGORY:
            return {
                ...state,
                category: categoryInitialState,
            };

        case ORGANIZATION_TYPES.GET_MANAGERS_START:
            return {
                ...state,
                managers: {
                    ...managersInitialState,
                    isFetching: true,
                },
            };

        case ORGANIZATION_TYPES.GET_MANAGERS_FINISH:
            return {
                ...state,
                managers: {
                    error: null,
                    isFetching: false,
                    isFinished: true,
                    managers: action.managers,
                },
            };

        case ORGANIZATION_TYPES.GET_MANAGERS_ERROR:
            return {
                ...state,
                managers: {
                    isFetching: false,
                    isFinished: true,
                    error: getErrorMessage(action.error) ?? 'unexpected error',
                    managers: null,
                },
            };

        case ORGANIZATION_TYPES.RESET_MANAGERS:
            return {
                ...state,
                managers: managersInitialState,
            };

        case ORGANIZATION_TYPES.SET_CHANGE_ORGANIZATION_MODAL_VISIBLE:
            return {
                ...state,
                changeOrganizationModal: {
                    isVisible: action.isVisible,
                    organizationName: action.organizationName,
                    organizationId: action.organizationId,
                    action: action.action,
                },
            };

        default:
            return state;
    }
}
