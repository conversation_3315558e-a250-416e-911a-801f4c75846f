import { type DeepPartial } from 'arui-private/types';

import { type TCustomCustomer } from '../../types/organization';
import { type ApplicationState } from '../application-state';

import { categoryInitialState, managersInitialState } from './reducer';
import { currentOrganizationSelector, getCurrentOrganizationNameSelector } from './selectors';

describe('Organization selectors', () => {
    describe('currentOrganizationSelector()', () => {
        const currentOrganization: DeepPartial<TCustomCustomer> = {
            eqId: 'orgId',
        };

        it('should return current organization', () => {
            const testState: DeepPartial<ApplicationState> = {
                organization: {
                    list: [
                        { eqId: 'randomOrg' },
                        currentOrganization,
                        { eqId: 'anotherRandomOrg' },
                    ],
                    current: currentOrganization.eqId || '',
                    isFetchingOrganizationIdToken: false,
                    isWrappingOrganizationIdError: false,
                    wrappedInTokenOrganizationId: null,
                    category: categoryInitialState,
                    managers: managersInitialState,
                },
            };

            const actual = currentOrganizationSelector(testState as ApplicationState);

            expect(actual).toEqual(currentOrganization);
        });

        it('should return undefined if no organization match', () => {
            const testState: DeepPartial<ApplicationState> = {
                organization: {
                    list: [{ eqId: 'randomOrg' }, { eqId: 'oops' }, { eqId: 'anotherRandomOrg' }],
                    current: currentOrganization.eqId || '',
                    isFetchingOrganizationIdToken: false,
                    isWrappingOrganizationIdError: false,
                    wrappedInTokenOrganizationId: null,
                    isFetchingCategory: false,
                    category: categoryInitialState,
                    managers: managersInitialState,
                },
            };

            const actual = currentOrganizationSelector(testState as ApplicationState);

            expect(actual).toBeUndefined();
        });
    });

    describe('getCurrentOrganizationNameSelector()', () => {
        const currentOrganization: DeepPartial<TCustomCustomer> = {
            eqId: 'orgId',
            name: 'organizatinName',
        };

        it('should return name of current organization', () => {
            const testState: DeepPartial<ApplicationState> = {
                organization: {
                    list: [
                        { eqId: 'randomOrg', name: 'r' },
                        currentOrganization,
                        { eqId: 'anotherRandomOrg', name: 'a' },
                    ],
                    current: currentOrganization.eqId || '',
                    isFetchingOrganizationIdToken: false,
                    isWrappingOrganizationIdError: false,
                    wrappedInTokenOrganizationId: null,
                    isFetchingCategory: false,
                    category: categoryInitialState,
                    managers: managersInitialState,
                },
            };

            const actual = getCurrentOrganizationNameSelector(testState as ApplicationState);

            expect(actual).toEqual(currentOrganization.name);
        });

        it('should return empty string if current organization not found', () => {
            const testState: DeepPartial<ApplicationState> = {
                organization: {
                    list: [
                        { eqId: 'randomOrg', name: 'r' },
                        { eqId: 'test', name: 'l' },
                        { eqId: 'anotherRandomOrg', name: 'a' },
                    ],
                    current: currentOrganization.eqId || '',
                    isFetchingOrganizationIdToken: false,
                    isWrappingOrganizationIdError: false,
                    wrappedInTokenOrganizationId: null,
                    isFetchingCategory: false,
                    category: categoryInitialState,
                    managers: managersInitialState,
                },
            };

            const actual = getCurrentOrganizationNameSelector(testState as ApplicationState);

            expect(actual).toEqual('');
        });
    });
});
