import { type FindRequestsInProgressStatisticsResponse } from 'corporate-services/server/types/ufr-ak-corp-gateway-rest';

import { type LOG_LEVEL } from '#/src/types/logger';

import { FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES } from './types';

export function findRequestsInProgressStatisticsStart({ cuses }: { cuses: string[] }) {
    return {
        type: FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_START,
        cuses,
    };
}

export function findRequestsInProgressStatisticsFinish(
    response: FindRequestsInProgressStatisticsResponse,
) {
    return {
        type: FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_FINISH,
        response,
    };
}

export function findRequestsInProgressStatisticsError(error: Error, logLevel: LOG_LEVEL) {
    return {
        type: FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_ERROR,
        error,
        logLevel,
    };
}
