import {
    ERequestCode,
    ERequestStatus,
} from 'corporate-services/server/types/ufr-ak-corp-gateway-rest';
import { createSelector } from 'reselect';

import { type ApplicationState } from '../application-state';

export const ufrAkCorpGatewayRestSelector = (state: ApplicationState) => state.ufrAkCorpGatewayRest;

export const findRequestsInProgressStatisticsSelector = createSelector(
    ufrAkCorpGatewayRestSelector,
    ({ data }) => {
        const initialState = { tranches: 0, contracts: 0, guarantee: 0, sendDocuments: 0 };

        return (
            data?.reduce((notificationCount, { status, requestCode, count }) => {
                if (!(status in ERequestStatus)) {
                    return notificationCount;
                }

                switch (requestCode) {
                    case ERequestCode.CreditRequest:
                        notificationCount.tranches += count;
                        break;
                    case ERequestCode.BankRequest:
                        notificationCount.contracts += count;
                        break;
                    case ERequestCode.GuaranteeRequest:
                        notificationCount.guarantee += count;
                        break;
                    case ERequestCode.DocumentQueryRequest:
                    case ERequestCode.DocumentRequest:
                        notificationCount.sendDocuments += count;
                        break;
                }

                return notificationCount;
            }, initialState) ?? initialState
        );
    },
);
