import { type FindRequestsInProgressStatisticsResponse } from 'corporate-services/server/types/ufr-ak-corp-gateway-rest';

import { type DeepReadonly, type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type UfrAkCorpGatewayRestState = DeepReadonly<{
    data: FindRequestsInProgressStatisticsResponse | null;
    isError: boolean;
    isFetching: boolean;
}>;

export const initialState: UfrAkCorpGatewayRestState = {
    data: null,
    isError: false,
    isFetching: false,
};

export function ufrAkCorpGatewayRestReducer(
    state = initialState,
    action: ActionTypes,
): UfrAkCorpGatewayRestState {
    switch (action.type) {
        case FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_START:
            return {
                ...initialState,
                isFetching: true,
            };
        case FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_FINISH: {
            return {
                ...state,
                data: action.response,
                isFetching: false,
                isError: false,
            };
        }
        case FIND_REQUESTS_IN_PROGRESS_STATISTICS_TYPES.FIND_REQUESTS_IN_PROGRESS_STATISTICS_ERROR:
            return {
                ...state,
                data: null,
                isFetching: false,
                isError: true,
            };
        default:
            return state;
    }
}
