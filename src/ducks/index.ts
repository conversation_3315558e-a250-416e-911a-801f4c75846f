import { combineReducers } from 'redux';
import { connectRouter } from 'connected-react-router';
import { blockingReducer } from 'corporate-blocking';
import { type History } from 'history';

import { accountsReducer } from './accounts/reducer';
import { addressesReducer } from './addresses/reducer';
import { appReducer } from './app/reducer';
import { attachDocumentsReducer } from './attach-documents/reducer';
import { signConfirmationReducer } from './correspondence/reducer';
import { creditCalculatorReducer } from './credit-calculator/reducer';
import { creditDocumentCirculationReducer } from './credit-document-circulation/reducer';
import { creditOffersReducer } from './credit-offers/reducer';
import { creditProcessingReducer } from './credit-processing/reducer';
import { creditProductsReducer } from './credit-products/reducer';
import { creditProductsHeaderReducer } from './credit-products-header/reducer';
import { creditProductsMainMenuReducer } from './credit-products-main-menu/reducer';
import { creditRequestsReducer } from './credit-requests/reducer';
import { creditWidgetStatusReducer } from './credit-widget-status/reducer';
import { documentsReducer } from './documents/reducer';
import { earlyPayReducer } from './early-pay/reducer';
import { holdingGroupReducer } from './holding-group/reducer';
import { mksPermissionsReducer } from './mks-permissions/reducer';
import { organizationReducer } from './organization/reducer';
import { paymentScheduleReducer } from './payment-schedule/reducer';
import { settingsReducer } from './settings/reducer';
import { sharedUIReducer } from './shared/reducer';
import { signModuleReducer } from './sign-module/reducer';
import { signedDocumentsReducer } from './signed-documents/reducer';
import { statementRequestsReducer } from './statement-requests/reducer';
import { suspensiveConditionsReducer } from './suspensive-conditions/reducer';
import { ufrAkCorpGatewayRestReducer } from './ufr-ak-corp-gateway-rest/reducer';
import { userReducer } from './user/reducer';
import { type ApplicationState } from './application-state';

export default (history: History) =>
    combineReducers<ApplicationState>({
        app: appReducer,
        settings: settingsReducer,
        user: userReducer,
        organization: organizationReducer,
        sharedUI: sharedUIReducer,
        creditOffers: creditOffersReducer,
        creditWidgetStatus: creditWidgetStatusReducer,
        creditProducts: creditProductsReducer,
        creditProductsMainMenu: creditProductsMainMenuReducer,
        creditProductsHeader: creditProductsHeaderReducer,
        paymentSchedule: paymentScheduleReducer,
        creditProcessing: creditProcessingReducer,
        router: connectRouter(history),
        blockings: blockingReducer,
        creditDocumentCirculation: creditDocumentCirculationReducer,
        creditRequests: creditRequestsReducer,
        suspensiveConditions: suspensiveConditionsReducer,
        accounts: accountsReducer,
        signConfirmation: signConfirmationReducer,
        earlyPay: earlyPayReducer,
        mksPermissions: mksPermissionsReducer,
        creditCalculator: creditCalculatorReducer,
        attachDocuments: attachDocumentsReducer,
        signedDocuments: signedDocumentsReducer,
        signModule: signModuleReducer,
        ufrAkCorpGatewayRest: ufrAkCorpGatewayRestReducer,
        statementRequests: statementRequestsReducer,
        documents: documentsReducer,
        addresses: addressesReducer,
        holdingGroup: holdingGroupReducer,
    });
