import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';

import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type AllProductsState = Record<string, CreditProduct>;

export function allProducts(state: AllProductsState = {}, action: ActionTypes): AllProductsState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return {
                ...state,
                ...action.allProductsIndexedByDocNumber,
            };
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH:
            return {
                ...state,
                ...action.tranchesIndexedByDocNumber,
            };
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return {};
        default:
            return state;
    }
}
