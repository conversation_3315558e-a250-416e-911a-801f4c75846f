import {
    type TMapGuaranties,
    type TMapGuarantyLines,
    type TMappedCreditLines,
    type TMappedCredits,
    type TMappedOverdrafts,
    type TMappedSopuks,
} from '#/src/utils/credit-products-mappers';
import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type ArrayElement, type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type ProductByType = {
    overdraft: ArrayElement<TMappedOverdrafts> | null;
    credit: ArrayElement<TMappedCredits> | null;
    creditLine: ArrayElement<TMappedCreditLines> | null;
    sopukLine: ArrayElement<TMappedSopuks> | null;
    guarantie: ArrayElement<TMapGuaranties> | null;
    guarantyLine: ArrayElement<TMapGuarantyLines> | null;
};

export type SelectedCreditProductState = {
    product: ProductByType;
    isFetching: boolean;
    error: null | string | EErrorMessages;
};

export const initialStateSelectedCreditProduct: SelectedCreditProductState = {
    product: {
        overdraft: null,
        credit: null,
        creditLine: null,
        guarantie: null,
        sopukLine: null,
        guarantyLine: null,
    },
    isFetching: false,
    error: null,
};

export const selectedCreditProduct = (
    state: SelectedCreditProductState = initialStateSelectedCreditProduct,
    action: ActionTypes,
): SelectedCreditProductState => {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_START:
            return {
                product: { ...initialStateSelectedCreditProduct.product },
                isFetching: true,
                error: null,
            };
        case CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_FINISH:
            return { product: { ...action.selectedProduct }, isFetching: false, error: null };
        case CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_ERROR:
            return {
                product: { ...initialStateSelectedCreditProduct.product },
                isFetching: false,
                error: action.error.message,
            };
        default:
            return state;
    }
};
