import { type TRepaymentResult } from 'thrift-services/services/credit_products';

import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import { ERepaymentStatus } from '#/src/types/overdraft';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export enum RepaymentStatusCode {
    NotFound = '404',
    Decline = 'Decline',
    New = 'New',
    ServerError = '500',
}

export type OverdraftRepaymentState = {
    statusPending: boolean;
    statusLoaded: boolean;
    statusData?: TRepaymentResult;
    statusError?: Error;
    changeStatus: ERepaymentStatus;
    changePending: boolean;
    changeLoaded: boolean;
    changeError?: Error;
    changeData?: TRepaymentResult;
};

const initialRepaymentState: OverdraftRepaymentState = {
    statusPending: false,
    statusLoaded: false,
    statusData: {},
    changeStatus: ERepaymentStatus.NOT_CHANGING,
    changePending: false,
    changeLoaded: false,
    changeData: {},
};

export type OverdraftChangeRepaymentState = Record<string, OverdraftRepaymentState>;

export function overdraftChangeRepayment(
    state: OverdraftChangeRepaymentState = {},
    action: ActionTypes,
): OverdraftChangeRepaymentState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_START:
            return {
                ...state,
                [action.dealId]: {
                    ...initialRepaymentState,
                    statusPending: true,
                },
            };
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_FINISH:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    statusPending: false,
                    statusLoaded: true,
                    statusData: action.repaymentData,
                },
            };
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_ERROR:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    statusPending: false,
                    statusLoaded: false,
                    statusError: action.error,
                },
            };
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_ERROR:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    changeStatus: ERepaymentStatus.ERROR,
                    changePending: false,
                    changeError: action.error,
                },
            };
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_START:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    changeStatus: ERepaymentStatus.IN_PROGRESS,
                    changePending: true,
                },
            };
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_FINISH:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    changeStatus: ERepaymentStatus.SET_SUCCESSFUL,
                    changePending: false,
                    changeLoaded: true,
                    changeData: action.repaymentChangeData,
                    statusData: action.repaymentChangeData,
                },
            };
        case CREDIT_PRODUCTS_TYPES.CLEAR_OVERDRAFT_REPAYMENT_STATUS:
            return {
                ...state,
                [action.dealId]: {
                    ...state[action.dealId],
                    changeStatus: ERepaymentStatus.NOT_CHANGING,
                    changeData: {},
                },
            };
        default:
            return state;
    }
}
