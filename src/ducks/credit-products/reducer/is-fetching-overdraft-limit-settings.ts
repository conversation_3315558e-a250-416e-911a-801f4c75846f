import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function isFetchingOverdraftLimitSettings(state = false, action: ActionTypes): boolean {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_START:
            return true;
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_FINISH:
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_ERROR:
            return false;
        default:
            return state;
    }
}
