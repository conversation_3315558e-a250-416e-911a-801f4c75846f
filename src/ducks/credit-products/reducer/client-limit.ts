import { type ClientLimitResponse } from 'corp-core-credit-products-api-typescript-services/dist/clientLimitResponse';

import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type ClientLimitState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    clientLimit: ClientLimitResponse | null;
    isFinished: boolean;
};

export const initialState: ClientLimitState = {
    error: null,
    isFetching: false,
    clientLimit: null,
    isFinished: false,
};

export function clientLimit(state = initialState, action: ActionTypes): ClientLimitState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_START:
            return {
                ...initialState,
                isFetching: true,
            };
        case CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_FINISH:
            return {
                ...state,
                isFetching: false,
                clientLimit: action.clientLimitResponse,
                isFinished: true,
            };
        case CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_ERROR:
            return {
                ...state,
                isFetching: false,
                error: getErrorMessage(action.error) ?? 'unexpected error',
                isFinished: true,
            };
        case CREDIT_PRODUCTS_TYPES.RESET_CLIENT_LIMIT_STATE:
            return initialState;
        default:
            return state;
    }
}
