import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditProductsErrorState = string | EErrorMessages | null;

const initialState: CreditProductsErrorState = null;

export function creditProductsError(
    state: CreditProductsErrorState = initialState,
    action: ActionTypes,
): CreditProductsErrorState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_START:
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return null;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
            return action.error.message;
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        default:
            return state;
    }
}
