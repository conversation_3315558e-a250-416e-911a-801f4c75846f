import { EOverdraftTypes } from '#/src/constants/credit-products';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type OverTypeState = EOverdraftTypes | null;

export function overType(state: OverTypeState = null, action: ActionTypes): OverTypeState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_FINISH:
            return action.tranchesCount > 0
                ? EOverdraftTypes.withTranches
                : EOverdraftTypes.withoutTranches;
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return null;
        default:
            return state;
    }
}
