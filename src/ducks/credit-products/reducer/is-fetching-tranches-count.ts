import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function isFetchingTranchesCount(state = false, action: ActionTypes): boolean {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_START:
            return true;
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_FINISH:
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return false;
        default:
            return state;
    }
}
