import { type Amount } from 'thrift-services/entities';

import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

const initialState: { [k: string]: Amount } = {};

export function closestTranchePayment(
    state = initialState,
    action: ActionTypes,
): { [k: string]: Amount } {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CLOSE_PAYMENT_AMOUNT_FINISH:
            return { ...state, ...action.amount };
        default:
            return state;
    }
}
