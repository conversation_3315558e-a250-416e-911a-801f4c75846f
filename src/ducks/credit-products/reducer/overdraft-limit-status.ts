import { ELimitStatus } from '#/src/types/overdraft';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

const initialState: ELimitStatus = ELimitStatus.NOT_CHANGING;

export function overdraftLimitStatus(
    state: ELimitStatus = initialState,
    action: ActionTypes,
): ELimitStatus {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_FINISH:
            return initialState;
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_START:
            return ELimitStatus.IN_PROGRESS;
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_FINISH:
            return ELimitStatus.SET_SUCCESSFUL;
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_ERROR:
            return ELimitStatus.ERROR;
        case CREDIT_PRODUCTS_TYPES.LIMIT_NOTIFICATION_CLOSE:
            return ELimitStatus.NOT_CHANGING;
        default:
            return state;
    }
}
