import { combineReducers } from 'redux';
import { type Amount } from 'thrift-services/entities';

import {
    clientLimit,
    type ClientLimitState,
} from '#/src/ducks/credit-products/reducer/client-limit';
import {
    overdraftChangeRepayment,
    type OverdraftChangeRepaymentState,
} from '#/src/ducks/credit-products/reducer/overdraft-change-repayment';
import {
    overdraftsNetTurnovers,
    type OverdraftsNetTurnoverState,
} from '#/src/ducks/credit-products/reducer/overdrafts-net-turnovers';
import {
    selectedCreditProduct,
    type SelectedCreditProductState,
} from '#/src/ducks/credit-products/reducer/selected-credit-product';
import { sopukLine, type SopukLineState } from '#/src/ducks/credit-products/reducer/sopuk-line';
import { type ELimitStatus } from '#/src/types/overdraft';

import { allProducts, type AllProductsState } from './all-products';
import { closestTranchePayment } from './closest-tranche-payment';
import { creditCards, type CreditCardsState } from './credit-cards';
import { creditLines, type CreditLinesState } from './credit-lines';
import { creditProductsError, type CreditProductsErrorState } from './credit-products-error';
import { credits, type CreditsState } from './credits';
import { disallowedDatesForLoanRepayment } from './disallowed-dates-for-loan-repayment';
import { guaranties, type GuarantiesState } from './guaranties';
import { guarantyLines, type GuarantyLinesState } from './guaranty-lines';
import { isAllCreditProductsTranches } from './is-all-credit-products-tranches';
import { isAllReceivedCreditProductsTranches } from './is-all-received-credit-products-tranches';
import { isClosedCreditProducts } from './is-closed-credit-products';
import { isClosedCreditProductsTranches } from './is-closed-credit-products-tranches';
import { isCreditProductsRecieved } from './is-credit-products-recieved';
import { isFetchingCreditProducts } from './is-fetching-credit-products';
import { isFetchingDisallowedDatesForLoanRepayment } from './is-fetching-disallowed-dates-for-loan-repayment';
import { isFetchingOverdraftLimitSettings } from './is-fetching-overdraft-limit-settings';
import { isFetchingSetOverdraftLimit } from './is-fetching-set-overdraft-limit';
import { isFetchingTranches } from './is-fetching-tranches';
import { isFetchingTranchesCount } from './is-fetching-tranches-count';
import {
    isGetOverdraftLimitSettingsError,
    type IsGetOverdraftLimitSettingsErrorState,
} from './is-get-overdraft-limit-settings-error';
import {
    isGetTrancheCreditProductsCountError,
    type IsGetTrancheCreditProductsCountErrorState,
} from './is-get-tranche-credit-products-count-error';
import {
    isGetTrancheCreditProductsError,
    type IsGetTrancheCreditProductsErrorState,
} from './is-get-tranche-credit-products-error';
import { overType, type OverTypeState } from './over-type';
import { overdraftLimitStatus } from './overdraft-limit-status';
import { overdrafts, type OverdraftsState } from './overdrafts';
import { overdraftsLimits, type OverdraftsLimitsState } from './overdrafts-limits';
import { tranches, type TranchesState } from './tranches';
import { tranchesInfoReducer, type TranchesInfoState } from './tranches-info';

export type CreditProductsState = {
    credits: CreditsState;
    overdrafts: OverdraftsState;
    creditCards: CreditCardsState;
    creditLines: CreditLinesState;
    sopukLine: SopukLineState;
    guaranties: GuarantiesState;
    guarantyLines: GuarantyLinesState;
    tranches: TranchesState;
    allProducts: AllProductsState;
    selectedProduct: SelectedCreditProductState;
    tranchesInfo: TranchesInfoState;
    overdraftsLimits: OverdraftsLimitsState;
    overType: OverTypeState;
    clientLimit: ClientLimitState;
    isFetchingCreditProducts: boolean;
    isFetchingTranches: boolean;
    isFetchingTranchesCount: boolean;
    isFetchingOverdraftLimitSettings: boolean;
    isFetchingSetOverdraftLimit: boolean;
    isFetchingDisallowedDatesForLoanRepayment: boolean;
    overdraftLimitStatus: ELimitStatus;
    creditProductsError: CreditProductsErrorState;
    isGetTrancheCreditProductsCountError: IsGetTrancheCreditProductsCountErrorState;
    isGetTrancheCreditProductsError: IsGetTrancheCreditProductsErrorState;
    isGetOverdraftLimitSettingsError: IsGetOverdraftLimitSettingsErrorState;
    /**
     * флаг, что информация по кредитным продуктам уже есть в хранилище
     */
    isCreditProductsRecieved: boolean;
    isClosedCreditProducts: boolean;
    isClosedCreditProductsTranches: boolean;
    isAllCreditProductsTranches: boolean;
    isAllReceivedCreditProductsTranches: boolean;
    disallowedDatesForLoanRepayment: number[];
    closestTranchePayment: { [k: string]: Amount };
    overdraftChangeRepayment: OverdraftChangeRepaymentState;
    overdraftsNetTurnovers: OverdraftsNetTurnoverState;
};

export const creditProductsReducer = combineReducers<CreditProductsState>({
    credits,
    overdrafts,
    creditCards,
    creditLines,
    guaranties,
    guarantyLines,
    sopukLine,
    tranches,
    allProducts,
    selectedProduct: selectedCreditProduct,
    tranchesInfo: tranchesInfoReducer,
    overdraftsLimits,
    overType,
    clientLimit,
    isFetchingCreditProducts,
    isFetchingTranches,
    isFetchingTranchesCount,
    isFetchingOverdraftLimitSettings,
    isFetchingSetOverdraftLimit,
    isFetchingDisallowedDatesForLoanRepayment,
    overdraftLimitStatus,
    creditProductsError,
    isGetTrancheCreditProductsCountError,
    isGetTrancheCreditProductsError,
    isGetOverdraftLimitSettingsError,
    isCreditProductsRecieved,
    isClosedCreditProducts,
    isClosedCreditProductsTranches,
    isAllCreditProductsTranches,
    isAllReceivedCreditProductsTranches,
    disallowedDatesForLoanRepayment,
    closestTranchePayment,
    overdraftChangeRepayment,
    overdraftsNetTurnovers,
});
