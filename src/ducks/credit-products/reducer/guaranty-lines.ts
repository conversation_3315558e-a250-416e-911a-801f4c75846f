import { type MappedGuarantyLine } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type GuarantyLinesState = Record<string, MappedGuarantyLine>;

const initialState: GuarantyLinesState = {};

export function guarantyLines(
    state: GuarantyLinesState = initialState,
    action: ActionTypes,
): GuarantyLinesState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return action.mappedGuarantyLines;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        default:
            return state;
    }
}
