import { type MappedGuaranty } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type GuarantiesState = Record<string, MappedGuaranty>;

const initialState: GuarantiesState = {};

export function guaranties(
    state: GuarantiesState = initialState,
    action: ActionTypes,
): GuarantiesState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return action.mappedGuaranties;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
            return initialState;
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        default:
            return state;
    }
}
