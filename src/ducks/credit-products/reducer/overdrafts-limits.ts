import { type TOverdraftLimit } from '#/src/types/overdraft';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type OverdraftsLimitsState = Record<string, TOverdraftLimit>;

export function overdraftsLimits(
    state: OverdraftsLimitsState = {},
    action: ActionTypes,
): OverdraftsLimitsState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return {};
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_FINISH:
            return {
                ...state,
                [action.docNumber]: {
                    ...state[action.docNumber],
                    minClientLimit: action.minClientLimit,
                    maxClientLimit: action.maxClientLimit,
                    hasClientLimitSetting: action.hasClientLimitSetting,
                    repaymentResult: action.repaymentResult,
                    overdraftLimitRulesUrl: action.overdraftLimitRulesUrl,
                },
            };
        default:
            return state;
    }
}
