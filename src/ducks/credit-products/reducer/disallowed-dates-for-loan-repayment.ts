import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

const initialState: number[] = [];

export function disallowedDatesForLoanRepayment(
    state: number[] = initialState,
    action: ActionTypes,
): number[] {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        case CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_ERROR:
        case CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_START:
            return [];
        case CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_FINISH:
            return action.dates;
        default:
            return state;
    }
}
