import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type IsGetTrancheCreditProductsErrorState = string | EErrorMessages | null;

export function isGetTrancheCreditProductsError(
    state: IsGetTrancheCreditProductsErrorState = null,
    action: ActionTypes,
): IsGetTrancheCreditProductsErrorState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_START:
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_START:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return null;
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_ERROR:
            return action.error.message;
        default:
            return state;
    }
}
