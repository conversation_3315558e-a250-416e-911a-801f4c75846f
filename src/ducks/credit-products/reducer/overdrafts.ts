import { type MappedOverdraft } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type OverdraftsState = Record<string, MappedOverdraft>;

export function overdrafts(state: OverdraftsState = {}, action: ActionTypes): OverdraftsState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return action.mappedOverdrafts;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return {};
        case CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_FINISH:
            return {
                ...state,
                [action.docNumber]: {
                    ...state[action.docNumber],
                    clientLimit: action.clientLimit,
                } as NonNullable<MappedOverdraft>,
            };
        default:
            return state;
    }
}
