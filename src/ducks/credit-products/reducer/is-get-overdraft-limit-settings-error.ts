import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type IsGetOverdraftLimitSettingsErrorState = string | EErrorMessages | null;

export function isGetOverdraftLimitSettingsError(
    state: IsGetOverdraftLimitSettingsErrorState = null,
    action: ActionTypes,
): IsGetOverdraftLimitSettingsErrorState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return null;
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_ERROR:
            return action.error.message;
        default:
            return state;
    }
}
