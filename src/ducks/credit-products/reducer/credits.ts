import { type MappedCredit } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditsState = Record<string, MappedCredit>;

const initialState: CreditsState = {};

export function credits(state: CreditsState = initialState, action: ActionTypes): CreditsState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return action.mappedCredits;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return initialState;
        default:
            return state;
    }
}
