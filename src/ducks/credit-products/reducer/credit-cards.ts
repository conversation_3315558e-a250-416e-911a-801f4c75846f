import { type MappedCreditCard } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditCardsState = Record<string, MappedCreditCard>;

export function creditCards(state: CreditCardsState = {}, action: ActionTypes): CreditCardsState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH:
            return action.mappedCreditCards;
        case CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return {};
        default:
            return state;
    }
}
