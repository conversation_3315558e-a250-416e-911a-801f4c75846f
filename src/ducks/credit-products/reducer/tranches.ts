import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { CREDIT_PRODUCTS_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type TranchesState = Record<string, MappedTranche>;

export function tranches(state: TranchesState = {}, action: ActionTypes): TranchesState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_START:
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return {};
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_START:
            return action.isFromTab
                ? {}
                : {
                      ...state,
                  };
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH:
            return action.pageFilter.pageNumber === 0
                ? action.mappedTranches
                : {
                      ...state,
                      ...action.mappedTranches,
                  };
        case CREDIT_PRODUCTS_TYPES.TOGGLE_TRANCHE_DETAIL:
            return {
                ...state,
                [action.docNumber]: (state[action.docNumber] && state[action.docNumber].isExpanded
                    ? { ...state[action.docNumber], isExpanded: false }
                    : {
                          ...(state[action.docNumber] || {}),
                          isExpanded: true,
                      }) as NonNullable<MappedTranche>,
            };
        default:
            return state;
    }
}
