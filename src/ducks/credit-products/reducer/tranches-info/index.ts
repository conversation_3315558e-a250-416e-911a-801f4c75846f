import { combineReducers } from 'redux';

import { tranchesSelectReducer, type TranchesSelectState } from '../../tranches-select/reducer';

import { pageFilterReducer, type PageFilterState } from './page-filter';
import { receivedCountReducer } from './received-count';
import { totalCountReducer } from './total-count';

export type TranchesInfoState = {
    totalCount: number;
    receivedCount: number;
    pageFilter: PageFilterState;
    select: TranchesSelectState;
};

export const tranchesInfoReducer = combineReducers<TranchesInfoState>({
    pageFilter: pageFilterReducer,
    receivedCount: receivedCountReducer,
    totalCount: totalCountReducer,
    select: tranchesSelectReducer,
});
