import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../../actions';
import { CREDIT_PRODUCTS_TYPES } from '../../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function receivedCountReducer(state = 0, action: ActionTypes): number {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_START:
        case CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE:
            return 0;
        case CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH:
            return state + action.receivedTranchesCount;
        default:
            return state;
    }
}
