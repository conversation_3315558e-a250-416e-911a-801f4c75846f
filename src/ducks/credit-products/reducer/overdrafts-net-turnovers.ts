import { type TTurnoverData } from 'thrift-services/services/credit_products';

import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type OverdraftsNetTurnoverState = Record<
    string,
    {
        loaded: boolean;
        pending: boolean;
        error?: Error;
        turnoverData?: TTurnoverData;
    }
>;

export function overdraftsNetTurnovers(
    state: OverdraftsNetTurnoverState = {},
    action: ActionTypes,
): OverdraftsNetTurnoverState {
    switch (action.type) {
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_START:
            return {
                ...state,
                [action.docNumber]: {
                    loaded: false,
                    pending: true,
                },
            };
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_FINISH:
            return {
                ...state,
                [action.docNumber]: {
                    loaded: true,
                    pending: false,
                    turnoverData: action.turnoverData,
                },
            };
        case CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_ERROR:
            return {
                ...state,
                [action.docNumber]: {
                    loaded: false,
                    pending: false,
                    error: action.error,
                },
            };
        default:
            return state;
    }
}
