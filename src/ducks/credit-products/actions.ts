import {
    type CreditProduct,
    type CreditProductProjectionRequest,
} from 'corp-core-credit-products-api-typescript-services';
import { type ClientLimitResponse } from 'corp-core-credit-products-api-typescript-services/dist/clientLimitResponse';
import { type ResponseError } from 'corp-core-credit-products-api-typescript-services/dist/responseError';
import { type TPageFilter } from 'corporate-services/server/types/utils';
import { type Amount } from 'thrift-services/entities';
import {
    type TRepaymentResult,
    type TTurnoverData,
} from 'thrift-services/services/credit_products';

import { type SelectedCreditProductState } from '#/src/ducks/credit-products/reducer/selected-credit-product';
import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import { type ERepaymentType } from '#/src/types/overdraft';

import { type LOG_LEVEL } from '../../types/logger';
import {
    type MappedCredit,
    type MappedCreditCard,
    type MappedCreditLine,
    type MappedGuaranty,
    type MappedGuarantyLine,
    type MappedOverdraft,
    type MappedSopukLine,
} from '../../utils/credit-products-mappers';
import { type CreditProductErrors } from '../../utils/errors';

import { CREDIT_PRODUCTS_TYPES } from './types';

export function getTrancheCreditProductsCountStart(data?: {
    docNumber?: string;
    trancheType?: ETrancheTypes;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_START,
        ...data,
    };
}

export function getTrancheCreditProductsCountFinish(data: {
    docNumber?: string;
    trancheType?: ETrancheTypes;
    all: boolean;
    tranchesCount: number;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_FINISH,
        ...data,
    };
}

export function getTrancheCreditProductsCountError(
    error: CreditProductErrors,
    logLevel: LOG_LEVEL,
) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR,
        error,
        logLevel,
    };
}

export function getTrancheClosePaymentAmountStart(
    docNumber: string,
    organizationId: string,
    projection: CreditProductProjectionRequest,
) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CLOSE_PAYMENT_AMOUNT_START,
        docNumber,
        organizationId,
        projection,
    };
}

export function getTrancheClosePaymentAmountError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CLOSE_PAYMENT_AMOUNT_ERROR,
        error,
        logLevel,
    };
}

export function getTrancheClosePaymentAmountFinish(amount: { [k: string]: Amount }) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CLOSE_PAYMENT_AMOUNT_FINISH,
        amount,
    };
}

export function getCreditProductsStart({
    docNumber,
    withFault = true,
    withDocs = true,
    withClosed,
    withPaymentStatus = false,
}: {
    docNumber?: string;
    withFault?: boolean;
    withDocs?: boolean;
    withClosed?: boolean;
    withPaymentStatus?: boolean;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_START,
        docNumber,
        withFault,
        withDocs,
        withClosed,
        withPaymentStatus,
    };
}

export function getCreditProductsError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR,
        error,
        logLevel,
    };
}

export type GetCreditProductsFinishParam = {
    mappedCredits: Record<string, MappedCredit>;
    mappedCreditCards: Record<string, MappedCreditCard>;
    mappedOverdrafts: Record<string, MappedOverdraft>;
    mappedCreditLines: Record<string, MappedCreditLine>;
    mappedSopukLine: Record<string, MappedSopukLine>;
    mappedGuaranties: Record<string, MappedGuaranty>;
    mappedGuarantyLines: Record<string, MappedGuarantyLine>;
    allProductsIndexedByDocNumber: Record<string, CreditProduct>;
    withClosed: boolean;
};

export function getCreditProductsFinish(creditProducts: GetCreditProductsFinishParam) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH,
        mappedCredits: creditProducts.mappedCredits,
        mappedCreditCards: creditProducts.mappedCreditCards,
        mappedOverdrafts: creditProducts.mappedOverdrafts,
        mappedCreditLines: creditProducts.mappedCreditLines,
        mappedSopukLine: creditProducts.mappedSopukLine,
        mappedGuaranties: creditProducts.mappedGuaranties,
        mappedGuarantyLines: creditProducts.mappedGuarantyLines,
        allProductsIndexedByDocNumber: creditProducts.allProductsIndexedByDocNumber,
        withClosed: creditProducts.withClosed,
    };
}

export const getSelectedCreditProductStart = (docNumber: string) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_START,
    docNumber,
});

export const getSelectedCreditProductError = (error: CreditProductErrors, logLevel: LOG_LEVEL) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_ERROR,
    error,
    logLevel,
});

export const getSelectedCreditProductFinish = (
    selectedProduct: SelectedCreditProductState['product'],
) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_SELECTED_CREDIT_PRODUCT_FINISH,
    selectedProduct,
});

export function getTrancheCreditProductsStart({
    docNumber,
    trancheType = ETrancheTypes.tranche,
    isFromFirstPage = true,
    all = false,
    isFromTab = false,
    isClosed = false,
    isAll = false,
    isForSelect = false,
}: {
    docNumber?: string;
    trancheType?: ETrancheTypes;
    isFromFirstPage?: boolean;
    all?: boolean;
    isFromTab?: boolean;
    isClosed?: boolean;
    isAll?: boolean;
    isForSelect?: boolean;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_START,
        docNumber,
        trancheType,
        isFromFirstPage,
        all,
        isFromTab,
        isClosed,
        isAll,
        isForSelect,
    };
}

export function getTrancheCreditProductsFinish({
    mappedTranches,
    pageFilter,
    receivedTranchesCount,
    tranchesIndexedByDocNumber,
    isClosedCreditProductsTranches,
    isAllCreditProductsTranches,
    isAllReceivedCreditProductsTranches,
}: {
    mappedTranches: any;
    pageFilter: TPageFilter;
    receivedTranchesCount: number;
    tranchesIndexedByDocNumber: Record<string, CreditProduct>;
    isClosedCreditProductsTranches: boolean;
    isAllCreditProductsTranches: boolean;
    isAllReceivedCreditProductsTranches: boolean;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH,
        mappedTranches,
        pageFilter,
        receivedTranchesCount,
        tranchesIndexedByDocNumber,
        isClosedCreditProductsTranches,
        isAllCreditProductsTranches,
        isAllReceivedCreditProductsTranches,
    };
}

export function getTrancheCreditProductsError({
    error,
    logLevel,
    isClosedCreditProductsTranches,
    isAllCreditProductsTranches,
}: {
    error: CreditProductErrors;
    logLevel: LOG_LEVEL;
    isClosedCreditProductsTranches: boolean;
    isAllCreditProductsTranches: boolean;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_ERROR,
        error,
        logLevel,
        isClosedCreditProductsTranches,
        isAllCreditProductsTranches,
    };
}

export function resetCreditProductsState() {
    return {
        type: CREDIT_PRODUCTS_TYPES.RESET_CREDIT_PRODUCTS_STATE,
    };
}

export function toggleTrancheDetail(docNumber: string) {
    return {
        type: CREDIT_PRODUCTS_TYPES.TOGGLE_TRANCHE_DETAIL,
        docNumber,
    };
}

export function setOverdraftLimitStart(docNumber: string, limit: Amount) {
    return {
        type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_START,
        docNumber,
        limit,
    };
}

export function setOverdraftLimitFinish(docNumber: string, clientLimit: Amount) {
    return {
        type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_FINISH,
        docNumber,
        clientLimit,
    };
}

export function setOverdraftLimitError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_ERROR,
        error,
        logLevel,
    };
}

export function getOverdraftLimitSettingsStart() {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_START,
    };
}

export function getOverdraftLimitSettingsFinish({
    docNumber,
    hasClientLimitSetting,
    maxClientLimit,
    minClientLimit,
    repaymentResult,
    overdraftLimitRulesUrl,
}: {
    docNumber: string;
    hasClientLimitSetting?: boolean;
    maxClientLimit?: Amount;
    minClientLimit?: Amount;
    repaymentResult?: TRepaymentResult;
    overdraftLimitRulesUrl?: string;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_FINISH,
        docNumber,
        hasClientLimitSetting,
        maxClientLimit,
        minClientLimit,
        repaymentResult,
        overdraftLimitRulesUrl,
    };
}

export function getOverdraftLimitSettingsError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_ERROR,
        error,
        logLevel,
    };
}

export function limitNotificationClose() {
    return {
        type: CREDIT_PRODUCTS_TYPES.LIMIT_NOTIFICATION_CLOSE,
    };
}

export const goCreditProductPage = ({
    path,
    docNumber,
    customerId,
    tab,
}: {
    path: string;
    docNumber: string;
    customerId?: string;
    tab?: string;
}) => ({
    type: CREDIT_PRODUCTS_TYPES.GO_CREDIT_PRODUCT_PAGE,
    path,
    docNumber,
    customerId,
    tab,
});

export function goTranchePage({
    path,
    docNumber,
    trancheNumber,
    customerId,
    tab,
}: {
    path: string;
    docNumber: string;
    trancheNumber?: string;
    customerId?: string;
    tab?: string;
}) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GO_TRANCHE_PAGE,
        path,
        docNumber,
        trancheNumber,
        customerId,
        tab,
    };
}

export const getDisallowedDatesForLoanRepaymentStart = (startDate: string, endDate: string) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_START,
    startDate,
    endDate,
});

export const getDisallowedDatesForLoanRepaymentFinish = (dates: number[]) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_FINISH,
    dates,
});

export const getDisallowedDatesForLoanRepaymentError = (error: Error) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_DISALLOWED_DATES_FOR_LOAN_REPAYMENT_ERROR,
    error,
});

export const getOverdraftRepaymentStatusStart = (dealId: string) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_START,
    dealId,
});

export const getOverdraftRepaymentStatusError = (dealId: string, error: Error) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_ERROR,
    dealId,
    error,
});

export const getOverdraftRepaymentStatusFinish = (
    dealId: string,
    repaymentData: TRepaymentResult,
) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_FINISH,
    dealId,
    repaymentData,
});

export const setOverdraftRepaymentStart = (dealId: string, repayment: ERepaymentType) => ({
    type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_START,
    repayment,
    dealId,
});

export const setOverdraftRepaymentError = (dealId: string, error: Error) => ({
    type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_ERROR,
    dealId,
    error,
});

export const setOverdraftRepaymentFinish = (
    dealId: string,
    repaymentChangeData?: TRepaymentResult,
) => ({
    type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_FINISH,
    dealId,
    repaymentChangeData,
});

export const clearOverdraftRepaymentStatus = (dealId: string) => ({
    type: CREDIT_PRODUCTS_TYPES.CLEAR_OVERDRAFT_REPAYMENT_STATUS,
    dealId,
});

export function getClientLimitStart(payload: { inn: string; kpp: string }) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_START,
        payload,
    };
}

export function getClientLimitFinish(clientLimitResponse: ClientLimitResponse) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_FINISH,
        clientLimitResponse,
    };
}

export function getClientLimitError(responseError: ResponseError, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PRODUCTS_TYPES.GET_CLIENT_LIMIT_ERROR,
        error: responseError,
        logLevel,
    };
}

export function resetClientLimitState() {
    return {
        type: CREDIT_PRODUCTS_TYPES.RESET_CLIENT_LIMIT_STATE,
    };
}

export const getOverdraftNetTurnoverStart = (docNumber: string, accountNumber: string) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_START,
    docNumber,
    accountNumber,
});

export const getOverdraftNetTurnoverFinish = (docNumber: string, turnoverData?: TTurnoverData) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_FINISH,
    docNumber,
    turnoverData,
});

export const getOverdraftNetTurnoverError = (docNumber: string, error?: Error) => ({
    type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_NET_TURNOVER_ERROR,
    docNumber,
    error,
});
