import { createSelector } from 'reselect';

import { DECISION_COMMITTEE_UTK } from '#/src/containers/main-page/main-page-header/main-page-smart-limits/utils/constants';
import { type ApplicationState } from '#/src/ducks/application-state';

export const creditClientLimitSelector = (state: ApplicationState) =>
    state.creditProducts.clientLimit;

export const isActualLimitsCommitteeUTKSelector = createSelector(
    creditClientLimitSelector,
    ({ clientLimit }) => {
        const isRestReserveAmount = clientLimit?.limit?.sublimits?.some(
            (item) => !!item?.restReserveAmount?.amount,
        );

        return (
            clientLimit?.limit?.decisionCommittee === DECISION_COMMITTEE_UTK && isRestReserveAmount
        );
    },
);

export const actualLimitsLimitIdSelector = createSelector(
    creditClientLimitSelector,
    ({ clientLimit }) => clientLimit?.limit?.id.toString(),
);

export const maxActualLimitsAmountSelector = createSelector(
    creditClientLimitSelector,
    ({ clientLimit }) =>
        clientLimit?.limit?.sublimits?.reduce((acc, current) => {
            if (acc < Number(current.restReserveAmount.amount)) {
                acc = Number(current.restReserveAmount.amount);
            }

            return acc;
        }, 0) || 0,
);
