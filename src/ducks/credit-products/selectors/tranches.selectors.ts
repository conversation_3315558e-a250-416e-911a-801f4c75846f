import qs from 'qs';
import { createSelector } from 'reselect';

import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { type ApplicationState } from '../../application-state';
import { searchParamsSelector } from '../../router/selectors';

export const tranchesSelector = (state: ApplicationState) => state.creditProducts.tranches;
export const isCreditProductsTranchesFetchingSelector = (state: ApplicationState) =>
    state.creditProducts.isFetchingTranches;
export const isTrancheCountFetchingSelector = (state: ApplicationState) =>
    state.creditProducts.isFetchingTranchesCount;
export const isClosedCreditProductsTranchesSelector = (state: ApplicationState) =>
    state.creditProducts.isClosedCreditProductsTranches;
export const isAllCreditProductsTranchesSelector = (state: ApplicationState) =>
    state.creditProducts.isAllCreditProductsTranches;
export const isAllReceivedCreditProductsTranchesSelector = (state: ApplicationState) =>
    state.creditProducts.isAllReceivedCreditProductsTranches;

export const parentTrancheNumberSelector = createSelector(
    searchParamsSelector,
    (search) => `${qs.parse(search, { ignoreQueryPrefix: true }).trancheNumber ?? ''}`,
);

export const parentTrancheSelector = createSelector(
    tranchesSelector,
    parentTrancheNumberSelector,
    (tranches, trancheNumber) => tranches[trancheNumber],
);

export const isTrancheCreditProductsCountErrorSelector = (state: ApplicationState) =>
    state.creditProducts.isGetTrancheCreditProductsCountError;
export const isTrancheCreditProductsErrorSelector = (state: ApplicationState) =>
    state.creditProducts.isGetTrancheCreditProductsError;

export const isGetTrancheCreditProductsErrorSelector = createSelector(
    isTrancheCreditProductsCountErrorSelector,
    isTrancheCreditProductsErrorSelector,
    (isTrancheCreditProductsCountError, isTrancheCreditProductsError) =>
        isTrancheCreditProductsCountError === EErrorMessages.SERVER_RESPONSE_ERROR ||
        isTrancheCreditProductsError === EErrorMessages.SERVER_RESPONSE_ERROR,
);
