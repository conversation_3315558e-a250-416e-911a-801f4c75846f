import { startOfDay } from 'date-fns';
import qs from 'qs';
import { createSelector } from 'reselect';
import { Role } from 'thrift-services/users';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { ERequisitesProductCodes } from '#/src/constants/credit-products';
import { OrganizationCategoryCodes } from '#/src/constants/organization-category-codes';
import { type ApplicationState } from '#/src/ducks/application-state';
import { isCreditApplicationsFetchingSelector } from '#/src/ducks/credit-processing/selectors';
import {
    isCeoSelector,
    isMksPermissionsFetchingSelector,
} from '#/src/ducks/mks-permissions/selectors';
import {
    isFetchingCategorySelector,
    organizationCategoryCodeSelector,
} from '#/src/ducks/organization/selectors';
import { creditProductHasOverdue } from '#/src/utils/early-pay';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { searchParamsSelector } from '../../router/selectors';
import { currentTimeSelector } from '../../settings/selectors';
import { currentRoleSelector } from '../../user/selectors';

import { selectedProductSelector } from './credit-line.selectors';

export const creditProductsErrorSelector = (state: ApplicationState) =>
    state.creditProducts.creditProductsError;
export const isCreditProductsFetchingSelector = (state: ApplicationState) =>
    state.creditProducts.isFetchingCreditProducts;

export const isGetCreditProductsErrorSelector = (state) =>
    state.creditProducts.isGetCreditProductsError &&
    state.creditProducts.isGetCreditProductsError === EErrorMessages.SERVER_RESPONSE_ERROR;

export const isGetNoCreditProductsErrorSelector = createSelector(
    creditProductsErrorSelector,
    (creditProductsError) =>
        !!creditProductsError && creditProductsError === EErrorMessages.NO_PRODUCTS,
);

export const hasCreditOffersSmallSizeSelector = (state: ApplicationState) =>
    !isGetNoCreditProductsErrorSelector(state) && !state;

export const parentDocNumberSelector = createSelector(
    searchParamsSelector,
    (search) => `${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`,
);

export const allProductsSelector = (state: ApplicationState) => state.creditProducts.allProducts;

export const productByDocNumberSelector = createSelector(
    parentDocNumberSelector,
    allProductsSelector,
    selectedProductSelector,
    (docNumber, products, selectedProduct) =>
        products[docNumber] ||
        Object.values(selectedProduct.product).find((product) => product?.docNumber == docNumber),
);

export const hasInterestAmountSelector = createSelector(
    productByDocNumberSelector,
    (product) => !!product?.debts?.interest?.interest?.amount,
);

export const issueDateSelector = createSelector(
    productByDocNumberSelector,
    (product) =>
        product?.requisites?.issueDate.seconds || product?.requisites?.fromDate.seconds || 0,
);

export const hasOverdueSelector = createSelector(productByDocNumberSelector, (product) =>
    creditProductHasOverdue(product),
);

export const closestTranchePaymentsSelector = (state: ApplicationState) =>
    state.creditProducts.closestTranchePayment;

export const closestTranchePaymentAmountSelector = createSelector(
    parentDocNumberSelector,
    closestTranchePaymentsSelector,
    (docNumber, closestTranchePayments) => closestTranchePayments[docNumber],
);

export const productCodeSelector = createSelector(
    productByDocNumberSelector,
    (product) => product?.requisites?.productCode,
);

export const notifyAboutAdvancedRepaySelector = createSelector(
    productByDocNumberSelector,
    (product) => product?.requisites?.notifyAboutAdvancedRepay,
);

export const disallowedDatesForLoanRepaymentSelector = (state: ApplicationState) =>
    state.creditProducts.disallowedDatesForLoanRepayment;

export const isFetchingDisallowedDatesForLoanRepaymentSelector = (state: ApplicationState) =>
    state.creditProducts.isFetchingDisallowedDatesForLoanRepayment;

export const isFetchingEarlyRepaymentButtonStateSelector = createSelector(
    isCreditApplicationsFetchingSelector,
    isMksPermissionsFetchingSelector,
    isFetchingDisallowedDatesForLoanRepaymentSelector,
    isFetchingCategorySelector,
    isCreditProductsFetchingSelector,
    (
        isCreditApplicationsFetching,
        isMksPermissionsFetching,
        isFetchingDisallowedDatesForLoanRepayment,
        isFetchingCategory,
        isCreditProductsFetching,
    ) =>
        isCreditApplicationsFetching ||
        isMksPermissionsFetching ||
        isFetchingDisallowedDatesForLoanRepayment ||
        isFetchingCategory ||
        isCreditProductsFetching,
);

export const isSBCategoryCodeSelector = createSelector(
    organizationCategoryCodeSelector,
    (organizationCategoryCode) =>
        [
            OrganizationCategoryCodes.ClientCategorySKS8,
            OrganizationCategoryCodes.ClientCategorySKS18,
        ].includes(organizationCategoryCode as OrganizationCategoryCodes),
);

export const isKIBCategoryCodeSelector = createSelector(
    organizationCategoryCodeSelector,
    (organizationCategoryCode) =>
        [
            OrganizationCategoryCodes.ClientCategorySKS2,
            OrganizationCategoryCodes.ClientCategorySKS3,
            OrganizationCategoryCodes.ClientCategorySKS5,
            OrganizationCategoryCodes.ClientCategorySKS9,
        ].includes(organizationCategoryCode as OrganizationCategoryCodes),
);

export const earlyRepaymentButtonStateSelector = createSelector(
    isKIBCategoryCodeSelector,
    isFetchingEarlyRepaymentButtonStateSelector,
    isCeoSelector,
    currentTimeSelector,
    hasOverdueSelector,
    currentRoleSelector,
    issueDateSelector,
    (
        isKIBCategoryCode,
        isFetchingEarlyRepaymentButtonState,
        isCeo,
        currentTime,
        hasOverdue,
        currentRole,
        issueDate,
    ): earlyRepaymentButtonStates => {
        if (currentRole === Role.OPERATOR || isKIBCategoryCode) {
            return earlyRepaymentButtonStates.hidden;
        }

        if (isFetchingEarlyRepaymentButtonState) {
            return earlyRepaymentButtonStates.disabled;
        }

        if (!isCeo && !isKIBCategoryCode) {
            return earlyRepaymentButtonStates.noCeo;
        }

        if (hasOverdue) {
            return earlyRepaymentButtonStates.hasOverdue;
        }

        const currentDateSeconds = startOfDay(currentTime).getTime() / 1000;

        if (issueDate >= currentDateSeconds) {
            return earlyRepaymentButtonStates.disabled;
        }

        return earlyRepaymentButtonStates.enabled;
    },
);

export const hasPrepaymentAvailabilitySelector = createSelector(
    productByDocNumberSelector,
    (product) => product?.frontParams?.hasPrepaymentAvailability,
);

export const isCalculateFeeForEarlyRepaymentSelector = createSelector(
    hasPrepaymentAvailabilitySelector,
    productCodeSelector,
    (hasPrepaymentAvailability, productCode): boolean =>
        !hasPrepaymentAvailability &&
        [
            ERequisitesProductCodes.UMCRRUBP01,
            ERequisitesProductCodes.UMCRRUBP02,
            ERequisitesProductCodes.UMCRRUBP11,
            ERequisitesProductCodes.UMCRRUBP14,
        ].includes(productCode as ERequisitesProductCodes),
);
