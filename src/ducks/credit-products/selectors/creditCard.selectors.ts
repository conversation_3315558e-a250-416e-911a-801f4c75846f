import { createSelector } from 'reselect';

import { mapObjectToArray } from '#/src/utils/object-helpers';

import { type ApplicationState } from '../../application-state';

export const creditCardsSelector = (state: ApplicationState) => state.creditProducts.creditCards;

export const creditCardsAsArraySelector = createSelector(creditCardsSelector, (creditCards) => {
    const creditCardsList = mapObjectToArray(creditCards);

    return creditCardsList.length ? creditCardsList : [];
});
