import isBefore from 'date-fns/isBefore';
import qs from 'qs';
import { createSelector } from 'reselect';

import { TWO_THOUSAND_AND_NINETEEN_YEAR_DATES } from '#/src/constants/date';
import { PercentTypeSubscriptionMB } from '#/src/constants/overdraft';
import { parentDocNumberSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import OVERDRAFT_GENERAL_TERMS_24_10_22 from '#/src/static-files/overdraft/overdraft_general_terms_24_10_22.pdf';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';
import { getMoscowDate, parseDateFromAny } from '#/src/utils/date';

import { type ApplicationState } from '../../application-state';
import { searchParamsSelector } from '../../router/selectors';

const overdraftsSelector = (state: ApplicationState) =>
    state.creditProducts.selectedProduct.product.overdraft;
const overdraftsLimitsSelector = (state: ApplicationState) => state.creditProducts.overdraftsLimits;

export const isOverdraftLimitFetchingSelector = (state: ApplicationState) =>
    state.creditProducts.isFetchingOverdraftLimitSettings;
export const overdraftTypeSelector = (state: ApplicationState) => state.creditProducts.overType;

export const parentOverdraftSelector = createSelector(overdraftsSelector, (overdraft) => overdraft);

export const parentOverdraftStartDateSelector = createSelector(
    parentOverdraftSelector,
    (overdraft) => !!overdraft && !checkIsFaultyCreditProduct(overdraft) && overdraft.fromDate,
);

export const parentOverdraftLimitSelector = createSelector(
    overdraftsLimitsSelector,
    searchParamsSelector,
    (overdraftsLimits, search) =>
        overdraftsLimits[`${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`],
);

export const overdraftRulesFilePathSelector = createSelector(
    parentOverdraftStartDateSelector,
    (startDate) => {
        const overdraftStartDateAsJSDate = getMoscowDate(parseDateFromAny(new Date(), startDate));
        const hasOverdraftRulesFile =
            overdraftStartDateAsJSDate &&
            !isBefore(
                overdraftStartDateAsJSDate,
                TWO_THOUSAND_AND_NINETEEN_YEAR_DATES.SECOND_OF_APRIL,
            );

        if (!hasOverdraftRulesFile) return null;

        return OVERDRAFT_GENERAL_TERMS_24_10_22;
    },
);

export const parentOverdraftDealIdSelector = createSelector(
    parentOverdraftSelector,
    (parentOverdraft) => parentOverdraft?.dealId,
);

export const parentOverdraftRepaymentTypeSelector = createSelector(
    parentOverdraftSelector,
    (parentOverdraft) => parentOverdraft?.requisites?.repaymentType,
);

export const overdraftRepaymentsSelector = (state: ApplicationState) =>
    state.creditProducts.overdraftChangeRepayment;

export const parentOverdraftRepaymentSelector = createSelector(
    parentOverdraftDealIdSelector,
    overdraftRepaymentsSelector,
    (dealId, repayments) => {
        if (dealId) {
            return repayments[dealId];
        }

        return null;
    },
);

export const isParentOverdraftSubscriptionSelector = createSelector(
    parentOverdraftSelector,
    (parentOverdraft) =>
        parentOverdraft?.requisites?.commissions?.some(
            (item) => item?.percentType === PercentTypeSubscriptionMB && (item?.rate ?? 0) > 0,
        ),
);

export const overdraftsNetTurnoversSelector = (state: ApplicationState) =>
    state.creditProducts.overdraftsNetTurnovers;
export const parentOverdraftNetTurnoverSelector = createSelector(
    overdraftsNetTurnoversSelector,
    parentDocNumberSelector,
    (turnovers, docNumber) => turnovers[docNumber],
);
