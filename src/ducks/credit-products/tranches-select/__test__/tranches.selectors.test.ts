import { type ApplicationState } from '#/src/ducks/application-state';

import { tranchesSelectIsLoadingSelector } from '../selectors/tranches.selectors';

const buildState = (isLoading: boolean) =>
    ({
        creditProducts: {
            tranchesInfo: {
                select: {
                    isLoading,
                    tranches: [],
                },
            },
        },
    }) as unknown as ApplicationState;

describe('tranches.selectors', () => {
    it('tracks loading', () => {
        expect(tranchesSelectIsLoadingSelector(buildState(true))).toBeTruthy();
        expect(tranchesSelectIsLoadingSelector(buildState(false))).toBeFalsy();
    });
});
