import { createSelector } from 'reselect';

import { mapTranchesToSelectOptions } from '#/src/utils/credit-products-mappers';

import { type ApplicationState } from '../../../application-state';

export const tranchesSelectSelector = (state: ApplicationState) =>
    state.creditProducts.tranchesInfo.select;

export const tranchesSelectIsLoadingSelector = createSelector(
    tranchesSelectSelector,
    ({ isLoading }) => isLoading,
);

export const tranchesSelectAsOptionsSelector = createSelector(
    tranchesSelectSelector,
    ({ tranches }) => mapTranchesToSelectOptions(tranches),
);
