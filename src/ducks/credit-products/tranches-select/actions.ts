import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { type CreditProductErrors } from '#/src/utils/errors';

import { type LOG_LEVEL } from '../../../types/logger';

import { TRANCHES_SELECT_TYPES } from './types';

export function getTranchesSelectStart({
    organizationId,
    docNumber,
    trancheType = ETrancheTypes.tranche,
}: {
    organizationId: string;
    docNumber: string;
    trancheType?: ETrancheTypes;
}) {
    return {
        type: TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_START,
        organizationId,
        docNumber,
        trancheType,
    };
}

export function getTranchesSelectFinish(payload: MappedTranche[]) {
    return {
        type: TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_FINISH,
        payload,
    };
}

export function getTranchesSelectError(error: CreditProductErrors, logLevel: LOG_LEVEL) {
    return {
        type: TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_ERROR,
        error,
        logLevel,
    };
}
