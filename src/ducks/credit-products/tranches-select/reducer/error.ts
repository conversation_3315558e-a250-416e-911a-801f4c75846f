import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { TRANCHES_SELECT_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type TranchesSelectErrorState = string | null;

const initialState: TranchesSelectErrorState = null;

export function errorReducer(
    state: TranchesSelectErrorState = initialState,
    action: ActionTypes,
): TranchesSelectErrorState {
    switch (action.type) {
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_START:
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_FINISH:
            return null;
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_ERROR:
            return action.error.message;
        default:
            return state;
    }
}
