import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { TRANCHES_SELECT_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type TranchesSelectTranchesState = MappedTranche[];

const initialState: TranchesSelectTranchesState = [];

export function tranchesReducer(
    state: TranchesSelectTranchesState = initialState,
    action: ActionTypes,
): TranchesSelectTranchesState {
    switch (action.type) {
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_START:
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_ERROR:
            return initialState;
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_FINISH:
            return action.payload;
        default:
            return state;
    }
}
