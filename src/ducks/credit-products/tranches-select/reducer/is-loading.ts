import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from '../actions';
import { TRANCHES_SELECT_TYPES } from '../types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function isLoadingReducer(state = false, action: ActionTypes): boolean {
    switch (action.type) {
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_START:
            return true;
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_ERROR:
        case TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_FINISH:
            return false;
        default:
            return state;
    }
}
