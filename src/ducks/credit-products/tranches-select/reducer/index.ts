import { combineReducers } from 'redux';

import { errorReducer, type TranchesSelectErrorState } from './error';
import { isLoadingReducer } from './is-loading';
import { tranchesReducer, type TranchesSelectTranchesState } from './tranches';

export type TranchesSelectState = {
    error: TranchesSelectErrorState;
    isLoading: boolean;
    tranches: TranchesSelectTranchesState;
};

export const tranchesSelectReducer = combineReducers<TranchesSelectState>({
    error: errorReducer,
    isLoading: isLoadingReducer,
    tranches: tranchesReducer,
});
