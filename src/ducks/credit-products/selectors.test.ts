import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';
import setIn from 'lodash/fp/set';
import { Role } from 'thrift-services/users';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { EOverdraftTypes } from '#/src/constants/credit-products';
import { type ClientLimitState } from '#/src/ducks/credit-products/reducer/client-limit';
import { type SelectedCreditProductState } from '#/src/ducks/credit-products/reducer/selected-credit-product';
import { ELimitStatus } from '#/src/types/overdraft';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { type ApplicationState } from '../application-state';

import {
    earlyRepaymentButtonStateSelector,
    hasInterestAmountSelector,
    isGetCreditProductsErrorSelector,
    productByDocNumberSelector,
} from './selectors/common-credit-products.selectors';
import { overdraftRulesFilePathSelector } from './selectors/overdraft.selectors';
import {
    isGetTrancheCreditProductsErrorSelector,
    parentTrancheSelector,
} from './selectors/tranches.selectors';

const tranchesInfo = {
    pageFilter: {
        pageNumber: 0,
        perPage: 10,
    },
    totalCount: 10,
    receivedCount: 0,
    select: {
        isLoading: false,
        tranches: [],
        error: null,
    },
};

describe('credit products selectors', () => {
    const creditProduct = {
        availableAmount: null,
        fromDate: { seconds: 1422921600 }, // 2/03/2015 @ 12:00am (UTC)
    };

    const credits: Record<string, any> = { AAA: creditProduct };
    const overdrafts: Record<string, any> = { AAA: creditProduct };
    const creditCards: Record<string, any> = { AAA: creditProduct };
    const creditLines: Record<string, any> = { AAA: creditProduct };
    const guaranties: Record<string, any> = { AAA: creditProduct };
    const guarantyLines: Record<string, any> = { AAA: creditProduct };
    const tranches: Record<string, any> = { AAA: creditProduct };
    const sopukLine: Record<string, any> = { AAA: creditProduct };
    const allProducts: Record<string, any> = { AAA: creditProduct };
    const clientLimit: ClientLimitState = {
        error: null,
        isFetching: false,
        clientLimit: null,
        isFinished: false,
    };
    const selectedProduct: SelectedCreditProductState = {
        product: {
            overdraft: null,
            credit: null,
            creditLine: null,
            guarantie: null,
            sopukLine: null,
            guarantyLine: null,
        },
        isFetching: false,
        error: null,
    };

    const testState: Partial<ApplicationState> = {
        creditProducts: {
            credits,
            overdrafts,
            creditCards,
            creditLines,
            guaranties,
            guarantyLines,
            tranches,
            sopukLine,
            clientLimit,
            allProducts,
            overdraftsLimits: {},
            tranchesInfo,
            selectedProduct,
            overType: EOverdraftTypes.withTranches,
            isFetchingCreditProducts: true,
            isFetchingTranches: false,
            isFetchingTranchesCount: false,
            creditProductsError: null,
            isCreditProductsRecieved: false,
            isClosedCreditProducts: false,
            isGetTrancheCreditProductsCountError: null,
            isGetTrancheCreditProductsError: null,
            isGetOverdraftLimitSettingsError: null,
            isFetchingOverdraftLimitSettings: false,
            isFetchingDisallowedDatesForLoanRepayment: false,
            isAllCreditProductsTranches: false,
            isAllReceivedCreditProductsTranches: false,
            isClosedCreditProductsTranches: false,
            overdraftLimitStatus: ELimitStatus.NOT_CHANGING,
            isFetchingSetOverdraftLimit: false,
            disallowedDatesForLoanRepayment: [],
            closestTranchePayment: {},
            overdraftsNetTurnovers: {},
            overdraftChangeRepayment: {},
        },
        router: {
            location: {
                search: '?docNumber=AAA',
                query: {},
                pathname: '',
                state: '',
                hash: '',
            },
            action: 'POP' as const,
        },
    };

    describe('selectors find parent product with docNumber from query params', () => {
        it('parentGuarantyLineTrancheSelector should return product with docnumber is equal to search param', () => {
            const modifiedTestState = setIn(
                'router.location.search',
                '?trancheNumber=AAA',
                testState,
            );

            const actual = parentTrancheSelector(modifiedTestState as ApplicationState);

            expect(actual).toEqual(creditProduct);
        });

        // it('parentOverdraftSelector', () => {
        //     const actual = parentOverdraftSelector(testState as ApplicationState);
        //
        //     expect(actual).toEqual(creditProduct);
        // });
    });

    describe('isGetCreditProductsErrorSelector()', () => {
        it('isGetCreditProductsErrorSelector should return true if getCreditProducts returns server response error', () => {
            const modifiedTestState = setIn(
                'creditProducts.isGetCreditProductsError',
                EErrorMessages.SERVER_RESPONSE_ERROR,
                testState,
            );

            const actual = isGetCreditProductsErrorSelector(modifiedTestState as ApplicationState);

            expect(actual).toEqual(true);
        });
    });

    describe('productByDocNumberSelector', () => {
        it('should return product by key', () => {
            const someProduct = {};
            const applicationState = {
                creditProducts: {
                    allProducts: {
                        AAA: someProduct,
                    },
                },
                router: {
                    location: {
                        search: '?docNumber=AAA',
                        query: {},
                        pathname: '',
                        state: '',
                        hash: '',
                    },
                    action: 'POP' as const,
                },
            } as unknown as ApplicationState;

            expect(productByDocNumberSelector(applicationState)).toBe(someProduct);
        });
    });

    describe('hasInterestAmountSelector', () => {
        it('should return true when interest amount more than 0', () => {
            const moreThanZero = 1;
            const productsWithAmountMoreThanZero = [
                {
                    interest: { interest: { amount: moreThanZero } },
                },
                {
                    interest: { interest: { amount: moreThanZero } },
                },
            ].map((debts) => ({ debts })) as CreditProduct[];

            productsWithAmountMoreThanZero.forEach((product) => {
                expect(hasInterestAmountSelector.resultFunc(product)).toBe(true);
            });
        });

        it('should return false when interest amount equal 0', () => {
            const debts = {
                interest: { interest: { amount: 0 } },
            };

            expect(hasInterestAmountSelector.resultFunc({ debts } as CreditProduct)).toBe(false);
        });
    });

    describe('isGetTrancheCreditProductsErrorSelector()', () => {
        const testState: Partial<ApplicationState> = {
            creditProducts: {
                credits,
                overdrafts,
                creditCards,
                creditLines,
                guaranties,
                guarantyLines,
                tranches,
                sopukLine,
                clientLimit,
                allProducts,
                overdraftsLimits: {},
                tranchesInfo,
                selectedProduct,
                overType: EOverdraftTypes.withTranches,
                isFetchingCreditProducts: false,
                isFetchingTranches: false,
                isFetchingTranchesCount: false,
                creditProductsError: null,
                isCreditProductsRecieved: false,
                isClosedCreditProducts: false,
                isGetTrancheCreditProductsCountError: EErrorMessages.SERVER_RESPONSE_ERROR,
                isGetTrancheCreditProductsError: EErrorMessages.SERVER_RESPONSE_ERROR,
                isGetOverdraftLimitSettingsError: null,
                isFetchingOverdraftLimitSettings: false,
                isFetchingDisallowedDatesForLoanRepayment: false,
                isFetchingSetOverdraftLimit: false,
                isAllCreditProductsTranches: false,
                isClosedCreditProductsTranches: false,
                isAllReceivedCreditProductsTranches: false,
                overdraftLimitStatus: ELimitStatus.NOT_CHANGING,
                disallowedDatesForLoanRepayment: [],
                closestTranchePayment: {},
                overdraftsNetTurnovers: {},
                overdraftChangeRepayment: {},
            },
            router: {
                location: {
                    search: '',
                    pathname: '',
                    state: '',
                    hash: '',
                    query: {},
                },
                action: 'POP' as const,
            },
        };

        it('isGetTrancheCreditProductsErrorSelector should return true if getTrancheCreditProducts returns server response error', () => {
            const modifiedTestState = setIn(
                'creditProducts.isTrancheCreditProductsCountError',
                EErrorMessages.SERVER_RESPONSE_ERROR,
                testState,
            );

            const actual = isGetTrancheCreditProductsErrorSelector(
                modifiedTestState as ApplicationState,
            );

            expect(actual).toEqual(true);
        });
    });

    describe('overdraftRulesFilePathSelector()', () => {
        // it('should return null if overdraft fromDate field is before 2nd april of 2019', () => {
        //     const actual = overdraftRulesFilePathSelector(testState as ApplicationState);
        //
        //     expect(actual).toEqual(null);
        // });

        it('should return correct file if overdraft fromDate field is after 2nd april of 2019 and before 24th april of 2019', () => {
            const modifiedState = setIn(
                'creditProducts.overdrafts.AAA.fromDate.seconds',
                1555718400,
                testState,
            ); // 04/20/2019 @ 12:00am (UTC)
            const actual = overdraftRulesFilePathSelector(modifiedState as ApplicationState);

            expect(actual).toEqual('overdraft_general_terms_24_10_22.pdf');
        });

        it('should return correct file if overdraft fromDate field is equal to 24th april of 2019', () => {
            const modifiedState = setIn(
                'creditProducts.overdrafts.AAA.fromDate.seconds',
                1556064000,
                testState,
            ); // 04/24/2019 @ 12:00am (UTC)
            const actual = overdraftRulesFilePathSelector(modifiedState as ApplicationState);

            expect(actual).toEqual('overdraft_general_terms_24_10_22.pdf');
        });

        it('should return correct file if overdraft fromDate field is after 24th april of 2019 and less 25th february of 2020', () => {
            const modifiedState = setIn(
                'creditProducts.overdrafts.AAA.fromDate.seconds',
                1556150400,
                testState,
            ); // 04/25/2019 @ 12:00am (UTC)
            const actual = overdraftRulesFilePathSelector(modifiedState as ApplicationState);

            expect(actual).toEqual('overdraft_general_terms_24_10_22.pdf');
        });

        it('should return correct file if overdraft fromDate field is equal to 25th february of 2020', () => {
            const modifiedState = setIn(
                'creditProducts.overdrafts.AAA.fromDate.seconds',
                1582632000,
                testState,
            ); // 02/25/2020 @ 12:00am (UTC)
            const actual = overdraftRulesFilePathSelector(modifiedState as ApplicationState);

            expect(actual).toEqual('overdraft_general_terms_24_10_22.pdf');
        });

        it('should return correct file if overdraft fromDate field is after 25th february of 2020', () => {
            const modifiedState = setIn(
                'creditProducts.overdrafts.AAA.fromDate.seconds',
                32508216000,
                testState,
            ); // 02/22/3000 @ 12:00am (UTC)
            const actual = overdraftRulesFilePathSelector(modifiedState as ApplicationState);

            expect(actual).toEqual('overdraft_general_terms_24_10_22.pdf');
        });
    });

    describe('earlyRepaymentButtonStateSelector()', () => {
        const currentDate = new Date();
        const issueDate = new Date().getTime() / 1000;
        const verificationData: Array<
            [
                ReturnType<typeof earlyRepaymentButtonStateSelector>,
                Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ]
        > = [
            [
                earlyRepaymentButtonStates.hidden,
                [
                    [true, false, false, currentDate, false, Role.MANAGER, issueDate],
                    [false, false, true, currentDate, false, Role.OPERATOR, issueDate],
                    [true, false, true, currentDate, false, Role.OPERATOR, issueDate],
                ] as Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ],
            [
                earlyRepaymentButtonStates.disabled,
                [
                    [false, true, true, currentDate, true, Role.MANAGER, issueDate],
                    [false, true, false, currentDate, false, Role.MANAGER, issueDate],
                    [false, true, true, currentDate, false, Role.MANAGER, issueDate],
                    [false, true, false, currentDate, false, Role.MANAGER, issueDate],
                    [false, true, false, currentDate, true, Role.MANAGER, issueDate],
                    [false, true, false, currentDate, true, Role.MANAGER, issueDate],
                ] as Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ],
            [
                earlyRepaymentButtonStates.enabled,
                [
                    [false, false, true, currentDate, false, Role.MANAGER, issueDate - 100000],
                    [false, false, true, currentDate, false, Role.MANAGER, issueDate - 100000],
                ] as Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ],
            [
                earlyRepaymentButtonStates.noCeo,
                [
                    [false, false, false, currentDate, false, Role.MANAGER, issueDate],
                    [false, false, false, currentDate, false, Role.MANAGER, issueDate],
                    [false, false, false, currentDate, true, Role.MANAGER, issueDate],
                ] as Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ],
            [
                earlyRepaymentButtonStates.hasOverdue,
                [
                    [false, false, true, currentDate, true, Role.MANAGER, issueDate],
                    [false, false, true, currentDate, true, Role.MANAGER, issueDate],
                ] as Array<Parameters<typeof earlyRepaymentButtonStateSelector.resultFunc>>,
            ],
        ];

        verificationData.forEach(([result, argsCollection]) => {
            argsCollection.forEach((args) => {
                it(`should return "${result}" for args:
                    isKIBCategoryCodeSelector = ${args[0]},
                    isFetchingEarlyRepaymentButtonStateSelector = ${args[1]},
                    isCeoSelector = ${args[2]},
                    currentTimeSelector = ${args[3]}
                    hasOverdueSelector = ${args[4]},
                    currentRoleSelector = ${args[5]},
                    issueDateSelector= ${args[6]}
                `, () => {
                    expect(earlyRepaymentButtonStateSelector.resultFunc(...args)).toBe(result);
                });
            });
        });
    });
});
