import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';

export const signedDocumentsSelector = (state: ApplicationState) => state.signedDocuments;

export const currentSignedDocumentsSelector = createSelector(
    signedDocumentsSelector,
    (signedDocuments) => signedDocuments.documents,
);

export const isSignedDocumentsFetchingSelector = createSelector(
    signedDocumentsSelector,
    (signedDocuments) => signedDocuments.isFetching,
);
