import { type TCreditProductDocumentV2 } from 'thrift-services/services/credit_products_v2';

import { SIGNED_DOCUMENTS_TYPES } from '#/src/ducks/signed-documents/types';
import { type SignedDocument } from '#/src/types/signed-document';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

export function checkSignedDocumentsStart(docs: TCreditProductDocumentV2[]) {
    return {
        type: SIGNED_DOCUMENTS_TYPES.CHECK_SIGNED_DOCUMENTS_START,
        docs,
    };
}

export function checkSignedDocumentsFinish(documents: SignedDocument[]) {
    return {
        type: SIGNED_DOCUMENTS_TYPES.CHECK_SIGNED_DOCUMENTS_FINISH,
        documents,
    };
}

export function checkSignedDocumentsError(error: ServerResponseError) {
    return {
        type: SIGNED_DOCUMENTS_TYPES.GET_DOCUMENTS_INFO_ERROR,
        error,
    };
}
