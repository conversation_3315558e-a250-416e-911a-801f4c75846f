import { type InferValueTypes } from 'corporate-blocking/common/actions-type';

import { type SignedDocument } from '#/src/types/signed-document';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import type * as actions from './actions';
import { SIGNED_DOCUMENTS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type SignedDocumentsState = {
    error: string | ServerResponseError | null;
    isFetching: boolean;
    documents: SignedDocument[];
};

export const initialState: SignedDocumentsState = {
    error: null,
    isFetching: false,
    documents: [],
};

export function signedDocumentsReducer(
    state = initialState,
    action: ActionTypes,
): SignedDocumentsState {
    switch (action.type) {
        case SIGNED_DOCUMENTS_TYPES.CHECK_SIGNED_DOCUMENTS_START:
            return {
                ...state,
                isFetching: true,
            };
        case SIGNED_DOCUMENTS_TYPES.CHECK_SIGNED_DOCUMENTS_FINISH:
            return {
                ...state,
                documents: action.documents,
                isFetching: false,
            };
        case SIGNED_DOCUMENTS_TYPES.GET_DOCUMENTS_INFO_ERROR:
            return {
                ...state,
                error: action.error,
                isFetching: false,
            };
        default:
            return state;
    }
}
