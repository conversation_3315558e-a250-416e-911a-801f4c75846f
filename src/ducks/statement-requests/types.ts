import { type StatementRequest } from 'corp-loan-statements-api-typescript-services';

import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

export const STATEMENT_REQUESTS_TYPES = {
    GET_STATEMENTS_REQUESTS_START: 'GET_STATEMENTS_REQUESTS_START',
    GET_STATEMENTS_REQUESTS_FINISH: 'GET_STATEMENTS_REQUESTS_FINISH',
    GET_STATEMENTS_REQUESTS_ERROR: 'GET_STATEMENTS_REQUESTS_ERROR',
    RESET_STATEMENTS_REQUESTS_STATE: 'RESET_STATEMENTS_REQUESTS_STATE',
    DOWNLOAD_STATEMENT_REQUEST_START: 'DOWNLOAD_STATEMENT_REQUEST_START',
    DOWNLOAD_STATEMENT_REQUEST_FINISH: 'DOWNLOAD_STATEMENT_REQUEST_FINISH',
    DOWNLOAD_STATEMENT_REQUEST_ERROR: 'DOWNLOAD_STATEMENT_REQUEST_ERROR',
    UPDATE_STATEMENT_REQUESTS_VIEW_START: 'UPDATE_STATEMENT_REQUESTS_VIEW_START',
    UPDATE_STATEMENT_REQUESTS_VIEW_FINISH: 'UPDATE_STATEMENT_REQUESTS_VIEW_FINISH',
    UPDATE_STATEMENT_REQUESTS_VIEW_ERROR: 'UPDATE_STATEMENT_REQUESTS_VIEW_ERROR',
    CREATE_STATEMENT_REQUEST_START: 'CREATE_STATEMENT_REQUEST_START',
    CREATE_STATEMENT_REQUEST_FINISH: 'CREATE_STATEMENT_REQUEST_FINISH',
    CREATE_STATEMENT_REQUEST_ERROR: 'CREATE_STATEMENT_REQUEST_ERROR',
} as const;

export type TStatementRequest = StatementRequest;
export type TStatementRequestError = ServerResponseError;
