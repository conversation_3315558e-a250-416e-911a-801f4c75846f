import { createSelector } from 'reselect';

import { type ApplicationState } from '../application-state';

export const statementRequestsSelector = (state: ApplicationState) => state.statementRequests;

export const statementRequestsListSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.list || [],
);

export const pagesCountStatementRequestsSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.pagesCount || 0,
);

export const isStatementRequestsFetchingSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.isFetching || false,
);

export const statementRequestsErrorSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.error || '',
);

export const newStatementsCountSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.newStatementsCount || 0,
);

export const createStatementRequestStatusSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.createStatementRequestStatus,
);

export const isFetchingCreateStatementRequestSelector = createSelector(
    createStatementRequestStatusSelector,
    ({ isFetching }) => isFetching,
);

export const isErrorCreateFetchingRequestSelector = createSelector(
    createStatementRequestStatusSelector,
    ({ error }) => error !== null,
);

export const downloadStatementRequestStatusSelector = createSelector(
    statementRequestsSelector,
    (statementRequests) => statementRequests.downloadStatementRequestStatus,
);

export const isFetchingDownloadStatementRequestSelector = createSelector(
    downloadStatementRequestStatusSelector,
    ({ isFetching }) => isFetching,
);

export const isErrorDownloadStatementRequestSelector = createSelector(
    downloadStatementRequestStatusSelector,
    ({ error }) => error !== null,
);
