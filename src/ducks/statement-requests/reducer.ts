import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { STATEMENT_REQUESTS_TYPES, type TStatementRequest } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type StatementRequestsState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    list: TStatementRequest[];
    pagesCount: number;
    newStatementsCount: number;
    createStatementRequestStatus: {
        isFetching: boolean;
        error: string | EErrorMessages | null;
    };
    downloadStatementRequestStatus: {
        isFetching: boolean;
        error: string | EErrorMessages | null;
    };
};

export const initialState: StatementRequestsState = {
    error: null,
    isFetching: false,
    list: [],
    pagesCount: 0,
    newStatementsCount: 0,
    createStatementRequestStatus: {
        isFetching: false,
        error: null,
    },
    downloadStatementRequestStatus: {
        isFetching: false,
        error: null,
    },
};

export function statementRequestsReducer(state = initialState, action: ActionTypes) {
    switch (action.type) {
        case STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_START:
            return {
                ...state,
                error: null,
                isFetching: true,
            };
        case STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_FINISH:
            return {
                ...state,
                newStatementsCount: action.newStatementsCount,
            };
        case STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH:
            return {
                ...state,
                isFetching: false,
                list: action.list,
                pagesCount: action.pagesCount,
                newStatementsCount: action.newStatementsCount,
            };
        case STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_ERROR:
            return {
                ...state,
                isFetching: false,
                error: action.error.message,
            };
        case STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_START:
            return {
                ...state,
                createStatementRequestStatus: {
                    isFetching: true,
                    error: null,
                },
            };
        case STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_FINISH:
            return {
                ...state,
                createStatementRequestStatus: {
                    isFetching: false,
                    error: null,
                },
            };
        case STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_ERROR:
            return {
                ...state,
                createStatementRequestStatus: {
                    isFetching: false,
                    error: action.error.message,
                },
            };
        case STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_START:
            return {
                ...state,
                downloadStatementRequestStatus: {
                    isFetching: true,
                    error: null,
                },
            };
        case STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_FINISH:
            return {
                ...state,
                list: action.list,
                downloadStatementRequestStatus: {
                    isFetching: false,
                    error: null,
                },
            };
        case STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_ERROR:
            return {
                ...state,
                downloadStatementRequestStatus: {
                    isFetching: false,
                    error: action.error.message,
                },
            };
        case STATEMENT_REQUESTS_TYPES.RESET_STATEMENTS_REQUESTS_STATE:
            return initialState;
        default:
            return state;
    }
}
