import { type StatementFormat } from 'corp-loan-statements-api-typescript-services';

import { type LOG_LEVEL } from '#/src/types/logger';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import { STATEMENT_REQUESTS_TYPES, type TStatementRequest } from './types';

export const getStatementRequestsStart = ({
    pageNumber,
    pageSize,
    docNumber,
}: {
    pageNumber: number;
    pageSize: number;
    docNumber: string;
}) => ({
    type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_START,
    pageNumber,
    pageSize,
    docNumber,
});

export const getStatementRequestsFinish = ({
    list,
    pagesCount,
    newStatementsCount,
}: {
    list: TStatementRequest[];
    pagesCount: number;
    newStatementsCount: number;
}) => ({
    type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH,
    list,
    pagesCount,
    newStatementsCount,
});

export const getStatementRequestsError = (error: ServerResponseError, logLevel: LOG_LEVEL) => ({
    type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_ERROR,
    error,
    logLevel,
});

export const resetStatementRequestsState = () => ({
    type: STATEMENT_REQUESTS_TYPES.RESET_STATEMENTS_REQUESTS_STATE,
});

export const downloadStatementRequestStart = ({
    fileId,
    fromDate,
    toDate,
    docNumber,
}: {
    fileId: string;
    fromDate: string;
    toDate: string;
    docNumber: string;
}) => ({
    type: STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_START,
    fileId,
    fromDate,
    toDate,
    docNumber,
});

export const downloadStatementRequestError = (error: ServerResponseError, logLevel: LOG_LEVEL) => ({
    type: STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_ERROR,
    error,
    logLevel,
});

export const downloadStatementRequestFinish = ({ list }: { list: TStatementRequest[] }) => ({
    type: STATEMENT_REQUESTS_TYPES.DOWNLOAD_STATEMENT_REQUEST_FINISH,
    list,
});

export const updateStatementRequestsViewStart = ({
    list,
    docNumber,
}: {
    list?: TStatementRequest[];
    docNumber: string;
}) => ({
    type: STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_START,
    list,
    docNumber,
});

export const updateStatementRequestsViewFinish = ({
    newStatementsCount,
}: {
    newStatementsCount: number;
}) => ({
    type: STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_FINISH,
    newStatementsCount,
});

export const updateStatementRequestsViewError = (
    error: ServerResponseError,
    logLevel: LOG_LEVEL,
) => ({
    type: STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_ERROR,
    error,
    logLevel,
});

export function createStatementRequestStart({
    docNumber,
    fromDate,
    toDate,
    format,
    selectedTranches = [],
    withSignature,
    organizationId,
}: {
    docNumber: string;
    fromDate: string;
    toDate: string;
    format: StatementFormat;
    selectedTranches?: string[];
    withSignature?: boolean;
    organizationId?: string;
}) {
    return {
        type: STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_START,
        docNumber,
        fromDate,
        toDate,
        format,
        selectedTranches,
        withSignature,
        organizationId,
    };
}

export function createStatementRequestFinish() {
    return {
        type: STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_FINISH,
    };
}

export function createStatementRequestError(error: Error, logLevel: LOG_LEVEL) {
    return {
        type: STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_ERROR,
        error,
        logLevel,
    };
}
