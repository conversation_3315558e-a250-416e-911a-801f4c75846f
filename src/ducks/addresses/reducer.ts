import { type ApiGetAddressesV2Response } from 'corp-customers-api-typescript-services/dist/api-types.d';

import { type DeepReadonly } from 'arui-private/types';

export type AddressesState = DeepReadonly<ApiGetAddressesV2Response>;

export const addressInitialState: ApiGetAddressesV2Response = [
    {
        customerId: '',
        address: {
            fullAddress: '',
            country: {
                title: '',
                charCode: '',
                numericCode: '',
                titleInRussian: '',
            },

            region: {
                code: '',
                fullName: '',
                shortName: '',
            },
            district: null,
            city: null,
            streetInfo: {
                name: '',
                fullTypeName: '',
                shortTypeName: '',
            },
            house: '',
            building: null,
            flat: null,
            locality: {
                name: '',
                fullTypeName: '',
                shortTypeName: '',
            },
            postcode: '',
        },
        swiftAddress: {
            name: '',
            localAddress: '',
            cityAndCountry: '',
        },
    },
];

export function addressesReducer(state = addressInitialState) {
    return state;
}
