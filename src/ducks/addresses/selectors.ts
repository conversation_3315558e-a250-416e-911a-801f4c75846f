import { createSelector } from 'reselect';

import { type ApplicationState } from '../application-state';
import { currentOrganizationEqIdSelector } from '../organization/selectors';

export const addressesSelector = (state: ApplicationState) => state.addresses;

export const organizationAddressSelector = createSelector(
    currentOrganizationEqIdSelector,
    addressesSelector,
    (organizationId, addresses) =>
        addresses.find((address) => address.customerId === organizationId),
);
