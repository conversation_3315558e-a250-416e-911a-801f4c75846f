import { CREDIT_PURPOSE } from '#/src/constants/credit-calculator';
import { type SliderValue } from '#/src/types/core-components';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { CREDIT_CALCULATOR_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditCalculatorState = {
    creditSum: SliderValue;
    creditPurpose: CREDIT_PURPOSE;
    isActive: boolean;
};

const initialState: CreditCalculatorState = {
    creditSum: 0,
    creditPurpose: CREDIT_PURPOSE.ALL_VARIANTS,
    isActive: false,
};

export function creditCalculatorReducer(
    state = initialState,
    action: ActionTypes,
): CreditCalculatorState {
    switch (action.type) {
        case CREDIT_CALCULATOR_TYPES.SET_CREDIT_SUM:
            return {
                ...state,
                creditSum: action.payload,
            };
        case CREDIT_CALCULATOR_TYPES.SET_CREDIT_PURPOSE:
            return {
                ...state,
                creditPurpose: action.payload,
            };
        case CREDIT_CALCULATOR_TYPES.ACTIVE_CREDIT_CALCULATOR:
            return {
                ...state,
                isActive: action.payload,
            };
        default:
            return state;
    }
}
