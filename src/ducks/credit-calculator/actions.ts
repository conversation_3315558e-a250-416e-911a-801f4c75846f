import { type CREDIT_PURPOSE } from '#/src/constants/credit-calculator';
import { type SliderValue } from '#/src/types/core-components';

import { CREDIT_CALCULATOR_TYPES } from './types';

export function setCreditSum(value: SliderValue) {
    return {
        type: CREDIT_CALCULATOR_TYPES.SET_CREDIT_SUM,
        payload: value,
    };
}

export function setCreditPurpose(creditPurpose: CREDIT_PURPOSE) {
    return {
        type: CREDIT_CALCULATOR_TYPES.SET_CREDIT_PURPOSE,
        payload: creditPurpose,
    };
}

export function activeCreditCalculator(isActive: boolean) {
    return {
        type: CREDIT_CALCULATOR_TYPES.ACTIVE_CREDIT_CALCULATOR,
        payload: isActive,
    };
}
