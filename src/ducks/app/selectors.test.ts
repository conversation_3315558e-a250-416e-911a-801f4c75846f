import fp from 'lodash/fp';

import { type DeepPartial } from 'arui-private/types';

import { EErrorMessages } from '../../utils/errors/error-messages';
import { type AccountsState } from '../accounts/reducer';
import { type ApplicationState } from '../application-state';
import { type CreditDocumentCirculationState } from '../credit-document-circulation/reducer';
import { type CreditOffersState } from '../credit-offers/reducer';
import { type CreditProcessingState } from '../credit-processing/reducer';
import { type CreditProductsState } from '../credit-products/reducer';
import { creditProductMainMenuInitialState } from '../credit-products-main-menu/reducer';
import { type MksPermissionsState } from '../mks-permissions/reducer';
import { type OrganizationsState } from '../organization/reducer';
import { type SettingsState } from '../settings/reducer';

import { isAppErrorSelector, isAppFetchingSelector } from './selectors';

describe('app selectors', () => {
    const TEST_SETTINGS_STATE: Partial<SettingsState> = {};

    const TEST_USER_RIGHTS_STATE: Partial<MksPermissionsState> = {
        isFetching: true,
        isError: false,
        userClient: {},
    };

    const TEST_CREDIT_PRODUCTS_STATE: Partial<CreditProductsState> = {
        isFetchingCreditProducts: true,
        creditProductsError: EErrorMessages.SERVER_RESPONSE_ERROR,
    };

    const TEST_CREDIT_OFFERS_STATE: Partial<CreditOffersState> = {
        isFetching: false,
        availableList: [],
        defaultOffers: [],
        preApproved: [],
        rawCreditOffers: [],
        error: EErrorMessages.SERVER_RESPONSE_ERROR,
    };

    const TEST_CREDIT_PROCESSING_STATE: Partial<CreditProcessingState> = { isFetching: false };
    const TEST_CREDIT_DOCUMENT_CIRCULATION_STATE: Partial<CreditDocumentCirculationState> = {
        error: EErrorMessages.SERVER_RESPONSE_ERROR,
    };
    const TEST_ORGANIZATIONS_STATE: DeepPartial<OrganizationsState> = {
        current: '1',
        list: [{ eqId: '1' }],
        isFetchingOrganizationIdToken: false,
        wrappedInTokenOrganizationId: 'string',
        isWrappingOrganizationIdError: false,
    };

    const TEST_STATE: Partial<ApplicationState> = {
        creditProductsMainMenu: creditProductMainMenuInitialState,
        creditProducts: TEST_CREDIT_PRODUCTS_STATE as CreditProductsState,
        creditOffers: TEST_CREDIT_OFFERS_STATE as CreditOffersState,
        creditProcessing: TEST_CREDIT_PROCESSING_STATE as CreditProcessingState,
        creditDocumentCirculation:
            TEST_CREDIT_DOCUMENT_CIRCULATION_STATE as CreditDocumentCirculationState,
        accounts: { isFetching: true } as AccountsState,
        organization: TEST_ORGANIZATIONS_STATE as OrganizationsState,
        mksPermissions: TEST_USER_RIGHTS_STATE as MksPermissionsState,
        settings: TEST_SETTINGS_STATE as SettingsState,
    };

    describe('isAppFetchingSelector', () => {
        it('should return true if offers or applications are fetching', () => {
            const actual = isAppFetchingSelector(TEST_STATE as ApplicationState);

            expect(actual).toEqual(true);
        });
    });

    describe('isAppErrorSelector', () => {
        it('should return true if offers and credit document circulation requests returned error', () => {
            const actual = isAppErrorSelector(TEST_STATE as ApplicationState);

            expect(actual).toEqual(true);
        });

        it('should return true if there are no credit offers in state and credit products request returned error', () => {
            const modifiedTestState = fp.set(
                'creditOffers.error',
                EErrorMessages.NO_PRODUCTS,
                TEST_STATE,
            ) as any;
            const actual = isAppFetchingSelector(modifiedTestState as ApplicationState);

            expect(actual).toEqual(true);
        });
    });
});
