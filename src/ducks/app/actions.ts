import { type ECreditOffers, ECreditOffersCampaignCode } from '../../constants/credit-offers';

import { APP_TYPES } from './types';

export function goToHome() {
    return { type: APP_TYPES.GO_TO_HOME };
}

export function goToCreditOffers() {
    return { type: APP_TYPES.GO_TO_CREDIT_OFFERS };
}

export function goToPaymentsSchedulePage({
    docNumber,
    trancheNumber,
    path,
}: {
    docNumber: string;
    trancheNumber?: string;
    path: string;
}) {
    return { type: APP_TYPES.GO_TO_PAYMENTS_SCHEDULE, trancheNumber, docNumber, path };
}

export function goToAttachDocumentsPage() {
    return { type: APP_TYPES.GO_TO_ASSIGN_DOCUMENTS_PAGE };
}

export function goToDashboardAccount({
    accountNumber,
    organizationId,
}: {
    accountNumber: string;
    organizationId?: string;
}) {
    return { type: APP_TYPES.GO_TO_DASHBOARD_ACCOUNT, accountNumber, organizationId };
}

export function goToCreditFormsApp({
    offerType,
    organizationId,
    id,
}: {
    offerType?: ECreditOffers;
    organizationId?: string;
    id?: string;
}) {
    return {
        type: APP_TYPES.GO_TO_CREDIT_FORMS_APP,
        offerType,
        organizationId,
        id,
    };
}

export function goToDashboard({ organizationId }: { organizationId?: string }) {
    return { type: APP_TYPES.GO_TO_DASHBOARD, organizationId };
}

export function goLanding({
    landingType,
    campaignCode = ECreditOffersCampaignCode.DEFAULT,
    organizationId,
}: {
    landingType: ECreditOffers;
    campaignCode?: ECreditOffersCampaignCode;
    organizationId?: string;
}) {
    return {
        type: APP_TYPES.GO_LANDING,
        landingType,
        campaignCode,
        organizationId,
    };
}

export function initExternalRedirect({
    link,
    withOrganizationId = false,
    addContextRoot = true,
    organizationId,
    parameters = {},
}: {
    link: string;
    withOrganizationId?: boolean;
    addContextRoot?: boolean;
    organizationId?: string;
    parameters?: { [k: string]: string | undefined };
}) {
    return {
        type: APP_TYPES.INIT_EXTERNAL_REDIRECT,
        link,
        withOrganizationId,
        addContextRoot,
        organizationId,
        parameters,
    };
}

export function goToOverformAgreement({ organizationId }: { organizationId?: string }) {
    return { type: APP_TYPES.GO_TO_OVERFORM_AGREEMENT, organizationId };
}

export function goCreditTrancheApp({
    organizationId,
    docNumber,
}: {
    organizationId?: string;
    docNumber: string;
}) {
    return {
        type: APP_TYPES.GO_TRANCHE_APP,
        docNumber,
        organizationId,
    };
}

export function goToBusinessCreditCard({ organizationId }: { organizationId?: string }) {
    return { type: APP_TYPES.GO_TO_BUSINESS_CREDIT_CARD, organizationId };
}

export function unexpectedAppError() {
    return {
        type: APP_TYPES.UNEXPECTED_APP_ERROR,
    };
}
