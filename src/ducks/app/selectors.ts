import { blockingSelectors } from 'corporate-blocking';
import { createSelector } from 'reselect';

import { EErrorMessages } from '../../utils/errors/error-messages';
import { type ApplicationState } from '../application-state';
import {
    creditDocumentCirculationErrorSelector,
    isProductInfoFetchingSelector,
} from '../credit-document-circulation/selectors';
import { creditOffersErrorSelector, isCreditOffersExistSelector } from '../credit-offers/selectors';
import { creditProductsErrorSelector } from '../credit-products/selectors/common-credit-products.selectors';
import { isCategoryMainMenuFetchingSelector } from '../credit-products-main-menu/selectors';
import { isMksPermissionsFetchingSelector } from '../mks-permissions/selectors';

export const overdraftApplicationCreate = 'OVERDRAFT_APPLICATION_CREATE';

export const { blockedRightsSelector } = blockingSelectors;

export const isAppFetchingSelector = createSelector(
    isMksPermissionsFetchingSelector,
    isProductInfoFetchingSelector,
    isCategoryMainMenuFetchingSelector,
    (...args) => args.some(Boolean),
);

export const isTotalProductsRequestErrorSelector = createSelector(
    creditProductsErrorSelector,
    creditOffersErrorSelector,
    creditDocumentCirculationErrorSelector,
    (creditProductsError, creditOffersError, creditDocumentCirculationError) =>
        creditProductsError === creditOffersError &&
        creditOffersError === creditDocumentCirculationError &&
        creditOffersError === EErrorMessages.SERVER_RESPONSE_ERROR,
);

export const isAppErrorSelector = createSelector(
    isTotalProductsRequestErrorSelector,
    isCreditOffersExistSelector,
    creditProductsErrorSelector,
    (isTotalProductsRequestError, isCreditOffersExist, creditProductsError) =>
        isTotalProductsRequestError ||
        (!isCreditOffersExist && creditProductsError === EErrorMessages.SERVER_RESPONSE_ERROR),
);

export const isCommonCheckBlockedOverdraftSelector = createSelector(
    blockedRightsSelector,
    (blockedRights) => blockedRights && blockedRights.includes(overdraftApplicationCreate),
);

export const appErrorSelector = (state: ApplicationState) => state.app.error;
export const blockingsSelector = (state: ApplicationState) => state.blockings;
