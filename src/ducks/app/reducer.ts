import { type DeepReadonly, type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { APP_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type AppState = DeepReadonly<{
    error: boolean;
}>;

export const initialState: AppState = {
    error: false,
};

export function appReducer(state: AppState = initialState, action: ActionTypes): AppState {
    switch (action.type) {
        case APP_TYPES.UNEXPECTED_APP_ERROR:
            return {
                ...state,
                error: true,
            };

        default:
            return state;
    }
}
