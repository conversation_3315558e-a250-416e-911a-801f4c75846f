import { type TAlfaCreditDocument } from 'thrift-services/services/credit_document_circulation';

export const CREDIT_DOCUMENT_CIRCULATION_TYPES = {
    GET_PRODUCT_INFO_START: 'GET_PRODUCT_INFO_START',
    GET_PRODUCT_INFO_FINISH: 'GET_PRODUCT_INFO_FINISH',
    GET_PRODUCT_INFO_ERROR: 'GET_PRODUCT_INFO_ERROR',
    GET_ALFA_CREDIT_STATUS_START: 'GET_ALFA_CREDIT_STATUS_START',
    GET_ALFA_CREDIT_STATUS_FINISH: 'GET_ALFA_CREDIT_STATUS_FINISH',
    GET_ALFA_CREDIT_STATUS_ERROR: 'GET_ALFA_CREDIT_STATUS_ERROR',
    GET_DOCUMENT_LIST_START: 'GET_DOCUMENT_LIST_START',
    GET_DOCUMENT_LIST_FINISH: 'GET_DOCUMENT_LIST_FINISH',
    GET_DOCUMENT_LIST_ERROR: 'GET_DOCUMENT_LIST_ERROR',
} as const;

export type TDocument = TAlfaCreditDocument;

export type TDocumentList = {
    [k: string]: TDocument[];
};
