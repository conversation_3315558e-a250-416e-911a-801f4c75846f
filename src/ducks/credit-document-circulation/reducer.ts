import { type TAlfaCreditStatus } from 'thrift-services/services/credit_document_circulation';

import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';
import type * as mainMenuActions from '../credit-products-main-menu/actions';

import type * as actions from './actions';
import { CREDIT_DOCUMENT_CIRCULATION_TYPES, type TDocumentList } from './types';

type ActionTypes =
    | ReturnType<InferValueTypes<typeof actions>>
    | ReturnType<InferValueTypes<typeof mainMenuActions>>;

export type CreditDocumentCirculationState = {
    error: string | EErrorMessages | null;
    isFetching: boolean;
    alfaCreditStatus: TAlfaCreditStatus | null;
    isDocumentListFetching: boolean;
    isAllDocumentList: boolean;
    documentList: TDocumentList;
};

export const initialState: CreditDocumentCirculationState = {
    error: null,
    isFetching: false,
    alfaCreditStatus: null,
    isDocumentListFetching: false,
    isAllDocumentList: false,
    documentList: {},
};

export function creditDocumentCirculationReducer(state = initialState, action: ActionTypes) {
    switch (action.type) {
        case CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_START:
            return {
                ...state,
                isDocumentListFetching: true,
            };
        case CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_FINISH:
            return {
                ...state,
                isDocumentListFetching: false,
                documentList: action.documentList,
                isAllDocumentList: action.isAllDocumentList,
            };
        case CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_ERROR:
            return {
                ...state,
                error: action.error.message,
                isDocumentListFetching: false,
                isAllDocumentList: true,
            };
        default:
            return state;
    }
}
