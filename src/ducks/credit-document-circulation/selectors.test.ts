import setIn from 'lodash/fp/set';

import { EErrorMessages } from '../../utils/errors/error-messages';
import { type ApplicationState } from '../application-state';

import { type CreditDocumentCirculationState } from './reducer';
import { creditDocumentCirculationErrorSelector } from './selectors';

describe('credit document circulation selectors tests', () => {
    const TEST_CREDIT_DOCUMENT_CIRCULATION_STATE: Partial<CreditDocumentCirculationState> = {
        error: EErrorMessages.SERVER_RESPONSE_ERROR,
    };

    const TEST_STATE: Partial<ApplicationState> = {
        creditDocumentCirculation:
            TEST_CREDIT_DOCUMENT_CIRCULATION_STATE as CreditDocumentCirculationState,
    };

    describe('creditDocumentCirculationErrorSelector', () => {
        it('should return null if there is no error', () => {
            const modifiedTestState = setIn(
                'creditDocumentCirculation.error',
                null,
                TEST_STATE,
            ) as any;

            const actual = creditDocumentCirculationErrorSelector(modifiedTestState);

            expect(actual).toEqual(null);
        });

        it('should return error message if server response error exists', () => {
            const actual = creditDocumentCirculationErrorSelector(TEST_STATE as ApplicationState);

            expect(actual).toEqual(EErrorMessages.SERVER_RESPONSE_ERROR);
        });
    });
});
