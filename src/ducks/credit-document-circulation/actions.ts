import { type TAlfaCreditDocFilter } from 'thrift-services/services/credit_document_circulation';

import { type EDocTypes } from '#/src/constants/credit-document-circulation';

import { type LOG_LEVEL } from '../../types/logger';
import { type CreditDocumentCirculationErrors } from '../../utils/errors';

import { CREDIT_DOCUMENT_CIRCULATION_TYPES, type TDocumentList } from './types';

export function getDocumentListStart(
    tabName: string,
    docType: EDocTypes,
    pageFilter: {
        pageNumber: number;
        perPage: number;
    },
    filter?: TAlfaCreditDocFilter,
) {
    return {
        type: CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_START,
        tabName,
        docType,
        pageFilter,
        filter,
    };
}

export function getDocumentListFinish(documentList: TDocumentList, isAllDocumentList = false) {
    return {
        type: CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_FINISH,
        documentList,
        isAllDocumentList,
    };
}

export function getDocumentListError(error: CreditDocumentCirculationErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_DOCUMENT_LIST_ERROR,
        error,
        logLevel,
    };
}
