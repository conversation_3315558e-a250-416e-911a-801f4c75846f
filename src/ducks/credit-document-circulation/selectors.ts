import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

export const isProductInfoFetchingSelector = (state: ApplicationState) =>
    state.creditDocumentCirculation.isFetching;

export const isDocumentListFetchingSelector = (state: ApplicationState) =>
    state.creditDocumentCirculation.isDocumentListFetching;

export const isAllDocumentListSelector = (state: ApplicationState) =>
    state.creditDocumentCirculation.isAllDocumentList;

export const documentListSelector = (state: ApplicationState) =>
    state.creditDocumentCirculation?.documentList || {};

export const creditDocumentCirculationErrorSelector = (state: ApplicationState) =>
    state.creditDocumentCirculation.error;

export const isCreditDocumentCirculationAccessErrorSelector = createSelector(
    creditDocumentCirculationErrorSelector,
    (creditDocumentCirculationError) =>
        !!creditDocumentCirculationError &&
        creditDocumentCirculationError === EErrorMessages.ACCESS_ERROR,
);
