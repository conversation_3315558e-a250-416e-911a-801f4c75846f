import { type ApplicationState } from '#/src/ducks/application-state';
import { DocumentTypes } from '#/src/types/documents';

import { getDocumentStatusSelector } from './selectors';

describe('documents selectors test', () => {
    const stateMock = {
        documents: {
            participant1: {
                [DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS]: 'approved',
            },
            participant2: {
                [DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS]: 'rejected',
            },
        },
    };

    it('should return the document status for the specified participant and document type', () => {
        const participantCode = 'participant1';
        const docType = DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS;

        const selector = getDocumentStatusSelector(participantCode, docType);
        const result = selector(stateMock as unknown as ApplicationState);

        expect(result).toBe('approved');
    });

    it('should return undefined if the participant is not present in the state', () => {
        const participantCode = 'participant3';
        const docType = DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS;

        const selector = getDocumentStatusSelector(participantCode, docType);
        const result = selector(stateMock as unknown as ApplicationState);

        expect(result).toBeUndefined();
    });

    it('should work correctly if the documents state is undefined', () => {
        const participantCode = 'participant1';
        const docType = DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS;

        const selector = getDocumentStatusSelector(participantCode, docType);
        const result = selector({ documents: undefined } as unknown as ApplicationState);

        expect(result).toBeUndefined();
    });

    it('should work correctly if the state is completely empty', () => {
        const participantCode = 'participant1';
        const docType = DocumentTypes.REQUIREMENT_CREDIT_HOLIDAYS;

        const selector = getDocumentStatusSelector(participantCode, docType);
        const result = selector({} as unknown as ApplicationState);

        expect(result).toBeUndefined();
    });
});
