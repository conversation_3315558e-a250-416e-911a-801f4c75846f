import { type DocumentTypes } from '#/src/types/documents';
import { type LOG_LEVEL } from '#/src/types/logger';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import { DOCUMENTS_TYPES } from './types';

export function getDocumentStart(
    documentType: DocumentTypes,
    documentName: string,
    participantCode: string,
) {
    return {
        type: DOCUMENTS_TYPES.GET_DOCUMENT_START,
        documentType,
        documentName,
        participantCode,
    };
}

export function getDocumentFinish(documentType: DocumentTypes, participantCode: string) {
    return {
        type: DOCUMENTS_TYPES.GET_DOCUMENT_FINISH,
        documentType,
        participantCode,
    };
}

export function getDocumentError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: DOCUMENTS_TYPES.GET_DOCUMENT_ERROR,
        error,
        logLevel,
    };
}
