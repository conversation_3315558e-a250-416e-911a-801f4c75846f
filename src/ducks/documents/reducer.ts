import { type InferValueTypes } from 'corporate-blocking/common/actions-type';

import { DocumentDownloadStatuses, type DocumentTypes } from '#/src/types/documents';

import type * as actions from './actions';
import { DOCUMENTS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type DocumentsState = Record<string, Record<DocumentTypes, DocumentDownloadStatuses>>;

export const initialState: DocumentsState = {};

export function documentsReducer(state = initialState, action: ActionTypes): DocumentsState {
    switch (action.type) {
        case DOCUMENTS_TYPES.GET_DOCUMENT_START:
            return {
                ...state,
                [action.participantCode]: {
                    ...state?.[action.participantCode],
                    [action.documentType]: DocumentDownloadStatuses.IN_PROGRESS,
                },
            };
        case DOCUMENTS_TYPES.GET_DOCUMENT_FINISH:
            return {
                ...state,
                [action.participantCode]: {
                    ...state?.[action.participantCode],
                },
            };
        default:
            return state;
    }
}
