import { type CreditOffer } from 'thrift-services/services/credit_offers';

import { type LOG_LEVEL } from '../../types/logger';
import {
    type TMappedCreditOffer,
    type TMappedCreditOffers,
} from '../../utils/credit-offers-mappers';
import { type CreditOffersErrors } from '../../utils/errors';

import { CREDIT_OFFERS_TYPES } from './types';

export function getCreditOffersStart(organizationId?: string) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_START,
        organizationId,
    };
}

export function getCreditOffersFinish(
    mappedCreditOffers: TMappedCreditOffers,
    rawCreditOffers: CreditOffer[] = [],
) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_FINISH,
        creditOffers: mappedCreditOffers.offers,
        preApproved: mappedCreditOffers.preApproved,
        rawCreditOffers,
    };
}

export function getDefaultCreditOffersFinish(defaultOffers: TMappedCreditOffer[]) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_DEFAULT_OFFERS_FINISH,
        defaultOffers,
    };
}

export function getDigitalSalesCreditOffersFinish(isEmpty: boolean) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_DIGITAL_SALES_CREDIT_OFFERS_FINISH,
        isEmpty,
    };
}

export function getDigitalSalesCreditSliderFinish(isEmpty: boolean) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_DIGITAL_SALES_CREDIT_SLIDER_FINISH,
        isEmpty,
    };
}

export function resetCreditOffersState() {
    return {
        type: CREDIT_OFFERS_TYPES.RESET_CREDIT_OFFERS_STATE,
    };
}

export function getCreditOffersError(error: CreditOffersErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_ERROR,
        error,
        logLevel,
    };
}
