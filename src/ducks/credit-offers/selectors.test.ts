import setIn from 'lodash/fp/set';

import { ECreditOffers } from '../../constants/credit-offers';
import { type TMappedCreditOffer } from '../../utils/credit-offers-mappers';
import { type ApplicationState } from '../application-state';

import { type CreditOffersState } from './reducer';
import { expressOverdraftIdSelector, expressOverdraftOfferSelector } from './selectors';

describe('credit offers selector', () => {
    const expressOverdraft = {
        type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
        titlesVariants: {
            short: 'Овердрафт',
            long: 'Овердрафт',
        },
        isOverformAvailable: true,
        text: '',
        maximumTerm: 1,
        maximumAmount: {
            amount: 2,
            currency: {
                code: 1,
                mnemonicCode: 'string',
                minorUnits: 2,
                unicodeSymbol: 'string',
                fullName: 'string',
            },
        },
        maximumLimit: {
            amount: 2,
            currency: {
                code: 1,
                mnemonicCode: 'string',
                minorUnits: 2,
                unicodeSymbol: 'string',
                fullName: 'string',
            },
        },
        creditCardVariant: null,
        landingVersion: 1,
        yearRatePct: 12,
        oneTimeComissionPct: null,
        expiryDate: {
            seconds: 22,
        },
        visibleForUser: true,
        id: 1,
    } as unknown as TMappedCreditOffer;

    const creditOffersState: Partial<CreditOffersState> = {
        preApproved: [expressOverdraft],
        isFetching: false,
    };

    const testState: Partial<ApplicationState> = {
        creditOffers: creditOffersState as CreditOffersState,
    };

    it('should return express-overdraft-offer', () => {
        const result = expressOverdraftOfferSelector(testState as ApplicationState);

        expect(result).toEqual(expressOverdraft);
    });

    it('should not return express-overdraft-offer if it is not visible for user', () => {
        const modifiedExpressOverdraft = {
            ...expressOverdraft,
            visibleForUser: false,
        };
        const modifiedState = setIn(
            'creditOffers.preApproved',
            [modifiedExpressOverdraft],
            testState,
        );
        const result = expressOverdraftOfferSelector(modifiedState as ApplicationState);

        expect(result).toEqual(undefined);
    });

    it('should not return express-overdraft-offer', () => {
        const modifiedState = setIn('creditOffers.preApproved', [], testState);
        const result = expressOverdraftOfferSelector(modifiedState as ApplicationState);

        expect(result).toEqual(undefined);
    });

    it('should return id of express-overdraft-offer', () => {
        const result = expressOverdraftIdSelector(testState as ApplicationState);

        expect(result).toEqual(1);
    });
});
