import { type CreditOffer } from 'thrift-services/services/credit_offers';

import { type TMappedCreditOffer } from '../../utils/credit-offers-mappers';
import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { CREDIT_OFFERS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditOffersState = {
    availableList: TMappedCreditOffer[];
    preApproved: TMappedCreditOffer[];
    defaultOffers: TMappedCreditOffer[];
    rawCreditOffers: CreditOffer[];
    isFetching: boolean;
    isReceived: boolean;
    isDigitalSalesCreditOffersEmpty: boolean;
    isDigitalSalesCreditOffersReceived: boolean;
    isDigitalSalesCreditSliderEmpty: boolean;
    isDigitalSalesCreditSliderReceived: boolean;
    error: string | EErrorMessages | null;
};

export const initialState: CreditOffersState = {
    availableList: [],
    preApproved: [],
    defaultOffers: [],
    rawCreditOffers: [],
    isFetching: false,
    isReceived: false,
    isDigitalSalesCreditOffersEmpty: true,
    isDigitalSalesCreditOffersReceived: false,
    isDigitalSalesCreditSliderEmpty: true,
    isDigitalSalesCreditSliderReceived: false,
    error: null,
};

export function creditOffersReducer(state = initialState, action: ActionTypes): CreditOffersState {
    switch (action.type) {
        case CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_START:
            return {
                ...state,
                availableList: [],
                preApproved: [],
                rawCreditOffers: [],
                isFetching: true,
            };
        case CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_FINISH: {
            return {
                ...state,
                availableList: action.creditOffers,
                preApproved: action.preApproved,
                rawCreditOffers: action.rawCreditOffers,
                isFetching: false,
                isReceived: true,
                error: null,
            };
        }
        case CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_ERROR:
            return {
                ...state,
                availableList: [],
                preApproved: [],
                rawCreditOffers: [],
                isFetching: false,
                isReceived: true,
                error: action.error.message,
            };
        case CREDIT_OFFERS_TYPES.GET_DEFAULT_OFFERS_FINISH:
            return {
                ...state,
                defaultOffers: action.defaultOffers,
            };
        case CREDIT_OFFERS_TYPES.GET_DIGITAL_SALES_CREDIT_OFFERS_FINISH:
            return {
                ...state,
                isDigitalSalesCreditOffersEmpty: action.isEmpty,
                isDigitalSalesCreditOffersReceived: true,
            };
        case CREDIT_OFFERS_TYPES.GET_DIGITAL_SALES_CREDIT_SLIDER_FINISH:
            return {
                ...state,
                isDigitalSalesCreditSliderEmpty: action.isEmpty,
                isDigitalSalesCreditSliderReceived: true,
            };
        case CREDIT_OFFERS_TYPES.RESET_CREDIT_OFFERS_STATE:
            return initialState;
        default:
            return state;
    }
}
