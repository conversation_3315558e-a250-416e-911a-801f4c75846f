import { createSelector } from 'reselect';

import { CREDIT_PURPOSE, CREDIT_PURPOSE_CONTENT } from '#/src/constants/credit-calculator';
import {
    ECreditOffers,
    type ECreditOffersCampaignCode,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { type ApplicationState } from '#/src/ducks/application-state';
import { creditPurposeSelector, creditSumSelector } from '#/src/ducks/credit-calculator/selectors';
import { getIsCreditSumInsideCreditAmount } from '#/src/utils/credit-calculator';
import { combineOffers } from '#/src/utils/credit-offers-helpers';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

export const creditOffersErrorSelector = (state: ApplicationState) => state.creditOffers.error;
export const preApprovedCreditOffersSelector = (state: ApplicationState) =>
    state.creditOffers.preApproved;
export const defaultCreditOffersSelector = (state: ApplicationState) =>
    state.creditOffers.defaultOffers;
export const isCreditOffersFetchingSelector = (state: ApplicationState) =>
    state.creditOffers.isFetching;

export const isDigitalSalesCreditOffersEmptySelector = (state: ApplicationState) =>
    state.creditOffers.isDigitalSalesCreditOffersEmpty;
export const isDigitalSalesCreditOffersReceivedSelector = (state: ApplicationState) =>
    state.creditOffers.isDigitalSalesCreditOffersReceived;
export const isDigitalSalesCreditSliderEmptySelector = (state: ApplicationState) =>
    state.creditOffers.isDigitalSalesCreditSliderEmpty;
export const isDigitalSalesCreditSliderReceivedSelector = (state: ApplicationState) =>
    state.creditOffers.isDigitalSalesCreditSliderReceived;

export const isGetCreditOffersAccessErrorSelector = createSelector(
    creditOffersErrorSelector,
    (creditOffersError) => !!creditOffersError && creditOffersError === EErrorMessages.ACCESS_ERROR,
);

export const creditOffersAvailableListSelector = (state: ApplicationState) =>
    state.creditOffers.availableList;

export const mappedCreditOffersSelector = createSelector(
    preApprovedCreditOffersSelector,
    defaultCreditOffersSelector,
    creditOffersAvailableListSelector,

    (preApproved = [], defaultOffers = [], availableList = []) => {
        // Note: Безусловные Предодобы идут выше
        const sortedByTotalOfferDesc = preApproved.sort((a, _b) => {
            const isTotalOffer = TotalOffersCampaignCode.includes(
                a.campaignCode as ECreditOffersCampaignCode,
            );

            return isTotalOffer ? -1 : 1;
        });

        // Note: если есть preApproved с таким же типом, то обычный удаляем
        const availableListWithoutPreApprovedDoubles = availableList.filter(
            (offer) =>
                !sortedByTotalOfferDesc.some(
                    (preApprovedOffer) => preApprovedOffer?.type === offer?.type,
                ),
        );

        return [
            ...sortedByTotalOfferDesc,
            ...defaultOffers,
            ...availableListWithoutPreApprovedDoubles,
        ];
    },
);

export const mainPageCreditOffersSelector = createSelector(
    mappedCreditOffersSelector,
    (mappedCreditOffers) =>
        mappedCreditOffers.filter(
            (mappedOffer) =>
                mappedOffer &&
                mappedOffer?.visibleForUser &&
                mappedOffer.type !== ECreditOffers.BUSINESS_FACTORING,
        ),
);

export const creditOffersWithLoanPurposeSelector = createSelector(
    mainPageCreditOffersSelector,
    (mainPageCreditOffers) =>
        mainPageCreditOffers.filter((offer) => (offer.loanPurpose?.length || 0) > 0),
);

export const creditOffersWithoutLoanPurposeSelector = createSelector(
    mainPageCreditOffersSelector,
    (mainPageCreditOffers) =>
        mainPageCreditOffers.filter((offer) => (offer.loanPurpose?.length || 0) === 0),
);

export const minimalMinimumAmountSelector = createSelector(
    creditOffersWithLoanPurposeSelector,
    (creditOffers) => {
        const allMinimumAmounts = creditOffers.map(
            (creditOffer) => creditOffer.minimumAmount?.amount || 0,
        );

        return allMinimumAmounts.length ? Math.min(...allMinimumAmounts) : 0;
    },
);

export const maximalMaximumAmountSelector = createSelector(
    creditOffersWithLoanPurposeSelector,
    (creditOffers) => {
        const allMaximumAmounts = creditOffers.map(
            (creditOffer) => creditOffer.maximumAmount?.amount || 0,
        );

        return allMaximumAmounts.length ? Math.max(...allMaximumAmounts) : 0;
    },
);

const filteredByCreditSumCreditOffersSelector = createSelector(
    creditOffersWithLoanPurposeSelector,
    creditSumSelector,
    (creditOffers, creditSum) =>
        creditOffers.filter(
            (creditOffer) =>
                creditSum === '' || getIsCreditSumInsideCreditAmount(creditOffer, creditSum),
        ),
);

const filteredByPurposeCreditOffersSelector = createSelector(
    creditOffersWithLoanPurposeSelector,
    creditPurposeSelector,
    (creditOffers, creditPurpose) =>
        creditPurpose === CREDIT_PURPOSE.ALL_VARIANTS
            ? creditOffers
            : creditOffers.filter((creditOffer) =>
                  creditOffer.loanPurpose?.includes(creditPurpose),
              ),
);

export const creditPurposesSelector = createSelector(
    creditOffersWithLoanPurposeSelector,
    minimalMinimumAmountSelector,
    maximalMaximumAmountSelector,
    (creditOffers, allMinAmount, allMaxAmount) => {
        const creditPurposes = [
            {
                key: CREDIT_PURPOSE.ALL_VARIANTS,
                content: CREDIT_PURPOSE_CONTENT[CREDIT_PURPOSE.ALL_VARIANTS],
                minAmount: allMinAmount,
                maxAmount: allMaxAmount,
            },
        ];

        creditOffers.forEach((creditOffer) => {
            const creditOfferMinAmount = creditOffer.minimumAmount?.amount || 0;
            const creditOfferMaxAmount = creditOffer.maximumAmount?.amount || 0;

            creditOffer.loanPurpose?.forEach((creditPurpose: string) => {
                const creditPurposeIndex = creditPurposes.findIndex(
                    (creditPurposeItem) => creditPurposeItem.key === creditPurpose,
                );

                if (creditPurposeIndex > -1) {
                    const creditPurposeMinAmount =
                        creditPurposes[creditPurposeIndex].minAmount || 0;
                    const creditPurposeMaxAmount =
                        creditPurposes[creditPurposeIndex].maxAmount || 0;

                    creditPurposes[creditPurposeIndex].minAmount =
                        creditPurposeMinAmount < creditOfferMinAmount
                            ? creditPurposeMinAmount
                            : creditOfferMinAmount;
                    creditPurposes[creditPurposeIndex].maxAmount =
                        creditPurposeMaxAmount > creditOfferMaxAmount
                            ? creditPurposeMaxAmount
                            : creditOfferMaxAmount;
                } else {
                    creditPurposes.push({
                        key: creditPurpose as CREDIT_PURPOSE,
                        content: CREDIT_PURPOSE_CONTENT[creditPurpose as CREDIT_PURPOSE],
                        minAmount: creditOfferMinAmount,
                        maxAmount: creditOfferMaxAmount,
                    });
                }
            });
        });

        return creditPurposes;
    },
);

export const activeCreditPurposeSelector = createSelector(
    creditPurposesSelector,
    creditPurposeSelector,
    (creditPurposes, creditPurpose) =>
        creditPurposes.filter((creditPurposeItem) => creditPurposeItem.key === creditPurpose)[0],
);

export const filteredByCalculatorCreditOffersSelector = createSelector(
    filteredByPurposeCreditOffersSelector,
    filteredByCreditSumCreditOffersSelector,
    (filteredByPurpose, filteredByCreditSum) =>
        filteredByPurpose.filter((offerByPurpose) =>
            filteredByCreditSum.some(
                (offerByCreditSum) => offerByCreditSum.type === offerByPurpose.type,
            ),
        ),
);

export const expressOverdraftOfferSelector = createSelector(
    preApprovedCreditOffersSelector,
    (preapprovedCreditOffers) =>
        preapprovedCreditOffers &&
        preapprovedCreditOffers.find(
            (offer) =>
                offer?.type === ECreditOffers.EXPRESS_OVERDRAFT_OFFER && offer.visibleForUser,
        ),
);

export const leasingOfferSelector = createSelector(
    preApprovedCreditOffersSelector,
    (preapprovedCreditOffers) =>
        preapprovedCreditOffers &&
        preapprovedCreditOffers.find(
            (offer) => offer?.type === ECreditOffers.LEASING && offer.visibleForUser,
        ),
);

export const businessCreditCardSelector = createSelector(
    preApprovedCreditOffersSelector,
    (preapprovedCreditOffers) =>
        preapprovedCreditOffers &&
        preapprovedCreditOffers.find(
            (offer) => offer?.type === ECreditOffers.BUSINESS_CREDIT_CARD && offer.visibleForUser,
        ),
);

export const allCreditOffersSelector = createSelector(
    mainPageCreditOffersSelector,
    (mainPageCreditOffers) =>
        combineOffers(mainPageCreditOffers, [
            {
                mainOffer: ECreditOffers.BUSINESS_CREDIT,
                additionalOffer: ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE,
            },
            {
                mainOffer: ECreditOffers.REFILLABLE_CREDIT_LINE,
                additionalOffer: ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE,
            },
        ]),
);

export const isCreditOffersExistSelector = createSelector(
    allCreditOffersSelector,
    expressOverdraftOfferSelector,
    businessCreditCardSelector,
    leasingOfferSelector,
    (creditOffers, expressOverdraftOffer, businessCreditCardOffer, leasingOffer) =>
        !!creditOffers.length ||
        !!expressOverdraftOffer ||
        !!businessCreditCardOffer ||
        !!leasingOffer,
);

export const expressOverdraftIdSelector = createSelector(
    expressOverdraftOfferSelector,
    (expressOverdraftOffer) => expressOverdraftOffer && expressOverdraftOffer.id,
);

export const businessCreditCardIdSelector = createSelector(
    businessCreditCardSelector,
    (businessCreditCardOffer) => businessCreditCardOffer && businessCreditCardOffer.id,
);
