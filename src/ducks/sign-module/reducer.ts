import { type InferValueTypes } from 'corporate-blocking/common/actions-type';

import type * as actions from './actions';
import { SIGN_MODULE_TYPES } from './types';

export type SignModuleState = {
    isSignModuleLoaded: boolean;
    signModuleFunction: (() => Promise<any>) | null;
    error: boolean;
};

export const signModuleInitialState: SignModuleState = {
    isSignModuleLoaded: false,
    signModuleFunction: null,
    error: false,
};
type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function signModuleReducer(state = signModuleInitialState, action: ActionTypes) {
    switch (action.type) {
        case SIGN_MODULE_TYPES.SIGN_MODULE_LOAD_START:
            return {
                ...state,
            };
        case SIGN_MODULE_TYPES.SIGN_MODULE_LOAD_FINISH:
            return {
                ...state,
                isSignModuleLoaded: true,
                signModuleFunction: action.signModuleFunction,
            };
        case SIGN_MODULE_TYPES.SIGN_MODULE_LOAD_ERROR:
            return {
                ...state,
                isSignModuleLoaded: false,
                error: action.error,
            };
    }

    return state;
}
