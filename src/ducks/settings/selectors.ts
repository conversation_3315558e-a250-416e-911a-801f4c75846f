import { createSelector } from 'reselect';

import { getMoscowDate, getMoscowTimeZone } from '#/src/utils/date';

import { type ApplicationState } from '../application-state';

export const settingsSelector = (state: ApplicationState) => state.settings;

export const supportPhoneForMoscowAndAbroadSelector = (state: ApplicationState) =>
    settingsSelector(state).supportPhoneForMoscowAndAbroad;

export const supportPhoneForAnyCityInRussiaSelector = (state: ApplicationState) =>
    settingsSelector(state).supportPhoneForAnyCityInRussia;

export const contextRootSelector = (state: ApplicationState) =>
    settingsSelector(state)?.contextRoot || '';

export const deadlineForAcceptingApplicationsMoscowTimeSelector = createSelector(
    settingsSelector,
    ({ deadlineForAcceptingApplicationsMoscowTime }) => {
        if (!deadlineForAcceptingApplicationsMoscowTime) return null;

        const [hours, minutes] = deadlineForAcceptingApplicationsMoscowTime.split(':').map(Number);

        return { hours, minutes };
    },
);

export const externalRedirectSelector = (state: ApplicationState) =>
    settingsSelector(state).externalRedirect;

export const externalRedirectAlfaCreditRequestBankSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).alfaCreditRequestBank;

export const externalRedirectAlfaCreditRequestCreditSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).alfaCreditRequestCredit;

export const externalRedirectAlfaCreditRequestDocumentSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).alfaCreditRequestDocument;

export const externalRedirectAlfaCreditRequestGuaranteeSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).alfaCreditRequestGuarantee;

export const externalRedirectAlfaCreditHostSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).alfaCreditHost;

export const externalRedirectCreditSBSelector = (state: ApplicationState) =>
    externalRedirectSelector(state).creditsb;

export const externalRedirectRublePayment = (state: ApplicationState) =>
    externalRedirectSelector(state).rublePayment;

export const currentTimeSelector = createSelector(settingsSelector, ({ currentTime }) => {
    if (currentTime) {
        return getMoscowTimeZone(new Date(currentTime));
    }

    return getMoscowTimeZone(getMoscowDate(new Date()));
});

export const isTimeAfterDeadlineErrorSelector = (state: ApplicationState) =>
    settingsSelector(state).timeAfterDeadlineError;

export const digitalSalesLandingsSelector = (state: ApplicationState) =>
    settingsSelector(state).digitalSalesLandings;
