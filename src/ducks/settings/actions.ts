import { type NavigationPaneSettings } from './reducer';
import { SETTINGS_TYPES } from './types';

export function changeNavigationPaneSettings(settings: NavigationPaneSettings) {
    const { label, linkText, isVisible, onClick } = settings;

    return {
        type: SETTINGS_TYPES.CHANGE_NAVIGATION_PANE_SETTINGS,
        label,
        linkText,
        isVisible,
        onClick,
    };
}

export function resetNavigationPaneSettings() {
    return {
        type: SETTINGS_TYPES.RESET_NAVIGATION_PANE_SETTINGS,
    };
}

export function setCurrentTime(time: string) {
    return {
        type: SETTINGS_TYPES.SET_CURRENT_TIME,
        time,
    };
}

export function setTimeAfterDeadlineError(error: boolean) {
    return {
        type: SETTINGS_TYPES.SET_TIME_AFTER_DEADLINE_ERROR,
        error,
    };
}
