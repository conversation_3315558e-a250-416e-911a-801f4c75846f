import { type DeepReadonly, type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { SETTINGS_TYPES } from './types';

type Actions = ReturnType<InferValueTypes<typeof actions>>;

export type NavigationPaneSettings = DeepReadonly<{
    label: string;
    linkText: string;
    onClick?(...args: any[]): void;
    isVisible: boolean;
}>;

export type Redirect = DeepReadonly<{
    dashboard: string;
    paylist: string;
    rpay: string;
    tariffs: string;
    overdrafts: string;
    credits: string;
    landings: string;
    overformAgreementContextRoot: string;
    creditTrancheApp: string;
    creditFormsApp: string;
}>;

export type ExternalRedirect = DeepReadonly<{
    alfaCreditRequestGuarantee: string;
    alfaCreditRequestDocument: string;
    alfaCreditRequestCredit: string;
    alfaCreditRequestBank: string;
    alfaCreditDocumentQuery: string;
    alfaCreditHost: string;
    creditsb: string;
    rublePayment: string;
}>;

export type SettingsState = DeepReadonly<{
    pageTitle: string;
    alfaMetricsId: string;
    tranchesPerPage: number;
    projectName: string;
    authPage: string;
    contextRoot: string;
    devAccess: boolean;
    version: string;
    traceId?: string;
    navigationPaneSettings: NavigationPaneSettings;
    externalRedirect: ExternalRedirect;
    redirect: Redirect;
    digitalSalesLandings: Record<string, string>;
    supportPhoneForMoscowAndAbroad: string;
    supportPhoneForAnyCityInRussia: string;
    /** HH:mm */
    deadlineForAcceptingApplicationsMoscowTime: string;
    timeAfterDeadlineError: boolean;
    currentTime: string;
    error: string;
    signSource: string;
    signMethod: string;
    gatewayHost: string;
}>;

export const initialState: SettingsState = {
    pageTitle: '',
    alfaMetricsId: process.env.PROJECT_NAME ?? 'corp-credit-products',
    tranchesPerPage: 10,
    projectName: process.env.PROJECT_NAME ?? 'corp-credit-products',
    authPage: process.env.AUTH_PAGE ?? 'https://testlink.alfabank.ru/webclient/pages',
    contextRoot: process.env.NODE_ENV === 'production' ? (process.env.CONTEXT_ROOT ?? '') : '',
    devAccess: false,
    version: '',
    traceId: undefined,
    navigationPaneSettings: {
        label: '',
        linkText: '',
        onClick: undefined,
        isVisible: false,
    },
    externalRedirect: {
        alfaCreditRequestGuarantee: '',
        alfaCreditRequestDocument: '',
        alfaCreditRequestCredit: '',
        alfaCreditRequestBank: '',
        alfaCreditDocumentQuery: '',
        alfaCreditHost: '',
        creditsb: '',
        rublePayment: '',
    },
    redirect: {
        dashboard: '/dashboard',
        paylist: '/paylist',
        rpay: '/payment',
        tariffs: '/tariffs',
        overdrafts: '/overdrafts',
        landings: '/credit-products-landings',
        credits: '/credits',
        overformAgreementContextRoot: '/credit-overform',
        creditTrancheApp: '/credit-tranche',
        creditFormsApp: '/credit-forms',
    },
    digitalSalesLandings: {},
    supportPhoneForMoscowAndAbroad: '',
    supportPhoneForAnyCityInRussia: '',
    deadlineForAcceptingApplicationsMoscowTime: '',
    timeAfterDeadlineError: false,
    currentTime: '',
    error: '',
    signSource: '',
    signMethod: '',
    gatewayHost: '',
};

export function settingsReducer(
    state: SettingsState = initialState,
    action: Actions,
): SettingsState {
    switch (action.type) {
        case SETTINGS_TYPES.CHANGE_NAVIGATION_PANE_SETTINGS:
            return {
                ...state,
                navigationPaneSettings: {
                    label: action.label,
                    linkText: action.linkText,
                    onClick: action.onClick,
                    isVisible: action.isVisible,
                },
            };
        case SETTINGS_TYPES.RESET_NAVIGATION_PANE_SETTINGS:
            return {
                ...state,
                navigationPaneSettings: initialState.navigationPaneSettings,
            };
        case SETTINGS_TYPES.SET_CURRENT_TIME:
            return {
                ...state,
                currentTime: action.time,
            };
        case SETTINGS_TYPES.SET_TIME_AFTER_DEADLINE_ERROR:
            return {
                ...state,
                timeAfterDeadlineError: action.error,
            };

        default:
            return state;
    }
}
