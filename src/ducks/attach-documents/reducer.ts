import {
    type TDeliveryInfo,
    type TInfoForClientDocumentsResponse,
} from 'thrift-services/services/credit_application_processing';
import { type UnixEpoch } from 'thrift-services/utils';

import type * as organizationActions from '#/src/ducks/organization/actions';
import {
    type DocumentInfo,
    type EClientDocumentsRequestStatuses,
} from '#/src/types/client-documents';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { ATTACH_DOCUMENTS_TYPES } from './types';

type ActionTypes =
    | ReturnType<InferValueTypes<typeof actions>>
    | ReturnType<InferValueTypes<typeof organizationActions>>;

export type AttachDocumentsState = {
    requestStatus: EClientDocumentsRequestStatuses | null;
    isFetchingCheckDocumentsNecessity: boolean;
    isFetchingInfoDocuments: boolean;
    isFetchingAcceptAttachedFiles: boolean;
    isError: boolean;
    stage: string;
    infoForClientDocuments: TInfoForClientDocumentsResponse;
    loanMBId: string;
    documents: Map<string, DocumentInfo>;
    productCode: string;
    preferredProduct: string;
    isTotalOffer: boolean;
    scoringExpiryDate: UnixEpoch | null;
    loanAmount: string;
    isOnlineSigningAvailable: boolean;
    productName: string;
    platformId: string;
    deliveryInfo: TDeliveryInfo | null;
};

// TODO: переименовать доменную сущность attach-documents или разделить на две из-за банеров
const initialState: AttachDocumentsState = {
    requestStatus: null,
    isFetchingCheckDocumentsNecessity: false,
    isFetchingInfoDocuments: false,
    isFetchingAcceptAttachedFiles: false,
    isError: false,
    stage: '',
    infoForClientDocuments: {},
    loanMBId: '',
    documents: new Map(),
    productCode: '',
    preferredProduct: '',
    isTotalOffer: false,
    scoringExpiryDate: null,
    loanAmount: '',
    isOnlineSigningAvailable: false,
    productName: '',
    platformId: '',
    deliveryInfo: null,
};

export function attachDocumentsReducer(
    state: AttachDocumentsState = initialState,
    action: ActionTypes,
): AttachDocumentsState {
    switch (action.type) {
        case ATTACH_DOCUMENTS_TYPES.RESET_ATTACH_DOCUMENTS_STATE:
            return {
                ...initialState,
            };
        case ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_START:
            return {
                ...state,
                isFetchingCheckDocumentsNecessity: true,
            };
        case ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_FINISH:
            return {
                ...state,
                isFetchingCheckDocumentsNecessity: false,
                stage: action.stage,
                loanMBId: action.loanMBId,
                productCode: action.productCode,
                preferredProduct: action.preferredProduct,
                isTotalOffer: action.isTotalOffer,
                scoringExpiryDate: action.scoringExpiryDate,
                loanAmount: action.loanAmount,
                isOnlineSigningAvailable: action.isOnlineSigningAvailable,
                productName: action.productName,
                platformId: action.platformId,
                deliveryInfo: action.deliveryInfo || null,
            };

        case ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_ERROR:
            return {
                ...state,
                isFetchingCheckDocumentsNecessity: false,
                stage: '',
                isError: true,
            };
        case ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_START:
            return {
                ...state,
                isFetchingInfoDocuments: true,
            };

        case ATTACH_DOCUMENTS_TYPES.SET_INFO_FOR_CLIENT_DOCUMENTS:
            return {
                ...state,
                infoForClientDocuments: action.infoForClientDocuments,
            };

        case ATTACH_DOCUMENTS_TYPES.SET_CLIENT_DOCUMENTS_REQUEST_STATUS:
            return {
                ...state,
                requestStatus: action.status,
            };

        case ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_FINISH:
            return {
                ...state,
                isFetchingInfoDocuments: false,
            };

        case ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_ERROR:
            return {
                ...state,
                infoForClientDocuments: {},
                isError: true,
                isFetchingInfoDocuments: false,
            };

        case ATTACH_DOCUMENTS_TYPES.INIT_UNSAFE_DOCUMENTS_IN_STORE:
            return {
                ...state,
                documents: action.documents.reduce(
                    (map, document) => map.set(document.id, document),
                    new Map(),
                ),
            };

        case ATTACH_DOCUMENTS_TYPES.UPDATE_UNSAFE_DOCUMENTS_IN_STORE:
            return {
                ...state,
                documents: action.documents.reduce(
                    (map, document) => map.set(document.id, document),
                    new Map(state.documents),
                ),
            };

        case ATTACH_DOCUMENTS_TYPES.REMOVE_UNSAFE_DOCUMENT_FROM_STORE: {
            const documents = new Map(state.documents);

            documents.delete(action.documentId);

            return {
                ...state,
                documents,
            };
        }

        case ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_START: {
            return {
                ...state,
                isFetchingAcceptAttachedFiles: true,
            };
        }

        case ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_FINISH: {
            return {
                ...state,
                isFetchingAcceptAttachedFiles: false,
            };
        }

        default:
            return state;
    }
}
