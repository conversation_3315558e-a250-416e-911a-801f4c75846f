import {
    type TActiveCreditCaseResponse,
    type TInfoForClientDocumentsResponse,
} from 'thrift-services/services/credit_application_processing';

import {
    type DocumentInfo,
    type DocumentInfoWithFile,
    type EClientDocumentsRequestStatuses,
} from '#/src/types/client-documents';
import { type LOG_LEVEL } from '#/src/types/logger';
import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import { ATTACH_DOCUMENTS_TYPES } from './types';

export function resetAttachDocumentsState() {
    return {
        type: ATTACH_DOCUMENTS_TYPES.RESET_ATTACH_DOCUMENTS_STATE,
    };
}

export function getActiveCreditCaseStart(organizationId?: string) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_START,
        organizationId,
    };
}

export function getActiveCreditCaseFinish({
    stage = '',
    loanMBId = '',
    productCode = '',
    preferredProduct = '',
    isTotalOffer = false,
    scoringExpiryDate = { seconds: 0 },
    loanAmount = '',
    isOnlineSigningAvailable = false,
    productName = '',
    platformId = '',
    deliveryInfo,
}: TActiveCreditCaseResponse) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_FINISH,
        stage,
        loanMBId,
        productCode,
        preferredProduct,
        isTotalOffer,
        scoringExpiryDate,
        loanAmount,
        isOnlineSigningAvailable,
        productName,
        platformId,
        deliveryInfo,
    };
}

export function getActiveCreditCaseError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_ERROR,
        error,
        logLevel,
    };
}

export function getInfoForClientDocumentsStart() {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_START,
    };
}

export function setInfoForClientDocuments(infoForClientDocuments: TInfoForClientDocumentsResponse) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.SET_INFO_FOR_CLIENT_DOCUMENTS,
        infoForClientDocuments,
    };
}

export function setClientDocumentsRequestStatus(status: EClientDocumentsRequestStatuses) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.SET_CLIENT_DOCUMENTS_REQUEST_STATUS,
        status,
    };
}

export function getInfoForClientDocumentsFinish() {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_FINISH,
    };
}

export function getInfoForClientDocumentsError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_ERROR,
        error,
        logLevel,
    };
}

export function sendUnsafeDocumentsStart(documents: DocumentInfoWithFile[]) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.SEND_UNSAFE_DOCUMENTS_START,
        documents,
    };
}

export function initUnsafeDocumentsInStore(documents: DocumentInfo[]) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.INIT_UNSAFE_DOCUMENTS_IN_STORE,
        documents,
    };
}

export function updateUnsafeDocumentsInStore(documents: DocumentInfo[]) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.UPDATE_UNSAFE_DOCUMENTS_IN_STORE,
        documents,
    };
}

export function acceptAttachedFilesStart() {
    return {
        type: ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_START,
    };
}

export function acceptAttachedFilesFinish() {
    return {
        type: ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_FINISH,
    };
}

export function acceptAttachedFilesError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_ERROR,
        error,
        logLevel,
    };
}

export function removeUnsafeDocumentFromStore(documentId: string) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.REMOVE_UNSAFE_DOCUMENT_FROM_STORE,
        documentId,
    };
}

export function getAttachedDocuments({
    organizationId,
    loanMBId,
    borrowerCode,
}: {
    organizationId: string;
    loanMBId: string;
    borrowerCode: string;
}) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_ATTACHED_DOCUMENTS_START,
        organizationId,
        loanMBId,
        borrowerCode,
    };
}

export function getAttachedDocumentsError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.GET_ATTACHED_DOCUMENTS_ERROR,
        error,
        logLevel,
    };
}

export function removeAttachedDocument(documentId: string) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.REMOVE_ATTACHED_DOCUMENT_START,
        documentId,
    };
}

export function removeAttachedDocumentError(error: ServerResponseError, logLevel: LOG_LEVEL) {
    return {
        type: ATTACH_DOCUMENTS_TYPES.REMOVE_ATTACHED_DOCUMENT_ERROR,
        error,
        logLevel,
    };
}
