import { createSelector } from 'reselect';

import { MAX_DOCUMENTS_SIZE } from '#/src/constants/client-documents';
import { type ApplicationState } from '#/src/ducks/application-state';
import { type AttachDocumentsState } from '#/src/ducks/attach-documents/reducer';
import { type ActiveCreditCaseStage } from '#/src/types/client-documents';
import {
    getActiveCreditInfo,
    getActiveCreditInfoFromSFA,
    getDeliveryInfoData,
    mapDocumentToFileUpload,
} from '#/src/utils/client-documents';

const attachDocumentsSelector = (state: ApplicationState) => state.attachDocuments;

export const documentsSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).documents;

export const stageActiveCreditCaseSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).stage;

export const isOnlineSigningAvailableSelector = createSelector(
    attachDocumentsSelector,
    (state) => state.isOnlineSigningAvailable,
);

export const scoringExpiryDateSelector = createSelector(
    attachDocumentsSelector,
    (state) => state.scoringExpiryDate,
);

export const loanAmountSelector = createSelector(
    attachDocumentsSelector,
    (state) => state.loanAmount,
);

export const platformIdSelector = createSelector(
    attachDocumentsSelector,
    (state) => state.platformId,
);

export const activeCreditCaseInfoSelector = createSelector(stageActiveCreditCaseSelector, (stage) =>
    getActiveCreditInfo(stage as ActiveCreditCaseStage),
);

export const activeCreditCaseFromSFASelector = createSelector(
    attachDocumentsSelector,
    (state: AttachDocumentsState) =>
        getActiveCreditInfoFromSFA(
            state.isOnlineSigningAvailable,
            state.stage,
            state.productName,
            state.loanAmount,
        ),
);

export const infoForClientDocumentsSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).infoForClientDocuments;

export const isInfoDocumentsFetchingSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).isFetchingInfoDocuments;

export const isAcceptAttachedFilesFetchingSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).isFetchingAcceptAttachedFiles;

export const clientDocumentsRequestStatusSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).requestStatus;

export const loanMBIdSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).loanMBId;

export const borrowerCodeSelector = (state: ApplicationState) =>
    infoForClientDocumentsSelector(state).borrowerCode;

export const documentsMappedSelector = createSelector(documentsSelector, (documents) =>
    mapDocumentToFileUpload(documents),
);

export const totalSizeAttachDocumentsSelector = createSelector(documentsSelector, (documents) =>
    Array.from(documents || []).reduce((totalSize, document) => totalSize + document[1].size, 0),
);

export const isOverflowAllowedSizeDocumentsSelector = createSelector(
    totalSizeAttachDocumentsSelector,
    (totalSize) => totalSize > MAX_DOCUMENTS_SIZE,
);

export const isSuccessAttachedAllDocumentsSelector = createSelector(
    documentsSelector,
    (documents) =>
        Array.from(documents).every((document) => document[1].uploadStatus === 'SUCCESS'),
);

export const productCodeSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).productCode;

export const preferredProductSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).preferredProduct;

export const isTotalOfferSelector = (state: ApplicationState) =>
    attachDocumentsSelector(state).isTotalOffer;

export const deliveryCaseInfoData = createSelector(attachDocumentsSelector, (attachDocuments) => {
    if (!attachDocuments.deliveryInfo) {
        return null;
    }

    return getDeliveryInfoData(attachDocuments.deliveryInfo);
});
