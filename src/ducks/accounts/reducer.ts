import { type Account } from 'corp-accounts-api-typescript-services';

import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { ACCOUNTS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type AccountsState = {
    availableList: Account[];
    filteredList: Account[];
    isFetching: boolean;
    error: string | EErrorMessages | null;
};

export const initialState: AccountsState = {
    availableList: [],
    filteredList: [],
    isFetching: false,
    error: null,
};

export function accountsReducer(state = initialState, action: ActionTypes): AccountsState {
    switch (action.type) {
        case ACCOUNTS_TYPES.GET_ACCOUNTS_START:
            return {
                ...state,
                availableList: [],
                isFetching: true,
            };
        case ACCOUNTS_TYPES.GET_ACCOUNTS_FINISH: {
            return {
                ...state,
                availableList: action.accounts,
                isFetching: false,

                error: null,
            };
        }
        case ACCOUNTS_TYPES.GET_ACCOUNTS_ERROR:
            return {
                ...state,
                availableList: [],
                isFetching: false,

                error: action.error.message,
            };
        case ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_START:
            return {
                ...state,
                filteredList: [],
                isFetching: true,
            };
        case ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_FINISH: {
            return {
                ...state,
                filteredList: action.accounts,
                isFetching: false,
                error: null,
            };
        }
        case ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_ERROR:
            return {
                ...state,
                filteredList: [],
                isFetching: false,
                error: action.error.message,
            };
    }

    return state;
}
