import { type Account } from 'corp-accounts-api-typescript-services';

import { type LOG_LEVEL } from '../../types/logger';
import { type AccountsErrors } from '../../utils/errors';

import { ACCOUNTS_TYPES } from './types';

export function getAccountsStart() {
    return {
        type: ACCOUNTS_TYPES.GET_ACCOUNTS_START,
    };
}

export function getAccountsFinish(accounts: Account[]) {
    return {
        type: ACCOUNTS_TYPES.GET_ACCOUNTS_FINISH,
        accounts,
    };
}

export function getAccountsError(error: AccountsErrors, logLevel: LOG_LEVEL) {
    return {
        type: ACCOUNTS_TYPES.GET_ACCOUNTS_ERROR,
        error,
        logLevel,
    };
}

export function getByAccountNumbersStart({
    accountNumbers,
    organizationId,
}: {
    accountNumbers: string[];
    organizationId?: string;
}) {
    return {
        accountNumbers,
        organizationId,
        type: ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_START,
    };
}

export function getByAccountNumbersFinish(accounts: Account[]) {
    return {
        type: ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_FINISH,
        accounts,
    };
}

export function getByAccountNumbersError(error: AccountsErrors, logLevel: LOG_LEVEL) {
    return {
        type: ACCOUNTS_TYPES.GET_BY_ACCOUNT_NUMBERS_ERROR,
        error,
        logLevel,
    };
}
