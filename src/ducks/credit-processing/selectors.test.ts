import { type ApplicationState } from '../application-state';

import { type CreditProcessingState } from './reducer';
import { isCreditApplicationsFetchingSelector } from './selectors';

describe('credit processing selectors', () => {
    const TEST_CREDIT_PROCESSING_STATE: Partial<CreditProcessingState> = {
        isFetching: true,
        isApplicationsRecieved: true,
    };
    const TEST_STATE: Partial<ApplicationState> = {
        creditProcessing: TEST_CREDIT_PROCESSING_STATE as CreditProcessingState,
    };

    it('isCreditApplicationsFetchingSelector should return true if credit applications are fetching', () => {
        const actual = isCreditApplicationsFetchingSelector(TEST_STATE as ApplicationState);

        expect(actual).toEqual(true);
    });
});
