import { type Currency } from 'corp-early-repayment-api-typescript-services';

export const CREDIT_PROCESSING_TYPES = {
    GET_CREDIT_APPLICATIONS_START: 'GET_CREDIT_APPLICATIONS_START',
    GET_CREDIT_APPLICATIONS_FINISH: 'GET_CREDIT_APPLICATIONS_FINISH',
    GET_CREDIT_APPLICATIONS_ERROR: 'GET_CREDIT_APPLICATIONS_ERROR',
    GET_EARLY_REPAYMENT_PROCESSING_TYPE_START: 'GET_EARLY_REPAYMENT_PROCESSING_TYPE_START',
    GET_EARLY_REPAYMENT_PROCESSING_TYPE_FINISH: 'GET_EARLY_REPAYMENT_PROCESSING_TYPE_FINISH',
    GET_EARLY_REPAYMENT_PROCESSING_TYPE_ERROR: 'GET_EARLY_REPAYMENT_PROCESSING_TYPE_ERROR',
    CREATE_EARLY_REPAYMENT_APPL_START: 'CREATE_EARLY_REPAYMENT_APPL_START',
    CREATE_EARLY_REPAYMENT_APPL_FINISH: 'CREATE_EARLY_REPAYMENT_APPL_FINISH',
    GET_ACTIVE_CREDIT_CASE_START: 'GET_ACTIVE_CREDIT_CASE_START',
    GET_ACTIVE_CREDIT_CASE_FINISH: 'GET_ACTIVE_CREDIT_CASE_FINISH',
    GET_ACTIVE_CREDIT_CASE_ERROR: 'GET_ACTIVE_CREDIT_CASE_ERROR',
    CLOSE_ACTIVE_CASE: 'CLOSE_ACTIVE_CASE',
    CLOSE_ACTIVE_CASE_ERROR: 'CLOSE_ACTIVE_CASE_ERROR',
    CLOSE_ACTIVE_CASE_SUCCESS: 'CLOSE_ACTIVE_CASE_SUCCESS',
    SET_CONTINUE_ACTIVE_CASE_MODAL_VISIBLE: 'SET_CONTINUE_ACTIVE_CASE_MODAL_VISIBLE',
    SET_CONTINUE_SFA_ACTIVE_CASE_MODAL_VISIBLE: 'SET_CONTINUE_SFA_ACTIVE_CASE_MODAL_VISIBLE',
    SET_TOTAL_OFFER_NEED_QES_MODAL_VISIBLE: 'SET_TOTAL_OFFER_NEED_QES_MODAL_VISIBLE',
    SET_CALLBACK_REQUEST_CREATED_MODAL_VISIBLE: 'SET_CALLBACK_REQUEST_CREATED_MODAL_VISIBLE',
    SET_CLICKED_OFFER: 'SET_CLICKED_OFFER',
    CREATE_CALLBACK_REQUEST: 'CREATE_CALLBACK_REQUEST',
    SET_CALLBACK_REQUEST_STATUS: 'SET_CALLBACK_REQUEST_STATUS',
    MOVE_CASE_TO_STAGE: 'MOVE_CASE_TO_STAGE',
    SET_MANAGER_WORKS_MODAL_VISIBLE: 'SET_MANAGER_WORKS_MODAL_VISIBLE',
} as const;

export enum CallbackRequestStatusTypes {
    CALLBACK_REQUEST_CREATED = 'CALLBACK_REQUEST_CREATED',
    CALLBACK_REQUEST_HAS_ALREADY_CREATED = 'CALLBACK_REQUEST_HAS_ALREADY_CREATED',
}

export enum StagesForCaseTranslation {
    SET_ONLINE_SIGNING_CHANNEL = 'setOnlineSigningChannel',
}

export type TApplicationData = {
    docNumber: string;
    selectedAccountNumber: string;
    paymentType: string;
    paymentDate: string;
    debtToPay: string;
    interestToPay: string;
    total: string;
    currency: Currency;
};

export enum DeliveryDocumentStatus {
    NONE = '',
    DRAFT = 'draft',
    IN_PROGRESS = 'In progress',
    WAITING_FOR_DOCUMENTS_SIGNING = 'waitingForDocumentsSigning',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled',
    AT_CLIENT = 'atClient',
}
