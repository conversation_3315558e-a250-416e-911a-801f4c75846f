import {
    type ApiGetEarlyRepaymentProcessingTypeQueryParams,
    type ApiGetEarlyRepaymentProcessingTypeResponse,
} from 'corp-early-repayment-api-typescript-services';

import { type EProductCodes } from '#/src/constants/credit-products';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

import { type LOG_LEVEL } from '../../types/logger';
import { type CreditProcessingErrors } from '../../utils/errors';

import {
    type CallbackRequestStatusTypes,
    CREDIT_PROCESSING_TYPES,
    StagesForCaseTranslation,
    type TApplicationData,
} from './types';

export function getEarlyRepaymentProcessingTypeStart({
    agreementNumber = '',
    accountNumber = '',
}: Partial<ApiGetEarlyRepaymentProcessingTypeQueryParams>) {
    return {
        type: CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_START,
        agreementNumber,
        accountNumber,
    };
}

export function getEarlyRepaymentProcessingTypeFinish({
    processingType,
    agreementNumber,
}: {
    processingType: ApiGetEarlyRepaymentProcessingTypeResponse;
    agreementNumber: string;
}) {
    return {
        type: CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_FINISH,
        processingType,
        agreementNumber,
    };
}

export function getEarlyRepaymentProcessingTypeError({
    error,
    logLevel,
    agreementNumber,
}: {
    error: CreditProcessingErrors;
    logLevel: LOG_LEVEL;
    agreementNumber: string;
}) {
    return {
        type: CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_ERROR,
        error,
        logLevel,
        agreementNumber,
    };
}

export function createEarlyRepaymentApplStart(applicationData: TApplicationData) {
    return {
        type: CREDIT_PROCESSING_TYPES.CREATE_EARLY_REPAYMENT_APPL_START,
        applicationData,
    };
}

export function createEarlyRepaymentApplFinish(applicationId: string) {
    return {
        type: CREDIT_PROCESSING_TYPES.CREATE_EARLY_REPAYMENT_APPL_FINISH,
        applicationId,
    };
}

export function closeActiveCase() {
    return {
        type: CREDIT_PROCESSING_TYPES.CLOSE_ACTIVE_CASE,
    };
}

export function closeActiveCaseSuccess() {
    return {
        type: CREDIT_PROCESSING_TYPES.CLOSE_ACTIVE_CASE_SUCCESS,
    };
}

export function closeActiveCaseError(error: CreditProcessingErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_PROCESSING_TYPES.CLOSE_ACTIVE_CASE_ERROR,
        error,
        logLevel,
    };
}

export function setContinueActiveCaseModalVisible(isModalVisible: boolean) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_CONTINUE_ACTIVE_CASE_MODAL_VISIBLE,
        isModalVisible,
    };
}

export function setContinueSFAActiveCaseModalVisible(isModalVisible: boolean) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_CONTINUE_SFA_ACTIVE_CASE_MODAL_VISIBLE,
        isModalVisible,
    };
}

export function setTotalOfferNeedQESModalVisible(isModalVisible: boolean) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_TOTAL_OFFER_NEED_QES_MODAL_VISIBLE,
        isModalVisible,
    };
}

export function setCallbackRequestCreatedModalVisible(isModalVisible: boolean) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_CALLBACK_REQUEST_CREATED_MODAL_VISIBLE,
        isModalVisible,
    };
}

export function setClickedOffer(offer: TMappedCreditOffer) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_CLICKED_OFFER,
        offer,
    };
}

export function createCallbackRequest({
    productCode,
    isTotalOffer,
}: {
    productCode: EProductCodes;
    isTotalOffer: boolean;
}) {
    return {
        type: CREDIT_PROCESSING_TYPES.CREATE_CALLBACK_REQUEST,
        productCode,
        isTotalOffer,
    };
}

export function setCallbackRequestStatus(callbackRequestStatus: CallbackRequestStatusTypes) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_CALLBACK_REQUEST_STATUS,
        callbackRequestStatus,
    };
}

export function moveCaseToStage({
    actionType,
    organizationId,
}: {
    actionType: StagesForCaseTranslation;
    organizationId?: string;
}) {
    return {
        type: CREDIT_PROCESSING_TYPES.MOVE_CASE_TO_STAGE,
        actionType,
        organizationId,
    };
}

export function setOnlineSigningChannel({ organizationId }: { organizationId?: string }) {
    return moveCaseToStage({
        actionType: StagesForCaseTranslation.SET_ONLINE_SIGNING_CHANNEL,
        organizationId,
    });
}

export function setMangerWorksWithAppModalVisible(isModalVisible: boolean) {
    return {
        type: CREDIT_PROCESSING_TYPES.SET_MANAGER_WORKS_MODAL_VISIBLE,
        isModalVisible,
    };
}
