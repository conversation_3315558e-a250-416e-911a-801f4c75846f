import { type ApiGetEarlyRepaymentProcessingTypeResponse } from 'corp-early-repayment-api-typescript-services';

import type * as organizationActions from '#/src/ducks/organization/actions';
import { ORGANIZATION_TYPES } from '#/src/ducks/organization/types';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

import { earlyRepaymentProcessingTypes } from '../../constants/credit-processing';
import { type DeepReadonly, type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { type CallbackRequestStatusTypes, CREDIT_PROCESSING_TYPES } from './types';

type ActionTypes =
    | ReturnType<InferValueTypes<typeof actions>>
    | ReturnType<InferValueTypes<typeof organizationActions>>;

export type CreditProcessingState = DeepReadonly<{
    isFetching: boolean;
    isError: boolean;
    isInitialized: boolean;
    isApplicationsRecieved: boolean;
    earlyRepaymentProcessingType: { [k: string]: ApiGetEarlyRepaymentProcessingTypeResponse };
    earlyRepaymentApplicationId: string;
    isContinueActiveCaseModalVisible: boolean;
    isContinueSFAActiveCaseModalVisible: boolean;
    isTotalOfferNeedQESModalVisible: boolean;
    isCallbackRequestCreatedModalVisible: boolean;
    isManagerWorksModalVisible: boolean;
    callbackRequestStatus: CallbackRequestStatusTypes | null;
    isClosedActiveCase: boolean;
    clickedOffer: TMappedCreditOffer | null;
}>;

const initialState: CreditProcessingState = {
    isFetching: false,
    isError: false,
    isInitialized: false,
    isApplicationsRecieved: false,
    earlyRepaymentProcessingType: {},
    earlyRepaymentApplicationId: '',
    isContinueActiveCaseModalVisible: false,
    isContinueSFAActiveCaseModalVisible: false,
    isTotalOfferNeedQESModalVisible: false,
    isCallbackRequestCreatedModalVisible: false,
    isManagerWorksModalVisible: false,
    callbackRequestStatus: null,
    isClosedActiveCase: false,
    clickedOffer: null,
};

export function creditProcessingReducer(
    state: CreditProcessingState = initialState,
    action: ActionTypes,
): CreditProcessingState {
    switch (action.type) {
        case CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_START:
            return {
                ...state,
                isFetching: true,
                earlyRepaymentProcessingType: {
                    ...state.earlyRepaymentProcessingType,
                    [action.agreementNumber]: '',
                },
            };

        case CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_FINISH:
            return {
                ...state,
                isFetching: false,
                earlyRepaymentProcessingType: {
                    ...state.earlyRepaymentProcessingType,
                    [action.agreementNumber]: action.processingType,
                },
            };

        case CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_ERROR:
            return {
                ...state,
                isFetching: false,
                isError: true,
                earlyRepaymentProcessingType: {
                    ...state.earlyRepaymentProcessingType,
                    [action.agreementNumber]: earlyRepaymentProcessingTypes.MANUAL,
                },
            };

        case CREDIT_PROCESSING_TYPES.CREATE_EARLY_REPAYMENT_APPL_START:
            return {
                ...state,
                earlyRepaymentApplicationId: '',
            };

        case CREDIT_PROCESSING_TYPES.CREATE_EARLY_REPAYMENT_APPL_FINISH:
            return {
                ...state,
                earlyRepaymentApplicationId: action.applicationId,
            };

        case ORGANIZATION_TYPES.CHANGE_ORGANIZATION:
            return initialState;

        case CREDIT_PROCESSING_TYPES.CLOSE_ACTIVE_CASE_SUCCESS: {
            return {
                ...state,
                isClosedActiveCase: true,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_CONTINUE_ACTIVE_CASE_MODAL_VISIBLE: {
            return {
                ...state,
                isContinueActiveCaseModalVisible: action.isModalVisible,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_CONTINUE_SFA_ACTIVE_CASE_MODAL_VISIBLE: {
            return {
                ...state,
                isContinueSFAActiveCaseModalVisible: action.isModalVisible,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_TOTAL_OFFER_NEED_QES_MODAL_VISIBLE: {
            return {
                ...state,
                isTotalOfferNeedQESModalVisible: action.isModalVisible,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_CALLBACK_REQUEST_CREATED_MODAL_VISIBLE: {
            return {
                ...state,
                isCallbackRequestCreatedModalVisible: action.isModalVisible,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_MANAGER_WORKS_MODAL_VISIBLE: {
            return {
                ...state,
                isManagerWorksModalVisible: action.isModalVisible,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_CALLBACK_REQUEST_STATUS: {
            return {
                ...state,
                callbackRequestStatus: action.callbackRequestStatus,
            };
        }

        case CREDIT_PROCESSING_TYPES.SET_CLICKED_OFFER: {
            return {
                ...state,
                clickedOffer: action.offer,
            };
        }

        default:
            return state;
    }
}
