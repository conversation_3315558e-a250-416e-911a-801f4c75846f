import { createSelector } from 'reselect';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { type ApplicationState } from '#/src/ducks/application-state';

export const creditProcessingSelector = (state: ApplicationState) => state.creditProcessing;

export const isCreditApplicationsFetchingSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isFetching,
);

export const earlyRepaymentProcessingTypeSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.earlyRepaymentProcessingType,
);

export const earlyRepaymentApplicationIdSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.earlyRepaymentApplicationId,
);

export const isAutomaticEarlyRepaymentProcessingTypeSelector = (agreementNumber: string) =>
    createSelector(
        earlyRepaymentProcessingTypeSelector,
        (earlyRepaymentProcessingType) =>
            earlyRepaymentProcessingType[agreementNumber] === earlyRepaymentProcessingTypes.AUTO,
    );

export const isEarlyRepaymentRequestAlreadyCreatedSelector = (agreementNumber: string) =>
    createSelector(
        earlyRepaymentProcessingTypeSelector,
        (earlyRepaymentProcessingType) =>
            earlyRepaymentProcessingType[agreementNumber] ===
            earlyRepaymentProcessingTypes.APP_EXISTS,
    );

export const isContinueSFAActiveCaseModalVisibleSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isContinueSFAActiveCaseModalVisible,
);

export const isClosedActiveCaseSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isClosedActiveCase,
);

export const getClickedOfferSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.clickedOffer,
);

export const isTotalOfferNeedQESModalVisibleSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isTotalOfferNeedQESModalVisible,
);

export const isCallbackRequestCreatedModalVisibleSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isCallbackRequestCreatedModalVisible,
);

export const callbackRequestStatusSelector = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.callbackRequestStatus,
);

export const isManagerWorksModalVisible = createSelector(
    creditProcessingSelector,
    (creditProcessing) => creditProcessing.isManagerWorksModalVisible,
);
