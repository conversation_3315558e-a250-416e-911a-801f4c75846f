import { OrganizationCategoryCodes } from '#/src/constants/organization-category-codes';

import { type ApplicationState } from '../../application-state';
import {
    categoryCodeHeaderSelector,
    categoryHeaderErrorSelector,
    createOrganizationFeatureSelector,
    currentHeaderOrganizationEqIdSelector,
    currentHeaderOrganizationFeaturesListSelector,
    currentHeaderOrganizationSelector,
    currentHeaderOrganizationShortNameSelector,
    hasOffersViewRightsForHeaderSelector,
    headerCompanySelector,
    headerOrganizationsListSelector,
    isCategoryHeaderFetchingSelector,
    isKIBCategoryHeaderSelector,
    isMmbCategoryHeaderSelector,
    isSBCategoryHeaderSelector,
} from '../selectors';

describe('CreditProductsHeader Selectors', () => {
    const mockState = {
        creditProductsHeader: {
            company: { key: '1', value: 'Company A' },
            category: {
                isFetching: false,
                error: null,
                category: {
                    sksCode: 'code-1',
                    sksName: 'Category A',
                },
            },
        },
        organization: {
            list: [
                {
                    eqId: '1',
                    organizationName: { shortName: 'Org A' },
                    features: { nibCreditShowcaseSlider: true, nibCreditOffers: false },
                    inn: '',
                    kppList: [],
                    ogrn: '',
                    ogrnDate: '',
                    registrationDateInRP: '',
                    type: '',
                    okpo: '',
                    okved: '',
                    contacts: {},
                    name: '',
                    rights: undefined,
                },
                {
                    eqId: '2',
                    organizationName: { shortName: 'Org B' },
                    features: { nibCreditShowcaseSlider: false, nibCreditOffers: true },
                    inn: '',
                    kppList: [],
                    ogrn: '',
                    ogrnDate: '',
                    registrationDateInRP: '',
                    type: '',
                    okpo: '',
                    okved: '',
                    contacts: {},
                    name: '',
                    rights: undefined,
                },
            ],
            current: '',
            isFetchingCategory: false,
            isFetchingOrganizationIdToken: false,
            isWrappingOrganizationIdError: false,
            wrappedInTokenOrganizationId: null,
            category: {
                sksCode: '',
            },
            managers: {
                error: null,
                isFetching: false,
                managers: null,
            },
        },
        router: {
            location: {
                query: {},
                pathname: '',
                search: '?customerId=123',
                state: undefined,
                hash: '',
            },
            action: 'PUSH',
        },
    } as unknown as ApplicationState;

    it('should select header company', () => {
        const result = headerCompanySelector(mockState);

        expect(result).toEqual(mockState.creditProductsHeader.company);
    });

    it('should check if category header is fetching', () => {
        const result = isCategoryHeaderFetchingSelector(mockState);

        expect(result).toBe(mockState.creditProductsHeader.category.isFetching);
    });

    it('should select category header error', () => {
        const result = categoryHeaderErrorSelector(mockState);

        expect(result).toEqual(mockState.creditProductsHeader.category.error);
    });

    it('should select category code header', () => {
        const result = categoryCodeHeaderSelector(mockState);

        expect(result).toEqual(mockState.creditProductsHeader.category.category.sksCode);
    });

    it('should check if the category is an MMB category', () => {
        const result = isMmbCategoryHeaderSelector({
            ...mockState,
            creditProductsHeader: {
                ...mockState.creditProductsHeader,
                category: {
                    ...mockState.creditProductsHeader.category,
                    category: {
                        sksCode: OrganizationCategoryCodes.ClientCategorySKS1,
                    },
                },
            },
        });

        expect(result).toBe(true);
    });

    it('should check if the category is an SB category', () => {
        const result = isSBCategoryHeaderSelector({
            ...mockState,
            creditProductsHeader: {
                ...mockState.creditProductsHeader,
                category: {
                    ...mockState.creditProductsHeader.category,
                    category: {
                        sksCode: OrganizationCategoryCodes.ClientCategorySKS8,
                    },
                },
            },
        });

        expect(result).toBe(true);
    });

    it('should check if the category is a KIB category', () => {
        const result = isKIBCategoryHeaderSelector({
            ...mockState,
            creditProductsHeader: {
                ...mockState.creditProductsHeader,
                category: {
                    ...mockState.creditProductsHeader.category,
                    category: {
                        sksCode: OrganizationCategoryCodes.ClientCategorySKS2,
                    },
                },
            },
        });

        expect(result).toBe(true);
    });

    it('should select organizations list', () => {
        const result = headerOrganizationsListSelector(mockState);

        expect(result).toEqual(mockState.organization.list);
    });

    it('should select current header organization', () => {
        const result = currentHeaderOrganizationSelector(mockState);

        expect(result).toEqual(mockState.organization.list[0]);
    });

    it('should select current header organization eqId from search params', () => {
        const result = currentHeaderOrganizationEqIdSelector(mockState);

        expect(result).toEqual('123');
    });

    it('should select current header organization short name', () => {
        const result = currentHeaderOrganizationShortNameSelector(mockState);

        expect(result).toEqual(mockState.organization.list[0].organizationName.shortName);
    });

    it('should select current header organization features list', () => {
        const result = currentHeaderOrganizationFeaturesListSelector(mockState);

        expect(result).toEqual(mockState.organization.list[0].features);
    });

    it('should create organization feature selector', () => {
        const nibCreditShowcaseSliderSelector =
            createOrganizationFeatureSelector('nibCreditShowcaseSlider');
        const result = nibCreditShowcaseSliderSelector(mockState);

        expect(result).toBe(true);
    });

    it('should check offers view rights for header', () => {
        const result = hasOffersViewRightsForHeaderSelector(mockState);

        expect(result).toBe(false);
    });
});
