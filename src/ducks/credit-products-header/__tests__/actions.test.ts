import { type OptionShape } from '@corp-front/holding-controls/types';
import { type Category } from 'corp-customers-api-typescript-services';

import {
    getCategoryHeaderError,
    getCategoryHeaderFinish,
    getCategoryHeaderStart,
    resetCreditProductsHeaderState,
    setHeaderCompany,
} from '../actions';
import { CREDIT_PRODUCTS_HEADER_TYPES } from '../types';

describe('CreditProductsHeader Actions', () => {
    it('should create an action to set header company', () => {
        const company: OptionShape = { key: '1', content: 'Company A' }; // example OptionShape
        const expectedAction = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.SET_HEADER_COMPANY,
            company,
        };

        expect(setHeaderCompany(company)).toEqual(expectedAction);
    });

    it('should create an action to start getting category header', () => {
        const organizationId = 'org-123';
        const expectedAction = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_START,
            organizationId,
        };

        expect(getCategoryHeaderStart(organizationId)).toEqual(expectedAction);
    });

    it('should create an action to finish getting category header', () => {
        const category: Category = { sksCode: 'cat-1', sksName: 'Category A' }; // example Category
        const expectedAction = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_FINISH,
            category,
        };

        expect(getCategoryHeaderFinish(category)).toEqual(expectedAction);
    });

    it('should create an action for category header error', () => {
        const error = new Error('Something went wrong');
        const expectedAction = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_ERROR,
            error,
        };

        expect(getCategoryHeaderError(error)).toEqual(expectedAction);
    });

    it('should create an action to reset credit products header state', () => {
        const expectedAction = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.RESET_HEADER_STATE,
        };

        expect(resetCreditProductsHeaderState()).toEqual(expectedAction);
    });
});
