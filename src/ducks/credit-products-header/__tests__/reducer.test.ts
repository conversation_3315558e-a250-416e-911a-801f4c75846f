import { type Category } from 'corp-customers-api-typescript-services';

import { type OptionShape } from '@alfalab/core-components/select/typings';

import { creditProductHeaderInitialState, creditProductsHeaderReducer } from '../reducer';
import { CREDIT_PRODUCTS_HEADER_TYPES } from '../types';

describe('creditProductsHeader Reducer', () => {
    it('should handle SET_HEADER_COMPANY', () => {
        const company: OptionShape = { key: '1', value: 'Company A' }; // example OptionShape
        const action = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.SET_HEADER_COMPANY,
            company,
        };
        const expectedState = {
            ...creditProductHeaderInitialState,
            company,
        };
        const newState = creditProductsHeaderReducer(creditProductHeaderInitialState, action);

        expect(newState).toEqual(expectedState);
    });

    it('should handle GET_CATEGORY_HEADER_START', () => {
        const action = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_START,
            organizationId: '',
        };

        const expectedState = {
            ...creditProductHeaderInitialState,
            category: {
                ...creditProductHeaderInitialState.category,
                isFetching: true,
            },
        };
        const newState = creditProductsHeaderReducer(creditProductHeaderInitialState, action);

        expect(newState).toEqual(expectedState);
    });

    it('should handle GET_CATEGORY_HEADER_FINISH', () => {
        const category: Category = { sksCode: 'code-1', sksName: 'Category A' }; // example Category
        const action = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_FINISH,
            category,
        };
        const expectedState = {
            ...creditProductHeaderInitialState,
            category: {
                ...creditProductHeaderInitialState.category,
                isFetching: false,
                category,
            },
        };
        const newState = creditProductsHeaderReducer(creditProductHeaderInitialState, action);

        expect(newState).toEqual(expectedState);
    });

    it('should handle GET_CATEGORY_HEADER_ERROR', () => {
        const error = new Error('Something went wrong');
        const action = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_ERROR,
            error,
        };
        const expectedState = {
            ...creditProductHeaderInitialState,
            category: {
                ...creditProductHeaderInitialState.category,
                error: error.message,
            },
        };
        const newState = creditProductsHeaderReducer(creditProductHeaderInitialState, action);

        expect(newState).toEqual(expectedState);
    });

    it('should handle RESET_HEADER_STATE', () => {
        const action = {
            type: CREDIT_PRODUCTS_HEADER_TYPES.RESET_HEADER_STATE,
        };
        const newState = creditProductsHeaderReducer(creditProductHeaderInitialState, action);

        expect(newState).toEqual(creditProductHeaderInitialState);
    });
});
