import { type OptionShape } from '@corp-front/holding-controls/types';
import { type Category } from 'corp-customers-api-typescript-services';

import { CREDIT_PRODUCTS_HEADER_TYPES } from './types';

export function setHeaderCompany(company: OptionShape) {
    return {
        type: CREDIT_PRODUCTS_HEADER_TYPES.SET_HEADER_COMPANY,
        company,
    };
}

export function getCategoryHeaderStart(organizationId?: string) {
    return {
        type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_START,
        organizationId,
    };
}

export function getCategoryHeaderFinish(category: Category) {
    return {
        type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_FINISH,
        category,
    };
}

export function getCategoryHeaderError(error: Error) {
    return {
        type: CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_ERROR,
        error,
    };
}

export const resetCreditProductsHeaderState = () => ({
    type: CREDIT_PRODUCTS_HEADER_TYPES.RESET_HEADER_STATE,
});
