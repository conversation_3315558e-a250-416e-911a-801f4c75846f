import qs from 'qs';
import { createSelector } from 'reselect';

import {
    MMB_CATEGORY_CODES,
    OrganizationCategoryCodes,
} from '#/src/constants/organization-category-codes';
import { COMPLIANCE_BLOCKINGS_CHECK, EUserRights } from '#/src/constants/rights';
import { type ApplicationState } from '#/src/ducks/application-state';

import { activeCreditCaseInfoSelector } from '../attach-documents/selectors';
import {
    allCreditOffersSelector,
    isCreditOffersFetchingSelector,
    isDigitalSalesCreditSliderEmptySelector,
    isDigitalSalesCreditSliderReceivedSelector,
} from '../credit-offers/selectors';
import { isClosedActiveCaseSelector } from '../credit-processing/selectors';
import { creditClientLimitSelector } from '../credit-products/selectors/client-limit.selectors';
import {
    creditProductsMainMenuErrorSelector,
    hasCreditProductMainMenuSelector,
    isMmbCategoryMainMenuSelector,
} from '../credit-products-main-menu/selectors';
import {
    creditRequestsErrorSelector,
    creditRequestsListSelector,
    isCreditRequestsFetchingSelector,
} from '../credit-requests/selectors';
import { currentOrganizationEqIdSelector } from '../organization/selectors';
import { searchParamsSelector } from '../router/selectors';

export const headerCompanySelector = (state: ApplicationState) =>
    state.creditProductsHeader.company;

export const isCategoryHeaderFetchingSelector = (state: ApplicationState) =>
    state.creditProductsHeader.category.isFetching;

export const categoryHeaderErrorSelector = (state: ApplicationState) =>
    state.creditProductsHeader.category.error;

export const categoryCodeHeaderSelector = (state: ApplicationState) =>
    state.creditProductsHeader.category.category?.sksCode;

export const isMmbCategoryHeaderSelector = createSelector(categoryCodeHeaderSelector, (code = '') =>
    MMB_CATEGORY_CODES.includes(code as OrganizationCategoryCodes),
);

export const isSBCategoryHeaderSelector = createSelector(
    categoryCodeHeaderSelector,
    (organizationCategoryCode) =>
        [
            OrganizationCategoryCodes.ClientCategorySKS8,
            OrganizationCategoryCodes.ClientCategorySKS18,
        ].includes(organizationCategoryCode as OrganizationCategoryCodes),
);

export const isKIBCategoryHeaderSelector = createSelector(
    categoryCodeHeaderSelector,
    (organizationCategoryCode) =>
        [
            OrganizationCategoryCodes.ClientCategorySKS2,
            OrganizationCategoryCodes.ClientCategorySKS3,
            OrganizationCategoryCodes.ClientCategorySKS5,
            OrganizationCategoryCodes.ClientCategorySKS9,
        ].includes(organizationCategoryCode as OrganizationCategoryCodes),
);

export const headerOrganizationsListSelector = (state: ApplicationState) =>
    state.organization?.list || [];

export const currentHeaderOrganizationSelector = createSelector(
    headerOrganizationsListSelector,
    headerCompanySelector,
    currentOrganizationEqIdSelector,
    (organizations, company, currentOrganizationEqId) =>
        organizations.find(
            (organization) => organization.eqId === (company?.key ?? currentOrganizationEqId),
        ),
);

export const currentHeaderOrganizationEqIdSelector = createSelector(
    searchParamsSelector,
    currentHeaderOrganizationSelector,
    (search, organization) =>
        `${qs.parse(search, { ignoreQueryPrefix: true })?.customerId || ''}` ||
        organization?.eqId ||
        '',
);

export const currentHeaderOrganizationShortNameSelector = createSelector(
    currentHeaderOrganizationSelector,
    (currentOrganization) => currentOrganization?.organizationName.shortName || '',
);

export const currentHeaderOrganizationFeaturesListSelector = createSelector(
    currentHeaderOrganizationSelector,
    (currentOrganization) => currentOrganization?.features ?? {},
);

export const createOrganizationFeatureSelector = (name: string) =>
    createSelector(
        currentHeaderOrganizationFeaturesListSelector,
        (organizationFeatures) => organizationFeatures[name] || false,
    );

const createHasRightsSelector = (right: EUserRights) =>
    createSelector(
        currentHeaderOrganizationSelector,
        (organization) =>
            !!organization?.rights?.[right]?.checkResults?.find(
                (check) => check.checkName === COMPLIANCE_BLOCKINGS_CHECK,
            )?.successful,
    );

export const isNibCreditShowcaseSliderForHeaderSelector =
    createOrganizationFeatureSelector('nibCreditShowcaseSlider');

export const isNibCreditOffersForHeaderSelector =
    createOrganizationFeatureSelector('nibCreditOffers');

export const isEcoLimitsSmartWidgetDeniedForHeaderSelector = createOrganizationFeatureSelector(
    'EcoLimitsSmartWidgetDenied',
);

export const hasOffersViewRightsForHeaderSelector = createHasRightsSelector(
    EUserRights.CP_OFFERS_VIEW,
);

export const isShowOnlyOffersSelector = createSelector(
    activeCreditCaseInfoSelector,
    isClosedActiveCaseSelector,
    hasCreditProductMainMenuSelector,
    creditProductsMainMenuErrorSelector,
    isMmbCategoryMainMenuSelector,
    creditRequestsListSelector,
    creditRequestsErrorSelector,
    isCreditRequestsFetchingSelector,
    isCreditOffersFetchingSelector,
    isNibCreditOffersForHeaderSelector,
    allCreditOffersSelector,
    (
        activeCreditCaseInfo,
        isClosedActiveCase,
        hasCreditProductMainMenu,
        creditProductsMainMenuError,
        isMmbCategoryMainMenu,
        creditRequestsList,
        creditRequestsError,
        isCreditRequestsFetching,
        isCreditOffersFetching,
        isNibCreditOffersForHeader,
        allCreditOffers,
    ) =>
        (!activeCreditCaseInfo || isClosedActiveCase) &&
        hasCreditProductMainMenu === false &&
        !creditProductsMainMenuError &&
        isMmbCategoryMainMenu &&
        creditRequestsList.length == 0 &&
        !creditRequestsError &&
        !isCreditRequestsFetching &&
        !isCreditOffersFetching &&
        (isNibCreditOffersForHeader || allCreditOffers.length > 0),
);

export const isShowAllOffersButtonSelector = createSelector(
    isMmbCategoryHeaderSelector,
    isNibCreditOffersForHeaderSelector,
    isNibCreditShowcaseSliderForHeaderSelector,
    allCreditOffersSelector,
    isDigitalSalesCreditSliderEmptySelector,
    isDigitalSalesCreditSliderReceivedSelector,
    (
        isMMBCategoryHeader,
        isNibCreditOffersForHeader,
        isNibCreditShowcaseSliderForHeader,
        allCreditOffers,
        isDigitalSalesCreditSliderEmpty,
        isDigitalSalesCreditSliderReceived,
    ) =>
        isMMBCategoryHeader &&
        ((isNibCreditOffersForHeader && isNibCreditShowcaseSliderForHeader) ||
            (!isNibCreditShowcaseSliderForHeader && isNibCreditOffersForHeader) ||
            (isNibCreditShowcaseSliderForHeader &&
                isDigitalSalesCreditSliderEmpty &&
                isDigitalSalesCreditSliderReceived) ||
            (!isNibCreditOffersForHeader &&
                !isNibCreditShowcaseSliderForHeader &&
                allCreditOffers.length > 0)),
);

export const isShowSmartLimitsSelector = createSelector(
    isMmbCategoryHeaderSelector,
    isEcoLimitsSmartWidgetDeniedForHeaderSelector,
    creditClientLimitSelector,
    (isMmbCategoryHeader, isEcoLimitsSmartWidgetDeniedForHeader, { clientLimit }) =>
        !isMmbCategoryHeader && (!isEcoLimitsSmartWidgetDeniedForHeader || !!clientLimit),
);

export const isShowCreditCaseSelector = createSelector(
    activeCreditCaseInfoSelector,
    isClosedActiveCaseSelector,
    isShowSmartLimitsSelector,
    (activeCreditCaseInfo, isClosedActiveCase, isShowSmartLimits) =>
        !!activeCreditCaseInfo && !isClosedActiveCase && !isShowSmartLimits,
);
