import { type Category } from 'corp-customers-api-typescript-services';

import { type OptionShape } from '@alfalab/core-components/select/typings';

import { type EErrorMessages } from '#/src/utils/errors/error-messages';
import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as mainMenuActions from '../credit-products-main-menu/actions';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '../credit-products-main-menu/types';

import type * as actions from './actions';
import { CREDIT_PRODUCTS_HEADER_TYPES } from './types';

type ActionTypes =
    | ReturnType<InferValueTypes<typeof actions>>
    | ReturnType<InferValueTypes<typeof mainMenuActions>>;

type TCategoryState = {
    isFetching: boolean;
    error: null | string | EErrorMessages;
    category: Category;
};

export type CreditProductsHeaderState = {
    category: TCategoryState;
    company: OptionShape | null;
};

const categoryInitialState: TCategoryState = {
    isFetching: false,
    error: null,
    category: {
        sksCode: '',
        sksName: '',
    },
};

export const creditProductHeaderInitialState: CreditProductsHeaderState = {
    category: categoryInitialState,
    company: null,
};

export const creditProductsHeaderReducer = (
    state: CreditProductsHeaderState = creditProductHeaderInitialState,
    action: ActionTypes,
): CreditProductsHeaderState => {
    switch (action.type) {
        case CREDIT_PRODUCTS_HEADER_TYPES.SET_HEADER_COMPANY:
            return { ...state, company: action.company };
        case CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_START:
            return {
                ...state,
                category: { ...creditProductHeaderInitialState.category, isFetching: true },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_FINISH:
        case CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_FINISH:
            return {
                ...state,
                category: {
                    ...creditProductHeaderInitialState.category,
                    isFetching: false,
                    category: action.category,
                },
            };
        case CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_CATEGORY_MAIN_MENU_ERROR:
        case CREDIT_PRODUCTS_HEADER_TYPES.GET_CATEGORY_HEADER_ERROR:
            return {
                ...state,
                category: {
                    ...creditProductHeaderInitialState.category,
                    error: action.error?.message,
                },
            };
        case CREDIT_PRODUCTS_HEADER_TYPES.RESET_HEADER_STATE:
            return creditProductHeaderInitialState;
        default:
            return state;
    }
};
