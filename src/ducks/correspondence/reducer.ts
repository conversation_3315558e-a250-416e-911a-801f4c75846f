import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { CORRESPONDENCE_TYPES } from './types';

export type SignConfirmationState = {
    isSuccess: boolean;
};

const initialState: SignConfirmationState = {
    isSuccess: false,
};

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export function signConfirmationReducer(
    state = initialState,
    action: ActionTypes,
): SignConfirmationState {
    switch (action.type) {
        case CORRESPONDENCE_TYPES.SET_SAVE_LETTER_STATUS:
            return {
                ...state,
                isSuccess: action.isSuccess,
            };
        default:
            return state;
    }
}
