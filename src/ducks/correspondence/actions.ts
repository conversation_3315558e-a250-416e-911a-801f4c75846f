import { type Letter } from 'thrift-services/services/correspondence';

import { type EarlyPayData } from '#/src/types/early-pay-sidebar';

import { CORRESPONDENCE_TYPES } from './types';

export function saveLetterStart(letter: Letter, data: EarlyPayData) {
    return {
        type: CORRESPONDENCE_TYPES.SAVE_LETTER_START,
        letter,
        data,
    };
}

export const setIsSuccessSaveLetter = (isSuccess: boolean) => ({
    type: CORRESPONDENCE_TYPES.SET_SAVE_LETTER_STATUS,
    isSuccess,
});
