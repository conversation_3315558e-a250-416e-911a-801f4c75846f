import { type RunDecisionMakerCheckResponse } from 'corp-role-model-mks-permissions-api-typescript-services';

import { type InferValueTypes } from '#/src/utils/generic-types';

import type * as actions from './actions';
import { RUN_DECISION_MAKER_CHECK_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type MksPermissionsState = {
    isError: boolean;
    isFetching: boolean;
    userClient: RunDecisionMakerCheckResponse;
};

export const mksPermissionsInitialState: MksPermissionsState = {
    isError: false,
    isFetching: false,
    userClient: {},
};

export function mksPermissionsReducer(
    state = mksPermissionsInitialState,
    action: ActionTypes,
): MksPermissionsState {
    switch (action.type) {
        case RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_START:
            return {
                ...state,
                isFetching: true,
            };
        case RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_FINISH:
            return {
                ...state,
                isFetching: false,
                userClient: action.response,
            };
        case RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_ERROR:
            return {
                ...state,
                isError: true,
                isFetching: false,
                userClient: {},
            };
        default:
            return state;
    }
}
