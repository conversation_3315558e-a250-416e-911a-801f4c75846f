import { type RunDecisionMaker<PERSON>heckResponse } from 'corp-role-model-mks-permissions-api-typescript-services';

import { type ServerResponseError } from '#/src/utils/errors/server-response-error';

import { RUN_DECISION_MAKER_CHECK_TYPES } from './types';

export function runDecisionMakerCheckStart() {
    return { type: RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_START };
}

export function runDecisionMakerCheckFinish(response: RunDecisionMakerCheckResponse) {
    return {
        type: RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_FINISH,
        response,
    };
}

export function runDecisionMakerCheckError(error: ServerResponseError) {
    return {
        type: RUN_DECISION_MAKER_CHECK_TYPES.RUN_DECISION_MAKER_CHECK_ERROR,
        error,
    };
}
