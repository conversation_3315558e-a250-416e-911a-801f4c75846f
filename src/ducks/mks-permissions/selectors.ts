import { createSelector } from 'reselect';

import { COMPLIANCE_BLOCKINGS_CHECK, EUserRights } from '#/src/constants/rights';

import { type ApplicationState } from '../application-state';
import { currentOrganizationSelector } from '../organization/selectors';

export const mksPermissionsSelector = (state: ApplicationState) => state.mksPermissions;

export const isMksPermissionsFetchingSelector = createSelector(
    mksPermissionsSelector,
    (mksPermissions) => mksPermissions.isFetching,
);

const createHasRightsSelector = (right: EUserRights) =>
    createSelector(
        currentOrganizationSelector,
        (organization) =>
            !!organization?.rights?.[right]?.checkResults?.find(
                (check) => check.checkName === COMPLIANCE_BLOCKINGS_CHECK,
            )?.successful,
    );

export const hasAlfaCreditDocumentRequestViewRightsSelector = createHasRightsSelector(
    EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW,
);
export const hasAlfaCreditBankRequestViewRightsSelector = createHasRightsSelector(
    EUserRights.ALFACREDIT_BANK_REQUEST_VIEW,
);
export const hasAlfaCreditCreditRequestViewRightsSelector = createHasRightsSelector(
    EUserRights.ALFACREDIT_CREDIT_REQUEST_VIEW,
);
export const hasAlfaCreditGuaranteeRequestViewRightsSelector = createHasRightsSelector(
    EUserRights.ALFACREDIT_GUARANTEE_REQUEST_VIEW,
);
export const hasCreditDealsViewRightsSelector = createHasRightsSelector(
    EUserRights.CP_CREDIT_DEALS_VIEW,
);
export const hasGuaranteeDealsViewRightsSelector = createHasRightsSelector(
    EUserRights.CP_GUARANTEE_DEALS_VIEW,
);
export const hasOffersViewRightsSelector = createHasRightsSelector(EUserRights.CP_OFFERS_VIEW);

export const isCeoSelector = createSelector(
    mksPermissionsSelector,
    ({ userClient }) => userClient?.resultCode === 'OK',
);
