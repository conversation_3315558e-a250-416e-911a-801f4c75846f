import { createSelector } from 'reselect';

import { type ECreditOffers } from '#/src/constants/credit-offers';
import { ECreditOffersProductCodes, PRODUCT_NAMES } from '#/src/constants/credit-products';
import { type ApplicationState } from '#/src/ducks/application-state';
import { mappedCreditOffersSelector } from '#/src/ducks/credit-offers/selectors';
import {
    currentOrganizationSelector,
    getCurrentOrganizationNameSelector,
} from '#/src/ducks/organization/selectors';
import { convertAmountToNumber } from '#/src/utils/amount';

export const goToCreditFormsParamsQuestionnaireCustomerSelector = createSelector(
    mappedCreditOffersSelector,
    currentOrganizationSelector,
    getCurrentOrganizationNameSelector,
    (_: ApplicationState, offerType: ECreditOffers) => offerType,
    (mappedCreditOffers, currentOrganization, organizationName, offerType) => {
        if (!currentOrganization?.inn) return null;

        const productCode = ECreditOffersProductCodes[offerType];
        const product = productCode ? PRODUCT_NAMES[productCode] : '';
        const { inn } = currentOrganization;
        const opfCode = inn.length === 10 ? 'juridical' : 'physical';
        const creditOffer = mappedCreditOffers.find((offer) => offer.type === offerType);

        if (!creditOffer) {
            throw new Error(
                `Не нашли нужное предложение в mappedCreditOffers по offerType: ${offerType}`,
            );
        }

        const { maximumAmount, maximumTerm, type } = creditOffer;

        return {
            inn,
            opfCode,
            organizationName,
            productCode,
            product,
            calculatorAmount: convertAmountToNumber(maximumAmount),
            calculatorTerm: maximumTerm,
            productId: type,
        };
    },
);

export const goToCreditFormsParamsOfferSelector = createSelector(
    mappedCreditOffersSelector,
    (_: ApplicationState, offerType: ECreditOffers) => offerType,
    (mappedCreditOffers, offerType) => {
        const creditOffer = mappedCreditOffers.find((offer) => offer.type === offerType);

        if (!creditOffer) {
            throw new Error(
                `Не нашли нужное предложение в mappedCreditOffers по offerType: ${offerType}`,
            );
        }

        const {
            id,
            code,
            campaignCode,
            expiryDate,
            ewsId,
            approvalProbability,
            clientRating,
            ratingModel,
            maximumAmount,
            maximumTerm,
            yearRatePct,
            finalRate,
        } = creditOffer;

        return {
            id: code,
            variant: id,
            campaignCode,
            expiryDate,
            ewsId,
            approvalProbability,
            clientRating,
            ratingModel,
            maximumAmount: convertAmountToNumber(maximumAmount),
            maximumTerm,
            rate: yearRatePct,
            finalRate,
        };
    },
);
