import { type WidgetStatus } from 'corp-new-credit-api-typescript-services';

import { type EErrorMessages } from '../../utils/errors/error-messages';
import { type InferValueTypes } from '../../utils/generic-types';

import type * as actions from './actions';
import { CREDIT_WIDGET_STATUS_TYPES } from './types';

type ActionTypes = ReturnType<InferValueTypes<typeof actions>>;

export type CreditWidgetStatusState = {
    widgetStatus?: WidgetStatus;
    isFetching: boolean;
    error: string | EErrorMessages | null;
    isFinished: boolean;
};

export const initialState: CreditWidgetStatusState = {
    widgetStatus: undefined,
    isFetching: false,
    error: null,
    isFinished: false,
};

export function creditWidgetStatusReducer(
    state = initialState,
    action: ActionTypes,
): CreditWidgetStatusState {
    switch (action.type) {
        case CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_START:
            return {
                ...state,
                widgetStatus: undefined,
                isFetching: true,
            };
        case CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_FINISH: {
            return {
                ...state,
                widgetStatus: action.response,
                isFetching: false,
                error: null,
                isFinished: true,
            };
        }
        case CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_ERROR:
            return {
                ...state,
                widgetStatus: undefined,
                isFetching: false,
                error: action.error.message,
                isFinished: true,
            };
        case CREDIT_WIDGET_STATUS_TYPES.RESET_CREDIT_WIDGET_STATUS:
            return initialState;
        default:
            return state;
    }
}
