import { type ApiGetWidgetStatusResponse } from 'corp-new-credit-api-typescript-services';

import { type CreditWidgetStatusErrors } from '#/src/utils/errors';

import { type LOG_LEVEL } from '../../types/logger';

import { CREDIT_WIDGET_STATUS_TYPES } from './types';

export function getCreditWidgetStatusStart({ limitExists }: { limitExists: boolean }) {
    return {
        type: CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_START,
        limitExists,
    };
}

export function getCreditWidgetStatusFinish({
    response,
}: {
    response: ApiGetWidgetStatusResponse;
}) {
    return {
        type: CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_FINISH,
        response,
    };
}

export function getCreditWidgetStatusError(error: CreditWidgetStatusErrors, logLevel: LOG_LEVEL) {
    return {
        type: CREDIT_WIDGET_STATUS_TYPES.GET_CREDIT_WIDGET_STATUS_ERROR,
        error,
        logLevel,
    };
}
export function resetCreditWidgetStatus() {
    return {
        type: CREDIT_WIDGET_STATUS_TYPES.RESET_CREDIT_WIDGET_STATUS,
    };
}
