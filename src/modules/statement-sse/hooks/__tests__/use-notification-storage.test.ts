import { act, renderHook } from '@testing-library/react';

import { ECreditProductsCodes } from '#/src/constants/credit-products';

import { NOTIFICATION_STORAGE_KEY } from '../../constants';
import { type TaskDataSharedType } from '../../types';
import { useNotificationStorage } from '../use-notification-storage';

const mockTask: TaskDataSharedType = {
    docNumber: '123',
    customerId: '456',
    product: ECreditProductsCodes.BUSINESS_CREDIT,
    statementRequestId: 'statementRequestId',
};

describe(useNotificationStorage.name, () => {
    beforeEach(() => {
        localStorage.clear();
    });

    it('должен вернуть false при проверке нового уведомления', () => {
        const { result } = renderHook(() => useNotificationStorage());

        expect(result.current.hasNotificationBeenShown(mockTask)).toBe(false);
    });

    it('должен отметить уведомление как показанное и корректно проверить его', () => {
        const { result } = renderHook(() => useNotificationStorage());

        act(() => {
            result.current.markNotificationAsShown(mockTask);
        });

        expect(result.current.hasNotificationBeenShown(mockTask)).toBe(true);
        expect(localStorage.getItem(NOTIFICATION_STORAGE_KEY)).toBe(JSON.stringify(mockTask));
    });

    it('должен корректно обрабатывать несколько уведомлений', () => {
        const { result } = renderHook(() => useNotificationStorage());

        const mockTask2: TaskDataSharedType = {
            docNumber: '789',
            customerId: '012',
            product: ECreditProductsCodes.OVERDRAFT,
            statementRequestId: 'statementRequestId2',
        };

        act(() => {
            result.current.markNotificationAsShown(mockTask);
            result.current.markNotificationAsShown(mockTask2);
        });

        expect(result.current.hasNotificationBeenShown(mockTask)).toBe(false);
        expect(result.current.hasNotificationBeenShown(mockTask2)).toBe(true);
        expect(localStorage.getItem(NOTIFICATION_STORAGE_KEY)).toBe(JSON.stringify(mockTask2));
    });

    it('должен обрабатывать некорректный JSON в localStorage', () => {
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, 'invalid-json');

        const { result } = renderHook(() => useNotificationStorage());

        expect(result.current.hasNotificationBeenShown(mockTask)).toBe(false);
    });

    it('должен обрабатывать пустой объект в localStorage', () => {
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, '{}');

        const { result } = renderHook(() => useNotificationStorage());

        expect(result.current.hasNotificationBeenShown(mockTask)).toBe(false);
    });
});
