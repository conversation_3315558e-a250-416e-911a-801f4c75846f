import { useCallback } from 'react';

import { NOTIFICATION_STORAGE_KEY } from '../constants';
import { type TaskDataSharedType } from '../types';

export type StoredNotification = Partial<TaskDataSharedType>;

export const useNotificationStorage = () => {
    const getStoredNotification = useCallback((): StoredNotification | null => {
        try {
            const storedData = localStorage.getItem(NOTIFICATION_STORAGE_KEY);

            return storedData ? JSON.parse(storedData) : null;
        } catch (_) {
            return null;
        }
    }, []);

    const setStoredNotification = useCallback((notification: StoredNotification) => {
        localStorage.setItem(NOTIFICATION_STORAGE_KEY, JSON.stringify(notification));
    }, []);

    const hasNotificationBeenShown = useCallback(
        (task: TaskDataSharedType) => {
            const storedNotification = getStoredNotification();

            return storedNotification?.statementRequestId === task.statementRequestId;
        },
        [getStoredNotification],
    );

    const markNotificationAsShown = useCallback(
        (task: TaskDataSharedType) => {
            setStoredNotification(task);
        },
        [setStoredNotification],
    );

    return {
        hasNotificationBeenShown,
        markNotificationAsShown,
    };
};
