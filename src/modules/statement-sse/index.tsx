import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import qs from 'qs';

import { Link } from '@alfalab/core-components/link';
import { Notification } from '@alfalab/core-components/notification';

import { PATHS } from '#/src/constants/routing';
import { contextRootSelector } from '#/src/ducks/settings/selectors';
import { getStatementRequestsStart } from '#/src/ducks/statement-requests/actions';
import { STATEMENTS_METRICS } from '#/src/metrics';
import { useEventBusValue } from '#/src/utils/hooks/use-event-bus-value';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { useNotificationStorage } from './hooks/use-notification-storage';
import {
    NOTIFICATION_AUTO_CLOSE_DELAY,
    PRODUCT_PATH_MAP,
    SHARED_SSE_STATEMENT_CREATED,
} from './constants';
import { type TaskDataSharedType } from './types';

export const StatementSSEModule = () => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const contextRoot = useSelector(contextRootSelector);

    const [isNotificationVisible, setIsNotificationVisible] = useState(false);

    const { hasNotificationBeenShown, markNotificationAsShown } = useNotificationStorage();

    const taskFromShared = useEventBusValue(SHARED_SSE_STATEMENT_CREATED) as
        | TaskDataSharedType
        | undefined;

    const currentQueryParams = qs.parse(window?.location?.search ?? '', {
        ignoreQueryPrefix: true,
    });

    const handleNotificationClose = useCallback(() => {
        setIsNotificationVisible(false);
    }, []);

    const statementRequestsPath = useMemo(() => {
        const { product, docNumber, customerId } = taskFromShared ?? {};

        if (!product) return;

        const basePath = PRODUCT_PATH_MAP[product] ?? PATHS.MAIN_PAGE;

        const queryParams = qs.stringify(
            { docNumber, customerId, tab: 'statementRequests' },
            { addQueryPrefix: true },
        );

        return `${contextRoot}${basePath}${queryParams}`;
    }, [taskFromShared, contextRoot]);

    const shouldCallGetStatementRequests = useCallback(
        (task: TaskDataSharedType) =>
            task.docNumber === currentQueryParams.docNumber &&
            task.customerId === currentQueryParams.customerId,
        [currentQueryParams],
    );

    useEffect(() => {
        if (!!taskFromShared?.product && !hasNotificationBeenShown(taskFromShared)) {
            setIsNotificationVisible(true);
            markNotificationAsShown(taskFromShared);

            if (shouldCallGetStatementRequests(taskFromShared)) {
                dispatch(
                    getStatementRequestsStart({
                        pageNumber: 1,
                        pageSize: 10,
                        docNumber: taskFromShared.docNumber,
                    }),
                );
            }

            trackAlfaMetrics(STATEMENTS_METRICS.successfullyCreated, {
                product: taskFromShared.product,
            });
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [taskFromShared]);

    return (
        <Notification
            data-testid='statement-sse-notification'
            visible={isNotificationVisible}
            badge='positive'
            title='Выписка создана'
            onCloseTimeout={handleNotificationClose}
            onClose={handleNotificationClose}
            autoCloseDelay={NOTIFICATION_AUTO_CLOSE_DELAY}
            actionButton={
                !!statementRequestsPath && (
                    <Link
                        underline={false}
                        view='primary'
                        colors='inverted'
                        target='_blank'
                        href={statementRequestsPath}
                    >
                        Перейти
                    </Link>
                )
            }
        />
    );
};
