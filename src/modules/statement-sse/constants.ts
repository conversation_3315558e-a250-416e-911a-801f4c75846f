import { ECreditProductsCodes } from '#/src/constants/credit-products';
import { PATHS } from '#/src/constants/routing';

export const SHARED_SSE_STATEMENT_CREATED = 'shared-sse_statementSuccess';
export const NOTIFICATION_STORAGE_KEY = 'statement-sse-notification-shown';
export const NOTIFICATION_AUTO_CLOSE_DELAY = 5000;

export const PRODUCT_PATH_MAP: Partial<Record<ECreditProductsCodes, string>> = {
    [ECreditProductsCodes.CREDIT_LINE]: PATHS.CREDIT_LINE,
    [ECreditProductsCodes.SOPUK]: PATHS.SOPUK_LINE,
    [ECreditProductsCodes.BUSINESS_CREDIT]: PATHS.CREDIT,
    [ECreditProductsCodes.OVERDRAFT]: PATHS.OVERDRAFT,
};
