import { TrancheStatus } from 'corp-core-credit-products-api-typescript-services';
import differenceInDays from 'date-fns/differenceInDays';
import startOfDay from 'date-fns/startOfDay';

import { pluralize } from '@alfalab/utils';

import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';
import { DEFAULT_AMOUNT } from '#/src/view-utils/credit-pane';

export const calculatePaymentDateAndAmount = (tranche: MappedTranche) => {
    let paymentDate;
    let paymentAmount;

    let paymentLoan = tranche?.debtToPay ?? DEFAULT_AMOUNT;
    let paymentInterest = tranche?.interestToPay ?? DEFAULT_AMOUNT;

    const payInterestTillDate = tranche?.payInterestTillDate?.seconds || 0;
    const payDebtTillDate = tranche?.payDebtTillDate?.seconds || 0;

    if (payInterestTillDate === payDebtTillDate) {
        paymentDate = payInterestTillDate * 1000;
        paymentAmount = {
            ...paymentLoan,
            amount: (paymentLoan.amount || 0) + (paymentInterest.amount || 0),
        };
    } else if (payInterestTillDate < payDebtTillDate) {
        paymentDate = payInterestTillDate * 1000;
        paymentAmount = paymentInterest;
        paymentLoan = DEFAULT_AMOUNT;
    } else {
        paymentDate = payDebtTillDate * 1000;
        paymentAmount = paymentLoan;
        paymentInterest = DEFAULT_AMOUNT;
    }

    const totalToPay = {
        ...paymentAmount,
        amount:
            (paymentAmount.amount || 0) +
            (tranche?.fineInterest?.amount || 0) +
            (tranche?.fineDebt?.amount || 0),
    };

    return { paymentDate, paymentAmount: totalToPay, paymentLoan, paymentInterest };
};

export const calculateDifferenceDays = (dateLeft: number | Date, dateRight: number | Date) =>
    differenceInDays(startOfDay(dateLeft), startOfDay(dateRight));

export const getNotificationTextByStatus = ({
    trancheStatus,
    daysToPay,
    currentTime,
    paymentDate,
}: {
    trancheStatus?: TrancheStatus;
    daysToPay: number;
    currentTime: Date;
    paymentDate: number;
}) => {
    switch (trancheStatus) {
        case TrancheStatus.UpcomingPayment:
            const pluralizeDay = pluralize(daysToPay, 'день', 'дня', 'дней');

            if (daysToPay === 0) {
                return 'Платеж сегодня';
            }

            return `Платеж через ${daysToPay} ${pluralizeDay}`;

        case TrancheStatus.OverduePayment:
            return 'Просрочено';
        default:
            return `Платёж ${dateToCustomFormat(currentTime, paymentDate, DAY_MONTH_FORMAT)}`;
    }
};
