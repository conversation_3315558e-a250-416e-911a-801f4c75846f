import { TrancheStatus } from 'corp-core-credit-products-api-typescript-services';

import { pluralize } from '@alfalab/utils';

import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import {
    calculateDifferenceDays,
    calculatePaymentDateAndAmount,
    getNotificationTextByStatus,
} from '../tranches';

describe('calculatePaymentDateAndAmount', () => {
    it('should calculate payment for equal interest and debt dates', () => {
        const tranche = {
            debtToPay: { amount: 500 },
            interestToPay: { amount: 200 },
            payInterestTillDate: { seconds: 1609459200 }, // Date 2021-01-01
            payDebtTillDate: { seconds: 1609459200 }, // Date 2021-01-01
        } as MappedTranche;

        const result = calculatePaymentDateAndAmount(tranche);

        expect(result.paymentDate).toEqual(1609459200000); // Date in milliseconds
        expect(result.paymentAmount.amount).toEqual(700); // 500 + 200
    });

    it('should calculate payment for interest date earlier than debt date', () => {
        const tranche = {
            debtToPay: { amount: 500 },
            interestToPay: { amount: 200 },
            payInterestTillDate: { seconds: 1609459200 }, // Date 2021-01-01
            payDebtTillDate: { seconds: 1672531200 }, // Date 2023-01-01
        } as MappedTranche;

        const result = calculatePaymentDateAndAmount(tranche);

        expect(result.paymentDate).toEqual(1609459200000); // Date in milliseconds
        expect(result.paymentAmount.amount).toEqual(200); // Only interest
    });

    it('should calculate payment for debt date earlier than interest date', () => {
        const tranche = {
            debtToPay: { amount: 500 },
            interestToPay: { amount: 200 },
            payInterestTillDate: { seconds: 1672531200 }, // Date 2023-01-01
            payDebtTillDate: { seconds: 1609459200 }, // Date 2021-01-01
        } as MappedTranche;

        const result = calculatePaymentDateAndAmount(tranche);

        expect(result.paymentDate).toEqual(1609459200000); // Date in milliseconds
        expect(result.paymentAmount.amount).toEqual(500); // Only debt
    });
});

describe('calculateDifferenceDays', () => {
    it('should calculate the difference in days between two valid dates', () => {
        const date1 = new Date(2023, 0, 1); // January 1, 2023
        const date2 = new Date(2023, 0, 5); // January 5, 2023

        const result = calculateDifferenceDays(date2, date1);

        expect(result).toEqual(4); // 5 - 1 = 4 days
    });

    it('should calculate the difference in days when one date is a timestamp', () => {
        const date1 = new Date(2023, 0, 1); // January 1, 2023
        const date2 = 1640995200000; // January 1, 2022 in milliseconds

        const result = calculateDifferenceDays(date2, date1);

        expect(result).toEqual(-365); // 1/1/2023 - 1/1/2022 = -365 days
    });

    it('should calculate the difference in days when both dates are timestamps', () => {
        const date1 = 1672520400000; // January 1, 2023 in milliseconds
        const date2 = 1675112400000; // January 31, 2023 in milliseconds

        const result = calculateDifferenceDays(date2, date1);

        expect(result).toEqual(30); // 31 - 1 = 30 days
    });

    it('should handle equal dates and return 0', () => {
        const date1 = new Date(2023, 0, 1); // January 1, 2023

        const result = calculateDifferenceDays(date1, date1);

        expect(result).toEqual(0);
    });
});

describe('getNotificationTextByStatus', () => {
    test('should return "Платеж сегодня" for an upcoming payment with daysToPay = 0', () => {
        const result = getNotificationTextByStatus({
            trancheStatus: TrancheStatus.UpcomingPayment,
            daysToPay: 0,
            currentTime: new Date(),
            paymentDate: 12345,
        });

        expect(result).toBe('Платеж сегодня');
    });

    test('should return "Платеж через" for an upcoming payment with daysToPay > 0', () => {
        const daysToPay = 5;
        const result = getNotificationTextByStatus({
            trancheStatus: TrancheStatus.UpcomingPayment,
            daysToPay,
            currentTime: new Date(),
            paymentDate: 12345,
        });

        const pluralizeDay = pluralize(daysToPay, 'день', 'дня', 'дней');
        const expected = `Платеж через ${daysToPay} ${pluralizeDay}`;

        expect(result).toBe(expected);
    });

    test('should return "Просрочено" for an overdue payment', () => {
        const result = getNotificationTextByStatus({
            trancheStatus: TrancheStatus.OverduePayment,
            daysToPay: -1,
            currentTime: new Date(),
            paymentDate: 12345,
        });

        expect(result).toBe('Просрочено');
    });

    test('should return "Платеж" for other cases', () => {
        const currentTime = new Date();
        const paymentDate = 12345;
        const result = getNotificationTextByStatus({
            trancheStatus: undefined,
            daysToPay: 5,
            currentTime,
            paymentDate,
        });

        const expected = `Платёж ${dateToCustomFormat(currentTime, paymentDate, DAY_MONTH_FORMAT)}`;

        expect(result).toBe(expected);
    });
});
