import { useSelector } from 'react-redux';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { DATE_YEAR_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { dateToCustomFormat } from '#/src/utils/date';

export const useFilteredPayments = (
    futurePayments: TLoanPayment[],
    payedPayments: TLoanPayment[],
) => {
    const currentTime = useSelector(currentTimeSelector);

    const filteredPayments = [...futurePayments, ...payedPayments].reduce(
        (availableYears, payment) => {
            const paymentYear = dateToCustomFormat(
                currentTime,
                (payment?.paymentDate?.seconds ?? 0) * 1000,
                DATE_YEAR_FORMAT,
            );

            if (!availableYears[paymentYear]) {
                availableYears[paymentYear] = [];
            }

            availableYears[paymentYear].push(payment);

            return availableYears;
        },
        {} as { [key: string]: TLoanPayment[] },
    );

    return { filteredPayments, availableYearsArray: Object.keys(filteredPayments) };
};
