import React from 'react';
import { createCn } from 'bem-react-classname';

import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Typography } from '@alfalab/core-components/typography';
import { ExclamationMIcon } from '@alfalab/icons-glyph/ExclamationMIcon';

import './delete-request-modal.css';

const cn = createCn('modal-container');

type TDeleteRequestModal = {
    onCancel: () => void;
    onDelect: () => void;
};

export const DeleteRequestModal = ({ onCancel, onDelect }: TDeleteRequestModal) => (
    <div className={cn()}>
        <div>
            <SuperEllipse backgroundColor='#F15045'>
                <ExclamationMIcon color='#FFFFFF' />
            </SuperEllipse>
        </div>
        <Gap size='xl' />
        <Typography.Title view='small' tag='div'>
            Удалить заявку на кредит?
        </Typography.Title>
        <Gap size='s' />
        <Typography.Text>Восстановить данные будет невозможно</Typography.Text>
        <Gap size='xl' />

        <div>
            <Button view='secondary' onClick={onCancel}>
                Отмена
            </Button>
            <Gap direction='horizontal' size='m' />
            <Button view='primary' onClick={onDelect}>
                Удалить
            </Button>
        </div>
    </div>
);
