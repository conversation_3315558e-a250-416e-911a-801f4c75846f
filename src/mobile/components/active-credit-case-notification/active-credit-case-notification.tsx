import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';
import { InformationCircleMIcon } from '@alfalab/icons-glyph/InformationCircleMIcon';

import { ECreditOffersProductType, type EProductCodes } from '#/src/constants/credit-products';
import { goToAttachDocumentsPage, goToCreditFormsApp } from '#/src/ducks/app/actions';
import { productCodeSelector } from '#/src/ducks/attach-documents/selectors';
import { closeActiveCase } from '#/src/ducks/credit-processing/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';

import {
    ACTIVE_CREDIT_CASE_STAGES,
    ACTIVE_CREDIT_CASE_STAGES_LIST,
    type TActiveCreditCaseStage,
} from '../../constants/credit-request-statuses';

import { DeleteRequestModal } from './delete-request-modal';

import './active-credit-case-notification.css';

const cn = createCn('status-container');

type TCreditRequestStatusPane = {
    stage: TActiveCreditCaseStage;
};

export const ActiveCreditCaseNotification = ({ stage }: TCreditRequestStatusPane) => {
    const dispatch = useDispatch();
    const [isDeleteRequestOpen, setDeleteRequestOpen] = useState(false);

    const productCode = useSelector(productCodeSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const offerType = ECreditOffersProductType[productCode as EProductCodes];
    const { title, subtitle, primaryButtonText, secondaryButtonText, style } =
        ACTIVE_CREDIT_CASE_STAGES[stage];

    const toggleDeleteRequest = () => {
        setDeleteRequestOpen((value) => !value);
    };

    const handleCloseActiveCase = () => {
        dispatch(closeActiveCase());
        setDeleteRequestOpen((value) => !value);
    };

    const handleRedirect = () => {
        switch (stage) {
            case ACTIVE_CREDIT_CASE_STAGES_LIST.choiseDecision:
                dispatch(goToCreditFormsApp({ offerType, organizationId }));
                break;
            case ACTIVE_CREDIT_CASE_STAGES_LIST.fillingForm:
                dispatch(goToCreditFormsApp({ offerType, organizationId }));
                break;
            case ACTIVE_CREDIT_CASE_STAGES_LIST.signingAgreement:
                dispatch(goToCreditFormsApp({ offerType, organizationId }));
                break;
            case ACTIVE_CREDIT_CASE_STAGES_LIST.needClientDocuments:
                dispatch(goToAttachDocumentsPage());
                break;
        }
    };

    return (
        <div className={cn(style)} data-test-id='active-credit-case-notification'>
            <div>
                <InformationCircleMIcon className={cn(`icon-${style}`)} />
            </div>
            <Gap direction='horizontal' size='xs' />
            <div>
                <Typography.Text tag='p' weight='bold' view='primary-small'>
                    {title}
                </Typography.Text>
                <Typography.Text tag='p' view='primary-small'>
                    {subtitle}
                </Typography.Text>
                <Gap size='s' />
                <div>
                    <Button size='xxs' view='secondary' onClick={handleRedirect}>
                        {primaryButtonText}
                    </Button>
                    {!!secondaryButtonText && (
                        <Button view='link' size='xxs' onClick={toggleDeleteRequest}>
                            {secondaryButtonText}
                        </Button>
                    )}
                </div>
            </div>
            <BottomSheet open={isDeleteRequestOpen} onClose={toggleDeleteRequest} hasCloser={true}>
                <DeleteRequestModal
                    onCancel={toggleDeleteRequest}
                    onDelect={handleCloseActiveCase}
                />
            </BottomSheet>
        </div>
    );
};
