import React, { useState } from 'react';
import { createCn } from 'bem-react-classname';

import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Typography } from '@alfalab/core-components/typography';
import { ChevronRightShiftRightMIcon } from '@alfalab/icons-glyph/ChevronRightShiftRightMIcon';
import { ExclamationMIcon } from '@alfalab/icons-glyph/ExclamationMIcon';

import { OverdraftNetTurnoverModal } from '#/src/components/overdraft-net-turnover-modal';
import { type TTurnoverProps } from '#/src/components/overdraft-net-turnover-pane-content/overdraft-net-turnover-pane-content';
import AmountPure from '#/src/components/ui/amount-pure';

import './overdraft-net-turnover-button.css';

const cn = createCn('overdraft-net-turnover-button');

type TProps = {
    negative: boolean;
} & TTurnoverProps;

export const OverdraftNetTurnoverButton: React.FC<TProps> = ({
    warning,
    negative,
    pending,
    overdraftLimit,
    currentDate,
    neededAmount,
    maxAmount,
    turnoverData,
}) => {
    const [modalOpen, setModalOpen] = useState(false);

    const onModalClose = () => {
        setModalOpen(false);
    };

    const openModal = () => {
        setModalOpen(true);
    };

    return (
        <React.Fragment>
            <button className={cn()} onClick={openModal}>
                {(warning || negative) && (
                    <SuperEllipse
                        size={32}
                        iconContainerClassName={cn('status-icon')}
                        shapeClassName={cn('status-shape', {
                            warning,
                            negative: !warning && negative,
                        })}
                    >
                        <ExclamationMIcon />
                    </SuperEllipse>
                )}
                <Typography.TitleMobile
                    tag='div'
                    view='xsmall'
                    font='system'
                    className={cn('maintain-amount')}
                >
                    Требования к оборотам:
                    <br />
                    <AmountPure value={maxAmount} view='default' transparentMinor={false} />
                </Typography.TitleMobile>
                <ChevronRightShiftRightMIcon className={cn('right-icon')} />
            </button>
            <OverdraftNetTurnoverModal
                open={modalOpen}
                onClose={onModalClose}
                maintainAmount={maxAmount}
                warning={warning}
                maxAmount={maxAmount}
                pending={pending}
                neededAmount={neededAmount}
                currentDate={currentDate}
                turnoverData={turnoverData}
                overdraftLimit={overdraftLimit}
            />
        </React.Fragment>
    );
};
