import React from 'react';
import { Route, Switch } from 'react-router';
import { type History } from 'history';

import NotFound from '../routing/not-found';

import { App } from './containers/app';
import { PAGES_ACCORDING_TO_PATHS } from './view-utils/rounting';

import './styles.css';

type TProps = {
    history: History;
};

const MobileRoot = ({ history }: TProps) => (
    <App>
        <Switch>
            {Object.entries(PAGES_ACCORDING_TO_PATHS).map(([path, component]) => (
                <Route path={path} component={component} key={path} />
            ))}
            <NotFound history={history} />
        </Switch>
    </App>
);

export { MobileRoot };
