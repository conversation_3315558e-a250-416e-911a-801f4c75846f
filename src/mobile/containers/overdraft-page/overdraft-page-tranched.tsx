import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { ProgressBar } from '@alfalab/core-components/progress-bar';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import CalendarMIcon from '@alfalab/icons-glyph/CalendarMIcon';
import InformationCircleMIcon from '@alfalab/icons-glyph/InformationCircleMIcon';
import { PencilMIcon } from '@alfalab/icons-glyph/PencilMIcon';
import { pluralize } from '@alfalab/utils';

import AmountPure from '#/src/components/ui/amount-pure';
import { PATHS } from '#/src/constants/routing';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToHome } from '#/src/ducks/app/actions';
import {
    getCreditProductsStart,
    getTrancheCreditProductsStart,
} from '#/src/ducks/credit-products/actions';
import {
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
    parentDocNumberSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { tranchesSelector } from '#/src/ducks/credit-products/selectors/tranches.selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { contextRootSelector } from '#/src/ducks/settings/selectors';
import { parentOverdraftSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/overdraft-mmb.selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';

import { LoadingFailed } from '../../components/loading-failed';
import { BLUETINT_COLORS } from '../app/app';
import { Header } from '../header';
import { TrancheCardSkeleton } from '../tranche-card/tranche-card-skeleton';

import { ConditionsTab } from './overdraft-page-conditions-tab';
import { DebtInfo } from './overdraft-page-debt-info';
import { TranchesTab } from './overdraft-page-tranches-tab';

import './overdraft-page.css';

const cn = createCn('overdraft-page');

const enum TABS {
    TRANCHES = 'TRANCHES',
    CONDITIONS = 'CONDITIONS',
}

export const TranchedOverdraftPage = () => {
    const dispatch = useDispatch();
    const currentOverdraft = useSelector(parentOverdraftSelector);
    const tranches = useSelector(tranchesSelector);
    const contextRoot = useSelector(contextRootSelector);
    const docNumber = useSelector(parentDocNumberSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const [selectedId, setSelectedId] = useState<IDType>(TABS.TRANCHES);
    const [showDebtInfo, setShowDebtInfo] = useState(false);
    const [isStartFetching, setStartFetching] = useState(true);

    const isFetching = isCreditProductsFetching || isStartFetching;
    const isError =
        !isFetching && (checkIsFaultyCreditProduct(currentOverdraft) || isGetCreditProductsError);
    const progressBarValue =
        ((currentOverdraft?.availableAmount?.amount || 0) * 100) /
        (currentOverdraft?.requisites?.limit?.amount || 1);

    let notification = null;

    const handleBackBtnClick = () => {
        dispatch(goToHome());
    };

    const getTranches = useCallback(() => {
        dispatch(getTrancheCreditProductsStart({}));
    }, [dispatch]);

    const refreshCreditProducts = useCallback(() => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
    }, [dispatch]);

    const toggleDebtInfo = () => {
        setShowDebtInfo((value) => !value);
    };

    const redirectChangeLimit = () => {
        // Временное решение, пока не будет готова мобильная версия
        window.location.assign(
            `${window.location.origin}${contextRoot}${PATHS.OVERDRAFT}${qs.stringify(
                {
                    docNumber,
                    desktop: 'true',
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    const getCurrentTab = (tab: IDType) => {
        switch (tab) {
            case TABS.CONDITIONS:
                return (
                    <ConditionsTab
                        limit={currentOverdraft?.requisites?.limit}
                        rate={currentOverdraft?.debtRate || 0}
                        toDate={currentOverdraft?.requisites?.toDate ?? { seconds: 0 }}
                        paymentDate={currentOverdraft?.payDebtTillDate ?? { seconds: 0 }}
                        overdraftType={!!currentOverdraft?.isTrancheAllowed}
                        isFetching={isFetching}
                    />
                );
            case TABS.TRANCHES:
            default:
                return <TranchesTab />;
        }
    };

    if (Object.keys(tranches).length > 0) {
        notification = (
            <GenericWrapper
                column={false}
                justifyContent='between'
                className={cn('repayment-container')}
            >
                <div>
                    <Typography.Text tag='div' view='primary-small' color='secondary'>
                        Осталось выплатить
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text dataTestId='debt-to-pay' weight='medium' view='component'>
                        <AmountPure
                            transparentMinor={false}
                            value={currentOverdraft?.summary?.totalToPay}
                        />
                    </Typography.Text>
                </div>
                <IconButton
                    icon={InformationCircleMIcon}
                    style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                    onClick={toggleDebtInfo}
                    size='xxs'
                    dataTestId='debt-to-pay-button'
                />
            </GenericWrapper>
        );
    } else {
        notification = (
            <GenericWrapper className={cn('repayment-container')}>
                <SuperEllipse backgroundColor='white' size={48}>
                    <CalendarMIcon color='black' />
                </SuperEllipse>
                <Gap direction='horizontal' size={16} />
                <div className={cn('repayment-date')}>
                    <Typography.Text tag='span' weight='bold' view='component'>
                        {new Date(currentOverdraft?.requisites?.toDate?.seconds || 0).getDay()}
                        &nbsp;
                        {pluralize(
                            new Date(currentOverdraft?.requisites?.toDate?.seconds || 0).getDay(),
                            'день',
                            'дня',
                            'дней',
                        )}
                    </Typography.Text>
                    <Typography.Text view='primary-small' color='secondary'>
                        Срок погашения
                    </Typography.Text>
                </div>
            </GenericWrapper>
        );
    }

    useEffect(() => {
        if (isStartFetching) {
            getTranches();
        }

        setStartFetching(false);
    }, [currentOverdraft, dispatch, getTranches, isStartFetching]);

    return (
        <ProductPageWrapper>
            <Header
                title='Овердрафт'
                subTitle={`Cчёт ••${currentOverdraft?.shortAccountNumber}`}
                onBackBtnClick={handleBackBtnClick}
            />
            {isError ? (
                <LoadingFailed refresh={refreshCreditProducts} />
            ) : (
                <React.Fragment>
                    <div className={cn('limit-container')}>
                        <Skeleton visible={isFetching}>
                            <GenericWrapper justifyContent='between' alignItems='end'>
                                <div>
                                    <Typography.Text
                                        tag='div'
                                        view='primary-small'
                                        weight='regular'
                                        color='secondary'
                                    >
                                        Доступно
                                    </Typography.Text>
                                    <Gap size={4} />
                                    <Typography.Title
                                        dataTestId='available-amount'
                                        tag='div'
                                        view='medium'
                                        font='system'
                                    >
                                        <AmountPure
                                            transparentMinor={false}
                                            value={currentOverdraft?.availableAmount}
                                        />
                                    </Typography.Title>
                                </div>
                                <IconButton
                                    icon={PencilMIcon}
                                    view='secondary'
                                    onClick={redirectChangeLimit}
                                    size='xxs'
                                />
                            </GenericWrapper>
                        </Skeleton>
                        <Skeleton visible={isFetching}>
                            <ProgressBar
                                className={cn('progress-bar')}
                                size='s'
                                value={progressBarValue}
                            />
                        </Skeleton>
                        <Skeleton visible={isFetching}>
                            <Typography.Text
                                tag='span'
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                            >
                                из&nbsp;
                                <AmountPure
                                    bold='none'
                                    value={currentOverdraft?.requisites?.limit}
                                />
                            </Typography.Text>
                        </Skeleton>
                    </div>
                    <Skeleton visible={isFetching}>{notification}</Skeleton>
                    <Skeleton visible={isFetching}>
                        <SegmentedControl
                            className={cn('segment-control')}
                            onChange={setSelectedId}
                            selectedId={selectedId}
                            shape='rectangular'
                        >
                            <Segment id={TABS.TRANCHES} title='Активные транши' />
                            <Segment id={TABS.CONDITIONS} title='Условия' />
                        </SegmentedControl>
                    </Skeleton>
                    {isFetching ? (
                        <TrancheCardSkeleton length={3} />
                    ) : (
                        <div className={cn('tab-container')}>{getCurrentTab(selectedId)}</div>
                    )}
                    <BottomSheet
                        title='Осталось выплатить'
                        open={showDebtInfo}
                        onClose={toggleDebtInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='m' block={true} onClick={toggleDebtInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <DebtInfo currentOverdraft={currentOverdraft} />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
