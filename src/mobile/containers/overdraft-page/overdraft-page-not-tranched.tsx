import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { ProgressBar } from '@alfalab/core-components/progress-bar';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import PencilMIcon from '@alfalab/icons-glyph/PencilMIcon';

import AmountPure from '#/src/components/ui/amount-pure';
import { PATHS } from '#/src/constants/routing';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToHome } from '#/src/ducks/app/actions';
import { getCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
    parentDocNumberSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { contextRootSelector } from '#/src/ducks/settings/selectors';
import { parentOverdraftSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/overdraft-mmb.selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';

import { LoadingFailed } from '../../components/loading-failed';
import { Header } from '../header';

import { ConditionsTab } from './overdraft-page-conditions-tab';
import { DebtInfo } from './overdraft-page-debt-info';
import { DebtTab } from './overdraft-page-debt-tab';

import './overdraft-page.css';

export const cn = createCn('overdraft-page');

const enum TABS {
    DEBT = 'DEBT',
    CONDITIONS = 'CONDITIONS',
}

export const NotTranchedOverdraftPage = () => {
    const dispatch = useDispatch();
    const currentOverdraft = useSelector(parentOverdraftSelector);
    const contextRoot = useSelector(contextRootSelector);
    const docNumber = useSelector(parentDocNumberSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const [selectedId, setSelectedId] = useState<IDType>(TABS.DEBT);
    const [showDebtInfo, setShowDebtInfo] = useState(false);
    const [isStartFetching, setStartFetching] = useState(true);

    const isFetching = isCreditProductsFetching || isStartFetching;
    const isError =
        !isFetching && (checkIsFaultyCreditProduct(currentOverdraft) || isGetCreditProductsError);
    const progressBarValue =
        ((currentOverdraft?.availableAmount?.amount || 0) * 100) /
        (currentOverdraft?.requisites?.limit?.amount || 1);

    const handleBackBtnClick = () => {
        dispatch(goToHome());
    };

    const refreshCreditProducts = useCallback(() => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
    }, [dispatch]);

    const toggleDebtInfo = () => {
        setShowDebtInfo((value) => !value);
    };

    const getCurrentTab = (tab: IDType) => {
        switch (tab) {
            case TABS.CONDITIONS:
                return (
                    <ConditionsTab
                        limit={currentOverdraft?.requisites?.limit}
                        rate={currentOverdraft?.debtRate || 0}
                        toDate={currentOverdraft?.requisites?.toDate ?? { seconds: 0 }}
                        paymentDate={currentOverdraft?.payDebtTillDate ?? { seconds: 0 }}
                        overdraftType={!!currentOverdraft?.isTrancheAllowed}
                        dailyFine={currentOverdraft?.summary?.dailyFine}
                        isFetching={isFetching}
                    />
                );
            case TABS.DEBT:
            default:
                return <DebtTab currentOverdraft={currentOverdraft} isFetching={isFetching} />;
        }
    };

    const redirectChangeLimit = () => {
        // Временное решение, пока не будет готова мобильная версия
        window.location.assign(
            `${window.location.origin}${contextRoot}${PATHS.OVERDRAFT}${qs.stringify(
                {
                    docNumber,
                    desktop: 'true',
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    useEffect(() => {
        setStartFetching(false);
    }, []);

    return (
        <ProductPageWrapper>
            <Header
                title='Овердрафт'
                subTitle={
                    currentOverdraft?.shortAccountNumber
                        ? `Cчёт ••${currentOverdraft.shortAccountNumber}`
                        : ''
                }
                onBackBtnClick={handleBackBtnClick}
            />
            {isError ? (
                <LoadingFailed refresh={refreshCreditProducts} />
            ) : (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <div className={cn('limit-container')}>
                            <GenericWrapper justifyContent='between' alignItems='end'>
                                <div>
                                    <Typography.Text
                                        tag='div'
                                        view='primary-small'
                                        weight='regular'
                                        color='secondary'
                                    >
                                        Доступно
                                    </Typography.Text>
                                    <Gap size={4} />
                                    <Typography.Title
                                        dataTestId='available-amount'
                                        tag='div'
                                        view='medium'
                                        font='system'
                                    >
                                        <AmountPure
                                            transparentMinor={false}
                                            value={currentOverdraft?.availableAmount}
                                        />
                                    </Typography.Title>
                                </div>
                                <IconButton
                                    icon={PencilMIcon}
                                    view='secondary'
                                    onClick={redirectChangeLimit}
                                    size='xxs'
                                />
                            </GenericWrapper>

                            <ProgressBar
                                className={cn('progress-bar')}
                                size='s'
                                value={progressBarValue}
                            />

                            <Typography.Text
                                tag='span'
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                            >
                                из&nbsp;
                                <AmountPure
                                    bold='none'
                                    value={currentOverdraft?.requisites?.limit}
                                />
                            </Typography.Text>
                        </div>
                        <Gap size={2} />
                        <SegmentedControl
                            className={cn('segment-control')}
                            onChange={setSelectedId}
                            selectedId={selectedId}
                            shape='rectangular'
                        >
                            <Segment id={TABS.DEBT} title='Задолженность' />
                            <Segment id={TABS.CONDITIONS} title='Условия' />
                        </SegmentedControl>
                    </Skeleton>
                    <div className={cn('tab-container')}>{getCurrentTab(selectedId)}</div>
                    <BottomSheet
                        title='Осталось выплатить'
                        open={showDebtInfo}
                        onClose={toggleDebtInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='m' block={true} onClick={toggleDebtInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <DebtInfo currentOverdraft={currentOverdraft} />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
