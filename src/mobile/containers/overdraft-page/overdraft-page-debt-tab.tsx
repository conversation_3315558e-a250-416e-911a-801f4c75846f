import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import InformationCircleMIcon from '@alfalab/icons-glyph/InformationCircleMIcon';
import ListBulletedMIcon from '@alfalab/icons-glyph/ListBulletedMIcon';

import AmountPure from '#/src/components/ui/amount-pure/amount-pure';
import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { type MappedOverdraft } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';
import { DEFAULT_AMOUNT } from '#/src/view-utils/credit-pane';

import { EmptyState } from '../../components/empty-state';
import { BLUETINT_COLORS } from '../app/app';

const debtModalMessages = {
    totalLoanAndSum: {
        title: 'Использованный овердрафт',
        text: 'Это деньги, которые вы уже израсходовали из лимита овердрафта',
    },
    payTillDate: {
        title: 'Выплатить до',
        text: `Срок погашения овердрафта, чтобы не${NBSP}просрочить платёж. Если не вернуть деньги вовремя, мы будем начислять неустойку от суммы просроченной задолженности каждый день`,
    },
    total: {
        title: 'Всего',
        text: `Это${NBSP}сумма к${NBSP}погашению на${NBSP}начало операционного${NBSP}дня. Сюда входит использованный лимит овердрафта, проценты и${NBSP}неустойка (при${NBSP}наличии)`,
    },
};

type TDebtInfo = {
    currentOverdraft: MappedOverdraft;
    isFetching?: boolean;
};

export const DebtTab = ({ currentOverdraft, isFetching = false }: TDebtInfo) => {
    const currentTime = useSelector(currentTimeSelector);
    const [showDebtInfo, setShowDebtInfo] = useState(false);
    const [debtInfo, setDebtInfo] = useState({
        title: '',
        text: '',
    });

    const isEmpty = !currentOverdraft?.summary?.totalLoanAndFine?.amount;
    const toDate = currentOverdraft?.requisites?.toDate ?? { seconds: 0 };
    const total = {
        ...(currentOverdraft?.summary?.totalInterestOverdue
            ? currentOverdraft?.summary?.totalInterestOverdue
            : DEFAULT_AMOUNT),
        amount:
            (currentOverdraft?.summary?.totalInterestOverdue?.amount ?? 0) +
            (currentOverdraft?.summary?.totalInterestAndFine?.amount ?? 0) +
            (currentOverdraft?.debts?.interest?.overdueInterest?.amount ?? 0),
    };

    const toggleDebtInfo = () => {
        setShowDebtInfo((value) => !value);
    };

    const showDebtInfoModal = (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        const value = event.currentTarget.value as keyof typeof debtModalMessages;

        setDebtInfo({
            title: debtModalMessages[value].title,
            text: debtModalMessages[value].text,
        });
        setShowDebtInfo((visible) => !visible);
    };

    if (!isFetching && isEmpty) {
        return (
            <EmptyState
                icon={<ListBulletedMIcon color={BLUETINT_COLORS.сolorLightIcon} />}
                title='Нет задолженности'
            />
        );
    }

    return (
        <React.Fragment>
            <Skeleton visible={isFetching}>
                <GenericWrapper justifyContent='between'>
                    <div>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Использованный овердрафт
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text
                            dataTestId='debt-to-pay'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            <AmountPure
                                transparentMinor={false}
                                value={currentOverdraft?.debts?.loan?.debtToPay}
                            />
                        </Typography.Text>
                    </div>
                    <IconButton
                        icon={InformationCircleMIcon}
                        style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                        onClick={showDebtInfoModal}
                        value='totalLoanAndSum'
                        dataTestId='total-loan-button'
                    />
                </GenericWrapper>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text dataTestId='interest' tag='span' view='component' weight='medium'>
                    <AmountPure
                        transparentMinor={false}
                        value={currentOverdraft?.summary?.totalInterestAndFine}
                    />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <GenericWrapper justifyContent='between'>
                    <div>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Выплатить до
                        </Typography.Text>
                        <Typography.Text
                            dataTestId='to-date'
                            tag='div'
                            view='component'
                            weight='medium'
                        >
                            {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                        </Typography.Text>
                    </div>
                    <IconButton
                        dataTestId='pay-till-button'
                        icon={InformationCircleMIcon}
                        style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                        onClick={showDebtInfoModal}
                        value='payTillDate'
                    />
                </GenericWrapper>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <GenericWrapper justifyContent='between'>
                    <div>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Всего
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text
                            dataTestId='total'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            <AmountPure transparentMinor={false} value={total} />
                        </Typography.Text>
                    </div>
                    <IconButton
                        icon={InformationCircleMIcon}
                        style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                        onClick={showDebtInfoModal}
                        value='total'
                        dataTestId='total-button'
                    />
                </GenericWrapper>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Срок действия договора
                </Typography.Text>
                <Typography.Text dataTestId='term' tag='div' view='component' weight='medium'>
                    {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                </Typography.Text>
            </Skeleton>
            <BottomSheet
                title={debtInfo.title}
                open={showDebtInfo}
                onClose={toggleDebtInfo}
                hasCloser={true}
                actionButton={
                    <Button size='m' block={true} onClick={toggleDebtInfo}>
                        Понятно
                    </Button>
                }
            >
                <Typography.Text>{debtInfo.text}</Typography.Text>
            </BottomSheet>
        </React.Fragment>
    );
};
