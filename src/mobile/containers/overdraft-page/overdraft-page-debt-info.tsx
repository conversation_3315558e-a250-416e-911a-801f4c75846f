import React from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';
import {
    checkIsFaultyCreditProduct,
    type MappedOverdraft,
} from '#/src/utils/credit-products-mappers';

type TDebtInfo = {
    currentOverdraft: MappedOverdraft;
    isFetching?: boolean;
};

export const DebtInfo = ({ currentOverdraft, isFetching = false }: TDebtInfo) => {
    if (checkIsFaultyCreditProduct(currentOverdraft)) {
        return null;
    }

    const { fineDebt, fineInterest, totalDebt } = currentOverdraft;

    return (
        <React.Fragment>
            <Gap size={12} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Основной долг
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text tag='span' view='component' weight='medium'>
                    <AmountPure value={currentOverdraft?.summary?.totalLoanAndFine} />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text tag='span' view='component' weight='medium'>
                    <AmountPure value={currentOverdraft?.summary?.totalInterestAndFine} />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            {!!fineDebt && (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Неустойка за основной долг
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text tag='span' view='component' weight='medium'>
                            <AmountPure value={fineDebt} />
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={24} />
                </React.Fragment>
            )}
            {!!fineInterest && (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Неустойка за основной проценты
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text tag='span' view='component' weight='medium'>
                            <AmountPure value={fineInterest} />
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={24} />
                </React.Fragment>
            )}
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Всего
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text tag='span' view='component' weight='medium'>
                    <AmountPure value={totalDebt} />
                </Typography.Text>
            </Skeleton>
            <Gap size={16} />
        </React.Fragment>
    );
};
