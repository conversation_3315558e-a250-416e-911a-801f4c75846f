import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { List } from '@alfalab/core-components/list';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import InformationCircleMIcon from '@alfalab/icons-glyph/InformationCircleMIcon';
import { pluralize } from 'arui-private/lib/formatters';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { dateToCustomFormat } from '#/src/utils/date';

import { BLUETINT_COLORS } from '../app/app';

const INFO_MESSAGES = {
    limit: {
        title: 'Лимит',
        markdown: (
            <React.Fragment>
                <Typography.Text tag='div' view='primary-medium'>
                    Деньги, которые банк готов предоставить. Лимит овердрафта зависит:
                </Typography.Text>
                <Gap size='m' />
                <List tag='ul' marker='•'>
                    <List.Item>
                        От оборотов. В начале каждого месяца мы оцениваем обороты за текущие месяцы
                    </List.Item>
                    <List.Item>
                        От информации из внешних и внутренних источников, например из бюро кредитных
                        историй
                    </List.Item>
                </List>
            </React.Fragment>
        ),
    },
    rate: {
        title: 'Ставка',
        markdown: (
            <Typography.Text>
                Годовая ставка на использованный лимит овердрафта. Проценты начисляются только на
                сумму долга и только за дни, когда вы пользовались деньгами. При поступлении денег
                на счёт сначала будут списаны проценты
            </Typography.Text>
        ),
    },
    overdraftType: {
        title: 'Потраншевый овердрафт',
        markdown: (
            <React.Fragment>
                <Typography.Text tag='div'>
                    Банк выделяет деньги отдельными траншами в конце дня. Транш — это часть лимита,
                    которая ушла на оплату ваших расходов.{' '}
                </Typography.Text>
                <Gap size={16} />
                <Typography.Text tag='div'>
                    Для погашения каждого транша у вас есть 60 дней, а проценты начисляются только
                    за дни использования.{' '}
                </Typography.Text>
                <Gap size={16} />
                <Typography.Text>Количество траншей не ограничено. </Typography.Text>
            </React.Fragment>
        ),
    },
};

type TConditionsTab = {
    limit?: TAmount;
    rate: number;
    toDate: UnixEpoch;
    paymentDate: UnixEpoch;
    overdraftType: boolean;
    dailyFine?: TAmount;
    isFetching?: boolean;
};

export const ConditionsTab: React.FC<TConditionsTab> = ({
    limit,
    rate,
    toDate,
    paymentDate,
    overdraftType,
    dailyFine,
    isFetching,
}) => {
    const currentTime = useSelector(currentTimeSelector);
    const [showInfoModal, setShowInfoModal] = useState(false);
    const [infoMessage, setInfoMessage] = useState({
        title: '',
        type: '',
    });

    const toggleConditionsInfo = () => setShowInfoModal((show) => !show);

    const onInfoModalClick = (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
        const value = event.currentTarget.value as keyof typeof INFO_MESSAGES;

        setInfoMessage({
            title: INFO_MESSAGES[value].title,
            type: value,
        });
        setShowInfoModal((show) => !show);
    };

    if (overdraftType) {
        return (
            <React.Fragment>
                <Skeleton visible={isFetching}>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Лимит
                    </Typography.Text>
                    <Gap size={8} />
                    <Typography.Text dataTestId='limit' tag='span' view='component' weight='medium'>
                        <AmountPure transparentMinor={false} value={limit} />
                    </Typography.Text>
                </Skeleton>
                <Gap size={24} />
                <Skeleton visible={isFetching}>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Ставка
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text dataTestId='rate' tag='span' view='component' weight='medium'>
                        {rate}% годовых
                    </Typography.Text>
                </Skeleton>
                <Gap size={24} />
                <Skeleton visible={isFetching}>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Срок действия договора
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text dataTestId='term' tag='span' view='component' weight='medium'>
                        До {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                    </Typography.Text>
                </Skeleton>
                <Gap size={24} />
                <Skeleton visible={isFetching}>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Срок погашения
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text
                        dataTestId='pay-date'
                        tag='span'
                        view='component'
                        weight='medium'
                    >
                        {new Date(paymentDate?.seconds || 0).getDay()}&nbsp;
                        {pluralize(new Date(paymentDate?.seconds || 0).getDay(), [
                            'день',
                            'дня',
                            'дней',
                        ])}
                    </Typography.Text>
                </Skeleton>
                <Gap size={24} />
                <Skeleton visible={isFetching}>
                    <GenericWrapper justifyContent='between'>
                        <div>
                            <Typography.Text
                                tag='span'
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                            >
                                Тип овердрафта
                            </Typography.Text>
                            <Gap size={4} />
                            <Typography.Text
                                dataTestId='type'
                                tag='span'
                                weight='medium'
                                view='component'
                            >
                                Потраншевый
                            </Typography.Text>
                        </div>
                        <IconButton
                            icon={InformationCircleMIcon}
                            style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                            onClick={onInfoModalClick}
                            value='overdraftType'
                            dataTestId='type-button'
                        />
                    </GenericWrapper>
                </Skeleton>
                <BottomSheet
                    title={infoMessage.title}
                    open={showInfoModal}
                    onClose={toggleConditionsInfo}
                    hasCloser={true}
                    actionButton={
                        <Button size='m' block={true} onClick={toggleConditionsInfo}>
                            Понятно
                        </Button>
                    }
                >
                    {INFO_MESSAGES[infoMessage.type as keyof typeof INFO_MESSAGES]?.markdown}
                </BottomSheet>
            </React.Fragment>
        );
    }

    return (
        <React.Fragment>
            <Skeleton visible={isFetching}>
                <GenericWrapper justifyContent='between'>
                    <div>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Лимит
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text tag='span' view='component' weight='medium'>
                            <AmountPure transparentMinor={false} value={limit} />
                        </Typography.Text>
                    </div>
                    <IconButton
                        icon={InformationCircleMIcon}
                        style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                        onClick={onInfoModalClick}
                        value='limit'
                    />
                </GenericWrapper>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <GenericWrapper justifyContent='between'>
                    <div>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Ставка
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text tag='span' view='component' weight='medium'>
                            {rate}% годовых
                        </Typography.Text>
                    </div>
                    <IconButton
                        icon={InformationCircleMIcon}
                        style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                        onClick={onInfoModalClick}
                        value='rate'
                    />
                </GenericWrapper>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Срок действия договора
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text tag='span' view='component' weight='medium'>
                    До {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Ежемесячная комиссия
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text tag='span' view='component' weight='medium'>
                    <AmountPure transparentMinor={false} value={dailyFine} />
                </Typography.Text>
            </Skeleton>
            <BottomSheet
                title={infoMessage.title}
                open={showInfoModal}
                onClose={toggleConditionsInfo}
                hasCloser={true}
                actionButton={
                    <Button size='m' block={true} onClick={toggleConditionsInfo}>
                        Понятно
                    </Button>
                }
            >
                {INFO_MESSAGES[infoMessage.type as keyof typeof INFO_MESSAGES]?.markdown}
            </BottomSheet>
        </React.Fragment>
    );
};
