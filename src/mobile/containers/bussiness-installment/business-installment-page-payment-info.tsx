import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Gap } from '@alfalab/core-components/gap';
import { Grid } from '@alfalab/core-components/grid';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';

type TPaymentInfo = {
    totalDebtToPay?: TAmount;
    totalFine?: TAmount;
    isFetching?: boolean;
};

export const PaymentInfo = ({ totalDebtToPay, totalFine, isFetching = false }: TPaymentInfo) => (
    <React.Fragment>
        <Gap size='s' />
        <Skeleton visible={isFetching}>
            <Grid.Row>
                <Grid.Col width='6'>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Основной долг
                    </Typography.Text>
                </Grid.Col>

                <Grid.Col width='6'>
                    <Typography.Text
                        dataTestId='total-debt'
                        tag='span'
                        view='component'
                        weight='medium'
                    >
                        <AmountPure value={totalDebtToPay} />
                    </Typography.Text>
                </Grid.Col>
            </Grid.Row>
        </Skeleton>
        <Gap size='xl' />
        {totalFine && (totalFine?.amount ?? 0) > 0 && (
            <React.Fragment>
                <Skeleton visible={isFetching}>
                    <Grid.Row>
                        <Grid.Col width='6'>
                            <Typography.Text
                                tag='span'
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                            >
                                Неустойка
                            </Typography.Text>
                        </Grid.Col>

                        <Grid.Col width='6'>
                            <Typography.Text
                                dataTestId='fine'
                                tag='span'
                                view='component'
                                weight='medium'
                            >
                                <AmountPure value={totalFine} />
                            </Typography.Text>

                            <Typography.Text
                                tag='div'
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                            >
                                0,5% за каждый день просрочки
                            </Typography.Text>
                        </Grid.Col>
                    </Grid.Row>
                </Skeleton>
                <Gap size='xl' />
            </React.Fragment>
        )}
    </React.Fragment>
);
