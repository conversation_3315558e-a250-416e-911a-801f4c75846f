import React from 'react';
import { useSelector } from 'react-redux';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';
import { DATE_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { dateToCustomFormat } from '#/src/utils/date';

type TConditionsTab = {
    debtAmount?: TAmount;
    toDate?: UnixEpoch;
    fromDate?: UnixEpoch;
    isFetching?: boolean;
};

export const ConditionsTab = ({
    debtAmount,
    fromDate,
    toDate,
    isFetching = false,
}: TConditionsTab) => {
    const currentTime = useSelector(currentTimeSelector);
    const startDealDate = dateToCustomFormat(currentTime, fromDate, DATE_FORMAT);
    const endDealDate = dateToCustomFormat(currentTime, toDate, DATE_FORMAT);

    return (
        <React.Fragment>
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Размер кредита
                </Typography.Text>
                <Gap size='2xs' />
                <Typography.Text
                    dataTestId='debt-amount'
                    tag='span'
                    view='component'
                    weight='medium'
                >
                    <AmountPure transparentMinor={false} value={debtAmount} />
                </Typography.Text>
            </Skeleton>
            <Gap size='xl' />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Комиссия
                </Typography.Text>
                <Gap size='2xs' />
                <Typography.Text dataTestId='rate' tag='span' view='component' weight='medium'>
                    В договоре оферты
                </Typography.Text>
            </Skeleton>
            <Gap size='xl' />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Действие договора
                </Typography.Text>
                <Gap size='2xs' />
                <Typography.Text dataTestId='term' tag='span' view='component' weight='medium'>
                    {`${startDealDate} – ${endDealDate}`}
                </Typography.Text>
            </Skeleton>
        </React.Fragment>
    );
};
