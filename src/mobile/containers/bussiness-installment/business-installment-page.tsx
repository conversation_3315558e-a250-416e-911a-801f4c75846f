import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';
import { CalendarMIcon } from '@alfalab/icons-glyph/CalendarMIcon';
import { DocumentPdfMIcon } from '@alfalab/icons-glyph/DocumentPdfMIcon';
import { InformationCircleMIcon } from '@alfalab/icons-glyph/InformationCircleMIcon';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { PATHS } from '#/src/constants/routing';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToHome, goToPaymentsSchedulePage } from '#/src/ducks/app/actions';
import { getCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isMksPermissionsFetchingSelector } from '#/src/ducks/mks-permissions/selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { checkSignedDocumentsStart } from '#/src/ducks/signed-documents/actions';
import { currentSignedDocumentsSelector } from '#/src/ducks/signed-documents/selectors';
import { parentCreditSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-mmb.selectors';
import { type SignedDocument } from '#/src/types/signed-document';
import { downloadSignedBinaryFileByExtension } from '#/src/utils/binary-download';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { LoadingFailed } from '../../components/loading-failed';
import { BLUETINT_COLORS } from '../app/app';
import { Header } from '../header';

import { ConditionsTab } from './business-installment-page-conditions-tab';
import { DebtTab } from './business-installment-page-debt-tab';
import { PaymentInfo } from './business-installment-page-payment-info';

import './business-installment-page.css';

export const cn = createCn('business-installment-container');

const enum TABS {
    DEBT = 'DEBT',
    CONDITIONS = 'CONDITIONS',
}

export const BusinessInstallmentPage = () => {
    const dispatch = useDispatch();
    const currentTime = useSelector(currentTimeSelector);
    const currentBusinessCredit = useSelector(parentCreditSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const signedDocuments = useSelector(currentSignedDocumentsSelector);
    const isFetchingMksPermissions = useSelector(isMksPermissionsFetchingSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);

    const [selectedId, setSelectedId] = useState<IDType>(TABS.DEBT);
    const [showPaymentInfo, setShowPaymentInfo] = useState(false);

    const isFetching = isCreditProductsFetching || isFetchingMksPermissions;
    const isError =
        !isFetching &&
        (checkIsFaultyCreditProduct(currentBusinessCredit) || isGetCreditProductsError);

    const filteredDocuments = useMemo(() => {
        if (!signedDocuments.length) return;

        return signedDocuments.filter((document) => !!document?.body?.data);
    }, [signedDocuments]);

    const refreshCreditProducts = () => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
    };

    const handleBackBtnClick = () => {
        dispatch(goToHome());
    };

    const togglePaymentInfo = () => {
        setShowPaymentInfo((value) => !value);
    };

    const redirectToPaymentsSchedule = () => {
        if (currentBusinessCredit?.docNumber) {
            dispatch(
                goToPaymentsSchedulePage({
                    docNumber: currentBusinessCredit.docNumber,
                    path: PATHS.PAYMENTS_SCHEDULE,
                }),
            );
        }
    };

    const handleDownloadButtonClick = (content: SignedDocument['body'], name: string) => {
        downloadSignedBinaryFileByExtension(content, name, 'pdf');
    };

    const getCurrentTab = (tab: IDType) => {
        switch (tab) {
            case TABS.CONDITIONS:
                return (
                    <ConditionsTab
                        isFetching={isFetching}
                        toDate={currentBusinessCredit?.requisites?.toDate}
                        fromDate={currentBusinessCredit?.requisites?.fromDate}
                        debtAmount={currentBusinessCredit?.requisites?.sum}
                    />
                );
            case TABS.DEBT:
            default:
                return (
                    <DebtTab
                        totalLoanSumToPay={currentBusinessCredit?.summary?.totalLoanAndFine}
                        isFetching={isFetching}
                    />
                );
        }
    };

    useEffect(() => {
        if (currentBusinessCredit?.productDocs?.length) {
            dispatch(checkSignedDocumentsStart(currentBusinessCredit?.productDocs));
        }
    }, [currentBusinessCredit, dispatch]);

    return (
        <ProductPageWrapper>
            <Header title='Бизнес-рассрочка' onBackBtnClick={handleBackBtnClick} />
            {isError ? (
                <LoadingFailed refresh={refreshCreditProducts} />
            ) : (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Status dataTestId='status' color='grey'>
                            ПЛАТЁЖ{' '}
                            {dateToCustomFormat(
                                currentTime,
                                currentBusinessCredit?.payDebtTillDate,
                                DAY_MONTH_FORMAT,
                            )}
                        </Status>
                        <GenericWrapper
                            className={cn('total-to-pay')}
                            column={false}
                            justifyContent='between'
                            alignItems='baseline'
                        >
                            <div>
                                <Typography.Title
                                    dataTestId='total-to-pay'
                                    tag='div'
                                    view='medium'
                                    font='system'
                                >
                                    <AmountPure
                                        transparentMinor={false}
                                        value={currentBusinessCredit?.summary?.totalToPay}
                                    />
                                </Typography.Title>
                                <Gap size='2xs' />
                                <Typography.Text
                                    tag='div'
                                    view='primary-small'
                                    weight='regular'
                                    color='secondary'
                                >
                                    Спишем со счёта ···{currentBusinessCredit?.shortAccountNumber}
                                </Typography.Text>
                            </div>
                            <IconButton
                                icon={InformationCircleMIcon}
                                style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                                onClick={togglePaymentInfo}
                                size='xxs'
                                dataTestId='total-to-pay-button'
                            />
                        </GenericWrapper>
                        <Gap size='3xs' />
                        <SegmentedControl
                            className={cn('segment-control')}
                            onChange={setSelectedId}
                            selectedId={selectedId}
                        >
                            <Segment id={TABS.DEBT} title='Задолженность' />
                            <Segment id={TABS.CONDITIONS} title='Условия' />
                        </SegmentedControl>
                    </Skeleton>
                    <div className={cn('tab-container')}>{getCurrentTab(selectedId)}</div>
                    <Skeleton visible={isFetching}>
                        <div
                            data-test-id='payments-schedule-button'
                            className={cn('button')}
                            onClick={redirectToPaymentsSchedule}
                        >
                            <SuperEllipse size={48} className={cn('button-icon')}>
                                <CalendarMIcon color='black' />
                            </SuperEllipse>
                            <Typography.Text view='component'>График платежей</Typography.Text>
                        </div>
                    </Skeleton>
                    <Gap size='2xl' />
                    {filteredDocuments?.map((signedDocument) => {
                        const { name = '', body: content } = signedDocument;

                        return (
                            <React.Fragment key={name}>
                                <Skeleton visible={isFetching}>
                                    <div
                                        data-test-id='payments-oferta-button'
                                        className={cn('button')}
                                        onClick={() => handleDownloadButtonClick(content, name)}
                                    >
                                        <SuperEllipse size={48} className={cn('button-icon')}>
                                            <DocumentPdfMIcon color='black' />
                                        </SuperEllipse>
                                        <Typography.Text view='component'>{name}</Typography.Text>
                                    </div>
                                </Skeleton>
                                <Gap size='2xl' />
                            </React.Fragment>
                        );
                    })}
                    <BottomSheet
                        title='Из чего состоит просрочка'
                        open={showPaymentInfo}
                        onClose={togglePaymentInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='s' block={true} onClick={togglePaymentInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <PaymentInfo
                            totalFine={currentBusinessCredit?.summary?.totalFine}
                            totalDebtToPay={currentBusinessCredit?.summary?.totalLoanSumToPay}
                        />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
