import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';

type TDebtTab = {
    totalLoanSumToPay?: TAmount;
    isFetching?: boolean;
};

export const DebtTab = ({ totalLoanSumToPay, isFetching = false }: TDebtTab) => (
    <Skeleton visible={isFetching}>
        <Typography.Text tag='span' view='primary-small' color='secondary'>
            Основной долг
        </Typography.Text>

        <Gap size='2xs' />

        <Typography.Text dataTestId='debt' tag='span' view='component' weight='medium'>
            <AmountPure transparentMinor={false} value={totalLoanSumToPay} />
        </Typography.Text>
    </Skeleton>
);
