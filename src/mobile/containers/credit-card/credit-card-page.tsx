/* eslint-disable complexity */
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { CircularProgressBar } from '@alfalab/core-components/circular-progress-bar';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { ProgressBar } from '@alfalab/core-components/progress-bar';
import { Typography } from '@alfalab/core-components/typography';
import * as vars from '@alfalab/core-components/vars';
import { BanknotesLineMIcon } from '@alfalab/icons-glyph/BanknotesLineMIcon';
import { ChevronDownMIcon } from '@alfalab/icons-glyph/ChevronDownMIcon';
import { ChevronRightMIcon } from '@alfalab/icons-glyph/ChevronRightMIcon';
import { ClockCoinLineMIcon } from '@alfalab/icons-glyph/ClockCoinLineMIcon';
import { ClockCrossLineMIcon } from '@alfalab/icons-glyph/ClockCrossLineMIcon';
import { InformationCircleLineMIcon } from '@alfalab/icons-glyph/InformationCircleLineMIcon';
import { LockClosedCircleMIcon } from '@alfalab/icons-glyph/LockClosedCircleMIcon';
import { LockClosedMIcon } from '@alfalab/icons-glyph/LockClosedMIcon';
import { ReceiptLineMIcon } from '@alfalab/icons-glyph/ReceiptLineMIcon';
import { pluralize } from '@alfalab/utils';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';
import { BannerWrapperMobile } from 'arui-private/banner-wrapper-mobile';

import AmountPure from '#/src/components/ui/amount-pure';
import { ECreditCardPaymentStatus } from '#/src/constants/credit-products';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { BULL, NBSP } from '#/src/constants/unicode-symbols';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { externalRedirectRublePayment } from '#/src/ducks/settings/selectors';
import { parentCreditCardSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-card-mmb.selectors';
import { dateToCustomFormat } from '#/src/utils/date';

import { PATH_CREDIT_CARD_INFO } from '../../view-utils/rounting';
import { Header } from '../header';

import { MinPaymentField } from './min-payment-field';
import { PaymentNotification } from './payment-notification';

import './credit-card-page.css';

export const cn = createCn('credit-card-page');

export const CreditCardPage = () => {
    const history = useHistory();
    const linkRublePayment = useSelector(externalRedirectRublePayment);
    const creditCard = useSelector(parentCreditCardSelector);

    const [showFineDebtModal, setShowFineDebtModal] = useState(false);
    const [showOverdueDebtModal, setShowOverdueDebtModal] = useState(false);
    const [showLimitBlockedModal, setShowLimiBlockedModal] = useState(false);

    const dateNowSeconds = Date.now() / 1000;

    const progressBarValue =
        ((creditCard?.availableAmount?.amount || 0) * 100) /
        (creditCard?.requisites?.limit?.amount || 1);

    const debtStatus = creditCard?.frontParams?.debtStatus;

    const minPaymentCalculatedStatus =
        debtStatus === ECreditCardPaymentStatus.MIN_DEBT_PAYMENT_CALCULATED;
    const twoOrMoreMinPaymentsOverdue =
        debtStatus === ECreditCardPaymentStatus.TWO_OR_MORE_MIN_DEBT_PAYMENTS_OVERDUE;
    const fiveDaysTillMinPaymentStatus =
        debtStatus === ECreditCardPaymentStatus.FIVE_DAYS_TILL_MIN_PAYMENT_DAY;
    const minPaymentInCalculation =
        debtStatus === ECreditCardPaymentStatus.MIN_DEBT_PAYMENT_IN_CALCULATION;
    const secondMinPaymentOverdueStatus =
        debtStatus === ECreditCardPaymentStatus.SECOND_MIN_DEBT_PAYMENT_OVERDUE;

    const gracePeriodDate =
        creditCard?.gracePeriod?.gracePeriodToDate?.seconds ||
        creditCard?.gracePeriod?.gracePeriodActualEndDate?.seconds;

    const gracePeriodEnded = (gracePeriodDate || 0) < dateNowSeconds;
    const gracePeriodDateSeconds = gracePeriodDate || 0;

    const gracePeriodSeconds = gracePeriodDateSeconds - dateNowSeconds;
    const gracePeriodDays = Math.ceil(gracePeriodSeconds / 86400);

    const gracePeriodTo = dateToCustomFormat(
        new Date(),
        { seconds: gracePeriodDate },
        DAY_MONTH_FORMAT,
    );

    const debtToPay = creditCard?.debts?.loan?.debtToPay?.amount;

    const showTotalInterestSumToPay = creditCard?.debts?.interest?.interestToPay?.amount;

    const fineDebt =
        (creditCard?.debts?.loan?.fineDebt?.amount || 0) +
        (creditCard?.debts?.interest?.fineInterest?.amount || 0);

    const overdue =
        (creditCard?.debts?.loan?.overdueDebt?.amount || 0) +
        (creditCard?.debts?.interest?.overdueInterest?.amount || 0);

    const agreementEnded = Date.now() - (creditCard?.requisites?.toDate?.seconds || 0) * 1000 > 0;

    const toDateSeconds = (creditCard?.requisites?.toDate?.seconds || 0) - Date.now() / 1000;
    const toDateDays = Math.ceil(toDateSeconds / 86400);
    const isToDateEnded = toDateDays <= 0;
    const isToDateSoonEnd = toDateDays > 0 && toDateDays <= 30;

    const minPaymentCalculated = minPaymentCalculatedStatus || fiveDaysTillMinPaymentStatus;

    const minDebt = creditCard?.debts?.loan?.minDebtToPay?.amount || 0;
    const minInterest = creditCard?.debts?.interest?.minInterestToPay?.amount || 0;
    const minPayment = gracePeriodEnded ? minDebt + minInterest : minDebt;

    const minPaymentPaidOff = minPayment <= 0;

    const overduePaidOff = overdue <= 0;

    const calculationDateLessThanGracePeriod =
        (creditCard?.debts?.loan?.minPayDebtCalculationDate?.seconds || 0) < (gracePeriodDate || 0);
    const calculationDateEqualGracePeriod =
        (creditCard?.debts?.loan?.minPayDebtCalculationDate?.seconds || 0) ===
        (gracePeriodDate || 0);

    // condition number соответсвует макету в документации.
    const conditionTwo =
        minPaymentInCalculation && calculationDateLessThanGracePeriod && !gracePeriodEnded;

    const conditionThree =
        minPaymentCalculated && calculationDateLessThanGracePeriod && !gracePeriodEnded;

    const conditionFour =
        minPaymentInCalculation && calculationDateEqualGracePeriod && !gracePeriodEnded;

    const conditionSix = minPaymentInCalculation && gracePeriodEnded;

    const conditionSeven = minPaymentCalculated && gracePeriodEnded;

    const conditionEight = secondMinPaymentOverdueStatus && gracePeriodEnded && minPaymentPaidOff;

    const conditionNine =
        secondMinPaymentOverdueStatus && gracePeriodEnded && !minPaymentPaidOff && !overduePaidOff;

    const conditionTen =
        secondMinPaymentOverdueStatus && gracePeriodEnded && !minPaymentPaidOff && overduePaidOff;

    const conditionEleven = twoOrMoreMinPaymentsOverdue && gracePeriodEnded;

    const limitBlocked = conditionEight || conditionNine || conditionTen || conditionEleven;

    const redirectToConditionsPage = () => {
        history.push(
            `${PATH_CREDIT_CARD_INFO}${qs.stringify(
                {
                    docNumber: creditCard?.docNumber,
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    const handleLimitBlockClick = () => {
        if (limitBlocked && !agreementEnded) {
            setShowLimiBlockedModal(true);
        }
    };

    const limiBlockedContent = limitBlocked ? (
        <GenericWrapper grow={false} className={cn('limit-blocked-icon')} alignItems='center'>
            <LockClosedCircleMIcon color={vars.colorDarkStatusNegative} />
            <Typography.Text tag='span' view='primary-small' color='negative'>
                Недоступно
            </Typography.Text>
        </GenericWrapper>
    ) : (
        <Typography.Text tag='span' view='primary-small' color='secondary'>
            Вам доступно
        </Typography.Text>
    );

    const gracePeriodNotification = gracePeriodEnded ? (
        <React.Fragment>
            <Typography.Text view='component'>Закончился</Typography.Text>
            <Gap size='s' />
            <Typography.Text tag='div' view='primary-small' color='secondary'>
                Возобновится после погашения всей задолженности
            </Typography.Text>
        </React.Fragment>
    ) : (
        <Typography.Text view='component'>
            Ещё {gracePeriodDays}
            {NBSP}
            {pluralize(gracePeriodDays, 'день', 'дня', 'дней')}
        </Typography.Text>
    );

    const deadLine = creditCard?.gracePeriod?.gracePeriodDeadLine || 60;

    const circlularProgressBarValue = gracePeriodEnded ? 100 : (gracePeriodDays * 100) / deadLine;
    const progressStrokeColorValue = gracePeriodEnded
        ? vars.colorDarkStatusNegative
        : gracePeriodDays <= 7
          ? vars.colorDarkTextAttention
          : vars.colorDarkTextPositive;

    const interestToPayColor =
        conditionEight || conditionNine || conditionTen || conditionEleven ? 'negative' : 'primary';

    const fineDebtStatusLabel = `${creditCard?.rate?.overdueDebtRate}% в день на сумму просроченного платежа`;

    const overdueDebtModalTitle = conditionEleven
        ? 'Из чего состоит просрочка'
        : `Есть задолженность ${
              overdue / (creditCard?.debts?.loan?.overdueDebt?.currency?.minorUnits || 100)
          }${NBSP}${creditCard?.debts?.loan?.overdueDebt?.currency?.unicodeSymbol}`;

    const limitBlockedValue =
        conditionEleven || conditionNine || conditionTen
            ? creditCard?.summary?.totalToPay
            : {
                  amount: overdue + minPayment,
                  currency: creditCard?.debts?.loan?.overdueDebt?.currency,
              };

    const hideGracePeriod = !debtStatus || agreementEnded;

    const shortAcountButton = (
        <Button href={linkRublePayment} view='primary' block={true}>
            Пополнить счёт {`${BULL}${BULL}${creditCard?.shortAccountNumber}`}
        </Button>
    );

    return (
        <ProductPageWrapper>
            <div className={cn('page-wrapper')}>
                <div>
                    <Header title='Кредитная карта' />
                    <div className={cn('available-amount-container')}>
                        {limiBlockedContent}
                        <div className={cn('limit-container')} onClick={handleLimitBlockClick}>
                            <Typography.TitleMobile
                                weight='semibold'
                                color={limitBlocked ? 'secondary' : 'primary'}
                                tag='div'
                                view='large'
                                font='system'
                                dataTestId='available-amount'
                            >
                                <AmountPure
                                    transparentMinor={false}
                                    view='withZeroMinorPart'
                                    value={creditCard?.availableAmount}
                                />
                            </Typography.TitleMobile>
                        </div>
                        <ProgressBar
                            size='s'
                            value={progressBarValue}
                            view={limitBlocked ? 'secondary' : 'positive'}
                            dataTestId='progress'
                            className={cn('progress-bar')}
                        />
                        <Typography.Text tag='span' view='primary-small' color='secondary'>
                            из&nbsp;
                            <AmountPure
                                transparentMinor={false}
                                view='withZeroMinorPart'
                                value={creditCard?.requisites?.limit}
                            />
                        </Typography.Text>
                    </div>

                    <PaymentNotification
                        minPayment={minPayment}
                        creditCard={creditCard}
                        conditionThree={conditionThree}
                        conditionFour={conditionFour}
                        conditionSix={conditionSix}
                        conditionSeven={conditionSeven}
                        conditionEight={conditionEight}
                        conditionNine={conditionNine}
                        conditionTen={conditionTen}
                        conditionEleven={conditionEleven}
                    />

                    {!hideGracePeriod && (
                        <BackgroundPlate
                            data-test-id='grace-period'
                            className={cn('grace-period-container')}
                            view={BackgroundPlateView.Secondary}
                        >
                            <CircularProgressBar
                                value={circlularProgressBarValue}
                                contentColor='secondary'
                                size={48}
                                progressStrokeColor={progressStrokeColorValue}
                            >
                                <Typography.Text tag='div' view='secondary-large' weight='bold'>
                                    {gracePeriodEnded ? 0 : gracePeriodDays}
                                </Typography.Text>
                            </CircularProgressBar>
                            <div>
                                <Typography.Text tag='div' view='primary-small' color='secondary'>
                                    Льготный период до {gracePeriodTo}
                                </Typography.Text>
                                <Gap size='3xs' />
                                {gracePeriodNotification}
                            </div>
                        </BackgroundPlate>
                    )}

                    {!!overdue && (
                        <BannerWrapperMobile
                            data-test-id='overdue'
                            className={cn('wrapper')}
                            showBottomButton={false}
                            onClick={() => setShowOverdueDebtModal(true)}
                            mainIcon={
                                <SuperEllipse size={48} border={true}>
                                    <ClockCrossLineMIcon color={vars.colorDarkNeutral100Hover} />
                                </SuperEllipse>
                            }
                            rightIconElement={
                                <ChevronDownMIcon
                                    className={cn('right-icon')}
                                    color={vars.colorLightNeutral700}
                                />
                            }
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Просрочено
                            </Typography.Text>
                            <Typography.Text view='component' color='accent'>
                                <AmountPure
                                    transparentMinor={false}
                                    value={{
                                        amount: overdue,
                                        currency: creditCard?.debts?.loan?.overdueDebt?.currency,
                                    }}
                                />
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    <MinPaymentField
                        linkRublePayment={linkRublePayment}
                        creditCard={creditCard}
                        conditionThree={conditionThree}
                        conditionFour={conditionFour}
                        conditionSix={conditionSix}
                        conditionSeven={conditionSeven}
                        conditionEight={conditionEight}
                        conditionNine={conditionNine}
                        conditionTen={conditionTen}
                        conditionEleven={conditionEleven}
                        conditionTwo={conditionTwo}
                        minPaymentInCalculation={
                            minPaymentInCalculation ||
                            (secondMinPaymentOverdueStatus && minPaymentPaidOff)
                        }
                        gracePeriodEnded={gracePeriodEnded}
                        minPayment={minPayment}
                    />

                    {!!debtToPay && (
                        <BannerWrapperMobile
                            className={cn('wrapper')}
                            showBottomButton={false}
                            mainIcon={
                                <SuperEllipse size={48} border={true}>
                                    <BanknotesLineMIcon color={vars.colorDarkNeutral100Hover} />
                                </SuperEllipse>
                            }
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Основной долг
                            </Typography.Text>
                            <Typography.Text view='component'>
                                <AmountPure
                                    transparentMinor={false}
                                    value={creditCard?.debts?.loan?.debtToPay}
                                />
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    {!!showTotalInterestSumToPay && (
                        <BannerWrapperMobile
                            className={cn('wrapper')}
                            showBottomButton={false}
                            mainIcon={
                                <SuperEllipse size={48} border={true}>
                                    <ClockCoinLineMIcon color={vars.colorDarkNeutral100Hover} />
                                </SuperEllipse>
                            }
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Проценты
                            </Typography.Text>
                            <Typography.Text view='component' color={interestToPayColor}>
                                <AmountPure
                                    transparentMinor={false}
                                    value={creditCard?.debts?.interest?.interestToPay}
                                />
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    {!!fineDebt && (
                        <BannerWrapperMobile
                            className={cn('wrapper')}
                            showBottomButton={false}
                            onClick={() => setShowFineDebtModal(true)}
                            rightIconElement={
                                <ChevronDownMIcon
                                    className={cn('right-icon')}
                                    color={vars.colorLightNeutral700}
                                />
                            }
                            mainIcon={
                                <SuperEllipse size={48} border={true}>
                                    <ReceiptLineMIcon color={vars.colorDarkNeutral100Hover} />
                                </SuperEllipse>
                            }
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Неустойка
                            </Typography.Text>
                            <Typography.Text view='component'>
                                <AmountPure
                                    transparentMinor={false}
                                    value={creditCard?.summary?.totalFine}
                                />
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    <BackgroundPlate view={BackgroundPlateView.Secondary} className={cn('plate')}>
                        <BannerWrapperMobile
                            data-test-id='conditions-button'
                            showBottomButton={false}
                            mainIcon={
                                <InformationCircleLineMIcon color={vars.colorDarkNeutral100Hover} />
                            }
                            rightIconElement={
                                <ChevronRightMIcon color={vars.colorLightNeutral700} />
                            }
                            onClick={redirectToConditionsPage}
                        >
                            <Typography.Text color='primary' view='primary-medium'>
                                Условия
                            </Typography.Text>
                            {isToDateEnded && (
                                <Typography.Text color='negative'>Договор истёк</Typography.Text>
                            )}
                            {isToDateSoonEnd && (
                                <Typography.Text color='attention'>
                                    Договор истекает через {toDateDays}
                                    {NBSP}
                                    {pluralize(toDateDays, 'день', 'дня', 'дней')}
                                </Typography.Text>
                            )}
                        </BannerWrapperMobile>
                    </BackgroundPlate>
                </div>

                {!!debtStatus && (
                    <Button
                        dataTestId='payment-button'
                        href={linkRublePayment}
                        size='m'
                        view='primary'
                        block={true}
                        className={cn('payment-button')}
                    >
                        Пополнить счёт {`${BULL}${BULL}${creditCard?.shortAccountNumber}`}
                    </Button>
                )}
            </div>

            <BottomSheet
                hasCloser={true}
                open={showFineDebtModal}
                dataTestId='fine-debt-modal'
                onClose={() => setShowFineDebtModal(false)}
                actionButton={
                    <Button size='m' block={true} onClick={() => setShowFineDebtModal(false)}>
                        Понятно
                    </Button>
                }
                title='Что входит в неустойку'
            >
                <Gap size={8} />
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Просроченный основной долг
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='component'>
                    <AmountPure
                        transparentMinor={false}
                        value={creditCard?.debts?.loan?.fineDebt}
                    />
                </Typography.Text>
                <Gap size={16} />
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Просроченные проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='component'>
                    <AmountPure
                        transparentMinor={false}
                        value={creditCard?.debts?.interest?.fineInterest}
                    />
                </Typography.Text>
                <Gap size={16} />
                <Typography.Text view='component-secondary' color='secondary'>
                    {fineDebtStatusLabel}
                </Typography.Text>
                <Gap size={8} />
            </BottomSheet>

            <BottomSheet
                hasCloser={true}
                open={showOverdueDebtModal}
                dataTestId='overdue-modal'
                onClose={() => setShowOverdueDebtModal(false)}
                actionButton={shortAcountButton}
                title={overdueDebtModalTitle}
            >
                <Gap size={8} />
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Просроченный основной долг
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='component'>
                    <AmountPure
                        transparentMinor={false}
                        value={creditCard?.debts?.loan?.overdueDebt}
                    />
                </Typography.Text>
                <Gap size={16} />
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Просроченные проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='primary-medium'>
                    <AmountPure
                        transparentMinor={false}
                        value={creditCard?.debts?.interest?.overdueInterest}
                    />
                </Typography.Text>
                <Gap size='xl' />
                {conditionEleven ? (
                    <React.Fragment>
                        <Typography.Text tag='div' color='secondary' view='primary-small'>
                            Всего
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text view='primary-medium'>
                            <AmountPure
                                transparentMinor={false}
                                value={{
                                    amount: overdue,
                                    currency: creditCard?.debts?.loan?.overdueDebt?.currency,
                                }}
                            />
                        </Typography.Text>
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        <Typography.Text view='primary-medium'>
                            На основной долг уже начисляются повышенные проценты, а на просроченный
                            платёж неустойка {creditCard?.rate?.overdueDebtRate}% в день.
                        </Typography.Text>
                        <Gap size='xl' />
                        <Typography.Text view='primary-medium'>
                            Пополните счёт, чтобы погасить задолженность.
                        </Typography.Text>
                    </React.Fragment>
                )}
                <Gap size={8} />
            </BottomSheet>

            <BottomSheet
                hasCloser={true}
                open={showLimitBlockedModal}
                onClose={() => setShowLimiBlockedModal(false)}
            >
                <SuperEllipse size={64} backgroundColor={vars.colorLightStatusMutedNegative}>
                    <LockClosedMIcon color={vars.colorDarkGraphicNegative} />
                </SuperEllipse>
                <Gap size={16} />
                <Typography.TitleMobile tag='div' view='medium'>
                    {conditionTen ? 'Есть задолженность' : 'Есть просроченная задолженность'}
                </Typography.TitleMobile>
                <Gap size={12} />
                <Typography.Text view='primary-medium'>
                    Внесите{NBSP}
                    <AmountPure transparentMinor={false} value={limitBlockedValue} />
                    {NBSP}— и лимит снова будет доступен
                </Typography.Text>
                <Gap size={24} />
                {shortAcountButton}
            </BottomSheet>
        </ProductPageWrapper>
    );
};
