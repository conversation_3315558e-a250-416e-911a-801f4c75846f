import React, { useState } from 'react';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Typography } from '@alfalab/core-components/typography';
import * as vars from '@alfalab/core-components/vars';
import ChartPieLineMIcon from '@alfalab/icons-glyph/ChartPieLineMIcon';
import ChevronDownMIcon from '@alfalab/icons-glyph/ChevronDownMIcon';
import { BannerWrapperMobile } from 'arui-private/banner-wrapper-mobile';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { BULL } from '#/src/constants/unicode-symbols';
import { type MappedCreditCard } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { cn } from './credit-card-page';

export const MinPaymentField = ({
    linkRublePayment,
    creditCard,
    conditionThree,
    conditionFour,
    conditionEleven,
    conditionSeven,
    conditionEight,
    conditionNine,
    conditionTen,
    conditionTwo,
    conditionSix,
    gracePeriodEnded,
    minPayment,
}: {
    linkRublePayment: string;
    creditCard: MappedCreditCard;
    conditionTwo: boolean;
    conditionThree: boolean;
    conditionFour: boolean;
    conditionSix: boolean;
    conditionEleven: boolean;
    conditionSeven: boolean;
    conditionEight: boolean;
    conditionNine: boolean;
    conditionTen: boolean;
    minPaymentInCalculation: boolean;
    gracePeriodEnded: boolean;
    minPayment: number;
}) => {
    const [showMinPaymentDebtModal, setShowMinPaymentDebtModal] = useState(false);

    const shortAccountNumber = creditCard?.shortAccountNumber;
    const totalInterestSumToPay = creditCard?.debts?.interest?.minInterestToPay;
    const percentage = creditCard?.requisites?.mainDebtShare;
    const percentageRate = creditCard?.rate?.overdueDebtRate;

    const calculationDate = dateToCustomFormat(
        new Date(),
        creditCard?.debts?.loan?.minPayDebtCalculationDate,
        DAY_MONTH_FORMAT,
    );

    const requisitesToDate = dateToCustomFormat(
        new Date(),
        creditCard?.debts?.loan?.minPayDebtTillDate,
        DAY_MONTH_FORMAT,
    );

    if (conditionEleven) return null;

    const minPaymentTitle = gracePeriodEnded ? (
        <Typography.TitleMobile tag='div' view='small' weight='semibold' font='system'>
            Минимальный платеж{' '}
            <AmountPure
                transparentMinor={false}
                value={{
                    amount: minPayment,
                    currency: creditCard?.debts?.loan?.minDebtToPay?.currency,
                }}
            />
        </Typography.TitleMobile>
    ) : (
        'Минимальный платёж'
    );

    let content = null;
    let modalContent = null;

    if (conditionTwo || conditionFour || conditionSix || conditionEight) {
        const color = conditionEight ? 'attention' : 'primary';

        content = (
            <React.Fragment>
                <Typography.Text color='secondary' view='primary-small'>
                    Минимальный платеж
                </Typography.Text>
                <Typography.Text color={color} view='primary-medium'>
                    Рассчитаем {calculationDate}
                </Typography.Text>
            </React.Fragment>
        );

        if (conditionTwo) {
            modalContent = (
                <Typography.Text view='primary-medium'>
                    Чтобы не платить проценты, погасите основной долг после внесения минимального
                    платежа, пока действует льготный период
                </Typography.Text>
            );
        }
    }

    const minPaymentField = (
        <React.Fragment>
            <Typography.Text color='secondary' view='primary-small'>
                Минимальный платеж
            </Typography.Text>
            <Typography.Text view='primary-medium'>
                <AmountPure
                    transparentMinor={false}
                    value={{
                        amount: minPayment,
                        currency: creditCard?.debts?.loan?.minDebtToPay?.currency,
                    }}
                />
            </Typography.Text>
        </React.Fragment>
    );

    const percentageField = (
        <React.Fragment>
            <Typography.Text tag='div' color='secondary' view='primary-small'>
                {percentage}% от основного долга
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text view='component'>
                <AmountPure
                    transparentMinor={false}
                    value={creditCard?.debts?.loan?.minDebtToPay}
                />
            </Typography.Text>
            <Gap size={16} />
        </React.Fragment>
    );

    if (conditionThree) {
        content = minPaymentField;

        modalContent = (
            <React.Fragment>
                {percentageField}
                <Typography.Text view='primary-medium'>
                    {percentage}% от основного долга. После погашения минимального платежа погасите
                    основной долг, чтобы не платить проценты
                </Typography.Text>
            </React.Fragment>
        );
    }

    if (conditionSeven) {
        content = minPaymentField;
        modalContent = (
            <React.Fragment>
                {percentageField}
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Начисленные проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='component'>
                    <AmountPure transparentMinor={false} value={totalInterestSumToPay} />
                </Typography.Text>
                <Gap size='m' />
                <Typography.Text view='primary-medium'>
                    После {requisitesToDate} на основной долг будут начисляться повышенные проценты,
                    а на просроченный платёж неустойка {percentageRate}% в день.
                </Typography.Text>
                <Gap size='m' />
                <Typography.Text view='primary-medium'>
                    Пополните счёт, чтобы погасить задолженность.
                </Typography.Text>
            </React.Fragment>
        );
    }

    if (conditionNine || conditionTen) {
        content = minPaymentField;
        modalContent = (
            <React.Fragment>
                {percentageField}
                <Typography.Text tag='div' color='secondary' view='primary-small'>
                    Начисленные проценты
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text view='component'>
                    <AmountPure transparentMinor={false} value={totalInterestSumToPay} />
                </Typography.Text>
            </React.Fragment>
        );
    }

    const handleMinPaymentTooltipClick = () => {
        if (modalContent) {
            setShowMinPaymentDebtModal(true);
        }
    };

    return (
        content && (
            <React.Fragment>
                <BannerWrapperMobile
                    data-test-id='min-payment-calculated'
                    className={cn('wrapper')}
                    showBottomButton={false}
                    onClick={handleMinPaymentTooltipClick}
                    mainIcon={
                        <SuperEllipse size={48} border={true}>
                            <ChartPieLineMIcon color={vars.colorDarkNeutral100Hover} />
                        </SuperEllipse>
                    }
                    rightIconElement={
                        !!modalContent && (
                            <ChevronDownMIcon
                                className={cn('right-icon')}
                                color={vars.colorLightNeutral700}
                            />
                        )
                    }
                >
                    {content}
                </BannerWrapperMobile>
                <BottomSheet
                    hasCloser={true}
                    open={showMinPaymentDebtModal}
                    onClose={() => setShowMinPaymentDebtModal(false)}
                    title={minPaymentTitle}
                    dataTestId='min-payment-modal'
                >
                    {modalContent}
                    <Gap size='xl' />
                    <Button href={linkRublePayment} view='primary' block={true}>
                        Пополнить счёт {`${BULL}${BULL}${shortAccountNumber}`}
                    </Button>
                </BottomSheet>
            </React.Fragment>
        )
    );
};
