import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import addDays from 'date-fns/addDays';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { DATE_FORMAT, formatDate } from '@alfalab/core-components/date-input';
import { Gap } from '@alfalab/core-components/gap';
import { Link } from '@alfalab/core-components/link';
import { Typography } from '@alfalab/core-components/typography';
import * as vars from '@alfalab/core-components/vars';
import { ChevronDownMIcon } from '@alfalab/icons-glyph/ChevronDownMIcon';
import { ExclamationCircleMIcon } from '@alfalab/icons-glyph/ExclamationCircleMIcon';
import { pluralize } from '@alfalab/utils';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';
import { BannerWrapperMobile } from 'arui-private/banner-wrapper-mobile';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_FORMAT, DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { BULL, NBSP } from '#/src/constants/unicode-symbols';
import { externalRedirectRublePayment } from '#/src/ducks/settings/selectors';
import { parentCreditCardSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-card-mmb.selectors';
import { dateToCustomFormat } from '#/src/utils/date';

import { Header } from '../header';

import { cn } from './credit-card-page';

export const CreditCardInfoPage = () => {
    const [showDebtModal, setShowDebtModal] = useState(false);
    const [showOverdueDebtModal, setShowOverdueDebtModal] = useState(false);
    const linkRublePayment = useSelector(externalRedirectRublePayment);
    const creditCard = useSelector(parentCreditCardSelector);
    const toDate = creditCard?.requisites?.toDate;
    const fromDate = creditCard?.requisites?.fromDate;
    const toDateSeconds = (toDate?.seconds || 0) - Date.now() / 1000;
    const toDateDays = Math.ceil(toDateSeconds / 86400);

    const toDateEnded = toDateDays <= 0;
    const toDateSoonEnd = toDateDays > 0 && toDateDays <= 30;

    const requisitesToDate = dateToCustomFormat(new Date(), toDate, DAY_MONTH_FORMAT);
    const overdueToDate = dateToCustomFormat(new Date(), toDate, DAY_MONTH_YEAR_FORMAT);

    const requisitesToDatePlusDay = dateToCustomFormat(
        new Date(),
        addDays((toDate?.seconds || 0) * 1000, 1),
        DAY_MONTH_FORMAT,
    );

    const totalToPay = (creditCard?.summary?.totalToPay?.amount || 0) / 100;

    const sumField = creditCard?.requisites?.limit?.amount;

    const gracePeriodDeadLine = creditCard?.gracePeriod?.gracePeriodDeadLine || 0;
    const actionButton = (
        <Button href={linkRublePayment} view='primary' block={true}>
            Пополнить счёт {`${BULL}${BULL}${creditCard?.shortAccountNumber}`}
        </Button>
    );

    const termLabel = useMemo(
        () =>
            !!toDate?.seconds && !!fromDate?.seconds
                ? `${formatDate(fromDate.seconds * 1000, DATE_FORMAT)} -
    ${formatDate(toDate.seconds * 1000, DATE_FORMAT)}`
                : fromDate?.seconds && `с ${formatDate(fromDate.seconds * 1000, DATE_FORMAT)}`,
        [fromDate?.seconds, toDate?.seconds],
    );

    return (
        <div className={cn('card-conditions-container')}>
            <Header title='Условия' />
            <div>
                <div>
                    {toDateEnded && (
                        <BackgroundPlate
                            onClick={() => setShowOverdueDebtModal(true)}
                            className={cn('info-notification', { error: true })}
                            view={BackgroundPlateView.Secondary}
                        >
                            <div>
                                <ExclamationCircleMIcon color={vars.colorLightStatusNegative} />
                                <Typography.Text view='component' color='primary'>
                                    Погасите задолженность
                                </Typography.Text>
                            </div>
                            <ChevronDownMIcon color={vars.colorStaticNeutral700} />
                        </BackgroundPlate>
                    )}

                    {toDateSoonEnd && (
                        <BackgroundPlate
                            onClick={() => setShowDebtModal(true)}
                            className={cn('info-notification', { warning: true })}
                            data-test-id='debt-notification'
                            view={BackgroundPlateView.Secondary}
                        >
                            <div>
                                <ExclamationCircleMIcon color={vars.colorLightStatusAttention} />
                                <Typography.Text view='component' color='primary'>
                                    Погасите задолженность до {overdueToDate} года
                                </Typography.Text>
                            </div>
                            <ChevronDownMIcon color={vars.colorStaticNeutral700} />
                        </BackgroundPlate>
                    )}

                    {!!sumField && (
                        <BannerWrapperMobile
                            className={cn('info-wrapper')}
                            showBottomButton={false}
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Сумма
                            </Typography.Text>
                            <Gap size={4} />
                            <Typography.Text view='primary-medium'>
                                <AmountPure
                                    value={{
                                        amount: sumField,
                                        currency: creditCard?.debts?.loan?.fineDebt?.currency,
                                    }}
                                    transparentMinor={false}
                                />
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}
                    {!!creditCard?.rate?.debtRate && (
                        <BannerWrapperMobile
                            className={cn('info-wrapper')}
                            showBottomButton={false}
                            data-test-id='rate'
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Ставка
                            </Typography.Text>
                            <Gap size={4} />
                            <Typography.Text view='primary-medium'>
                                {creditCard?.rate?.debtRate}% годовых
                            </Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    <BannerWrapperMobile className={cn('info-wrapper')} showBottomButton={false}>
                        <Typography.Text color='secondary' view='primary-small'>
                            Льготный период
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text data-test-id='grace' view='primary-medium'>
                            {gracePeriodDeadLine > 0
                                ? `${gracePeriodDeadLine}${NBSP}${pluralize(
                                      gracePeriodDeadLine,
                                      'день',
                                      'дня',
                                      'дней',
                                  )}`
                                : 'Закончился'}
                        </Typography.Text>
                        <Link
                            view='default'
                            underline={false}
                            rel='noopener'
                            target='_blank'
                            href='https://alfabank.ru/get-money/credit-cards/100-days/'
                        >
                            Как работает
                        </Link>
                    </BannerWrapperMobile>

                    {(!!toDate?.seconds || !!fromDate?.seconds) && (
                        <BannerWrapperMobile
                            className={cn('info-wrapper')}
                            showBottomButton={false}
                            data-test-id='term'
                        >
                            <Typography.Text color='secondary' view='primary-small'>
                                Действия договора
                            </Typography.Text>
                            <Gap size='2xs' />
                            <Typography.Text view='primary-medium'>{termLabel}</Typography.Text>
                        </BannerWrapperMobile>
                    )}

                    <BannerWrapperMobile className={cn('info-wrapper')} showBottomButton={false}>
                        <Typography.Text tag='div' color='secondary' view='primary-small'>
                            Снятие наличных
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text data-test-id='cash' view='primary-medium'>
                            Комиссия 7%, мин. 300 руб.
                        </Typography.Text>
                    </BannerWrapperMobile>

                    <BannerWrapperMobile className={cn('info-wrapper')} showBottomButton={false}>
                        <Typography.Text tag='div' color='secondary' view='primary-small'>
                            Перевод на счёт
                        </Typography.Text>
                        <Gap size={4} />
                        <Typography.Text data-test-id='transfer' view='primary-medium'>
                            3.9% от суммы
                        </Typography.Text>
                    </BannerWrapperMobile>
                </div>
                <div>
                    <Button
                        size='m'
                        className={cn('info-button')}
                        block={true}
                        dataTestId='tarrifs-button'
                        href='https://link.alfabank.ru/corp-tariffs/'
                    >
                        К тарифам
                    </Button>
                </div>
            </div>
            <BottomSheet
                hasCloser={true}
                open={showDebtModal}
                onClose={() => setShowDebtModal(false)}
                actionButton={actionButton}
                dataTestId='debt-modal'
                title={`Есть задолженность ${totalToPay}${NBSP}₽`}
            >
                <Typography.Text view='primary-medium' color='primary'>
                    Погасите её до {requisitesToDate}, чтобы не платить неустойку. С{NBSP}
                    {requisitesToDatePlusDay} на общую задолженность будет начисляться{NBSP}
                    {creditCard?.rate?.overdueDebtRate}% за каждый день просрочки
                </Typography.Text>
            </BottomSheet>
            <BottomSheet
                hasCloser={true}
                open={showOverdueDebtModal}
                onClose={() => setShowOverdueDebtModal(false)}
                actionButton={actionButton}
                dataTestId='overdue-debt-modal'
                title={`Есть просроченная задолженность ${totalToPay}${NBSP}₽`}
            >
                <Typography.Text view='primary-medium' color='primary'>
                    На неё уже начисляется неустойка {creditCard?.rate?.overdueDebtRate}% за каждый
                    день просрочки
                </Typography.Text>
            </BottomSheet>
        </div>
    );
};
