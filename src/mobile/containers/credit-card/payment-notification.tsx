import React from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { type MappedCreditCard } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { cn } from './credit-card-page';

export const PaymentNotification = ({
    minPayment,
    creditCard,
    conditionThree,
    conditionFour,
    conditionSix,
    conditionSeven,
    conditionEight,
    conditionNine,
    conditionTen,
    conditionEleven,
}: {
    minPayment: number;
    creditCard: MappedCreditCard;
    conditionThree: boolean;
    conditionFour: boolean;
    conditionSix: boolean;
    conditionSeven: boolean;
    conditionEight: boolean;
    conditionNine: boolean;
    conditionTen: boolean;
    conditionEleven: boolean;
}) => {
    const minPaymentDate = dateToCustomFormat(
        new Date(),
        creditCard?.debts?.loan?.minPayDebtTillDate,
        DAY_MONTH_FORMAT,
    );

    const debtDate = dateToCustomFormat(
        new Date(),
        creditCard?.gracePeriod?.gracePeriodToDate,
        DAY_MONTH_FORMAT,
    );

    const percentageLabel = (
        <React.Fragment>
            <Gap size={4} />
            <Typography.Text view='component' color='primary'>
                Начисляются повышенные проценты и неустойка
            </Typography.Text>
        </React.Fragment>
    );

    if (conditionEleven) {
        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container', { error: true })}
                data-test-id='payment-debt'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Задолженность
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure transparentMinor={false} value={creditCard?.summary?.totalToPay} />
                </Typography.Title>
                <Gap size={4} />
                <Typography.Text view='component' color='primary'>
                    Неустойка {creditCard?.rate?.overdueDebtRate}% за каждый день просроченной
                    задолженности
                </Typography.Text>
            </BackgroundPlate>
        );
    }

    if (conditionNine || conditionTen) {
        return (
            <BackgroundPlate
                data-test-id='payment-debt'
                className={cn('min-payment-container', { error: true })}
                view={BackgroundPlateView.Secondary}
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Задолженность
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure transparentMinor={false} value={creditCard?.summary?.totalToPay} />
                </Typography.Title>
                {percentageLabel}
            </BackgroundPlate>
        );
    }

    if (conditionEight) {
        const amountValue =
            (creditCard?.debts.loan.overdueDebt.amount || 0) +
            (creditCard?.debts.interest.overdueInterest.amount || 0) +
            (creditCard?.debts.loan?.minDebtToPay.amount || 0) +
            (creditCard?.debts.interest?.minInterestToPay?.amount || 0);
        const currency = creditCard?.debts?.loan?.minDebtToPay?.currency;

        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container', { error: true })}
                data-test-id='payment-min-debt'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Задолженность
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure
                        transparentMinor={false}
                        value={{
                            amount: amountValue,
                            currency,
                        }}
                    />
                </Typography.Title>
                {percentageLabel}
            </BackgroundPlate>
        );
    }

    if (conditionSeven) {
        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container', { warning: true })}
                data-test-id='payment-min'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Минимальный платеж
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure
                        transparentMinor={false}
                        value={{
                            amount: minPayment,
                            currency: creditCard?.debts?.loan?.minDebtToPay?.currency,
                        }}
                    />
                </Typography.Title>
                <Gap size={4} />
                <Typography.Text view='component' color='primary'>
                    Внесите до {minPaymentDate}, чтобы не платить повышенные проценты и неустойку
                </Typography.Text>
            </BackgroundPlate>
        );
    }

    if (conditionSix) {
        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container', { warning: true })}
                data-test-id='payment-total'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Общая задолженность
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure transparentMinor={false} value={creditCard?.summary?.totalToPay} />
                </Typography.Title>
                <Gap size='2xs' />
                <Typography.Text view='component' color='primary'>
                    Начисляются проценты — {creditCard?.rate?.debtRate}% годовых
                </Typography.Text>
            </BackgroundPlate>
        );
    }

    if (conditionFour) {
        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container')}
                data-test-id='payment-left-to-pay'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Осталось выплатить
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure
                        transparentMinor={false}
                        value={creditCard?.debts?.loan.debtToPay}
                    />
                </Typography.Title>
                <Gap size='2xs' />
                <Typography.Text view='component' color='primary'>
                    Внесите до {debtDate}, чтобы не платить проценты
                </Typography.Text>
            </BackgroundPlate>
        );
    }

    if (conditionThree) {
        return (
            <BackgroundPlate
                view={BackgroundPlateView.Secondary}
                className={cn('min-payment-container')}
                data-test-id='payment-min-to-pay'
            >
                <Typography.Text view='primary-small' color='secondary'>
                    Минимальный платеж
                </Typography.Text>
                <Typography.Title tag='div' view='xsmall' font='system' weight='bold'>
                    <AmountPure
                        transparentMinor={false}
                        value={{
                            amount: minPayment,
                            currency: creditCard?.debts?.loan?.minDebtToPay?.currency,
                        }}
                    />
                </Typography.Title>
                <Gap size='2xs' />
                <Typography.Text view='component' color='primary'>
                    Внесите до {minPaymentDate}, чтобы не платить проценты
                </Typography.Text>
            </BackgroundPlate>
        );
    }

    return null;
};
