.credit-card-page {
    &__limit-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--gap-2);
    }

    &__progress-bar {
        margin: var(--gap-8) var(--gap-0);
    }

    &__grace-period-container {
        display: flex;
        align-items: flex-start;
        gap: var(--gap-16);
        padding: var(--gap-16);
        background-color: var(--color-light-bg-secondary);
        margin-bottom: var(--gap-32);
    }

    &__info-notification {
        padding: var(--gap-16);
        background-color: var(--color-light-bg-secondary);
        margin-bottom: var(--gap-16);
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        box-shadow: none;

        & > div {
            display: flex;
            gap: var(--gap-16);
            align-items: center;
        }

        &_warning {
            background-color: var(--color-light-bg-attention-muted);
        }

        &_error {
            background-color: var(--color-light-bg-negative-muted);
        }
    }

    &__available-amount-container {
        margin-bottom: var(--gap-16);
    }

    &__min-payment-container {
        padding: var(--gap-16);
        background-color: var(--color-light-base-bg-secondary);
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        flex-direction: column;
        margin-bottom: var(--gap-24);

        &_warning {
            background-color: var(--color-light-bg-attention-muted);
        }

        &_error {
            background-color: var(--color-light-bg-negative-muted);
        }
    }

    &__wrapper {
        padding: var(--gap-4);
        margin-bottom: var(--gap-32);
    }

    &__info-wrapper {
        padding: var(--gap-4);
        margin-bottom: var(--gap-32);
    }

    &__plate {
        background-color: var(--color-light-bg-secondary);
        margin-bottom: var(--gap-24);
    }

    &__right-icon {
        align-self: center;
    }

    &__card-conditions-container {
        position: relative;
        height: 100%;
    }

    &__limit-blocked-icon {
        background-color: var(--color-light-bg-negative-muted);
        border-radius: var(--border-radius-8);
        width: fit-content;
        padding: var(--gap-0) var(--gap-4);
        gap: var(--gap-4);
    }

    &__page-wrapper {
        min-height: 96vh;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}
