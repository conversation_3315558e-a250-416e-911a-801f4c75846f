import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import CalendarMIcon from '@alfalab/icons-glyph/CalendarMIcon';
import ClockLineMIcon from '@alfalab/icons-glyph/ClockLineMIcon';
import InformationCircleMIcon from '@alfalab/icons-glyph/InformationCircleMIcon';
import RubMIcon from '@alfalab/icons-glyph/RubMIcon';
import { pluralize } from '@alfalab/utils';

import AmountPure from '#/src/components/ui/amount-pure/amount-pure';
import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { PATHS } from '#/src/constants/routing';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToPaymentsSchedulePage } from '#/src/ducks/app/actions';
import { getTrancheCreditProductsCountStart } from '#/src/ducks/credit-products/actions';
import {
    earlyRepaymentButtonStateSelector,
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
    parentDocNumberSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import {
    isCreditProductsTranchesFetchingSelector,
    parentTrancheSelector,
} from '#/src/ducks/credit-products/selectors/tranches.selectors';
import { isMksPermissionsFetchingSelector } from '#/src/ducks/mks-permissions/selectors';
import { getPaymentScheduleStart } from '#/src/ducks/payment-schedule/actions';
import {
    futurePaymentsSelector,
    isFetchingPaymentsErrorSelector,
    isPaymentScheduleFetchingSelector,
    payedPaymentsSelector,
    paymentScheduleDocNumberSelector,
} from '#/src/ducks/payment-schedule/selectors';
import { contextRootSelector, currentTimeSelector } from '#/src/ducks/settings/selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { LoadingFailed } from '../../components/loading-failed';
import { calculatePaymentDateAndAmount } from '../../utils/tranches';
import { BLUETINT_COLORS } from '../app/app';
import { Header } from '../header';
import { PaymentsSchedulePaymentInfo } from '../payments-schedule/payments-schedule-payment-info';

import { CreditLineTranchePageInfo } from './credit-line-tranche-page-info';
import { CreditLineTranchePageNotification } from './credit-line-tranche-page-notification';

import './credit-line-tranche-page.css';

export const cn = createCn('credit-line-tranche-page');

export const CreditLineTranchePage = () => {
    const dispatch = useDispatch();
    const history = useHistory();
    const contextRoot = useSelector(contextRootSelector);
    const currentTime = useSelector(currentTimeSelector);
    const docNumber = useSelector(parentDocNumberSelector);
    const tranche = useSelector(parentTrancheSelector);
    const payedPayments = useSelector(payedPaymentsSelector);
    const futurePayments = useSelector(futurePaymentsSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isCreditProductsTranchesFetching = useSelector(isCreditProductsTranchesFetchingSelector);
    const isPaymentScheduleFetching = useSelector(isPaymentScheduleFetchingSelector);
    const isFetchingMksPermissions = useSelector(isMksPermissionsFetchingSelector);
    const paymentScheduleDocNumber = useSelector(paymentScheduleDocNumberSelector);
    const earlyRepaymentButtonState = useSelector(earlyRepaymentButtonStateSelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const isFetchingPaymentsError = useSelector(isFetchingPaymentsErrorSelector);

    const [isStartFetching, setStartFetching] = useState(true);
    const [showTrancheInfo, setShowTrancheInfo] = useState(false);
    const [showPaymentScheduleInfo, setShowPaymentScheduleInfo] = useState(false);

    const { paymentDate, paymentAmount, paymentInterest, paymentLoan } =
        calculatePaymentDateAndAmount(tranche);

    const isFetching =
        isCreditProductsFetching ||
        isCreditProductsTranchesFetching ||
        isStartFetching ||
        isFetchingMksPermissions;
    const isError =
        !isFetching && (checkIsFaultyCreditProduct(tranche) || isGetCreditProductsError);

    const payment = {
        paymentDate: { seconds: paymentDate },
        paymentAmount,
        paymentInterest,
        paymentLoan,
    };

    const getTranches = useCallback(() => {
        dispatch(
            getTrancheCreditProductsCountStart({
                docNumber,
            }),
        );
    }, [dispatch, docNumber]);

    const handleBackBtnClick = () => {
        history.push(
            `${PATHS.CREDIT_LINE}${qs.stringify(
                {
                    docNumber,
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    const redirectToPaymentsSchedule = () => {
        if (tranche?.docNumber) {
            dispatch(
                goToPaymentsSchedulePage({
                    docNumber,
                    trancheNumber: tranche.docNumber,
                    path: PATHS.PAYMENTS_SCHEDULE,
                }),
            );
        }
    };

    const toggleTrancheInfo = () => setShowTrancheInfo((value) => !value);

    const togglePaymentScheduleInfo = () => setShowPaymentScheduleInfo((value) => !value);

    const handleOnNotificationClick = () => {
        setShowPaymentScheduleInfo(true);
    };

    const redirectEarlyPayment = () => {
        // Временное решение, пока не будет готова мобильная версия
        window.location.assign(
            `${window.location.origin}${contextRoot}${PATHS.CREDIT_LINE_TRANCHE}${qs.stringify(
                {
                    docNumber,
                    trancheNumber: tranche?.docNumber,
                    desktop: 'true',
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    useEffect(() => {
        if (isStartFetching) {
            if (!tranche) {
                getTranches();
            }
        }

        if (
            tranche?.docNumber &&
            !isPaymentScheduleFetching &&
            paymentScheduleDocNumber !== tranche.docNumber
        ) {
            dispatch(getPaymentScheduleStart(tranche?.docNumber));
        }

        setStartFetching(false);
    }, [
        dispatch,
        getTranches,
        isPaymentScheduleFetching,
        isStartFetching,
        paymentScheduleDocNumber,
        tranche,
    ]);

    return (
        <ProductPageWrapper>
            <Header title='Транш кредитной линии' onBackBtnClick={handleBackBtnClick} />
            {isError ? (
                <LoadingFailed refresh={getTranches} />
            ) : (
                <React.Fragment>
                    <Skeleton visible={isFetching} className={cn('left-to-pay-container')}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Осталось выплатить
                        </Typography.Text>
                        <Gap size={4} />
                        <GenericWrapper justifyContent='between' alignItems='center'>
                            <Typography.Title
                                dataTestId='total-debt'
                                tag='div'
                                view='medium'
                                font='system'
                            >
                                <AmountPure transparentMinor={false} value={tranche?.totalDebt} />
                            </Typography.Title>
                            <IconButton
                                style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                                icon={InformationCircleMIcon}
                                onClick={toggleTrancheInfo}
                                size='xxs'
                                dataTestId='total-debt-button'
                            />
                        </GenericWrapper>
                    </Skeleton>
                    <Gap size={4} />
                    <Skeleton
                        visible={isFetching || isPaymentScheduleFetching}
                        className={cn('left-to-pay-container')}
                    >
                        {!isFetchingPaymentsError && (
                            <Typography.Text
                                view='primary-small'
                                weight='regular'
                                color='secondary'
                                dataTestId='payments'
                            >
                                Внесено {payedPayments.length}{' '}
                                {pluralize(payedPayments.length, 'платеж', 'платежа', 'платежей')}{' '}
                                из {payedPayments.length + futurePayments.length}
                            </Typography.Text>
                        )}
                    </Skeleton>
                    <Gap size={32} />
                    <Skeleton visible={isFetching}>
                        <CreditLineTranchePageNotification onClick={handleOnNotificationClick} />
                    </Skeleton>
                    <Gap size={32} />
                    <Skeleton visible={isFetching}>
                        <GenericWrapper
                            className={cn('tranche-bill')}
                            justifyContent='start'
                            alignItems='center'
                        >
                            <div>
                                <SuperEllipse size={48} backgroundColor='black'>
                                    <RubMIcon width={48} height={48} color='white' />
                                </SuperEllipse>
                                <Gap direction='horizontal' size={16} />
                            </div>
                            <Typography.Text
                                dataTestId='tranche-number'
                                view='primary-medium'
                                weight='regular'
                            >
                                Счёт транша &bull;&bull;{tranche?.shortAccountNumber}
                            </Typography.Text>
                        </GenericWrapper>
                    </Skeleton>
                    <Gap size={32} />
                    <Skeleton visible={isFetching}>
                        <div
                            data-test-id='payments-schedule-button'
                            className={cn('button')}
                            onClick={redirectToPaymentsSchedule}
                        >
                            <SuperEllipse size={48} className={cn('button-icon')}>
                                <CalendarMIcon color='black' />
                            </SuperEllipse>
                            <Typography.Text view='component'>График платежей</Typography.Text>
                        </div>
                    </Skeleton>
                    <Gap size={32} />
                    <Skeleton visible={isFetching}>
                        {earlyRepaymentButtonState === earlyRepaymentButtonStates.enabled && (
                            <div
                                data-test-id='early-repayment-button'
                                className={cn('button')}
                                onClick={redirectEarlyPayment}
                            >
                                <GenericWrapper column={false} alignItems='center'>
                                    <SuperEllipse size={48} className={cn('button-icon')}>
                                        <ClockLineMIcon color='black' />
                                    </SuperEllipse>
                                    <Typography.Text view='component'>
                                        Погасить досрочно
                                    </Typography.Text>
                                </GenericWrapper>
                            </div>
                        )}
                    </Skeleton>
                    <BottomSheet
                        title='Условия транша'
                        open={showTrancheInfo}
                        onClose={toggleTrancheInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='m' block={true} onClick={toggleTrancheInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <CreditLineTranchePageInfo tranche={tranche} />
                    </BottomSheet>
                    <BottomSheet
                        title={`Платеж ${dateToCustomFormat(
                            currentTime,
                            paymentDate,
                            DAY_MONTH_FORMAT,
                        )}`}
                        open={showPaymentScheduleInfo}
                        onClose={togglePaymentScheduleInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='m' block={true} onClick={togglePaymentScheduleInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <PaymentsSchedulePaymentInfo tranche={tranche} payment={payment} />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
