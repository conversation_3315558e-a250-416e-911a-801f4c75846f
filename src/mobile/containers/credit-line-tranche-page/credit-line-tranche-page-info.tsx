/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { type ReactNode } from 'react';
import { useSelector } from 'react-redux';
import { TrancheStatus } from 'corp-core-credit-products-api-typescript-services';
import { type UnixEpoch } from 'thrift-services/utils';

import { Gap } from '@alfalab/core-components/gap';

import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { renderAmount } from '../payments-schedule/payments-schedule-payment-info';
import { PaymentsSchedulePaymentLabelAndValue } from '../payments-schedule/payments-schedule-payment-label-and-value';

type TCreditLineTranchePageInfo = {
    tranche: MappedTranche;
};

type TContent = {
    label: string;
    value: any;
    text?: string;
    renderFn?: (value: any) => ReactNode;
};

export const CreditLineTranchePageInfo = ({ tranche }: TCreditLineTranchePageInfo) => {
    const currentTime = useSelector(currentTimeSelector);

    const isOverdue = tranche?.requisites?.trancheStatus === TrancheStatus.OverduePayment;

    const renderDate = (value: UnixEpoch) =>
        dateToCustomFormat(currentTime, value, DAY_MONTH_YEAR_FORMAT);

    const overdueItems: TContent[] = [
        {
            label: 'Неустойка за основной долг',
            text: `По ставке ${tranche?.overdueDebtRate ?? 0}% годовых`,
            value: tranche?.fineDebt,
            renderFn: renderAmount,
        },
        {
            label: 'Неустойка за проценты',
            text: `По ставке ${tranche?.rate?.overdueInterestRate ?? 0}% годовых в день`,
            value: tranche?.fineInterest,
            renderFn: renderAmount,
        },
    ].filter(({ value }) => !!value);

    const contentItems: TContent[] = [
        {
            label: 'Общая задолженность',
            value: tranche?.totalDebt,
            renderFn: renderAmount,
        },
        {
            label: 'Основной долг',
            value: tranche?.totalLoanSum,
            renderFn: renderAmount,
        },
        {
            label: 'Проценты',
            value: tranche?.totalInterestOverdue,
            renderFn: renderAmount,
        },
        ...(isOverdue ? overdueItems : []),
        {
            label: 'Сумма транша',
            value: tranche?.sum,
            renderFn: renderAmount,
        },
        {
            label: 'Проценты',
            value: tranche?.debtRateDaily
                ? `${tranche.debtRateDaily}% в день`
                : `${tranche?.debtRate ?? 0}%`,
        },
        { label: 'Номер транша', value: tranche?.docNumber ?? '' },
        {
            label: 'Дата открытия транша',
            value: tranche?.issueDate ?? { seconds: 0 },
            renderFn: renderDate,
        },
        {
            label: 'Срок окончания транша',
            value: tranche?.toDate ?? { seconds: 0 },
            renderFn: renderDate,
        },
        {
            label: 'Счет кредитной линии',
            value: `••${tranche?.shortAccountNumber ?? ''}`,
        },
    ].filter(({ value }) => !!value);

    return (
        <React.Fragment>
            <Gap size={12} />
            {contentItems.map((item, index) => (
                <PaymentsSchedulePaymentLabelAndValue
                    key={`tranche-info-label_${index}`}
                    {...item}
                />
            ))}
        </React.Fragment>
    );
};
