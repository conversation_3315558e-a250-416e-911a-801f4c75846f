import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ContainerMIcon } from '@alfalab/icons-glyph/ContainerMIcon';

import { NBSP } from '#/src/constants/unicode-symbols';
import { getCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    creditProductsErrorSelector,
    isCreditProductsFetchingSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { creditCardsAsArraySelector } from '#/src/ducks/credit-products/selectors/creditCard.selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { creditLinesAsArraySelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-line-mmb.selectors';
import { creditsAsArraySelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-mmb.selectors';
import { overdraftsAsArraySelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/overdraft-mmb.selectors';
import { EmptyState } from '#/src/mobile/components/empty-state/empty-state';
import { LoadingFailed } from '#/src/mobile/components/loading-failed/loading-failed';
import { SkeletonView } from '#/src/mobile/components/skeleton/skeleton';
import { EErrorMessages } from '#/src/utils/errors/error-messages';

import { BLUETINT_COLORS } from '../app/app';

import { CreditProduct } from './credit-product';

import './credit-products.css';

const CreditProducts = () => {
    const dispatch = useDispatch();
    const creditCards = useSelector(creditCardsAsArraySelector);
    const overdrafts = useSelector(overdraftsAsArraySelector);
    const credits = useSelector(creditsAsArraySelector);
    const creditLines = useSelector(creditLinesAsArraySelector);
    const isCreditProductsError = useSelector(creditProductsErrorSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const [isStartFetching, setStartFetching] = useState(true);

    const isFetching = isCreditProductsFetching || isStartFetching;
    const products = [...creditCards, ...overdrafts, ...credits, ...creditLines];

    const isNoCreditProducts = !products.length;

    const refreshCreditProducts = useCallback(() => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        setStartFetching(false);
    }, [isNoCreditProducts, refreshCreditProducts]);

    if (isFetching) {
        return <SkeletonView />;
    }

    if (isCreditProductsError && isCreditProductsError !== EErrorMessages.NO_PRODUCTS) {
        return <LoadingFailed refresh={refreshCreditProducts} />;
    }

    if (isNoCreditProducts) {
        return (
            <EmptyState
                icon={<ContainerMIcon color={BLUETINT_COLORS.сolorLightIcon} />}
                title='Пока пусто'
                text={`Найдите выгодный для себя продукт на${NBSP}вкладке «Предложения»`}
            />
        );
    }

    return (
        <React.Fragment>
            {products.map((creditProduct, index) => (
                <CreditProduct
                    key={`${creditProduct.docNumber}_${index}`}
                    creditProduct={creditProduct}
                />
            ))}
        </React.Fragment>
    );
};

export { CreditProducts };
