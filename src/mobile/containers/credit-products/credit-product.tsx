import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import {
    type CreditProduct as TCreditProduct,
    type CreditProductProjectionRequest,
    PaymentStatus,
} from 'corp-core-credit-products-api-typescript-services';

import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';
import { ChevronRightMIcon } from '@alfalab/icons-glyph/ChevronRightMIcon';

import AmountPure from '#/src/components/ui/amount-pure';
import {
    ECreditCardUniversalCode,
    ECreditProducts,
    ECreditProductsCodes,
    ECreditProductsDealStatuses,
    ECreditProductsNames,
    ECreditProductsTabs,
    isOverdraftWithTranches,
} from '#/src/constants/credit-products';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { PATHS } from '#/src/constants/routing';
import {
    getTrancheClosePaymentAmountStart,
    goCreditProductPage,
} from '#/src/ducks/credit-products/actions';
import { closestTranchePaymentsSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { CREDIT_PRODUCTS_METRICS } from '#/src/metrics';
import { CreditProductBadge } from '#/src/mobile/containers/credit-products/credit-product-badge';
import { dateToCustomFormat } from '#/src/utils/date';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { BLUETINT_COLORS } from '../app/app';

import './credit-product.css';

type TCreditProductProps = {
    creditProduct: TCreditProduct;
};

const cn = createCn('credit-product-card');

const projection: CreditProductProjectionRequest = {
    withFault: true,
    withDocs: true,
};

const getProductType = (creditProduct: TCreditProduct) => {
    switch (creditProduct.requisites?.product) {
        case ECreditProductsCodes.BUSINESS_CREDIT:
            return ECreditProducts.BUSINESS_CREDIT;
        case ECreditProductsCodes.CREDIT_LINE:
            return ECreditProducts.CREDIT_LINE;
        case ECreditProductsCodes.OVERDRAFT:
            if (
                creditProduct?.requisites?.productCode?.includes(ECreditCardUniversalCode.OVERNZUC)
            ) {
                return ECreditProducts.CREDIT_CARD;
            }

            return ECreditProducts.OVERDRAFT;
        default:
            return null;
    }
};

export const CreditProduct: React.FC<TCreditProductProps> = ({ creditProduct }) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const currentTime = useSelector(currentTimeSelector);
    const organizationId = useSelector(currentOrganizationEqIdSelector);
    const closestTranchePayments = useSelector(closestTranchePaymentsSelector);

    const productType = getProductType(creditProduct);

    const payDebtTillDate = (creditProduct?.debts?.loan?.payDebtTillDate?.seconds ?? 0) * 1000;
    const totalToPay = creditProduct?.summary?.totalToPay;
    const availableAmount = creditProduct?.availableAmount;
    const limit = creditProduct?.requisites?.limit;
    const toDate = (creditProduct?.requisites?.toDate?.seconds ?? 0) * 1000;
    const totalDebt = creditProduct?.summary?.totalDebt;
    const isClosedDeal = creditProduct.requisites?.dealStatus === ECreditProductsDealStatuses.L;
    const paymentStatus = creditProduct?.requisites?.paymentStatus;

    const product = useMemo(() => {
        switch (productType) {
            case ECreditProducts.OVERDRAFT:
                let value = totalDebt;

                const withTranches = isOverdraftWithTranches(
                    creditProduct?.requisites?.productCode,
                );

                if (withTranches && creditProduct?.docNumber) {
                    if (closestTranchePayments[creditProduct.docNumber] === undefined) {
                        dispatch(
                            getTrancheClosePaymentAmountStart(
                                creditProduct.docNumber,
                                organizationId,
                                projection,
                            ),
                        );
                    } else {
                        value = closestTranchePayments[creditProduct.docNumber];
                    }
                }

                return {
                    title: ECreditProductsNames.OVERDRAFT,
                    items: [
                        {
                            label: `Вернуть до ${dateToCustomFormat(
                                currentTime,
                                withTranches ? payDebtTillDate : toDate,
                                DAY_MONTH_FORMAT,
                            )}`,
                            value,
                        },
                        { label: 'Доступно', value: availableAmount },
                    ],
                    path: PATHS.OVERDRAFT,
                };

            case ECreditProducts.CREDIT_LINE:
                return {
                    title: ECreditProductsNames.CREDIT_LINE,
                    items: [
                        { label: 'Доступно', value: availableAmount },
                        { label: 'Общий лимит', value: limit },
                    ],
                    path: PATHS.CREDIT_LINE,
                };

            case ECreditProducts.BUSINESS_CREDIT:
                return {
                    title: 'Кредит для бизнеса',
                    items: [
                        {
                            label: `Платеж ${dateToCustomFormat(
                                currentTime,
                                payDebtTillDate,
                                DAY_MONTH_FORMAT,
                            )}`,
                            value: totalToPay,
                        },
                    ],
                    path: PATHS.CREDIT,
                };
            case ECreditProducts.CREDIT_CARD:
                return {
                    title: 'Кредитная карта',
                    items: [
                        {
                            label: `Платеж ${dateToCustomFormat(
                                currentTime,
                                payDebtTillDate,
                                DAY_MONTH_FORMAT,
                            )}`,
                            value: totalToPay,
                        },
                    ],
                    path: PATHS.CREDIT_CARD,
                };
            default:
                return null;
        }
    }, [
        availableAmount,
        closestTranchePayments,
        creditProduct.docNumber,
        creditProduct?.requisites?.productCode,
        currentTime,
        dispatch,
        limit,
        organizationId,
        payDebtTillDate,
        productType,
        toDate,
        totalDebt,
        totalToPay,
    ]);

    const handleClick = () => {
        if (creditProduct.docNumber && product?.path) {
            dispatch(
                goCreditProductPage({ path: product.path, docNumber: creditProduct.docNumber }),
            );
        }
    };

    useEffect(() => {
        if (trackAlfaMetrics) {
            trackAlfaMetrics(CREDIT_PRODUCTS_METRICS.showCreditProductPane, {
                productCode: creditProduct?.requisites?.productCode,
                productType: creditProduct?.requisites?.product,
                status: isClosedDeal ? ECreditProductsTabs.closed : ECreditProductsTabs.opened,
            });
        }
    }, [
        creditProduct?.requisites?.product,
        creditProduct?.requisites?.productCode,
        isClosedDeal,
        trackAlfaMetrics,
    ]);

    if (!product) return null;

    return (
        <div className={cn()} onClick={handleClick}>
            <CreditProductBadge productType={productType} paymentStatus={paymentStatus} />
            <div className={cn('item')}>
                <Typography.Text
                    view='component'
                    color='static-primary-dark'
                    className={cn('header')}
                >
                    {product.title}
                </Typography.Text>
                <ChevronRightMIcon color={BLUETINT_COLORS.colorLightGraphicTertiary} />
            </div>
            {product.items.map(({ label, value }, index) => (
                <React.Fragment key={`item-label_${index}`}>
                    <div className={cn('item')}>
                        <Typography.Text
                            tag='span'
                            weight='regular'
                            view='primary-small'
                            style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                        >
                            {label}
                        </Typography.Text>
                        <Typography.Text
                            tag='span'
                            weight='regular'
                            view='primary-small'
                            style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                        >
                            <AmountPure bold='none' value={value} />
                        </Typography.Text>
                    </div>
                    {index !== product.items.length - 1 && <Gap size={4} />}
                </React.Fragment>
            ))}
            {productType === 'overdraft' && paymentStatus === PaymentStatus.PaymentOverdue && (
                <React.Fragment>
                    <Gap size={4} />
                    <Typography.Text
                        tag='span'
                        weight='regular'
                        view='primary-small'
                        color='negative'
                    >
                        Пока нельзя брать деньги
                    </Typography.Text>
                </React.Fragment>
            )}
        </div>
    );
};
