import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { ProgressBar } from '@alfalab/core-components/progress-bar';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';
import InformationCircleMIcon from '@alfalab/icons-glyph/InformationCircleMIcon';

import AmountPure from '#/src/components/ui/amount-pure';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToHome } from '#/src/ducks/app/actions';
import {
    getCreditProductsStart,
    getTrancheCreditProductsStart,
} from '#/src/ducks/credit-products/actions';
import {
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isCreditProductsTranchesFetchingSelector } from '#/src/ducks/credit-products/selectors/tranches.selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { parentCreditLineSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-line-mmb.selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';

import { LoadingFailed } from '../../components/loading-failed';
import { BLUETINT_COLORS } from '../app/app';
import { Header } from '../header';
import { TrancheCardSkeleton } from '../tranche-card/tranche-card-skeleton';

import { CreditLinePageConditionsTab } from './credit-line-page-conditions-tab';
import { CreditLinePageDebtInfo } from './credit-line-page-debt-info';
import { CreditLinePageTranchesTab } from './credit-line-page-tranches-tab';

import './credit-line-page.css';

export const cn = createCn('credit-line-page');

const enum TABS {
    TRANCHES = 'TRANCHES',
    CONDITIONS = 'CONDITIONS',
}

export const CreditLinePage = () => {
    const dispatch = useDispatch();
    const creditLine = useSelector(parentCreditLineSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const isFetchingTranches = useSelector(isCreditProductsTranchesFetchingSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);
    const [selectedId, setSelectedId] = useState<IDType>(TABS.TRANCHES);
    const [showDebtInfo, setShowDebtInfo] = useState(false);

    const isFetching = isCreditProductsFetching;
    const isError =
        !isFetching && (checkIsFaultyCreditProduct(creditLine) || isGetCreditProductsError);

    useEffect(() => {
        if (!isFetchingTranches) {
            dispatch(getTrancheCreditProductsStart({ isFromTab: true }));
        }
    }, []);

    const handleBackBtnClick = () => {
        dispatch(goToHome());
    };

    const refreshCreditProducts = useCallback(() => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
    }, [dispatch, isMmbCategory]);

    const toggleDebtInfo = () => setShowDebtInfo((value) => !value);

    const getCurrentTab = (tab: IDType) => {
        switch (tab) {
            case TABS.CONDITIONS:
                return (
                    <CreditLinePageConditionsTab
                        rate={creditLine?.debtRate || 0}
                        dailyRate={creditLine?.debtRateDaily}
                        toDate={creditLine?.requisites?.toDate}
                        fromDate={creditLine?.requisites?.fromDate}
                        trancheGettingDeadline={creditLine?.requisites?.trancheGettingDeadline}
                        productCode={creditLine?.requisites?.productCode || ''}
                        shortAccountNumber={creditLine?.shortAccountNumber || ''}
                        isFetching={isFetching}
                    />
                );
            case TABS.TRANCHES:
            default:
                return <CreditLinePageTranchesTab />;
        }
    };

    const progressBarValue =
        ((creditLine?.availableAmount?.amount || 0) * 100) /
        (creditLine?.requisites?.limit?.amount || 1);

    return (
        <ProductPageWrapper>
            <Header title='Кредитная линия' onBackBtnClick={handleBackBtnClick} />
            {isError ? (
                <LoadingFailed refresh={refreshCreditProducts} />
            ) : (
                <React.Fragment>
                    <Skeleton
                        visible={isFetching}
                        className={cn('available-limit-container')}
                        dataTestId='available-amount'
                    >
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Доступно
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Title tag='div' view='medium' font='system'>
                            <AmountPure
                                transparentMinor={false}
                                value={creditLine?.availableAmount}
                            />
                        </Typography.Title>
                        <Gap size={8} />
                        <ProgressBar size='s' value={progressBarValue} />
                        <Gap size={4} />
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            из&nbsp;
                            <AmountPure
                                transparentMinor={false}
                                bold='none'
                                value={creditLine?.requisites?.limit}
                            />
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={16} />
                    <Skeleton visible={isFetching}>
                        <GenericWrapper
                            column={false}
                            justifyContent='between'
                            className={cn('left-to-pay-container')}
                        >
                            <div>
                                <Typography.Text tag='div' view='primary-small' color='secondary'>
                                    Осталось выплатить
                                </Typography.Text>
                                <Typography.Text
                                    dataTestId='left-to-pay'
                                    weight='medium'
                                    view='component'
                                >
                                    <AmountPure
                                        transparentMinor={false}
                                        value={creditLine?.totalDebt}
                                    />
                                </Typography.Text>
                            </div>
                            <IconButton
                                icon={InformationCircleMIcon}
                                style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                                onClick={toggleDebtInfo}
                                size='xxs'
                                dataTestId='left-to-pay-button'
                            />
                        </GenericWrapper>
                    </Skeleton>
                    <Gap size={24} />
                    <Skeleton visible={isFetching}>
                        <SegmentedControl onChange={setSelectedId} selectedId={selectedId}>
                            <Segment
                                dataTestId='tranches'
                                id={TABS.TRANCHES}
                                title='Активные транши'
                            />
                            <Segment dataTestId='conditions' id={TABS.CONDITIONS} title='Условия' />
                        </SegmentedControl>
                    </Skeleton>
                    {isFetching ? (
                        <TrancheCardSkeleton length={3} />
                    ) : (
                        <div className={cn('tab-container')}>{getCurrentTab(selectedId)}</div>
                    )}
                    <BottomSheet
                        title='Что входит в задолженность'
                        open={showDebtInfo}
                        onClose={toggleDebtInfo}
                        hasCloser={true}
                        actionButton={
                            <Button
                                dataTestId='close-button'
                                size='m'
                                block={true}
                                onClick={toggleDebtInfo}
                            >
                                Понятно
                            </Button>
                        }
                    >
                        <CreditLinePageDebtInfo creditLine={creditLine} />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
