import React from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';
import {
    checkIsFaultyCreditProduct,
    type MappedCreditLine,
} from '#/src/utils/credit-products-mappers';

type TDebtInfo = {
    creditLine: MappedCreditLine;
    isFetching?: boolean;
};

export const CreditLinePageDebtInfo = ({ creditLine, isFetching = false }: TDebtInfo) => {
    if (checkIsFaultyCreditProduct(creditLine)) {
        return null;
    }

    const { fineDebt, fineInterest, totalDebt, totalInterestOverdue } = creditLine;

    return (
        <React.Fragment>
            <Gap size={12} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Основной долг
                </Typography.Text>
            </Skeleton>
            <Gap size={2} />
            <Skeleton visible={isFetching}>
                <Typography.Text
                    dataTestId='total-to-pay'
                    tag='span'
                    view='component'
                    weight='medium'
                >
                    <AmountPure
                        transparentMinor={false}
                        value={creditLine?.summary?.totalLoanSum}
                    />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Проценты
                </Typography.Text>
            </Skeleton>
            <Gap size={2} />
            <Skeleton visible={isFetching}>
                <Typography.Text dataTestId='interest' tag='span' view='component' weight='medium'>
                    <AmountPure transparentMinor={false} value={totalInterestOverdue} />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            {!!fineDebt?.amount && (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Неустойка за основной долг
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='debt'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            <AmountPure transparentMinor={false} value={fineDebt} />
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={24} />
                </React.Fragment>
            )}
            {!!fineInterest?.amount && (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Неустойка за проценты
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='debt-interest'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            <AmountPure transparentMinor={false} value={fineInterest} />
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={24} />
                </React.Fragment>
            )}
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Всего
                </Typography.Text>
            </Skeleton>
            <Gap size={2} />
            <Skeleton visible={isFetching}>
                <Typography.Text dataTestId='total' tag='span' view='primary-large' weight='bold'>
                    <AmountPure transparentMinor={false} value={totalDebt} />
                </Typography.Text>
            </Skeleton>
            <Gap size={16} />
        </React.Fragment>
    );
};
