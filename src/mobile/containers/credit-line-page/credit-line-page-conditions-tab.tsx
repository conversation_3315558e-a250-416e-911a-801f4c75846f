import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { type UnixEpoch } from 'thrift-services/utils';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { dateToCustomFormat } from '#/src/utils/date';

import { cn } from './credit-line-page';
import { CreditLinePageInfo } from './credit-line-page-info';

type TConditionsTab = {
    rate: number;
    dailyRate?: number | null;
    productCode: string;
    shortAccountNumber: string;
    toDate?: UnixEpoch;
    fromDate?: UnixEpoch;
    trancheGettingDeadline?: UnixEpoch;
    isFetching?: boolean;
};

export const CreditLinePageConditionsTab: React.FC<TConditionsTab> = ({
    rate,
    dailyRate,
    toDate,
    fromDate,
    trancheGettingDeadline,
    shortAccountNumber,
    productCode,
    isFetching = false,
}) => {
    const currentTime = useSelector(currentTimeSelector);
    const [showCreditLineInfo, setShowPaymentInfo] = useState(false);

    const toggleCreditLineInfo = () => {
        setShowPaymentInfo(!showCreditLineInfo);
    };

    const creditLineType = productCode.includes('VKL')
        ? 'Возобновляемая'
        : productCode.includes('UCL')
          ? 'Не возобновляемая'
          : undefined;

    return (
        <div className={cn('conditions')}>
            <Gap size={12} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Ставка
                </Typography.Text>
            </Skeleton>
            <Gap size={2} />
            <Skeleton visible={isFetching}>
                <Typography.Text dataTestId='rate' tag='span' view='component' weight='medium'>
                    {dailyRate ? `${dailyRate}% в день` : `${rate}% годовых`}
                </Typography.Text>
            </Skeleton>
            {!!fromDate && (
                <React.Fragment>
                    <Gap size={24} />
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Дата открытия
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='from-date'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            {dateToCustomFormat(currentTime, fromDate, DAY_MONTH_YEAR_FORMAT)}
                        </Typography.Text>
                    </Skeleton>
                </React.Fragment>
            )}
            {!!trancheGettingDeadline && (
                <React.Fragment>
                    <Gap size={24} />
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Выдача траншей доступна
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='deadline'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            До{' '}
                            {dateToCustomFormat(
                                currentTime,
                                trancheGettingDeadline,
                                DAY_MONTH_YEAR_FORMAT,
                            )}
                        </Typography.Text>
                    </Skeleton>
                </React.Fragment>
            )}
            {!!toDate && (
                <React.Fragment>
                    <Gap size={24} />
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Срок действия договора
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='to-date'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            До {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                        </Typography.Text>
                    </Skeleton>
                </React.Fragment>
            )}
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Счёт кредитной линии
                </Typography.Text>
            </Skeleton>
            <Gap size={2} />
            <Skeleton visible={isFetching}>
                <Typography.Text
                    dataTestId='account-number'
                    tag='span'
                    view='component'
                    weight='medium'
                >
                    Расчётный с кредитом {shortAccountNumber}
                </Typography.Text>{' '}
            </Skeleton>
            <Gap size={24} />
            {!!creditLineType && (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Typography.Text
                            tag='span'
                            view='primary-small'
                            weight='regular'
                            color='secondary'
                        >
                            Тип кредитной линии
                        </Typography.Text>
                        <Gap size={2} />
                        <Typography.Text
                            dataTestId='type'
                            tag='span'
                            view='component'
                            weight='medium'
                        >
                            {creditLineType}
                        </Typography.Text>
                    </Skeleton>
                    <Gap size={24} />
                </React.Fragment>
            )}
            <Skeleton visible={isFetching}>
                <Button
                    view='secondary'
                    block={true}
                    size='m'
                    nowrap={true}
                    onClick={toggleCreditLineInfo}
                    dataTestId='how-it-work-button'
                >
                    Как работает кредитная линия
                </Button>
            </Skeleton>
            <BottomSheet
                title='Как работает кредитная линия'
                open={showCreditLineInfo}
                onClose={toggleCreditLineInfo}
                hasCloser={true}
                actionButton={
                    <Button size='m' block={true} onClick={toggleCreditLineInfo}>
                        Понятно
                    </Button>
                }
            >
                <CreditLinePageInfo />
            </BottomSheet>
        </div>
    );
};
