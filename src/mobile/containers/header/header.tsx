import React from 'react';
import { useHistory } from 'react-router';
import { createCn } from 'bem-react-classname';

import { Gap } from '@alfalab/core-components/gap';
import { IconButton } from '@alfalab/core-components/icon-button';
import { Typography } from '@alfalab/core-components/typography';
import { IosBackMIcon } from '@alfalab/icons-glyph/IosBackMIcon';

import './header.css';

type THeaderProps = {
    title: string;
    subTitle?: string;
    rightAddon?: JSX.Element;
    onBackBtnClick?: () => void;
};

const cn = createCn('app-header-container');

const Header = ({ title, subTitle, rightAddon, onBackBtnClick }: THeaderProps) => {
    const history = useHistory();

    const handleOnBack = () => {
        if (onBackBtnClick) {
            onBackBtnClick();
        } else {
            history.goBack();
        }
    };

    return (
        <header className={cn()}>
            <IconButton
                dataTestId='back-button'
                className={cn('back-button')}
                icon={IosBackMIcon}
                onClick={handleOnBack}
            />
            <div className={cn('title')}>
                <Typography.Text view='component' weight='medium' color='static-primary-dark'>
                    {title}
                </Typography.Text>
                <Gap size={2} />
                {!!subTitle && (
                    <Typography.Text view='primary-small' weight='regular' color='secondary'>
                        {subTitle}
                    </Typography.Text>
                )}
            </div>
            {!!rightAddon && <div className={cn('right-addon')}>{rightAddon}</div>}
        </header>
    );
};

export { Header };
