import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { type Store } from 'redux';
import { createCn } from 'bem-react-classname';

import bluetintColors from '@alfalab/core-components/vars/colors-bluetint';
import { Content } from 'arui-private/content';
import CorporatePage from 'arui-private/corporate-page';
import { isBrowser } from 'arui-private/lib/window';

import HeaderFallback from '#/src/components/header-fallback';
import { type ApplicationState } from '#/src/ducks/application-state';
import { getActiveCreditCaseStart } from '#/src/ducks/attach-documents/actions';
import { getCreditOffersStart } from '#/src/ducks/credit-offers/actions';
import { getCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    hasCreditDealsViewRightsSelector,
    hasGuaranteeDealsViewRightsSelector,
    hasOffersViewRightsSelector,
} from '#/src/ducks/mks-permissions/selectors';
import { isMmbCategorySelector, organizationSelector } from '#/src/ducks/organization/selectors';
import { setCurrentTime } from '#/src/ducks/settings/actions';
import { settingsSelector } from '#/src/ducks/settings/selectors';
import { sharedUIStateSelector } from '#/src/ducks/shared/selectors';
import { currentRoleSelector } from '#/src/ducks/user/selectors';
import { START_PAGE_METRICS } from '#/src/metrics';
import { EWebVersion } from '#/src/metrics/start-page-metrics';
import { getCurrentMoscowDate } from '#/src/utils/date';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import './app.css';

type TProps = {
    store?: Store<ApplicationState>;
    history?: History;
    children?: React.ReactNode;
};

export const BLUETINT_COLORS = {
    colorLightGraphicTertiary: /--color-light-graphic-tertiary:\s+(.*?);/.exec(bluetintColors)?.[1],
    colorLightTextSecondary: /--color-light-text-secondary:\s+(.*?);/.exec(bluetintColors)?.[1],
    colorLightBorderTertiaryInverted: /--color-light-border-tertiary-inverted:\s+(.*?);/.exec(
        bluetintColors,
    )?.[1],
    colorLightGraphicSecondary: /--color-light-graphic-secondary:\s+(.*?);/.exec(
        bluetintColors,
    )?.[1],
    сolorLightIcon: '#747474',
};

const cn = createCn('mobile-content-container');

const App = ({ children }: TProps) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const currentRole = useSelector(currentRoleSelector);
    const hasOffersViewRights = useSelector(hasOffersViewRightsSelector);
    const hasCreditDealsViewRights = useSelector(hasCreditDealsViewRightsSelector);
    const hasGuaranteeDealsViewRights = useSelector(hasGuaranteeDealsViewRightsSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);

    // подключение shared-ui
    const appMenuState = useSelector(sharedUIStateSelector);
    const organization = useSelector(organizationSelector);
    const settings = useSelector(settingsSelector);

    useEffect(() => {
        const fromPage = isBrowser() && !!document.referrer ? document.referrer : 'direct link';

        if (trackAlfaMetrics) {
            trackAlfaMetrics(START_PAGE_METRICS.renderStartPage, {
                userRole: currentRole,
                fromPage,
                webVersion: EWebVersion.MOBILE_WEB,
            });
        }

        if (hasOffersViewRights) {
            dispatch(getCreditOffersStart());
        }

        if (hasCreditDealsViewRights || hasGuaranteeDealsViewRights) {
            dispatch(getCreditProductsStart({ withPaymentStatus: isMmbCategory }));
        }

        dispatch(getActiveCreditCaseStart());
        dispatch(setCurrentTime(getCurrentMoscowDate()));

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // подключение shared-ui
    const getSelectedOrganization = () => {
        const { list: organizationsList, current: currentOrganization } = organization;

        return organizationsList?.length
            ? organizationsList?.find((org) => org.eqId === currentOrganization)
            : '';
    };

    function renderHeader() {
        const isCorporateAppHeaderFallback = !appMenuState || !global?.__CorporateAppHeader;
        const CorporateAppHeader = isCorporateAppHeaderFallback
            ? HeaderFallback
            : global.__CorporateAppHeader;

        return (
            <div style={{ display: 'none' }}>
                <CorporateAppHeader
                    appMenuState={appMenuState}
                    isTrackingMetrics={false}
                    isTrackingNewMetrics={true}
                    customerSelectDisabled={false}
                    customers={organization?.list || []}
                    selectedCustomer={getSelectedOrganization()}
                    selectedMenuCode='eco/credit-products'
                    authPage={settings.authPage}
                    contextRoot={settings.contextRoot}
                    isError={settings.error}
                    ecoPageUrls=''
                    rootLink={settings.redirect.dashboard}
                    hasNavigationPane={false}
                    blockings={false}
                />
            </div>
        );
    }

    return (
        <CorporatePage theme='alfa-on-white'>
            {renderHeader()}
            <Content className={cn()} theme='alfa-on-white' disableVerticalInlinePaddings={true}>
                {children}
            </Content>
        </CorporatePage>
    );
};

export { App };
