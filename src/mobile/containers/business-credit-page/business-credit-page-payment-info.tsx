import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';
import { NBSP } from '#/src/constants/unicode-symbols';

type TPaymentInfo = {
    totalDebtToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalFine?: TAmount;
    debtRate?: number;
    isFetching?: boolean;
};

export const PaymentInfo = ({
    totalDebtToPay,
    totalInterestSumToPay,
    totalFine,
    debtRate,
    isFetching = false,
}: TPaymentInfo) => (
    <React.Fragment>
        <Gap size={12} />
        <Skeleton visible={isFetching}>
            <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                Основной долг
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text dataTestId='total-debt' tag='span' view='component' weight='medium'>
                <AmountPure transparentMinor={false} value={totalDebtToPay} />
            </Typography.Text>
        </Skeleton>
        <Gap size={24} />
        <Skeleton visible={isFetching}>
            <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                Проценты
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text dataTestId='tota-interest' tag='span' view='component' weight='medium'>
                <AmountPure transparentMinor={false} value={totalInterestSumToPay} />
            </Typography.Text>
        </Skeleton>
        <Gap size={24} />
        {!!totalFine && (
            <React.Fragment>
                <Skeleton visible={isFetching}>
                    <Typography.Text
                        tag='span'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        Неустойка
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text dataTestId='fine' tag='span' view='component' weight='medium'>
                        <AmountPure transparentMinor={false} value={totalFine} />
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text
                        tag='div'
                        view='primary-small'
                        weight='regular'
                        color='secondary'
                    >
                        По ставке {debtRate ?? 0} от неоплаченной суммы за{NBSP}каждый день
                        просрочки
                    </Typography.Text>
                </Skeleton>
                <Gap size={16} />
            </React.Fragment>
        )}
    </React.Fragment>
);
