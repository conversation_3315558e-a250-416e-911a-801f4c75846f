import React from 'react';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';

type TDebtTab = {
    totalToPay?: TAmount;
    totalInterestSumToPay?: TAmount;
    totalLoanSumToPay?: TAmount;
    isFetching?: boolean;
};

export const DebtTab = ({
    totalToPay,
    totalInterestSumToPay,
    totalLoanSumToPay,
    isFetching = false,
}: TDebtTab) => (
    <React.Fragment>
        <Skeleton visible={isFetching}>
            <Typography.Text tag='span' view='primary-small' color='secondary'>
                Основной долг
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text dataTestId='debt' tag='span' view='component' weight='medium'>
                <AmountPure transparentMinor={false} value={totalLoanSumToPay} />
            </Typography.Text>
        </Skeleton>
        <Gap size={24} />
        <Skeleton visible={isFetching}>
            <Typography.Text tag='span' view='primary-small' color='secondary'>
                Проценты
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text dataTestId='interest' tag='span' view='component' weight='medium'>
                <AmountPure transparentMinor={false} value={totalInterestSumToPay} />
            </Typography.Text>
        </Skeleton>
        <Gap size={24} />
        <Skeleton visible={isFetching}>
            <Typography.Text tag='span' view='primary-small' color='secondary'>
                Всего
            </Typography.Text>
            <Gap size={4} />
            <Typography.Text dataTestId='total' tag='span' view='component' weight='medium'>
                <AmountPure transparentMinor={false} value={totalToPay} />
            </Typography.Text>
        </Skeleton>
    </React.Fragment>
);
