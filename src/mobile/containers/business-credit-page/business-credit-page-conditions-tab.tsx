import React from 'react';
import { useSelector } from 'react-redux';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { Gap } from '@alfalab/core-components/gap';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Typography } from '@alfalab/core-components/typography';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_YEAR_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { dateToCustomFormat } from '#/src/utils/date';

type TConditionsTab = {
    debtAmount?: TAmount;
    rate: number;
    dailyRate?: number;
    docNumber: string;
    toDate?: UnixEpoch;
    isFetching?: boolean;
};

export const ConditionsTab = ({
    debtAmount,
    rate,
    dailyRate,
    docNumber,
    toDate,
    isFetching = false,
}: TConditionsTab) => {
    const currentTime = useSelector(currentTimeSelector);

    return (
        <React.Fragment>
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Размер кредита
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text
                    dataTestId='debt-amount'
                    tag='span'
                    view='component'
                    weight='medium'
                >
                    <AmountPure transparentMinor={false} value={debtAmount} />
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Ставка
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text dataTestId='rate' tag='span' view='component' weight='medium'>
                    {dailyRate ? `${dailyRate}% в день` : `${rate}% годовых`}
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Договор
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text
                    dataTestId='doc-number'
                    tag='span'
                    view='component'
                    weight='medium'
                >
                    № {docNumber}
                </Typography.Text>
            </Skeleton>
            <Gap size={24} />
            <Skeleton visible={isFetching}>
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    Срок действия договора
                </Typography.Text>
                <Gap size={4} />
                <Typography.Text dataTestId='term' tag='span' view='component' weight='medium'>
                    До {dateToCustomFormat(currentTime, toDate, DAY_MONTH_YEAR_FORMAT)}
                </Typography.Text>
            </Skeleton>
        </React.Fragment>
    );
};
