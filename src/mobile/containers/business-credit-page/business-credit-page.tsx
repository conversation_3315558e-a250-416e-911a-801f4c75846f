import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { IconButton } from '@alfalab/core-components/icon-button';
import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';
import { CalendarMIcon } from '@alfalab/icons-glyph/CalendarMIcon';
import { ClockLineMIcon } from '@alfalab/icons-glyph/ClockLineMIcon';
import { InformationCircleMIcon } from '@alfalab/icons-glyph/InformationCircleMIcon';

import AmountPure from '#/src/components/ui/amount-pure';
import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { PATHS } from '#/src/constants/routing';
import { NBSP } from '#/src/constants/unicode-symbols';
import ProductPageWrapper from '#/src/containers/product-page-wrapper';
import { goToHome, goToPaymentsSchedulePage } from '#/src/ducks/app/actions';
import { getCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    earlyRepaymentButtonStateSelector,
    isCreditProductsFetchingSelector,
    isGetCreditProductsErrorSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isMksPermissionsFetchingSelector } from '#/src/ducks/mks-permissions/selectors';
import { isMmbCategorySelector } from '#/src/ducks/organization/selectors';
import { contextRootSelector, currentTimeSelector } from '#/src/ducks/settings/selectors';
import { parentCreditSelector } from '#/src/mmb/selectors-mmb/credit-products-selectors-mmb/credit-mmb.selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';
import { dateToCustomFormat } from '#/src/utils/date';

import { LoadingFailed } from '../../components/loading-failed';
import { BLUETINT_COLORS } from '../app/app';
import { Header } from '../header';

import { ConditionsTab } from './business-credit-page-conditions-tab';
import { DebtTab } from './business-credit-page-debt-tab';
import { PaymentInfo } from './business-credit-page-payment-info';

import './business-credit-page.css';

export const cn = createCn('business-credit-container');

const enum TABS {
    DEBT = 'DEBT',
    CONDITIONS = 'CONDITIONS',
}

export const BusinessCreditPage = () => {
    const dispatch = useDispatch();
    const currentBusinessCredit = useSelector(parentCreditSelector);
    const contextRoot = useSelector(contextRootSelector);
    const earlyRepaymentButtonState = useSelector(earlyRepaymentButtonStateSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const isFetchingMksPermissions = useSelector(isMksPermissionsFetchingSelector);
    const currentTime = useSelector(currentTimeSelector);
    const isGetCreditProductsError = useSelector(isGetCreditProductsErrorSelector);
    const isMmbCategory = useSelector(isMmbCategorySelector);

    const [selectedId, setSelectedId] = useState<IDType>(TABS.DEBT);
    const [showPaymentInfo, setShowPaymentInfo] = useState(false);

    const isFetching = isCreditProductsFetching || isFetchingMksPermissions;
    const isError =
        !isFetching &&
        (checkIsFaultyCreditProduct(currentBusinessCredit) || isGetCreditProductsError);

    const handleBackBtnClick = () => {
        dispatch(goToHome());
    };

    const refreshCreditProducts = () => {
        dispatch(getCreditProductsStart({ withClosed: false, withPaymentStatus: isMmbCategory }));
    };

    const togglePaymentInfo = () => {
        setShowPaymentInfo((value) => !value);
    };

    const redirectToPaymentsSchedule = () => {
        if (currentBusinessCredit?.docNumber) {
            dispatch(
                goToPaymentsSchedulePage({
                    docNumber: currentBusinessCredit.docNumber,
                    path: PATHS.PAYMENTS_SCHEDULE,
                }),
            );
        }
    };

    const redirectEarlyPayment = () => {
        // Временное решение, пока не будет готова мобильная версия
        window.location.assign(
            `${window.location.origin}${contextRoot}${PATHS.CREDIT}${qs.stringify(
                {
                    docNumber: currentBusinessCredit?.docNumber,
                    desktop: 'true',
                },
                { addQueryPrefix: true },
            )}`,
        );
    };

    const getCurrentTab = (tab: IDType) => {
        switch (tab) {
            case TABS.CONDITIONS:
                return (
                    <ConditionsTab
                        debtAmount={currentBusinessCredit?.requisites?.sum}
                        rate={currentBusinessCredit?.rate?.debtRate || 0}
                        dailyRate={currentBusinessCredit?.debtRateDaily}
                        docNumber={currentBusinessCredit?.docNumber || ''}
                        toDate={currentBusinessCredit?.requisites?.toDate}
                        isFetching={isFetching}
                    />
                );
            case TABS.DEBT:
            default:
                return (
                    <DebtTab
                        totalLoanSumToPay={currentBusinessCredit?.summary?.totalLoanAndFine}
                        totalInterestSumToPay={currentBusinessCredit?.summary?.totalInterestAndFine}
                        totalToPay={currentBusinessCredit?.summary?.totalDebt}
                        isFetching={isFetching}
                    />
                );
        }
    };

    return (
        <ProductPageWrapper>
            <Header title='Кредит для бизнеса' onBackBtnClick={handleBackBtnClick} />
            {isError ? (
                <LoadingFailed refresh={refreshCreditProducts} />
            ) : (
                <React.Fragment>
                    <Skeleton visible={isFetching}>
                        <Status dataTestId='status' color='grey'>
                            ПЛАТЁЖ{' '}
                            {dateToCustomFormat(
                                currentTime,
                                currentBusinessCredit?.payDebtTillDate,
                                DAY_MONTH_FORMAT,
                            )}
                        </Status>
                        <GenericWrapper
                            className={cn('total-to-pay')}
                            column={false}
                            justifyContent='between'
                            alignItems='baseline'
                        >
                            <div>
                                <Typography.Title
                                    dataTestId='total-to-pay'
                                    tag='div'
                                    view='medium'
                                    font='system'
                                >
                                    <AmountPure
                                        transparentMinor={false}
                                        value={currentBusinessCredit?.summary?.totalToPay}
                                    />
                                </Typography.Title>
                                <Gap size={4} />
                                <Typography.Text
                                    tag='div'
                                    view='primary-small'
                                    weight='regular'
                                    color='secondary'
                                >
                                    Спишем со счёта ···{currentBusinessCredit?.shortAccountNumber}{' '}
                                    после
                                    {NBSP}
                                    16:00 мск
                                </Typography.Text>
                            </div>
                            <IconButton
                                icon={InformationCircleMIcon}
                                style={{ color: BLUETINT_COLORS.colorLightGraphicSecondary }}
                                onClick={togglePaymentInfo}
                                size='xxs'
                                dataTestId='total-to-pay-button'
                            />
                        </GenericWrapper>
                        <Gap size={2} />
                        <SegmentedControl
                            className={cn('segment-control')}
                            onChange={setSelectedId}
                            selectedId={selectedId}
                        >
                            <Segment id={TABS.DEBT} title='Задолженность' />
                            <Segment id={TABS.CONDITIONS} title='Условия' />
                        </SegmentedControl>
                    </Skeleton>
                    <div className={cn('tab-container')}>{getCurrentTab(selectedId)}</div>
                    <Skeleton visible={isFetching}>
                        <div
                            data-test-id='payments-schedule-button'
                            className={cn('button')}
                            onClick={redirectToPaymentsSchedule}
                        >
                            <SuperEllipse size={48} className={cn('button-icon')}>
                                <CalendarMIcon color='black' />
                            </SuperEllipse>
                            <Typography.Text view='component'>График платежей</Typography.Text>
                        </div>
                    </Skeleton>
                    <Gap size={32} />
                    <Skeleton visible={isFetching}>
                        {earlyRepaymentButtonState === earlyRepaymentButtonStates.enabled && (
                            <div className={cn('button')} onClick={redirectEarlyPayment}>
                                <SuperEllipse size={48} className={cn('button-icon')}>
                                    <ClockLineMIcon color='black' />
                                </SuperEllipse>
                                <Typography.Text view='component'>
                                    Погасить досрочно
                                </Typography.Text>
                            </div>
                        )}
                    </Skeleton>
                    <BottomSheet
                        title='Что входит в платёж'
                        open={showPaymentInfo}
                        onClose={togglePaymentInfo}
                        hasCloser={true}
                        actionButton={
                            <Button size='m' block={true} onClick={togglePaymentInfo}>
                                Понятно
                            </Button>
                        }
                    >
                        <PaymentInfo
                            totalDebtToPay={currentBusinessCredit?.summary?.totalLoanSumToPay}
                            totalInterestSumToPay={
                                currentBusinessCredit?.summary?.totalInterestSumToPay
                            }
                            totalFine={currentBusinessCredit?.summary?.totalFine}
                            debtRate={currentBusinessCredit?.rate?.overdueDebtRate}
                        />
                    </BottomSheet>
                </React.Fragment>
            )}
        </ProductPageWrapper>
    );
};
