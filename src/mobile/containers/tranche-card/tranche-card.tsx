import React from 'react';
import { useDispatch } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import ChevronRightMIcon from '@alfalab/icons-glyph/ChevronRightMIcon';

import { goTranchePage } from '#/src/ducks/credit-products/actions';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';

import { TrancheCardPaymentStatus } from './tranche-card-payment-status';

import './tranche-card.css';

export const cn = createCn('tranche-card');

type TTrancheCard = {
    tranche: NonNullable<MappedTranche>;
    trancheNumber: string;
    docNumber: string;
    path: string;
};

export const TrancheCard = ({ tranche, trancheNumber, docNumber, path }: TTrancheCard) => {
    const dispatch = useDispatch();

    const redirectToTranchePage = () => {
        dispatch(
            goTranchePage({
                path,
                docNumber,
                trancheNumber,
            }),
        );
    };

    return (
        <div onClick={redirectToTranchePage}>
            <GenericWrapper justifyContent='between' alignItems='center'>
                <TrancheCardPaymentStatus tranche={tranche} className={cn()} />
                <ChevronRightMIcon className={cn('arrow')} />
            </GenericWrapper>
        </div>
    );
};
