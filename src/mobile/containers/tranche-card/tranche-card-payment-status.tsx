import React from 'react';
import { useSelector } from 'react-redux';
import { TrancheStatus } from 'corp-core-credit-products-api-typescript-services';

import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';
import { pluralize } from 'arui-private/lib/formatters';

import AmountPure from '#/src/components/ui/amount-pure/amount-pure';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { getTrancheUpcomingPayment } from '#/src/containers/credit-line-tranches/utils';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';

import { cn } from './tranche-card';

type Props = {
    tranche: NonNullable<MappedTranche>;
    className?: string;
};

export const TrancheCardPaymentStatus = ({ tranche, className }: Props) => {
    const currentTime = useSelector(currentTimeSelector);

    const upcomingPayment = getTrancheUpcomingPayment(tranche, currentTime, DAY_MONTH_FORMAT);

    switch (tranche.requisites?.trancheStatus) {
        case TrancheStatus.UpcomingPayment: {
            const pluralizeDay = pluralize(upcomingPayment?.paymentInDays ?? 0, [
                'день',
                'дня',
                'дней',
            ]);

            const isZeroDays = (upcomingPayment?.paymentInDays ?? 0) === 0;

            const amountText = isZeroDays
                ? 'Платеж сегодня'
                : `Платеж через ${upcomingPayment?.paymentInDays ?? 0} ${pluralizeDay}`;

            return (
                <div className={className}>
                    <Typography.Text
                        view='primary-small'
                        color='attention'
                        className={cn('info-payment')}
                    >
                        {amountText}
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text dataTestId='tranche-payment' view='component' weight='medium'>
                        <AmountPure value={upcomingPayment?.sum} transparentMinor={false} />
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text
                        dataTestId='tranche-date'
                        color='secondary'
                        view='primary-small'
                    >
                        Выплатить до{NBSP}
                        {upcomingPayment?.date}
                    </Typography.Text>
                </div>
            );
        }
        case TrancheStatus.OverduePayment: {
            return (
                <div className={className}>
                    <Typography.Text
                        view='primary-small'
                        color='accent'
                        className={cn('info-payment')}
                    >
                        Просрочен
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text view='component' weight='medium'>
                        <AmountPure transparentMinor={false} value={tranche.totalDebt} />
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text color='secondary' view='primary-small'>
                        В том числе проценты&nbsp;
                        <AmountPure
                            value={tranche?.summary?.totalOverdue}
                            transparentMinor={false}
                        />
                    </Typography.Text>
                </div>
            );
        }
        default: {
            return (
                <div className={className}>
                    <Typography.Text dataTestId='tranche-payment' view='component' weight='medium'>
                        <AmountPure value={upcomingPayment?.sum} transparentMinor={false} />
                    </Typography.Text>
                    <Gap size={4} />
                    <Typography.Text
                        dataTestId='tranche-date'
                        color='secondary'
                        view='primary-small'
                    >
                        Выплатить до{NBSP}
                        {upcomingPayment?.date}
                    </Typography.Text>
                </div>
            );
        }
    }
};
