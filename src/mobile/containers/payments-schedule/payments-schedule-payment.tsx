import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Gap } from '@alfalab/core-components/gap';
import { GenericWrapper } from '@alfalab/core-components/generic-wrapper';
import { Typography } from '@alfalab/core-components/typography';
import { pluralize } from '@alfalab/utils';

import AmountPure from '#/src/components/ui/amount-pure';
import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { calculateDifferenceDays } from '#/src/mobile/utils/tranches';
import { dateToCustomFormat } from '#/src/utils/date';

type TPayment = {
    payment: TLoanPayment;
    onClick: (payment: TLoanPayment) => void;
    className?: string;
};

export const Payment = ({ payment, onClick, className }: TPayment) => {
    const currentTime = useSelector(currentTimeSelector);

    const { paymentDate, paymentAmount, isPayed } = payment;

    const color = isPayed ? 'secondary' : 'primary';
    const formattedDate = dateToCustomFormat(currentTime, paymentDate, DAY_MONTH_FORMAT);

    const overdueDays = useMemo(() => {
        if (isPayed) {
            return 0;
        }

        return calculateDifferenceDays(currentTime, (paymentDate?.seconds ?? 0) * 1000);
    }, [currentTime, isPayed, paymentDate]);

    const handleOnClick = () => {
        onClick(payment);
    };

    return (
        <div className={className} onClick={handleOnClick}>
            <GenericWrapper justifyContent='between'>
                <Typography.Text color={color}>{formattedDate}</Typography.Text>
                <Typography.Text color={color}>
                    <AmountPure value={paymentAmount} transparentMinor={false} />
                </Typography.Text>
            </GenericWrapper>
            {!!isPayed && <Typography.Text color='secondary'>Оплачено</Typography.Text>}
            {overdueDays > 0 && (
                <Typography.Text color='accent'>
                    Просрочен на {overdueDays} {pluralize(overdueDays, 'день', 'дня', 'дней')}
                </Typography.Text>
            )}
            <Gap size={24} />
        </div>
    );
};
