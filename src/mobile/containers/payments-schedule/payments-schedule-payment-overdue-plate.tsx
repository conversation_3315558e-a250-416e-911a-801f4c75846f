import React from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Plate } from '@alfalab/core-components/plate';

import { NBSP } from '#/src/constants/unicode-symbols';

type TOverduePlate = {
    isPayed: boolean;
    overdueDays: number;
    overdueDebtRate: number;
};

export const PaymentsSchedulePaymentOverduePlate = ({
    isPayed,
    overdueDays,
    overdueDebtRate,
}: TOverduePlate) => {
    if (isPayed) return null;

    if (overdueDays > 0) {
        return (
            <React.Fragment>
                <Plate title='Просрочен' view='negative' border={false}>
                    Каждый день пробуем списывать деньги для частичного или полного погашения
                    платежа. Неустойка — {overdueDebtRate}%{NBSP}в{NBSP}день от
                    {NBSP}
                    суммы общей задолженности
                </Plate>
                <Gap size={24} />
            </React.Fragment>
        );
    }

    return (
        <React.Fragment>
            <Plate title='Спишем автоматически после 16:00 мск' titleView='light' border={false} />
            <Gap size={24} />
        </React.Fragment>
    );
};
