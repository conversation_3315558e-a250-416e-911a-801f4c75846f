/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { type ReactNode } from 'react';

import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';

export type TContent = {
    label: string;
    value: any;
    text?: string;
    renderFn?: (value: any) => ReactNode;
};

export const PaymentsSchedulePaymentLabelAndValue = ({
    label,
    text,
    value,
    renderFn,
}: TContent) => (
    <React.Fragment>
        <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
            {label}
        </Typography.Text>
        <Gap size={2} />
        <Typography.Text tag='span' view='component' weight='medium'>
            {renderFn ? renderFn(value) : value}
        </Typography.Text>
        {!!text && (
            <React.Fragment>
                <Gap size={2} />
                <Typography.Text tag='span' view='primary-small' weight='regular' color='secondary'>
                    {text}
                </Typography.Text>
            </React.Fragment>
        )}
    </React.Fragment>
);
