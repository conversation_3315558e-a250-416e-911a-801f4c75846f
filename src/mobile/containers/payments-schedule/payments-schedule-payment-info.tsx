import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { Gap } from '@alfalab/core-components/gap';

import AmountPure from '#/src/components/ui/amount-pure';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { type MappedTranche } from '#/src/utils/credit-products-mappers';

import { calculateDifferenceDays } from '../../utils/tranches';

import {
    PaymentsSchedulePaymentLabelAndValue,
    type TContent,
} from './payments-schedule-payment-label-and-value';
import { PaymentsSchedulePaymentOverduePlate } from './payments-schedule-payment-overdue-plate';

type TFilteredPayments = {
    tranche: MappedTranche;
    payment: TLoanPayment;
};

export const renderAmount = (value: TAmount) => (
    <AmountPure transparentMinor={false} value={value} />
);

export const PaymentsSchedulePaymentInfo: React.FC<TFilteredPayments> = ({ tranche, payment }) => {
    const currentTime = useSelector(currentTimeSelector);

    const { isPayed, paymentDate, paymentAmount, loanBalance, paymentInterest, paymentLoan } =
        payment;

    const overdueDays = useMemo(() => {
        if (isPayed) {
            return 0;
        }

        return calculateDifferenceDays(currentTime, (paymentDate?.seconds ?? 0) * 1000);
    }, [currentTime, isPayed, paymentDate?.seconds]);

    const overdueItems: TContent[] = [
        {
            label: 'Неустойка за основной долг',
            text: `По ставке ${tranche?.overdueDebtRate ?? 0}% годовых`,
            value: tranche?.fineDebt,
            renderFn: renderAmount,
        },
        {
            label: 'Неустойка за проценты',
            text: `По ставке ${tranche?.rate?.overdueInterestRate ?? 0}% годовых в день`,
            value: tranche?.fineInterest,
            renderFn: renderAmount,
        },
    ].filter(({ value }) => !!value);

    const contentItems: TContent[] = [
        {
            label: 'Основной долг',
            value: paymentLoan,
            renderFn: renderAmount,
        },
        {
            label: 'Проценты',
            value: paymentInterest,
            renderFn: renderAmount,
        },
        ...(overdueDays > 0 ? overdueItems : []),
        { label: 'Всего', value: paymentAmount, renderFn: renderAmount },
        {
            label: 'Остаток долга после этого платежа',
            value: loanBalance,
            renderFn: renderAmount,
        },
    ].filter(({ value }) => !!value);

    return (
        <React.Fragment>
            <Gap size={12} />
            <PaymentsSchedulePaymentOverduePlate
                isPayed={!!isPayed}
                overdueDays={overdueDays}
                overdueDebtRate={tranche?.overdueDebtRate ?? 0}
            />
            {contentItems.map((item, index) => (
                <React.Fragment key={`payment-label_${index}`}>
                    <PaymentsSchedulePaymentLabelAndValue {...item} />
                    <Gap size={index < contentItems.length - 1 ? 24 : 16} />
                </React.Fragment>
            ))}
        </React.Fragment>
    );
};
