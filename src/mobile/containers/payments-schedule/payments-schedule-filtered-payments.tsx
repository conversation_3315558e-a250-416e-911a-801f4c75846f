import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ReportFormat } from 'thrift-services/entities';
import { type TLoanPayment } from 'thrift-services/services/credit_products';

import { BottomSheet } from '@alfalab/core-components/bottom-sheet';
import { Button } from '@alfalab/core-components/button';
import { Gap } from '@alfalab/core-components/gap';
import { IconButton } from '@alfalab/core-components/icon-button';
import { Tag, type TagProps } from '@alfalab/core-components/tag';
import ArrowDownLineDownMIcon from '@alfalab/icons-glyph/ArrowDownLineDownMIcon';

import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { productByDocNumberSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { parentTrancheSelector } from '#/src/ducks/credit-products/selectors/tranches.selectors';
import { getPaymentScheduleFile } from '#/src/ducks/payment-schedule/actions';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { useFilteredPayments } from '#/src/mobile/utils/hooks/use-filtered-payments';
import { dateToCustomFormat } from '#/src/utils/date';

import { Header } from '../header';

import { cn } from './payments-schedule';
import { Payment } from './payments-schedule-payment';
import { PaymentsSchedulePaymentInfo } from './payments-schedule-payment-info';

type TFilteredPayments = {
    docNumber: string;
    trancheNumber?: string;
    futurePayments: TLoanPayment[];
    payedPayments: TLoanPayment[];
};

export const FilteredPayments: React.FC<TFilteredPayments> = ({
    docNumber,
    trancheNumber,
    futurePayments,
    payedPayments,
}) => {
    const dispatch = useDispatch();
    const currentTime = useSelector(currentTimeSelector);
    const product = useSelector(productByDocNumberSelector);
    const tranche = useSelector(parentTrancheSelector);

    const { filteredPayments, availableYearsArray } = useFilteredPayments(
        futurePayments,
        payedPayments,
    );

    const [selectedYear, setSelectedYear] = useState(`${currentTime.getFullYear()}`);
    const [showPaymentInfo, setShowPaymentInfo] = useState(false);
    const [currentPayment, setCurrentPayment] = useState<TLoanPayment>();

    const currentDocNumber = trancheNumber || docNumber;
    const accountNumber = tranche?.requisites?.account || product?.requisites?.account;
    const pageTitle =
        availableYearsArray.length === 1
            ? `График платежей ${availableYearsArray[0]}`
            : 'График платежей';

    useEffect(() => {
        if (availableYearsArray[0]) {
            setSelectedYear(availableYearsArray[0]);
        }
    }, [availableYearsArray]);

    const togglePaymentInfo = () => setShowPaymentInfo((value) => !value);

    const handleDownload = () => {
        dispatch(
            getPaymentScheduleFile({
                docNumber: currentDocNumber,
                accountNumber,
                fileType: ReportFormat.PDF,
            }),
        );
    };

    const handleSelectYear: TagProps['onClick'] = (_, payload) => {
        if (payload?.checked && payload?.name) {
            setSelectedYear(payload.name);
        }
    };

    const handleOnPaymentClick = (payment: TLoanPayment) => {
        setShowPaymentInfo(true);
        setCurrentPayment(payment);
    };

    return (
        <React.Fragment>
            <Header
                title={pageTitle}
                rightAddon={<IconButton icon={ArrowDownLineDownMIcon} onClick={handleDownload} />}
            />
            {availableYearsArray.length > 1 && (
                <React.Fragment>
                    <Gap size={4} />
                    <div className={cn('tabs')}>
                        {availableYearsArray.map((year) => (
                            <Tag
                                key={year}
                                name={year}
                                checked={year === selectedYear}
                                size='xxs'
                                view='filled'
                                shape='rounded'
                                className={cn('tabs-item')}
                                onClick={handleSelectYear}
                                dataTestId={year}
                            >
                                {year}
                            </Tag>
                        ))}
                    </div>
                    <Gap size={16} />
                </React.Fragment>
            )}
            {filteredPayments[selectedYear]?.map((payment, index) => (
                <Payment
                    key={`payment_${index}`}
                    payment={payment}
                    onClick={handleOnPaymentClick}
                    className={cn('payment')}
                />
            ))}
            {currentPayment && (
                <BottomSheet
                    title={`Платеж ${dateToCustomFormat(
                        currentTime,
                        currentPayment?.paymentDate,
                        DAY_MONTH_FORMAT,
                    )}`}
                    open={showPaymentInfo}
                    onClose={togglePaymentInfo}
                    hasCloser={true}
                    actionButton={
                        <Button size='m' block={true} onClick={togglePaymentInfo}>
                            Понятно
                        </Button>
                    }
                >
                    <PaymentsSchedulePaymentInfo tranche={tranche} payment={currentPayment} />
                </BottomSheet>
            )}
        </React.Fragment>
    );
};
