import React from 'react';
import { createCn } from 'bem-react-classname/create-cn';
import { type UnixEpoch } from 'thrift-services/utils';

import { Status } from '@alfalab/core-components/status';
import { Typography } from '@alfalab/core-components/typography';

import { DATE_FORMAT } from '#/src/constants/date';
import { dateToCustomFormat } from '#/src/utils/date';

import './credit-offer-badge.css';

const cn = createCn('credit-offer-badge');

type Props = {
    preApproved?: boolean;
    currentTime?: Date;
    earlierExpiryDate?: UnixEpoch;
    isTotalOffer?: boolean;
};

export const CreditOfferBadge = ({
    preApproved = false,
    currentTime,
    earlierExpiryDate,
    isTotalOffer = false,
}: Props) => {
    const expiryDate = dateToCustomFormat(currentTime, earlierExpiryDate, DATE_FORMAT);

    if (!preApproved) {
        return null;
    }

    return (
        <div className={cn()}>
            <Status className={cn('status')} view='contrast' color='green'>
                <Typography.Text
                    view='secondary-small'
                    weight='bold'
                    className={cn('letter-spacing-zero')}
                >
                    {isTotalOffer ? 'Одобрено' : 'Предварительно одобрено'}
                </Typography.Text>
            </Status>
            {isTotalOffer && (
                <Typography.Text className={cn('text')} view='secondary-medium' weight='medium'>
                    Действует до {expiryDate}
                </Typography.Text>
            )}
        </div>
    );
};
