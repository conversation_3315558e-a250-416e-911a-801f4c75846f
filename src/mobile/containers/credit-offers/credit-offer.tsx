import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Gap } from '@alfalab/core-components/gap';
import { Typography } from '@alfalab/core-components/typography';

import {
    type ECreditOffersCampaignCode,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { NBSP } from '#/src/constants/unicode-symbols';
import { goLanding } from '#/src/ducks/app/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { CREDIT_OFFERS_METRICS } from '#/src/metrics';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';
import { convertAmountToText } from '#/src/utils/formatters';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { BLUETINT_COLORS } from '../app/app';

import { CreditOfferBadge } from './credit-offer-badge';

import './credit-offer.css';

const cn = createCn('credit-offer-container');

type Props = TMappedCreditOffer & {
    image: string;
};

export const CreditOffer = ({
    titlesVariants,
    additionalOffer,
    expiryDate,
    preApproved,
    yearRatePct,
    maximumAmount,
    type,
    campaignCode,
    productCode,
    image,
}: Props) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const currentTime = useSelector(currentTimeSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const isTotalOffer = TotalOffersCampaignCode.includes(
        campaignCode as ECreditOffersCampaignCode,
    );

    const additionalOfferExpiryDate = additionalOffer?.expiryDate;
    const earlierExpiryDate =
        !!preApproved &&
        !!additionalOfferExpiryDate &&
        additionalOfferExpiryDate.seconds < expiryDate.seconds
            ? additionalOfferExpiryDate
            : expiryDate;

    const view = !!preApproved && isTotalOffer ? 'positive' : 'default';

    useEffect(() => {
        trackAlfaMetrics(
            CREDIT_OFFERS_METRICS[
                preApproved ? 'showPreApprovedCreditOfferPane' : 'showStandardCreditOfferPane'
            ],
            {
                type,
                campaignCode,
                productCode,
                campaignCodeCommon: campaignCode,
                productIdCommon: type,
            },
        );
    }, [campaignCode, preApproved, productCode, trackAlfaMetrics, type]);

    const redirectToLandingPage = () => {
        dispatch(
            goLanding({
                landingType: type,
                campaignCode: campaignCode as ECreditOffersCampaignCode,
                organizationId,
            }),
        );
    };

    return (
        <div className={cn({ view })} onClick={redirectToLandingPage}>
            <div className={cn('content')}>
                <CreditOfferBadge
                    preApproved={preApproved}
                    currentTime={currentTime}
                    earlierExpiryDate={earlierExpiryDate}
                    isTotalOffer={isTotalOffer}
                />
                <Typography.Text view='component' color='static-primary-dark'>
                    {titlesVariants?.long}
                </Typography.Text>
                <Gap size={12} />
                <div className={cn('grid')}>
                    <Typography.Text
                        tag='span'
                        weight='regular'
                        view='primary-small'
                        style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                    >
                        Сумма
                    </Typography.Text>
                    <Typography.Text
                        tag='span'
                        weight='regular'
                        view='primary-small'
                        style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                    >
                        до{NBSP}
                        {convertAmountToText((maximumAmount?.amount ?? 0) / 100, {
                            hasCurrencySymbol: true,
                        })}
                    </Typography.Text>
                    <Typography.Text
                        tag='span'
                        weight='regular'
                        view='primary-small'
                        style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                    >
                        Ставка
                    </Typography.Text>
                    <Typography.Text
                        tag='span'
                        weight='regular'
                        view='primary-small'
                        style={{ color: BLUETINT_COLORS.colorLightTextSecondary }}
                    >
                        от{NBSP}
                        {yearRatePct}%
                    </Typography.Text>
                </div>
            </div>
            <div className={cn('image')}>
                <img src={image} />
            </div>
        </div>
    );
};
