import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Gap } from '@alfalab/core-components/gap';
import { Segment, SegmentedControl } from '@alfalab/core-components/segmented-control';
import { type IDType } from '@alfalab/core-components/segmented-control/typing';

import { DigitalSalesCreditOffersBanners } from '#/src/containers/digital-sales-credit-offers-banners/digital-sales-credit-offers-banners';
import { stageActiveCreditCaseSelector } from '#/src/ducks/attach-documents/selectors';
import {
    isDigitalSalesCreditOffersEmptySelector,
    isDigitalSalesCreditOffersReceivedSelector,
} from '#/src/ducks/credit-offers/selectors';
import { isNibCreditOffersForHeaderSelector } from '#/src/ducks/credit-products-header/selectors';
import { ActiveCreditCaseNotification } from '#/src/mobile/components/active-credit-case-notification';
import {
    ACTIVE_CREDIT_CASE_STAGES,
    type TActiveCreditCaseStage,
} from '#/src/mobile/constants/credit-request-statuses';
import { Header } from '#/src/mobile/containers/header';

import { CreditOffers } from '../credit-offers';
import { CreditProducts } from '../credit-products/credit-products';

import './main-page.css';

const cn = createCn('main-page');

const enum TABS {
    CREDIT_OFFERS = 'CREDIT_OFFERS',
    CREDIT_PRODUCTS = 'CREDIT_PRODUCTS',
}

const getCurrentTab = ({
    selectedId,
    isDigitalSalesCreditOffersEmpty,
    isDigitalSalesCreditOffersReceived,
    isNibCreditOffersForHeader,
}: {
    selectedId: IDType;
    isDigitalSalesCreditOffersEmpty: boolean;
    isDigitalSalesCreditOffersReceived: boolean;
    isNibCreditOffersForHeader: boolean;
}) => {
    switch (selectedId) {
        case TABS.CREDIT_OFFERS:
            if (
                (isDigitalSalesCreditOffersEmpty && isDigitalSalesCreditOffersReceived) ||
                !isNibCreditOffersForHeader
            ) {
                return <CreditOffers />;
            }

            return <DigitalSalesCreditOffersBanners />;
        case TABS.CREDIT_PRODUCTS:
            return <CreditProducts />;
        default:
            return <CreditOffers />;
    }
};

export const MainPage = () => {
    const stage = useSelector(stageActiveCreditCaseSelector);
    const isDigitalSalesCreditOffersEmpty = useSelector(isDigitalSalesCreditOffersEmptySelector);
    const isDigitalSalesCreditOffersReceived = useSelector(
        isDigitalSalesCreditOffersReceivedSelector,
    );
    const isNibCreditOffersForHeader = useSelector(isNibCreditOffersForHeaderSelector);

    const [selectedId, setSelectedId] = useState<IDType>(TABS.CREDIT_OFFERS);

    const selectedActiveCase = ACTIVE_CREDIT_CASE_STAGES[stage];

    return (
        <React.Fragment>
            <Header title='Кредитная витрина' />
            {!!selectedActiveCase && (
                <React.Fragment>
                    <ActiveCreditCaseNotification stage={stage as TActiveCreditCaseStage} />
                    <Gap size={16} />
                </React.Fragment>
            )}
            <main className={cn()}>
                <SegmentedControl
                    className={cn('tabs')}
                    onChange={setSelectedId}
                    selectedId={selectedId}
                >
                    <Segment dataTestId='offers' id={TABS.CREDIT_OFFERS} title='Предложения' />
                    <Segment dataTestId='products' id={TABS.CREDIT_PRODUCTS} title='Мои продукты' />
                </SegmentedControl>
                <Gap size={8} />
                {getCurrentTab({
                    selectedId,
                    isDigitalSalesCreditOffersEmpty,
                    isDigitalSalesCreditOffersReceived,
                    isNibCreditOffersForHeader,
                })}
            </main>
        </React.Fragment>
    );
};
