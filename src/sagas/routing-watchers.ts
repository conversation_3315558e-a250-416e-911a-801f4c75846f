import { takeLatest } from 'redux-saga/effects';

import { APP_TYPES } from '../ducks/app/types';
import { CREDIT_PRODUCTS_TYPES } from '../ducks/credit-products/types';

import {
    goCreditProductPageWorker,
    goCreditTrancheAppWorker,
    goLandingWorker,
    goToAttachDocumentsPageWorker,
    goToBusinessCreditCard,
    goToCreditFormsAppWorker,
    goToCreditOffersWorker,
    goToDashboardAccountWorker,
    goToDashboardWorker,
    goToHomeWorker,
    goToOveformAgreement,
    goToPaymentsScheduleWorker,
    goTranchePageWorker,
    initExternalRedirectWorker,
} from './workers/routing-workers';

export default function* watchRouting() {
    yield takeLatest(CREDIT_PRODUCTS_TYPES.GO_CREDIT_PRODUCT_PAGE, goCreditProductPageWorker);
    yield takeLatest(CREDIT_PRODUCTS_TYPES.GO_TRANCHE_PAGE, goTranchePageWorker);
    yield takeLatest(APP_TYPES.GO_TO_HOME, goToHomeWorker);
    yield takeLatest(APP_TYPES.GO_TO_CREDIT_OFFERS, goToCreditOffersWorker);
    yield takeLatest(APP_TYPES.GO_TO_PAYMENTS_SCHEDULE, goToPaymentsScheduleWorker);
    yield takeLatest(APP_TYPES.GO_TO_ASSIGN_DOCUMENTS_PAGE, goToAttachDocumentsPageWorker);
    yield takeLatest(APP_TYPES.GO_TO_CREDIT_FORMS_APP, goToCreditFormsAppWorker);
    yield takeLatest(APP_TYPES.GO_TO_DASHBOARD, goToDashboardWorker);
    yield takeLatest(APP_TYPES.GO_TO_DASHBOARD_ACCOUNT, goToDashboardAccountWorker);
    yield takeLatest(APP_TYPES.GO_TO_OVERFORM_AGREEMENT, goToOveformAgreement);
    yield takeLatest(APP_TYPES.GO_TO_BUSINESS_CREDIT_CARD, goToBusinessCreditCard);
    yield takeLatest(APP_TYPES.GO_LANDING, goLandingWorker);
    yield takeLatest(APP_TYPES.GO_TRANCHE_APP, goCreditTrancheAppWorker);
    yield takeLatest(APP_TYPES.INIT_EXTERNAL_REDIRECT, initExternalRedirectWorker);
}
