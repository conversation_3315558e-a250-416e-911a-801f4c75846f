import { all } from 'redux-saga/effects';

import watchAcceptAttachedFiles from './accepted-attached-files-watcher';
import watchAccounts from './accounts-watcher';
import watchAlfaMetrics from './alfa-metrics-watcher';
import watchOrganizationChange from './change-organization-watcher';
import watchCloseActiveCase from './close-active-case-watcher';
import watchCorrespondence from './correspondence-watcher';
import watchCreateCallbackRequest from './create-callback-request-watcher';
import watchCreateStatementRequest from './create-statement-request-watcher';
import watchCreditDocumentCirculation from './credit-document-circulation-watcher';
import watchCreditOffers from './credit-offers-watcher';
import watchCreditProductsHeader from './credit-products-header-watcher';
import watchCreditProductsMainMenu from './credit-products-main-menu-watcher';
import watchCreditProducts from './credit-products-watchers';
import watchCreditRequests from './credit-requests-watcher';
import watchGetCreditWidgetStatus from './credit-widget-status-watcher';
import watchCustomerApi from './customer-api-watcher';
import watchDeleteWelcomeDealById from './delete-welcome-deal-by-id-watcher';
import watchGetDocuments from './documents-watcher';
import watchDownloadStatementRequest from './download-statement-request-watcher';
import watchEarlyPay from './early-pay-watcher';
import watchCreateEarlyRepaymentAppl from './early-repayment-appl-watcher';
import watchEarlyRepaymentProcessingType from './early-repayment-processing-type-watcher';
import watchFindRequestsInProgressStatistics from './find-requests-in-progress-statistics-watcher';
import watchGetActiveCreditCase from './get-active-credit-case-watcher';
import watchGetAttachedDocuments from './get-attached-documents-watcher';
import watchGetInfoForClientDocuments from './get-info-for-client-documents-watcher';
import { watchLogError } from './logger-watcher';
import watchMoveCaseToStage from './move-case-to-stage-watcher';
import {
    watchCreditPaymentSchedule,
    watchCreditPaymentScheduleFile,
    watchGuarantyPaymentSchedule,
} from './payment-schedule-watchers';
import watchRemoveAttachedDocument from './remove-attached-document-watcher';
import watchRouting from './routing-watchers';
import runDecisionMakerCheckWatcher from './run-decision-maker-check-watcher';
import watchSendUnsafeDocuments from './send-unsafe-documents-watcher';
import watchSignModuleLoader from './sign-module-watcher';
import watchSignedDocuments from './signed-documents-watcher';
import watchStatementRequests from './statement-requests-watcher';
import watchSuspensiveConditions from './suspensive-conditions-watcher';
import watchUpdateStatementRequestsView from './update-statement-requests-view-watcher';
import watchVOC from './voc-watcher';

export default function* rootSaga() {
    yield all([
        watchAlfaMetrics(),
        watchCreditProducts(),
        watchCreditProductsMainMenu(),
        watchCreditOffers(),
        watchCreditPaymentSchedule(),
        watchCreditPaymentScheduleFile(),
        watchGuarantyPaymentSchedule(),
        watchOrganizationChange(),
        watchCreditDocumentCirculation(),
        watchCreditRequests(),
        watchLogError(),
        watchRouting(),
        watchAccounts(),
        watchCorrespondence(),
        watchEarlyRepaymentProcessingType(),
        watchCreateEarlyRepaymentAppl(),
        runDecisionMakerCheckWatcher(),
        watchGetActiveCreditCase(),
        watchGetInfoForClientDocuments(),
        watchSendUnsafeDocuments(),
        watchAcceptAttachedFiles(),
        watchSignedDocuments(),
        watchGetAttachedDocuments(),
        watchRemoveAttachedDocument(),
        watchFindRequestsInProgressStatistics(),
        watchRemoveAttachedDocument(),
        watchGetDocuments(),
        watchCloseActiveCase(),
        watchGetCreditWidgetStatus(),
        watchCreateCallbackRequest(),
        watchMoveCaseToStage(),
        watchCustomerApi(),
        watchSuspensiveConditions(),
        watchEarlyPay(),
        watchVOC(),
        watchSignModuleLoader(),
        watchCreditProductsHeader(),
        watchDeleteWelcomeDealById(),
        watchStatementRequests(),
        watchDownloadStatementRequest(),
        watchUpdateStatementRequestsView(),
        watchCreateStatementRequest(),
    ]);
}
