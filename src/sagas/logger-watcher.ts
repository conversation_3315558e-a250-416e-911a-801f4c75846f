import { takeLatest } from 'redux-saga/effects';

import { ATTACH_DOCUMENTS_TYPES } from '../ducks/attach-documents/types';
import { CREDIT_DOCUMENT_CIRCULATION_TYPES } from '../ducks/credit-document-circulation/types';
import { CREDIT_OFFERS_TYPES } from '../ducks/credit-offers/types';
import { CREDIT_PROCESSING_TYPES } from '../ducks/credit-processing/types';
import { CREDIT_PRODUCTS_TYPES } from '../ducks/credit-products/types';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '../ducks/credit-products-main-menu/types';
import { CREDIT_REQUESTS_TYPES } from '../ducks/credit-requests/types';
import { DOCUMENTS_TYPES } from '../ducks/documents/types';
import { EARLY_PAY } from '../ducks/early-pay/types';
import { ORGANIZATION_TYPES } from '../ducks/organization/types';
import { PAYMENT_SCHEDULE_TYPES } from '../ducks/payment-schedule/types';
import { SUSPENSIVE_CONDITIONS_TYPES } from '../ducks/suspensive-conditions/types';

import { logWorker } from './workers/logger-worker';

export const ERROR_ACTION_TYPES_TO_WATCH = [
    CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR,
    CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_ERROR,
    CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR,
    CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_ERROR,
    CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_ERROR,

    CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_ERROR,

    CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_PRODUCT_INFO_ERROR,
    CREDIT_DOCUMENT_CIRCULATION_TYPES.GET_ALFA_CREDIT_STATUS_ERROR,

    PAYMENT_SCHEDULE_TYPES.GET_FUTURE_PAYMENT_SCHEDULE_ERROR,
    PAYMENT_SCHEDULE_TYPES.GET_PAYED_PAYMENT_SCHEDULE_ERROR,
    PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_ERROR,
    PAYMENT_SCHEDULE_TYPES.GET_PAYMENT_SCHEDULE_FILE_ERROR,

    ATTACH_DOCUMENTS_TYPES.GET_ACTIVE_CREDIT_CASE_ERROR,
    ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_ERROR,
    ATTACH_DOCUMENTS_TYPES.ACCEPT_ATTACHED_FILES_ERROR,
    ATTACH_DOCUMENTS_TYPES.GET_ATTACHED_DOCUMENTS_ERROR,
    ATTACH_DOCUMENTS_TYPES.REMOVE_ATTACHED_DOCUMENT_ERROR,

    DOCUMENTS_TYPES.GET_DOCUMENT_ERROR,

    CREDIT_PROCESSING_TYPES.CLOSE_ACTIVE_CASE_ERROR,

    ORGANIZATION_TYPES.GET_CATEGORY_ERROR,
    ORGANIZATION_TYPES.GET_MANAGERS_ERROR,
    ORGANIZATION_TYPES.GET_SUBJECT_FEATURES_ERROR,

    CREDIT_REQUESTS_TYPES.GET_CREDIT_REQUESTS_LIST_ERROR,

    SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_ERROR,

    EARLY_PAY.SEND_MAIL_KM_ERROR,

    CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_OR_CONNECT_ALFA_CREDIT_MAIN_MENU_ERROR,
];

export function* watchLogError() {
    yield takeLatest(ERROR_ACTION_TYPES_TO_WATCH, logWorker);
}
