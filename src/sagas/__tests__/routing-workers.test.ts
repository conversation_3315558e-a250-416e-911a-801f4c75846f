import { expectSaga } from 'redux-saga-test-plan';
import { select } from 'redux-saga-test-plan/matchers';
import { push } from 'connected-react-router';

import { ECreditOffers, ECreditOffersCampaignCode } from '#/src/constants/credit-offers';
import { PATHS } from '#/src/constants/routing';
import {
    type goCreditTrancheApp,
    type goToCreditFormsApp,
    type goToDashboardAccount,
    type initExternalRedirect,
} from '#/src/ducks/app/actions';
import { APP_TYPES } from '#/src/ducks/app/types';
import { type ApplicationState } from '#/src/ducks/application-state';
import {
    isOnlineSigningAvailableSelector,
    loanMBIdSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    goToCreditFormsParamsOfferSelector,
    goToCreditFormsParamsQuestionnaireCustomerSelector,
} from '#/src/ducks/common-selectors/redirect-selectors';
import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import {
    goCreditProductPageWorker,
    goCreditTrancheAppWorker,
    goLandingWorker,
    goToAttachDocumentsPageWorker,
    goToBusinessCreditCard,
    goToCreditFormsAppWorker,
    goToDashboardAccountWorker,
    goToDashboardWorker,
    goToHomeWorker,
    goToOveformAgreement,
    goToPaymentsScheduleWorker,
    goTranchePageWorker,
    initExternalRedirectWorker,
    routingWorkersState,
} from '#/src/sagas/workers/routing-workers';

jest.mock('#/src/utils/client-api', () => ({
    fetchers: {
        sendUnsafeFileRest: jest.fn(),
    },
}));

describe('routing workers test', () => {
    const mockState = {
        settings: {
            redirect: {
                dashboard: '/dashboard',
                creditTrancheApp: '/tranche',
                creditFormsApp: '/credit-forms',
                landings: '/landings',
                overformAgreementContextRoot: '/overform-agreement',
            },
            externalCorporateCreditPage: '/corporate-credit',
            contextRoot: '/app',
        },
        creditOffers: {
            preApproved: [],
        },
        creditProductsHeader: {
            company: {
                key: 'companyKey',
            },
        },
    };

    const mockAssign = jest.fn();

    beforeAll(() => {
        Object.defineProperty(window, 'location', {
            writable: true,
            value: {
                assign: mockAssign,
            },
        });
    });

    afterEach(() => {
        mockAssign.mockClear();
    });

    afterAll(() => {
        mockAssign.mockRestore();
    });

    it('goCreditProductPageWorker should redirect correctly', () => {
        const action = {
            type: CREDIT_PRODUCTS_TYPES.GO_CREDIT_PRODUCT_PAGE,
            path: '/credit-product',
            docNumber: '123',
            customerId: 'U19931',
            tab: 'product',
        };

        return expectSaga(goCreditProductPageWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .put(push('/credit-product?docNumber=123&customerId=U19931&tab=product'))
            .run();
    });

    it('goTranchePageWorker should redirect correctly', () => {
        const action = {
            type: CREDIT_PRODUCTS_TYPES.GO_TRANCHE_PAGE,
            path: '/tranche',
            docNumber: '123',
            trancheNumber: '789',
            customerId: 'U19931',
            tab: 'tranche',
        };

        return expectSaga(goTranchePageWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .put(push('/tranche?docNumber=123&trancheNumber=789&customerId=U19931&tab=tranche'))
            .run();
    });

    it('goToHomeWorker should redirect to main page', () =>
        expectSaga(goToHomeWorker).put(push(PATHS.MAIN_PAGE)).run());

    it('goToPaymentsScheduleWorker should redirect to payment schedule', () => {
        const action = {
            type: APP_TYPES.GO_TO_PAYMENTS_SCHEDULE,
            path: '/payments-schedule',
            docNumber: '123',
            trancheNumber: '789',
        };

        return expectSaga(goToPaymentsScheduleWorker, action)
            .put(push('/payments-schedule?docNumber=123&trancheNumber=789'))
            .run();
    });

    it('goToAttachDocumentsPageWorker should redirect to attach documents page', () =>
        expectSaga(goToAttachDocumentsPageWorker).put(push(PATHS.ATTACH_DOCUMENTS)).run());

    it('goToCreditFormsAppWorker should redirect to credit forms app', async () => {
        const action = {
            type: APP_TYPES.GO_TO_CREDIT_FORMS_APP,
            offerType: ECreditOffers.OVERDRAFT,
            organizationId: 'org123', // добавлено!
        } as unknown as ReturnType<typeof goToCreditFormsApp>;

        const mockQuestionnaireCustomer = { field: 'value' };
        const mockOffer = { id: 'offer1' };
        const mockLoanMBId = 'loan123';
        const mockIsOnlineSigningAvailable = true;

        return expectSaga(goToCreditFormsAppWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    {
                        ...routingWorkersState(mockState as unknown as ApplicationState),
                        currentHeaderOrganizationEqId: 'org123',
                    },
                ],
                [
                    select(
                        goToCreditFormsParamsQuestionnaireCustomerSelector,
                        ECreditOffers.OVERDRAFT,
                    ),
                    mockQuestionnaireCustomer,
                ],
                [select(goToCreditFormsParamsOfferSelector, ECreditOffers.OVERDRAFT), mockOffer],
                [select(loanMBIdSelector), mockLoanMBId],
                [select(isOnlineSigningAvailableSelector), mockIsOnlineSigningAvailable],
            ])
            .run()
            .then(() => {
                expect(sessionStorage.getItem('questionnaireCustomer')).toEqual(
                    JSON.stringify(mockQuestionnaireCustomer),
                );
                expect(sessionStorage.getItem('offer')).toEqual(JSON.stringify(mockOffer));
            });
    });

    it('goToDashboardWorker should redirect to dashboard', async () =>
        expectSaga(goToDashboardWorker)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(window.location.assign).toHaveBeenCalledWith('/dashboard');
            }));

    it('goToDashboardAccountWorker should redirect to dashboard account', async () => {
        const action = {
            type: APP_TYPES.GO_TO_DASHBOARD_ACCOUNT,
            accountNumber: 'account123',
        } as unknown as ReturnType<typeof goToDashboardAccount>;

        return expectSaga(goToDashboardAccountWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(window.location.assign).toHaveBeenCalledWith(
                    '/dashboard/account/account123',
                );
            });
    });

    it('goCreditTrancheAppWorker should redirect to tranche app', async () => {
        const action = {
            type: APP_TYPES.GO_TRANCHE_APP,
            docNumber: 'doc456',
        } as unknown as ReturnType<typeof goCreditTrancheApp>;

        return expectSaga(goCreditTrancheAppWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(window.location.assign).toHaveBeenCalledWith('/tranche?docNumber=doc456');
            });
    });

    it('goLandingWorker should redirect to landing page', async () => {
        const action = {
            type: APP_TYPES.GO_LANDING,
            landingType: ECreditOffers.BUSINESS_CREDIT,
            campaignCode: ECreditOffersCampaignCode.DEFAULT,
            organizationId: undefined,
        };

        return expectSaga(goLandingWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(sessionStorage.getItem('acceptedCampaign')).toEqual(
                    JSON.stringify(ECreditOffersCampaignCode.DEFAULT),
                );
                expect(window.location.assign).toHaveBeenCalledWith('/landings/business-credit');
            });
    });

    it('goToOveformAgreement should redirect to overform agreement', async () =>
        expectSaga(goToOveformAgreement)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(window.location.assign).toHaveBeenCalledWith('/overform-agreement/');
            }));

    it('goToBusinessCreditCard should redirect to business credit card page', async () =>
        expectSaga(goToBusinessCreditCard)
            .provide([
                [
                    select(routingWorkersState),
                    routingWorkersState(mockState as unknown as ApplicationState),
                ],
            ])
            .run()
            .then(() => {
                expect(window.location.assign).toHaveBeenCalledWith(
                    '/overform-agreement/business-credit-card/',
                );
            }));

    it('initExternalRedirectWorker should redirect with correct parameters', async () => {
        const action = {
            type: APP_TYPES.INIT_EXTERNAL_REDIRECT,
            link: '/external-page',
            withOrganizationId: true,
            addContextRoot: true,
            parameters: { additional: 'info' },
        } as unknown as ReturnType<typeof initExternalRedirect>;

        Object.defineProperty(window, 'location', {
            writable: true,
            value: {
                assign: mockAssign,
            },
        });

        return expectSaga(initExternalRedirectWorker, action)
            .provide([
                [
                    select(routingWorkersState),
                    {
                        contextRoot: '/app',
                        currentOrganizationEqId: 'U19913',
                    },
                ],
            ])
            .run()
            .then(() => {
                expect(mockAssign).toHaveBeenCalledWith(
                    '/app/external-page?customerId=U19913&additional=info',
                );
            });
    });
});
