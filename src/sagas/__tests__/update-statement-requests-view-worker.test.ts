import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    updateStatementRequestsViewFinish,
    updateStatementRequestsViewStart,
} from '#/src/ducks/statement-requests/actions';
import { STATEMENT_REQUESTS_TYPES } from '#/src/ducks/statement-requests/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';
import { updateStatementRequestsViewWorker } from '../workers/update-statement-requests-view-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                corpLoanStatements: {
                    updateStatementRequestsView: () => Promise.resolve() as any,
                },
            },
        }) as any,
);

describe('updateStatementRequestsViewWorker', () => {
    const testState = {};

    it('should handle successful update of statement request view', () =>
        expectSaga(
            updateStatementRequestsViewWorker,
            updateStatementRequestsViewStart({
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.updateStatementRequestsView), {}],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put(
                updateStatementRequestsViewFinish({
                    newStatementsCount: 0,
                }),
            )
            .run());

    it('should handle error during update', () => {
        const error = new Error('API error');

        return expectSaga(
            updateStatementRequestsViewWorker,
            updateStatementRequestsViewStart({
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [
                    call.fn(fetchers.corpLoanStatements.updateStatementRequestsView),
                    throwError(error),
                ],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });

    it('should handle missing or invalid organization ID', () =>
        expectSaga(
            updateStatementRequestsViewWorker,
            updateStatementRequestsViewStart({
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.updateStatementRequestsView), {}],
                [select(currentHeaderOrganizationEqIdSelector), ''],
            ])
            .put(
                updateStatementRequestsViewFinish({
                    newStatementsCount: 0,
                }),
            )
            .run());

    it('should handle unauthorized error (403)', () => {
        const error = new Error('Forbidden');

        return expectSaga(
            updateStatementRequestsViewWorker,
            updateStatementRequestsViewStart({
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [
                    call.fn(fetchers.corpLoanStatements.updateStatementRequestsView),
                    throwError(error),
                ],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.UPDATE_STATEMENT_REQUESTS_VIEW_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
