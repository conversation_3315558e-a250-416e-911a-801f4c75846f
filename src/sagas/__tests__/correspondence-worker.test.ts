/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { parse } from 'date-fns';

import { setSigningErrorModalOpen } from '#/src/ducks/early-pay/actions';
import { setTimeAfterDeadlineError } from '#/src/ducks/settings/actions';
import { signDocuments } from '#/src/sagas/workers/sign-module-worker';

import { earlyRepaymentProcessingTypes } from '../../constants/credit-processing';
import { SIGN_MODULE_DOCUMENT_TYPES } from '../../constants/sign';
import { currentOrganizationSelector } from '../../ducks/organization/selectors';
import { deadlineForAcceptingApplicationsMoscowTimeSelector } from '../../ducks/settings/selectors';
import { fetchers } from '../../utils/client-api';
import { getCurrentMoscowDate, getIsDatesEqual } from '../../utils/date';
import { getIsTimeBeforeDeadline } from '../../utils/early-pay';
import { saveAndSignLetterWorker, sendLetterGroupToSign } from '../workers/correspondence-worker';

jest.mock('../../utils/client-api', () => ({
    fetchers: {
        sendLetterToBranch: jest.fn(),
        sendLetterGroupToSign: jest.fn(),
    },
}));

const letter = { id: 'testLetterId', content: 'Sample content' };
const messageId = 'test-message-id';
const organization = { eqId: 'org123' };
const deadline = { hours: 18, minutes: 0 };
const currentTime = getCurrentMoscowDate();

const action: any = {
    letter,
    data: {
        docNumber: '12345',
        paymentType: 'typeA',
        paymentDate: '2024-12-15',
    },
};

describe('correspondenceWorkerTest', () => {
    it('should handle deadline exceeded scenario', () =>
        expectSaga(saveAndSignLetterWorker, action)
            .provide([
                [select(currentOrganizationSelector), organization],
                [select(deadlineForAcceptingApplicationsMoscowTimeSelector), deadline],
                [call(getCurrentMoscowDate), currentTime],
                [
                    call(
                        getIsDatesEqual,
                        parse(action.data.paymentDate, 'yyyy-MM-dd', new Date()),
                        new Date(currentTime),
                    ),
                    true,
                ],
                [call(getIsTimeBeforeDeadline, new Date(currentTime), deadline), false],
            ])
            .put(setTimeAfterDeadlineError(true))
            .run());
});

describe('sendLetterGroupToSign', () => {
    it('should successfully send letter group to sign', () =>
        expectSaga(sendLetterGroupToSign, messageId, action.data.docNumber, action.data.paymentType)
            .provide([
                [select(currentOrganizationSelector), organization],
                [
                    call(fetchers.sendLetterGroupToSign, {
                        organizationId: organization.eqId,
                        letterIds: [messageId],
                    }),
                    undefined,
                ],
            ])
            .call(fetchers.sendLetterGroupToSign, {
                organizationId: organization.eqId,
                letterIds: [messageId],
            })
            .call(signDocuments, {
                docNumber: action.data.docNumber,
                paymentType: action.data.paymentType,
                processingType: earlyRepaymentProcessingTypes.MANUAL.toLowerCase(),
                payload: {
                    requestId: messageId,
                    systemName: 'nib-messages',
                    showResult: false,
                    documents: [
                        {
                            documentId: messageId,
                            documentType: SIGN_MODULE_DOCUMENT_TYPES.LETTER,
                            templateId: 'default',
                        },
                    ],
                },
            })
            .run());

    it('should handle error during signing', () => {
        const error = new Error('Test error');

        const docNumber = 'test-doc-number';
        const paymentType = 'test-payment-type';

        return expectSaga(sendLetterGroupToSign, messageId, docNumber, paymentType)
            .provide([
                [select(currentOrganizationSelector), organization],
                [
                    call(fetchers.sendLetterGroupToSign, {
                        organizationId: organization.eqId,
                        letterIds: [messageId],
                    }),
                    throwError(error),
                ],
            ])
            .put(setSigningErrorModalOpen(true))
            .run();
    });
});
