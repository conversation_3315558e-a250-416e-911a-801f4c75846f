import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import setIn from 'lodash/fp/set';
import { type CreditOffer, type CreditOffers } from 'thrift-services/services/credit_offers';

import { type DeepPartial } from 'arui-private/types';

import { EProductCodes } from '#/src/constants/credit-products';
import { creditProductHeaderInitialState } from '#/src/ducks/credit-products-header/reducer';

import { ECreditOffers } from '../../constants/credit-offers';
import { NBSP } from '../../constants/unicode-symbols';
import { type ApplicationState } from '../../ducks/application-state';
import { type CreditOffersState } from '../../ducks/credit-offers/reducer';
import { CREDIT_OFFERS_TYPES } from '../../ducks/credit-offers/types';
import { type OrganizationsState } from '../../ducks/organization/reducer';
import { type SettingsState } from '../../ducks/settings/reducer';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';
import { getCreditOffersWorker } from '../workers/get-credit-offers-worker';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => {
    const getCreditOffers: any = () => Promise.resolve({} as CreditOffers);

    getCreditOffers.abortLastRequest = () => ({});

    return {
        fetchers: { getCreditOffers },
    };
});

describe('getCreditOffersWorkerTest', () => {
    const creditOffer: Partial<CreditOffer> = {
        type: {
            campaignCode: null,
            id: 'overdraft',
            preApproved: true,
            visibleForUser: true,
        },
        expiryDate: { seconds: 100 },
        variants: [
            {
                maximumAmount: {
                    amount: 100,
                    currency: {
                        code: 810,
                        minorUnits: 100,
                    },
                },
                maximumTerm: 36,
                yearRatePct: 0.85,
                id: null,
                landingVersion: 2,
                maximumLimit: {
                    amount: 100,
                    currency: {
                        code: 810,
                        minorUnits: 100,
                    },
                },
                creditCardVariant: null,
            },
        ],
        id: { code: null },
        ewsId: 'EW3103222320UA9IWF',
        approvalProbability: 60,
        clientRating: 'C-2',
        ratingModel: 'RATING_MODEL_L1',
    };

    const settingsState: Partial<SettingsState> = {
        redirect: {
            dashboard: '/dashboard',
            paylist: '/paylist',
            rpay: '/payment',
            tariffs: '/tariffs',
            overdrafts: '/overdrafts',
            landings: '/credit-products-landings',
            overformAgreementContextRoot: '/credit-overform',
            creditTrancheApp: '/credit-tranche',
            credits: '/credits',
            creditFormsApp: '/credit-forms',
        },
    };

    const organizationState: DeepPartial<OrganizationsState> = {
        current: 'U12345',
        list: [
            {
                eqId: 'U12345',
            },
        ],
    };

    const creditOffersState: Partial<CreditOffersState> = {
        availableList: [],
        preApproved: [],
        isFetching: false,
    };

    const testState: Partial<ApplicationState> = {
        settings: settingsState as SettingsState,
        organization: organizationState as OrganizationsState,
        creditOffers: creditOffersState as CreditOffersState,
        creditProductsHeader: creditProductHeaderInitialState,
    };

    it('should record available express overdraft to store if fetcher return array with preapproved overdraft', () => {
        const response: ThenArg<typeof fetchers.getCreditOffers> = {
            offers: [creditOffer],
        };

        return expectSaga(getCreditOffersWorker)
            .withState(testState)
            .provide([[call.fn(fetchers.getCreditOffers), response]])
            .put({
                type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_FINISH,
                creditOffers: [],
                preApproved: [
                    {
                        titlesVariants: {
                            short: 'Овердрафт',
                            long: 'Овердрафт',
                        },
                        type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
                        text: `Возможность уходить в${NBSP}минус по${NBSP}счёту и${NBSP}пользоваться деньгами банка, когда нужно`,
                        maximumTerm: 36,
                        maximumAmount: response?.offers?.[0]?.variants?.[0]?.maximumAmount,
                        minimumAmount: response?.offers?.[0]?.variants?.[0]?.minimumAmount,
                        landingVersion: 2,
                        maximumLimit: {
                            amount: 100,
                            currency: {
                                code: 810,
                                minorUnits: 100,
                            },
                        },
                        yearRatePct: 0.85,
                        id: null,
                        code: null,
                        expiryDate: { seconds: 100 },
                        visibleForUser: true,
                        campaignCode: null,
                        preApproved: true,
                        loanPurpose: undefined,
                        productCode: EProductCodes.UP03,
                        ewsId: 'EW3103222320UA9IWF',
                        approvalProbability: 60,
                        clientRating: 'C-2',
                        ratingModel: 'RATING_MODEL_L1',
                        finalRate: undefined,
                    },
                ],
                rawCreditOffers: response.offers,
            })
            .run();
    });

    it('should record available credit product to store if fetcher returns not empty state', () => {
        const modifiedcreditOffer = setIn('creditOffer.type.preApproved', false, creditOffer);
        const response: ThenArg<typeof fetchers.getCreditOffers> = {
            offers: [modifiedcreditOffer],
        };

        return expectSaga(getCreditOffersWorker)
            .withState(testState)
            .provide([[call.fn(fetchers.getCreditOffers), response]])
            .put({
                type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_FINISH,
                creditOffers: [],
                preApproved: [
                    {
                        titlesVariants: {
                            short: 'Овердрафт',
                            long: 'Овердрафт',
                        },
                        type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
                        text: `Возможность уходить в${NBSP}минус по${NBSP}счёту и${NBSP}пользоваться деньгами банка, когда нужно`,
                        maximumTerm: 36,
                        maximumAmount: response?.offers?.[0]?.variants?.[0]?.maximumAmount,
                        minimumAmount: response?.offers?.[0]?.variants?.[0]?.minimumAmount,
                        yearRatePct: 0.85,
                        landingVersion: 2,
                        maximumLimit: {
                            amount: 100,
                            currency: {
                                code: 810,
                                minorUnits: 100,
                            },
                        },
                        expiryDate: { seconds: 100 },
                        visibleForUser: true,
                        id: null,
                        code: null,
                        campaignCode: null,
                        preApproved: true,
                        loanPurpose: undefined,
                        productCode: EProductCodes.UP03,
                        ewsId: 'EW3103222320UA9IWF',
                        approvalProbability: 60,
                        clientRating: 'C-2',
                        ratingModel: 'RATING_MODEL_L1',
                        finalRate: undefined,
                    },
                ],
                rawCreditOffers: response.offers,
            })
            .run();
    });

    it('should create error action if server return some error', () => {
        const error = new Error('error');

        return expectSaga(getCreditOffersWorker)
            .withState(testState)
            .provide([[call.fn(fetchers.getCreditOffers), throwError(error)]])
            .put({
                type: CREDIT_OFFERS_TYPES.GET_CREDIT_OFFERS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
