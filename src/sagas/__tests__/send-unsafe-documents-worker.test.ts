import { spawn } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';

import {
    sendUnsafeDocumentsStart,
    updateUnsafeDocumentsInStore,
} from '#/src/ducks/attach-documents/actions';
import {
    documentsSelector,
    totalSizeAttachDocumentsSelector,
} from '#/src/ducks/attach-documents/selectors';
import { sendUnsafeDocumentsWorker } from '#/src/sagas/workers/send-unsafe-documents-worker';
import { fetchers } from '#/src/utils/client-api';

jest.mock('#/src/utils/client-api', () => ({
    fetchers: {
        sendUnsafeFileRest: jest.fn(),
    },
}));

const mockDocuments = [
    {
        id: 'doc1',
        name: 'Document 1',
        type: 'application/pdf',
        size: 2 * 1024 * 1024,
        uploadStatus: undefined,
        file: new File([''], 'doc1.pdf'),
    },
    {
        id: 'doc2',
        name: 'Document 2',
        type: 'image/png',
        size: 6 * 1024 * 1024,
        uploadStatus: undefined,
        file: new File([''], 'doc2.png'),
    },
];

const mockFileIdResponse = { fileId: 'mockFileId' };

const mockSelectors = {
    totalSizeAttachDocumentsSelector: 5 * 1024 * 1024,
    documentsSelector: {},
};

describe('sendUnsafeDocumentsWorker', () => {
    const stateMock = {
        organization: {
            current: 'U19931',
        },
        attachDocuments: {
            loanMBId: '123',
            infoForClientDocuments: { borrowerCode: 'borrower code 123' },
        },
    };

    it('should process and upload documents successfully', () =>
        expectSaga(sendUnsafeDocumentsWorker, sendUnsafeDocumentsStart(mockDocuments))
            .withState(stateMock)
            .provide([
                [
                    select(totalSizeAttachDocumentsSelector),
                    mockSelectors.totalSizeAttachDocumentsSelector,
                ],
                [select(documentsSelector), mockSelectors.documentsSelector],
                [call(fetchers.sendUnsafeFileRest, expect.any(FormData)), mockFileIdResponse],
                [spawn, null],
            ])
            .put(
                updateUnsafeDocumentsInStore(
                    mockDocuments.map((doc) => ({
                        id: doc.id,
                        name: doc.name,
                        type: doc.type,
                        size: doc.size,
                        uploadStatus: 'UPLOADING',
                    })),
                ),
            )
            .run());

    it('should mark total size exceeding documents with an error', () => {
        const documentsExceedingLimit = [
            {
                ...mockDocuments[0],
                size: 16 * 1024 * 1024,
            },
        ];

        return expectSaga(
            sendUnsafeDocumentsWorker,
            sendUnsafeDocumentsStart(documentsExceedingLimit),
        )
            .withState(stateMock)
            .provide([
                [
                    select(totalSizeAttachDocumentsSelector),
                    mockSelectors.totalSizeAttachDocumentsSelector,
                ],
                [select(documentsSelector), mockSelectors.documentsSelector],
            ])
            .put(
                updateUnsafeDocumentsInStore([
                    {
                        ...documentsExceedingLimit[0],
                        error: 'общий размер загруженных файлов больше 20 МБ',
                        uploadStatus: 'ERROR',
                        showDelete: true,
                    },
                ]),
            )
            .run();
    });
});
