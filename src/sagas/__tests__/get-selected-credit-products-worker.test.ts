/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { type OrganizationsState } from '#/src/ducks/organization/reducer';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { creditProductsState } from '#/src/sagas/__tests__/credit-product-state';
import { getSelectedCreditProducts } from '#/src/sagas/workers/get-selected-credit-products-worker';
import { CREDIT_PRODUCTS_MAIN_MENU_MOCK } from '#/src/server/mocks/data/products';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { NoProductsError } from '#/src/utils/errors/no-products-error';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getSelectedCreditProductError,
    getSelectedCreditProductFinish,
    getSelectedCreditProductStart,
} from '../../ducks/credit-products/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';

const OVERDRAFT_MOCK = CREDIT_PRODUCTS_MAIN_MENU_MOCK.OVERDRAFT_MAIN_MENU_MOCK;

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                coreCreditProductsRestV2: {
                    getCreditProducts: jest.fn(() => Promise.resolve([OVERDRAFT_MOCK])),
                },
            },
        }) as any,
);

describe('getSelectedCreditProductsWorkerTest', () => {
    const mockResponse = [OVERDRAFT_MOCK];
    const dockNumberMock = 'doc-number-123';

    const action = getSelectedCreditProductStart(dockNumberMock);

    const mockOrganizationId = 'mock-organization-id';

    const organizationState: Partial<OrganizationsState> = {
        current: 'organization-id',
    };

    const testState: Partial<ApplicationState> = {
        organization: organizationState as OrganizationsState,
        creditProducts: creditProductsState,
    };

    const emptyProducts = {
        overdraft: null,
        credit: null,
        creditLine: null,
        sopukLine: null,
        guarantie: null,
        guarantyLine: null,
    };

    it('should return successful response with empty products', () =>
        expectSaga(getSelectedCreditProducts, action)
            .withState(testState)
            .provide([
                [select(currentOrganizationEqIdSelector), mockOrganizationId],
                [call.fn(fetchers.getCreditProductsV2), mockResponse],
            ])
            .put(getSelectedCreditProductFinish(emptyProducts))
            .run());

    it('should return no products error', () => {
        const error: NoProductsError = new NoProductsError();

        return expectSaga(getSelectedCreditProducts, action)
            .withState(testState)
            .provide([
                [select(currentOrganizationEqIdSelector), mockOrganizationId],
                [call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts), []],
            ])
            .put(getSelectedCreditProductError(error, LOG_LEVEL.WARN))
            .run();
    });

    it('should return access error 403', () => {
        const accessError = { statusCode: 403, message: 'Forbidden', name: 'Forbidden' };

        return expectSaga(getSelectedCreditProducts, action)
            .withState(testState)
            .provide([
                [select(currentOrganizationEqIdSelector), mockOrganizationId],
                [
                    call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts),
                    throwError(accessError),
                ],
            ])
            .put(
                getSelectedCreditProductError(
                    new ServerResponseError(EErrorMessages.ACCESS_ERROR),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });

    it('should return server error 500', () => {
        const serverError = new Error('Server Error');

        return expectSaga(getSelectedCreditProducts, action)
            .withState(testState)
            .provide([
                [select(currentOrganizationEqIdSelector), mockOrganizationId],
                [
                    call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts),
                    throwError(serverError),
                ],
            ])
            .put(
                getSelectedCreditProductError(
                    new ServerResponseError(getErrorMessage(serverError)),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });
});
