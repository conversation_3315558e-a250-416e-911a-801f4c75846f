import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { getActiveCreditCaseStart } from '#/src/ducks/attach-documents/actions';
import { DeliveryDocumentStatus } from '#/src/ducks/credit-processing/types';
import { type CreditProductsHeaderState } from '#/src/ducks/credit-products-header/reducer';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import { CREDIT_PROCESSING_TYPES } from '../../ducks/credit-processing/types';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';
import { getActiveCreditCaseWorker } from '../workers/get-active-credit-case-worker';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: { getActiveCreditCase: () => Promise.resolve() as any } as any,
}));

describe('getActiveCreditCaseWorkerTest', () => {
    const creditProductsHeaderState: Partial<CreditProductsHeaderState> = {
        company: {
            key: 'U12345',
        },
    };

    const testState: Partial<ApplicationState> = {
        creditProductsHeader: creditProductsHeaderState as CreditProductsHeaderState,
    };

    it('должен сохранить все данные в хранилище, если фетчер вернет их', () => {
        const deliveryInfo = {
            status: DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
            dateOfSigning: { seconds: 1234567890 },
            address: 'ул. Примерная, д.1',
            slotFrom: '09:00',
            slotTo: '10:00',
        };

        const response: ThenArg<typeof fetchers.getActiveCreditCase> = {
            stage: 'stage',
            loanMBId: 'loanMBId',
            productCode: 'productCode',
            preferredProduct: 'UC04',
            isTotalOffer: false,
            isOnlineSigningAvailable: false,
            loanAmount: '1000000',
            productName: 'Кредит для бизнеса',
            scoringExpiryDate: {
                seconds: 1651266000,
            },
            platformId: 'SFA',
            deliveryInfo,
        };

        return expectSaga(getActiveCreditCaseWorker, getActiveCreditCaseStart())
            .withState(testState)
            .provide([[call.fn(fetchers.getActiveCreditCase), response]])
            .put({
                type: CREDIT_PROCESSING_TYPES.GET_ACTIVE_CREDIT_CASE_FINISH,
                stage: 'stage',
                loanMBId: 'loanMBId',
                productCode: 'productCode',
                preferredProduct: 'UC04',
                isTotalOffer: false,
                isOnlineSigningAvailable: false,
                loanAmount: '1000000',
                productName: 'Кредит для бизнеса',
                scoringExpiryDate: {
                    seconds: 1651266000,
                },
                platformId: 'SFA',
                deliveryInfo,
            })
            .run();
    });

    it('должен создать ошибку, если сервер возвращает ошибку', () => {
        const error = new Error('error');

        return expectSaga(getActiveCreditCaseWorker, getActiveCreditCaseStart())
            .withState(testState)
            .provide([[call.fn(fetchers.getActiveCreditCase), throwError(error)]])
            .put({
                type: CREDIT_PROCESSING_TYPES.GET_ACTIVE_CREDIT_CASE_ERROR,
                error: new ServerResponseError(getErrorMessage(error)),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
