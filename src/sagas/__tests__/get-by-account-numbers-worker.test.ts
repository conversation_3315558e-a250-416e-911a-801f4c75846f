/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type ApiGetByAccountNumbersResponse } from 'corp-accounts-api-typescript-services';

import {
    getByAccountNumbersError,
    getByAccountNumbersFinish,
    getByAccountNumbersStart,
} from '#/src/ducks/accounts/actions';
import { initialState } from '#/src/ducks/accounts/reducer';
import { currentOrganizationSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';
import { getByAccountNumbersWorker } from '../workers/get-by-account-numbers-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: { mksAccounts: { getByAccountNumbers: () => Promise.resolve() as any } },
        }) as any,
);
describe('getByAccountNumbersWorker', () => {
    const ACCOUNT_NUMBER = 'TEST';
    const GET_BY_ACCOUNT_NUMBERS_RESPONSE_MOCK: ApiGetByAccountNumbersResponse = [
        {
            mainInfo: { number: ACCOUNT_NUMBER },
            currency: {
                code: undefined,
                mnemonics: undefined,
                minorUnitsNumber: undefined,
            },
            specConditions: [],
            clientInfo: {
                clientName: undefined,
                isOwner: undefined,
                ownerName: undefined,
                ownerSurname: undefined,
                ownerPatronymic: undefined,
            },
        },
    ];

    it('should record response in state filteredAccounts field', () =>
        expectSaga(
            getByAccountNumbersWorker,
            getByAccountNumbersStart({ accountNumbers: [ACCOUNT_NUMBER] }),
        )
            .withState(initialState)
            .provide([
                [select(currentOrganizationSelector), { name: 'organizationId', eqId: 'eqId' }],
                [
                    call.fn(fetchers.mksAccounts.getByAccountNumbers),
                    GET_BY_ACCOUNT_NUMBERS_RESPONSE_MOCK,
                ],
            ])
            .put(getByAccountNumbersFinish(GET_BY_ACCOUNT_NUMBERS_RESPONSE_MOCK))
            .run());

    it('should create error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(
            getByAccountNumbersWorker,
            getByAccountNumbersStart({ accountNumbers: [ACCOUNT_NUMBER] }),
        )
            .withState(initialState)
            .provide([
                [select(currentOrganizationSelector), { name: 'organizationId', eqId: 'eqId' }],
                [call.fn(fetchers.mksAccounts.getByAccountNumbers), throwError(ERROR)],
            ])
            .put(getByAccountNumbersError(new ServerResponseError(ERROR.message), LOG_LEVEL.ERROR))
            .run();
    });
});
