/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';

import { VOC_EVENTS } from '#/src/constants/voc';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    isKIBCategoryCodeSelector,
    notifyAboutAdvancedRepaySelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { sendMailKMStart, setSuccessfullySignedModalOpen } from '#/src/ducks/early-pay/actions';
import { sendVOCEvent } from '#/src/ducks/shared/actions';
import { getSignModuleFunctionSelector } from '#/src/ducks/sign-module/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { signDocuments } from '#/src/sagas/workers/sign-module-worker';

jest.mock('@alfalab/scripts-modules', () => ({
    createModuleLoader: jest.fn(),
    createServerStateModuleFetcher: jest.fn(),
    executeModuleFactory: jest.fn(),
}));

describe('signModuleWorkerTest', () => {
    const mockSignFunction = jest.fn();
    const mockPayload = {
        requestId: '123',
        systemName: 'testSystem',
        showResult: true,
        documents: [{ documentId: '1', documentType: 'type', templateId: 'template' }],
    };

    const mockAction = {
        payload: mockPayload,
        docNumber: '123',
        paymentType: 'testPayment',
        processingType: 'testProcessing',
    };

    it('should call the sign function and dispatch success actions', () =>
        expectSaga(signDocuments, mockAction)
            .provide([
                [select(getSignModuleFunctionSelector), mockSignFunction],
                [call.fn(mockSignFunction), undefined],
            ])
            .call(mockSignFunction, mockPayload)
            .put(
                trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSuccessfullySignedModal, {
                    agreementNumber: '123',
                    processingType: 'testProcessing',
                    paymentType: 'testPayment',
                }),
            )
            .put(setSuccessfullySignedModalOpen(true))
            .run());

    it('should handle callEventsAfterSuccessSign logic', () =>
        expectSaga(signDocuments, mockAction)
            .provide([
                [select(getSignModuleFunctionSelector), mockSignFunction],
                [call.fn(mockSignFunction), undefined],
                [select(notifyAboutAdvancedRepaySelector), true],
                [select(isKIBCategoryCodeSelector), true],
            ])
            .put(sendVOCEvent(VOC_EVENTS.NIB_CP_EP))
            .put(sendMailKMStart())
            .run());
});
