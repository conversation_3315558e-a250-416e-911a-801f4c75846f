import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type TClientLimitSettingProperties } from 'thrift-services/services/credit_products';

import { setOverdraftRepaymentStart } from '#/src/ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import { getOverdraftLimitSettingsState } from '#/src/sagas/workers/get-overdraft-limit-settings-worker';
import { setOverdraftRepaymentWorker } from '#/src/sagas/workers/set-overdraft-repayment-worker';
import { ERepaymentType } from '#/src/types/overdraft';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

const DEAL_ID = '1';
const REPAYMENT_TYPE = ERepaymentType.Online;

const overdraftLimitSettingsState: ReturnType<typeof getOverdraftLimitSettingsState> = {
    profileId: '1',
    organizationId: '2',
    docNumber: '3',
};

const setRepaymentTypeTestResponse: TClientLimitSettingProperties = {
    repaymentResult: {
        changeRepaymentTypeId: 1,
        repaymentType: 'EOD',
        statusCode: '200',
        creationDate: {
            seconds: 123,
        },
        approximateCompletionDate: {
            seconds: 123,
        },
    },
    dealId: Number(DEAL_ID),
};

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: {
        getClientLimitSettingPropertiesV2: () => Promise.resolve(),
    } as any,
}));

describe('setOverdraftRepaymentWorker', () => {
    it('should successfully change repayment', () => {
        const response: ThenArg<typeof fetchers.getClientLimitSettingPropertiesV2> = {
            ...setRepaymentTypeTestResponse,
        };

        return expectSaga(
            setOverdraftRepaymentWorker,
            setOverdraftRepaymentStart(DEAL_ID, REPAYMENT_TYPE),
        )
            .provide([
                [call.fn(fetchers.getClientLimitSettingPropertiesV2), response],
                [select(getOverdraftLimitSettingsState), overdraftLimitSettingsState],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_FINISH,
                repaymentChangeData: response.repaymentResult,
                dealId: DEAL_ID,
            })
            .run();
    });

    it('should return error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(
            setOverdraftRepaymentWorker,
            setOverdraftRepaymentStart(DEAL_ID, REPAYMENT_TYPE),
        )
            .provide([
                [call.fn(fetchers.getClientLimitSettingPropertiesV2), throwError(ERROR)],
                [select(getOverdraftLimitSettingsState), overdraftLimitSettingsState],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_REPAYMENT_ERROR,
                error: new ServerResponseError(ERROR.message),
                dealId: DEAL_ID,
            })
            .run();
    });
});
