/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type EmailToKMResponse } from 'corp-early-repayment-api-typescript-services';

import { sendMailKMFinish } from '#/src/ducks/early-pay/actions';
import { initialState } from '#/src/ducks/early-pay/reducer';
import { earlyRepaymentDataSelector } from '#/src/ducks/early-pay/selectors';
import { EARLY_PAY } from '#/src/ducks/early-pay/types';
import { currentOrganizationSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';
import { sendMailKMWorker } from '../workers/send-email-km-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: { earlyRepaymentRest: { sendEmailToKM: () => Promise.resolve() as any } },
        }) as any,
);
describe('sendMailKMWorker', () => {
    const SEND_EMAIL_KM_RESPONSE_MOCK: EmailToKMResponse = {
        emailId: 'emailId',
        status: 'status',
        message: 'message',
    };

    it('should record response in state category field', () =>
        expectSaga(sendMailKMWorker)
            .withState(initialState)
            .provide([
                [select(currentOrganizationSelector), { name: 'organizationId', eqId: 'eqId' }],
                [select(earlyRepaymentDataSelector), initialState.data],
                [call.fn(fetchers.earlyRepaymentRest.sendEmailToKM), SEND_EMAIL_KM_RESPONSE_MOCK],
            ])
            .put(sendMailKMFinish())
            .run());

    it('should create error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(sendMailKMWorker)
            .withState(initialState)
            .provide([
                [select(currentOrganizationSelector), { name: 'organizationId', eqId: 'eqId' }],
                [select(earlyRepaymentDataSelector), initialState.data],
                [call.fn(fetchers.earlyRepaymentRest.sendEmailToKM), throwError(ERROR)],
            ])
            .put({
                type: EARLY_PAY.SEND_MAIL_KM_ERROR,
                error: new ServerResponseError(ERROR.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
