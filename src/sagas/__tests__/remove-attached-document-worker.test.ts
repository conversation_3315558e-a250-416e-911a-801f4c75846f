import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { removeAttachedDocument } from '#/src/ducks/attach-documents/actions';
import {
    borrowerCodeSelector,
    documentsSelector,
    loanMBIdSelector,
} from '#/src/ducks/attach-documents/selectors';
import { ATTACH_DOCUMENTS_TYPES } from '#/src/ducks/attach-documents/types';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';

import { removeAttachedDocumentWorker } from '../workers/remove-attached-document-worker';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => {
    const removeAttachedFile: any = () => Promise.resolve({} as typeof fetchers.removeAttachedFile);

    removeAttachedFile.abortLastRequest = () => ({});

    return {
        fetchers: { removeAttachedFile },
    };
});

const documents = new Map().set('id', '123');

describe('removeAttachedDocumentWorker', () => {
    it('fetches removeAttachedFile', () =>
        expectSaga(removeAttachedDocumentWorker, removeAttachedDocument('123'))
            .provide([
                [select(currentOrganizationEqIdSelector), '123'],
                [select(loanMBIdSelector), '123'],
                [select(borrowerCodeSelector), '123'],
                [select(documentsSelector), documents],
                [call.fn(fetchers.removeAttachedFile), undefined],
            ])
            .put.like({
                action: { type: ATTACH_DOCUMENTS_TYPES.REMOVE_UNSAFE_DOCUMENT_FROM_STORE },
            })
            .run());

    it('fetches removeAttachedFile error', () =>
        expectSaga(removeAttachedDocumentWorker, removeAttachedDocument('123'))
            .provide([
                [select(currentOrganizationEqIdSelector), '123'],
                [select(loanMBIdSelector), '123'],
                [select(borrowerCodeSelector), '123'],
                [select(documentsSelector), documents],
                [call.fn(fetchers.removeAttachedFile), throwError(new Error('err'))],
            ])
            .put.like({ action: { type: ATTACH_DOCUMENTS_TYPES.REMOVE_ATTACHED_DOCUMENT_ERROR } })
            .run());
});
