import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { filteredAccountsSelector } from '#/src/ducks/accounts/selectors';
import { getTranchesSelectStart } from '#/src/ducks/credit-products/tranches-select/actions';
import { type TranchesSelectState } from '#/src/ducks/credit-products/tranches-select/reducer';
import { TRANCHES_SELECT_TYPES } from '#/src/ducks/credit-products/tranches-select/types';

import { fetchers } from '../../utils/client-api';
import { type ThenArg } from '../../utils/promise-helper';
import { getTranchesSelectWorker } from '../workers/get-tranches-select-worker';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: {
        getTrancheCreditProductsCountV2: (() =>
            Promise.resolve()) as unknown as typeof fetchers.getTrancheCreditProductsCountV2,
        getTrancheCreditProductsV2: (() =>
            Promise.resolve()) as unknown as typeof fetchers.getTrancheCreditProductsV2,
        mksAccounts: {
            checkAccess: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.checkAccess,
            getTurnoversCountByFilter: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getTurnoversCountByFilter,
            getTurnoversByFilter: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getTurnoversByFilter,
            getByCustomerIds: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getByCustomerIds,
            getByCustomerId: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getByCustomerId,
            getBalanceByCustomerId: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getBalanceByCustomerId,
            getAccountTypes: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getAccountTypes,
            getAccountsCountByFilter: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getAccountsCountByFilter,
            getAccountsByFilter: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getAccountsByFilter,
            getByAccountNumbers: (() =>
                Promise.resolve() as any) as unknown as typeof fetchers.mksAccounts.getByAccountNumbers,
        },
    },
}));

describe('getTranchesSelectWorker', () => {
    const actionParams = {
        docNumber: '11',
        organizationId: '22',
    };

    const testState: TranchesSelectState = {
        isLoading: false,
        error: null,
        tranches: [],
    };

    it('fetches tranches', () => {
        const responseCount: ThenArg<typeof fetchers.getTrancheCreditProductsCountV2> = 1;
        const response: ThenArg<typeof fetchers.getTrancheCreditProductsV2> = [
            { docNumber: '1', servicingAccounts: [{ number: '123' }] },
        ];

        return expectSaga(getTranchesSelectWorker, getTranchesSelectStart(actionParams))
            .withState(testState)
            .provide([
                [select(filteredAccountsSelector), []],

                [call.fn(fetchers.getTrancheCreditProductsCountV2), responseCount],
                [call.fn(fetchers.getTrancheCreditProductsV2), response],
                [call.fn(fetchers.mksAccounts.getByAccountNumbers), []],
            ])
            .put.like({ action: { type: TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_FINISH } })
            .run();
    });

    it('logs error', () => {
        const error = 'some error';

        return expectSaga(getTranchesSelectWorker, getTranchesSelectStart(actionParams))
            .withState(testState)
            .provide([
                [select(filteredAccountsSelector), []],
                [call.fn(fetchers.getTrancheCreditProductsCountV2), throwError(new Error(error))],
            ])
            .put.like({
                action: {
                    type: TRANCHES_SELECT_TYPES.GET_TRANCHES_SELECT_ERROR,
                },
            })
            .run();
    });
});
