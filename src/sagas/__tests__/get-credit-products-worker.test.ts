import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import setIn from 'lodash/fp/set';

import { createCreditProduct } from '#/src/server/mocks/utils/create-credit-product';

import { ECreditProducts, ECreditProductsCodes } from '../../constants/credit-products';
import { type ApplicationState } from '../../ducks/application-state';
import { getCreditProductsStart } from '../../ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '../../ducks/credit-products/types';
import { type OrganizationsState } from '../../ducks/organization/reducer';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { mapCreditProduct } from '../../utils/credit-products-mappers';
import { NoProductsError } from '../../utils/errors/no-products-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';
import { getCreditProductsWorker } from '../workers/get-credit-products-worker';

import { creditProductsState } from './credit-product-state';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: {
        coreCreditProductsRestV2: {
            getCreditProducts: () => Promise.resolve(),
        },
    } as any /* eslint-disable-line @typescript-eslint/no-explicit-any */,
}));

describe('getCreditProductsWorkerTest', () => {
    const getCreditProductsWorkerArgs = {
        filter: {
            docNumber: '',
        },
        withFault: true,
    };

    const creditProduct = createCreditProduct({
        productType: ECreditProductsCodes.BUSINESS_CREDIT,
    });

    const organizationState: Partial<OrganizationsState> = {
        current: 'U12345',
    };

    const testState: Partial<ApplicationState> = {
        organization: organizationState as OrganizationsState,
        creditProducts: creditProductsState,
    };

    it('should record credit to store if fetcher return array with requisities.product = 1', () => {
        const response: ThenArg<typeof fetchers.coreCreditProductsRestV2.getCreditProducts> = [
            creditProduct,
        ];
        const mappedCredit = mapCreditProduct(creditProduct, ECreditProducts.BUSINESS_CREDIT);

        return expectSaga(
            getCreditProductsWorker,
            getCreditProductsStart({
                docNumber: getCreditProductsWorkerArgs.filter.docNumber,
                withFault: getCreditProductsWorkerArgs.withFault,
            }),
        )
            .withState(testState)
            .provide([[call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts), response]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH,
                mappedCredits: { [creditProduct.docNumber ?? '']: mappedCredit },
                mappedOverdrafts: {},
                mappedCreditCards: {},
                mappedCreditLines: {},
                mappedGuaranties: {},
                mappedGuarantyLines: {},
                mappedSopukLine: {},
                allProductsIndexedByDocNumber: { [creditProduct.docNumber ?? '']: creditProduct },
                withClosed: false,
            })
            .run();
    });

    it('should record overdraft to store if fetcher return array with requisities.product = 2', () => {
        const modifiedCreditProduct = setIn(
            'requisites.product',
            ECreditProductsCodes.OVERDRAFT,
            creditProduct,
        );
        const mappedOverdraft = mapCreditProduct(modifiedCreditProduct, ECreditProducts.OVERDRAFT);
        const response: ThenArg<typeof fetchers.coreCreditProductsRestV2.getCreditProducts> = [
            modifiedCreditProduct,
        ];

        return expectSaga(
            getCreditProductsWorker,
            getCreditProductsStart({
                docNumber: getCreditProductsWorkerArgs.filter.docNumber,
                withFault: getCreditProductsWorkerArgs.withFault,
            }),
        )
            .withState(testState)
            .provide([[call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts), response]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_FINISH,
                mappedCredits: {},
                mappedOverdrafts: { [modifiedCreditProduct.docNumber ?? '']: mappedOverdraft },
                mappedCreditCards: {},
                mappedCreditLines: {},
                mappedGuaranties: {},
                mappedGuarantyLines: {},
                mappedSopukLine: {},
                allProductsIndexedByDocNumber: {
                    [modifiedCreditProduct.docNumber ?? '']: modifiedCreditProduct,
                },
                withClosed: false,
            })
            .run();
    });

    it('should throw NO_CREDIT_PRODUCTS error if fetcher return empty array', () => {
        const response: ThenArg<typeof fetchers.getCreditProductsV2> = [];

        return expectSaga(
            getCreditProductsWorker,
            getCreditProductsStart({
                docNumber: getCreditProductsWorkerArgs.filter.docNumber,
                withFault: getCreditProductsWorkerArgs.withFault,
            }),
        )
            .withState(testState)
            .provide([[call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts), response]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR,
                error: new NoProductsError(),
                logLevel: LOG_LEVEL.WARN,
            })
            .run();
    });

    it('should throw SERVER_RESPONSE_ERROR error if fetcher return error', () => {
        const error = new Error('error');

        return expectSaga(
            getCreditProductsWorker,
            getCreditProductsStart({
                docNumber: getCreditProductsWorkerArgs.filter.docNumber,
                withFault: getCreditProductsWorkerArgs.withFault,
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.coreCreditProductsRestV2.getCreditProducts), throwError(error)],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_CREDIT_PRODUCTS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
