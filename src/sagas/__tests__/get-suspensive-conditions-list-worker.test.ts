import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import {
    DealType,
    MonitoringOperationStatus,
    Status,
    SuspensiveConditionType,
} from 'corp-credit-products-api-typescript-services';

import { type ApplicationState } from '#/src/ducks/application-state';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { getSuspensiveConditionsStart } from '#/src/ducks/suspensive-conditions/actions';
import { initialState } from '#/src/ducks/suspensive-conditions/reducer';
import { isSuspensiveConditionsVisibleSelector } from '#/src/ducks/suspensive-conditions/selectors';
import { SUSPENSIVE_CONDITIONS_TYPES } from '#/src/ducks/suspensive-conditions/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { getSuspensiveConditionsListWorker } from '../workers/get-suspensive-conditions-list-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                creditProductsRestV2: {
                    getSuspensiveConditions: () => Promise.resolve() as any,
                },
            },
        }) as any,
);

describe('getSuspensiveConditionsListWorker', () => {
    const testState: Partial<ApplicationState> = {
        suspensiveConditions: initialState,
    };

    it('should handle when currentOrganizationEqIdSelector returns a non-empty string', () => {
        const condition = {
            suspensiveConditionId: 4410,
            monitoringOperationId: 593567,
            dealId: 63037763,
            type: SuspensiveConditionType.Ou1,
            description: 'Automation test generated description',
            planDate: 1716751516,
            status: MonitoringOperationStatus.Violated,
        };

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [condition],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 10,
                totalPages: 1,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), 'organizationId'],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: [condition],
                pagesCount: 1,
                isVisible: true,
            })
            .run();
    });

    it('should handle when isSuspensiveConditionsVisibleSelector returns false', () => {
        const condition = {
            suspensiveConditionId: 4410,
            monitoringOperationId: 593567,
            dealId: 63037763,
            type: SuspensiveConditionType.Ou1,
            description: 'Automation test generated description',
            planDate: 1716751516,
            status: MonitoringOperationStatus.Violated,
        };

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [condition],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 10,
                totalPages: 1,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), false],
            ])
            .not.put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
            })
            .run();
    });

    it('should handle when fetchers.creditProductsRestV2.getSuspensiveConditions throws an error', () => {
        const error = new Error('API error');

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), throwError(error)],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });

    it('should handle different page number and page size', () => {
        const condition = {
            suspensiveConditionId: 4410,
            monitoringOperationId: 593567,
            dealId: 63037763,
            type: SuspensiveConditionType.Ou1,
            description: 'Automation test generated description',
            planDate: 1716751516,
            status: MonitoringOperationStatus.Violated,
        };

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [condition],
            pagination: {
                pageNumber: 2,
                pageSize: 5,
                totalElements: 10,
                totalPages: 2,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 2,
                pageSize: 5,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: [condition],
                pagesCount: 2,
                isVisible: true,
            })
            .run();
    });

    it('should handle multiple pages of suspensive conditions', () => {
        const conditions = Array.from({ length: 15 }, (_, index) => ({
            suspensiveConditionId: 4410 + index,
            monitoringOperationId: 593567 + index,
            dealId: 63037763,
            type: SuspensiveConditionType.Ou1,
            description: `Automation test generated description ${index}`,
            planDate: 1716751516,
            status: MonitoringOperationStatus.Violated,
        }));

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: conditions,
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 15,
                totalPages: 2,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: conditions,
                pagesCount: 2,
                isVisible: true,
            })
            .run();
    });

    it('should handle no suspensive conditions available', () => {
        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 0,
                totalPages: 0,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: [],
                pagesCount: 0,
                isVisible: false,
            })
            .run();
    });

    it('should handle incomplete or missing pagination information', () => {
        const condition = {
            suspensiveConditionId: 4410,
            monitoringOperationId: 593567,
            dealId: 63037763,
            type: SuspensiveConditionType.Ou1,
            description: 'Automation test generated description',
            planDate: 1716751516,
            status: MonitoringOperationStatus.Violated,
        };

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [condition],
            pagination: {}, // Incomplete pagination information
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: [condition],
                pagesCount: 0,
                isVisible: false,
            })
            .run();
    });

    it('should handle suspensive condition with missing or undefined fields', () => {
        const condition = {
            suspensiveConditionId: 4410,
            monitoringOperationId: 593567,
            dealId: 63037763,
            // Missing "type" field
            // Missing "description" field
            // Missing "planDate" field
            status: MonitoringOperationStatus.Violated,
        };

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> = {
            suspensiveConditions: [condition],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 1,
                totalPages: 1,
            },
        };

        return expectSaga(
            getSuspensiveConditionsListWorker,
            getSuspensiveConditionsStart({
                dealId: '',
                dealType: DealType.All,
                status: Status.All,
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getSuspensiveConditions), response],
                [select(currentOrganizationEqIdSelector), ''],
                [select(isSuspensiveConditionsVisibleSelector), true],
            ])
            .put({
                type: SUSPENSIVE_CONDITIONS_TYPES.GET_SUSPENSIVE_CONDITIONS_FINISH,
                list: [condition],
                pagesCount: 1,
                isVisible: true,
            })
            .run();
    });
});
