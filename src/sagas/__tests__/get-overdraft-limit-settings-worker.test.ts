import { select } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type RouterState } from 'connected-react-router';
import { type Amount } from 'thrift-services/entities';

import { type ApplicationState } from '../../ducks/application-state';
import { CREDIT_PRODUCTS_TYPES } from '../../ducks/credit-products/types';
import { type OrganizationsState } from '../../ducks/organization/reducer';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';
import {
    getOverdraftLimitSettingsState,
    getOverdraftLimitSettingsWorker,
} from '../workers/get-overdraft-limit-settings-worker';

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: { getClientLimitSettingPropertiesV2: () => Promise.resolve() as any },
        }) as any,
);

describe('getOverdraftLimitSettingsWorkerTest', () => {
    const organizationState: Partial<OrganizationsState> = {
        current: 'U12345',
    };

    const docNumber = 'D123';

    const routeState: Partial<RouterState> = {
        location: {
            search: `docNumber=${docNumber}`,
            pathname: '',
            state: '',
            hash: '',
            query: {},
        },
    };

    const testState: Partial<ApplicationState> = {
        user: { id: 'XA3SDX' },
        organization: organizationState as OrganizationsState,
        router: routeState as RouterState,
    };

    const limit: Amount = {
        amount: 1000000000,
        currency: {
            code: 810,
            mnemonicCode: 'RUR',
            minorUnits: 100,
            unicodeSymbol: '₽',
            fullName: 'Российский рубль',
        },
    };

    const overdraftLimitSettings: ThenArg<typeof fetchers.getClientLimitSettingPropertiesV2> = {
        hasClientLimitSetting: true,
        maxLimit: limit,
        minLimit: limit,
        overdraftLimitRulesUrl: '',
        dealId: 1,
    };

    it('should get overdraft limit settings', () =>
        expectSaga(getOverdraftLimitSettingsWorker)
            .withState(testState)
            .provide([
                [call.fn(fetchers.getClientLimitSettingPropertiesV2), overdraftLimitSettings],
                [
                    select(getOverdraftLimitSettingsState),
                    { organizationId: organizationState.current, docNumber },
                ],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_FINISH,
                docNumber,
                hasClientLimitSetting: overdraftLimitSettings.hasClientLimitSetting,
                maxClientLimit: overdraftLimitSettings.maxLimit,
                minClientLimit: overdraftLimitSettings.minLimit,
                repaymentResult: overdraftLimitSettings.repaymentResult,
                overdraftLimitRulesUrl: overdraftLimitSettings.overdraftLimitRulesUrl,
            })
            .run());

    it('should create error action if server return some error', () => {
        const error = new Error('error');

        return expectSaga(getOverdraftLimitSettingsWorker)
            .withState(testState)
            .provide([[call.fn(fetchers.getClientLimitSettingPropertiesV2), throwError(error)]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_LIMIT_SETTINGS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
