import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getProgressStagesError,
    getProgressStagesFinish,
    getProgressStagesStart,
} from '#/src/ducks/credit-requests/actions';
import { type TCreditRequestProgressStagesResponse } from '#/src/ducks/credit-requests/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { getProgressStagesWorker } from '../workers/get-credit-requests-progress-stages-worker';

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                statusModel: {
                    getApplicationProgressStages: () => Promise.resolve() as any,
                },
            },
        }) as any,
);

describe('getProgressStagesWorker tests', () => {
    const mockOrganizationId: ReturnType<typeof currentHeaderOrganizationEqIdSelector> =
        'mock-organization-id';

    const mockResponse: TCreditRequestProgressStagesResponse = [
        {
            stageOrder: 1,
            stageName: 'В АРХИВЕ',
            progressStage: 'CURRENT',
            sla: undefined,
            status: {
                clientStatusName: 'В АРХИВЕ',
                clientDescription: 'Заявка закрыта',
            },
        },
    ];

    const action = getProgressStagesStart('mock_request_id');

    it('should work succesfully', () =>
        expectSaga(getProgressStagesWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [call.fn(fetchers.statusModel.getApplicationProgressStages), mockResponse],
            ])
            .call(fetchers.statusModel.getApplicationProgressStages, {
                headers: { [HEADERS.OPENAPI_COMPANY_ID]: mockOrganizationId },
                urlParams: { id: 'mock_request_id' },
            })
            .put(getProgressStagesFinish(mockResponse))
            .run());

    it('should handle unexpected error', () =>
        expectSaga(getProgressStagesWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [
                    call.fn(fetchers.statusModel.getApplicationProgressStages),
                    throwError(new Error('Unexpected Error')),
                ],
            ])
            .put(
                getProgressStagesError(
                    new ServerResponseError(getErrorMessage(new Error('Unexpected Error'))),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run());

    it('should return 500 server error', () => {
        const serverError = new Error('Server Error');

        return expectSaga(getProgressStagesWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [
                    call.fn(fetchers.statusModel.getApplicationProgressStages),
                    throwError(serverError),
                ],
            ])
            .put(
                getProgressStagesError(
                    new ServerResponseError(getErrorMessage(serverError)),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });
});
