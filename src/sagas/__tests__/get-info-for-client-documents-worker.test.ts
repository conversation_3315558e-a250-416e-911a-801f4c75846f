import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type TInfoForClientDocumentsResponse } from 'thrift-services/services/credit_application_processing';

import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { type ApplicationState } from '#/src/ducks/application-state';
import { type AttachDocumentsState } from '#/src/ducks/attach-documents/reducer';
import { ATTACH_DOCUMENTS_TYPES } from '#/src/ducks/attach-documents/types';
import { type OrganizationsState } from '#/src/ducks/organization/reducer';
import { CLIENT_DOCUMENTS_ATTACH_METRICS } from '#/src/metrics';
import { getInfoForClientDocumentsWorker } from '#/src/sagas/workers/get-info-for-client-documents-worker';
import { EClientDocumentsRequestStatuses } from '#/src/types/client-documents';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { getActiveCreditCaseWorker } from '../workers/get-active-credit-case-worker';

jest.mock(
    '../../utils/client-api',
    (): {
        fetchers: Partial<typeof fetchers>;
    } => ({
        fetchers: {
            getInfoForClientDocuments: () => Promise.resolve() as any,
            getRequestStatus: () => Promise.resolve() as any,
        } as any,
    }),
);

describe('getInfoForClientDocumentsWorkerTest', () => {
    const organizationState: Partial<OrganizationsState> = {
        current: 'U12345',
    };

    const attachDocumentsState: Partial<AttachDocumentsState> = {
        loanMBId: 'loanMBId',
        stage: 'stage',
    };

    const testState: Partial<ApplicationState> = {
        organization: organizationState as OrganizationsState,
        attachDocuments: attachDocumentsState as AttachDocumentsState,
    };

    it('should record info for client documents to store if fetcher completed success', () => {
        const infoForClientDocuments: TInfoForClientDocumentsResponse = {
            borrowerCode: 'MB-6365P1504728338',
            docsRequestComments: [
                {
                    text: 'Тестовый документ 1',
                },
            ],
            loanAnalystInfo: {
                phones: {
                    mobile: '74954561235',
                    work: '74954561233',
                    additional: '4544',
                },
                fullName: 'Иванов Иван Иванович',
            },
        };

        const getInfoForClientDocumentsResponse: ThenArg<
            typeof fetchers.getInfoForClientDocuments
        > = infoForClientDocuments;
        const getRequestStatusResponse: ThenArg<typeof fetchers.getRequestStatus> =
            EClientDocumentsRequestStatuses.INIT;

        return expectSaga(getInfoForClientDocumentsWorker)
            .withState(testState)
            .provide([
                [call.fn(getActiveCreditCaseWorker), undefined],
                [call.fn(fetchers.getRequestStatus), getRequestStatusResponse],
                [call.fn(fetchers.getInfoForClientDocuments), getInfoForClientDocumentsResponse],
            ])
            .put(
                trackAlfaMetrics(CLIENT_DOCUMENTS_ATTACH_METRICS.getClientDocumentsStatus, {
                    loanMBId: attachDocumentsState.loanMBId,
                    borrowerCode: infoForClientDocuments.borrowerCode,
                    status: getRequestStatusResponse,
                }),
            )
            .put({
                type: ATTACH_DOCUMENTS_TYPES.SET_CLIENT_DOCUMENTS_REQUEST_STATUS,
                status: EClientDocumentsRequestStatuses.INIT,
            })
            .put({
                type: ATTACH_DOCUMENTS_TYPES.SET_INFO_FOR_CLIENT_DOCUMENTS,
                infoForClientDocuments,
            })
            .put({
                type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_FINISH,
            })
            .run();
    });

    it('should create error action if getInfoForClientDocuments return some error', () => {
        const getRequestStatusResponse: ThenArg<typeof fetchers.getRequestStatus> =
            EClientDocumentsRequestStatuses.INIT;
        const error = new Error('error');

        return expectSaga(getInfoForClientDocumentsWorker)
            .withState(testState)
            .provide([
                [call.fn(getActiveCreditCaseWorker), undefined],
                [call.fn(fetchers.getRequestStatus), getRequestStatusResponse],
                [call.fn(fetchers.getInfoForClientDocuments), throwError(error)],
            ])
            .put({
                type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_ERROR,
                error: new ServerResponseError(),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });

    it('should create error action if getRequestStatus return some error', () => {
        const error = new Error('error');

        return expectSaga(getInfoForClientDocumentsWorker)
            .withState(testState)
            .provide([
                [call.fn(getActiveCreditCaseWorker), undefined],
                [call.fn(fetchers.getRequestStatus), throwError(error)],
                [call.fn(fetchers.getInfoForClientDocuments), throwError(error)],
            ])
            .put({
                type: ATTACH_DOCUMENTS_TYPES.GET_INFO_FOR_CLIENT_DOCUMENTS_ERROR,
                error: new ServerResponseError(),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
