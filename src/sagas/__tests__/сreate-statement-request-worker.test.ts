import { delay, select } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { StatementFormat } from 'corp-loan-statements-api-typescript-services';

import {
    createStatementRequestError,
    createStatementRequestFinish,
} from '#/src/ducks/statement-requests/actions';
import { STATEMENT_REQUESTS_TYPES } from '#/src/ducks/statement-requests/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import {
    currentOrganizationEqIdSelector,
    getCurrentOrganizationShortNameSelector,
} from '../../ducks/organization/selectors';
import { fetchers } from '../../utils/client-api';
import { createStatementRequestWorker } from '../workers/create-statement-request-worker';

jest.mock('../../utils/client-api', () => ({
    fetchers: {
        corpLoanStatements: {
            createStatementRequest: jest.fn(),
        },
    },
}));

describe('createStatementRequestWorker', () => {
    const docNumber = '0D7O1L';
    const fromDate = '2024-11-11';
    const toDate = '2024-11-13';
    const format = StatementFormat.Pdf;
    const selectedTranches = ['0D7O1T001'];
    const withSignature = false;
    const organizationId = 'UABITA';

    const currentOrganizationId = 'UABITA';
    const currentOrganizationShortName = 'ИП';

    it('should dispatch createStatementRequestFinish on success', () => {
        const result = {};

        return expectSaga(createStatementRequestWorker, {
            type: STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_START,
            docNumber,
            fromDate,
            toDate,
            format,
            selectedTranches,
            withSignature,
            organizationId,
        })
            .withState({
                organization: { current: currentOrganizationId },
            })
            .provide([
                [select(currentOrganizationEqIdSelector), currentOrganizationId],
                [select(getCurrentOrganizationShortNameSelector), currentOrganizationShortName],
                [call.fn(fetchers.corpLoanStatements.createStatementRequest), result],
                [delay(2000), undefined],
            ])
            .put(createStatementRequestFinish())
            .run();
    });

    it('should dispatch createStatementRequestError on failure', () => {
        const error = new Error('Some server error');

        return expectSaga(createStatementRequestWorker, {
            type: STATEMENT_REQUESTS_TYPES.CREATE_STATEMENT_REQUEST_START,
            docNumber,
            fromDate,
            toDate,
            format,
            selectedTranches,
            withSignature,
            organizationId,
        })
            .withState({
                organization: { current: currentOrganizationId },
            })
            .provide([
                [select(currentOrganizationEqIdSelector), currentOrganizationId],
                [select(getCurrentOrganizationShortNameSelector), currentOrganizationShortName],
                [call.fn(fetchers.corpLoanStatements.createStatementRequest), throwError(error)],
            ])
            .put(
                createStatementRequestError(
                    new ServerResponseError(error.message),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });
});
