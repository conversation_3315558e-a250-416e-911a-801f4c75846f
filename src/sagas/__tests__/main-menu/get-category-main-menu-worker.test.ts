import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type Category } from 'corp-customers-api-typescript-services/dist/category';

import {
    getCategoryMainMenuError,
    getCategoryMainMenuFinish,
} from '#/src/ducks/credit-products-main-menu/actions';
import { type CreditProductsMainMenuState } from '#/src/ducks/credit-products-main-menu/reducer';
import { categoryInitialState } from '#/src/ducks/organization/reducer';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../../utils/client-api';
import { type ThenArg } from '../../../utils/promise-helper';
import { getCategoryMainMenuWorker } from '../../workers/main-menu/get-category-main-menu-worker';

jest.mock(
    '../../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({ fetchers: { mksCustomers: { getCategory: () => Promise.resolve() as any } } }) as any,
);
describe('getCategoryMainMenyWorker', () => {
    const GET_CATEGORY_RESPONSE_MOCK: Category = {
        sksCode: 'ClientCategorySKS2',
        sksName: 'Отраслевые',
    };

    const INITIAL_STATE: Partial<CreditProductsMainMenuState> = {
        category: {
            isFetching: false,
            error: null,
            category: categoryInitialState,
        },
    };

    const customerIds = ['U11923', 'UA1KLM', 'UAA79G', 'UAABQ7', 'UABITA', 'UAD111', 'UCJF2P'];

    it('should record response in state category field', () => {
        const response: ThenArg<typeof fetchers.mksCustomers.getCategory> =
            GET_CATEGORY_RESPONSE_MOCK;

        return expectSaga(getCategoryMainMenuWorker as any, { customerIds })
            .withState(INITIAL_STATE)
            .provide([
                [call.fn(fetchers.mksCustomers.getCategory), response],
                [select(currentOrganizationEqIdSelector), 'organizationId'],
            ])
            .put(getCategoryMainMenuFinish(response))
            .run();
    });

    it('should create error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(getCategoryMainMenuWorker as any, { customerIds })
            .withState(INITIAL_STATE)
            .provide([[call.fn(fetchers.mksCustomers.getCategory), throwError(ERROR)]])
            .put(getCategoryMainMenuError(new ServerResponseError(ERROR.message)))
            .run();
    });
});
