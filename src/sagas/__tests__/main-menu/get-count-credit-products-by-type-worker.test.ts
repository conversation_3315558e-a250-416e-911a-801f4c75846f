import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { getCountCreditProductsByTypeStart } from '#/src/ducks/credit-products-main-menu/actions';
import { CREDIT_PRODUCTS_MAIN_MENU_TYPES } from '#/src/ducks/credit-products-main-menu/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { getCountCreditProductsByTypeWorker } from '../../workers/main-menu/get-count-credit-products-by-type-worker';

jest.mock(
    '../../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                creditProductsRestV2: {
                    getCountCreditProductsByType: () => Promise.resolve() as any,
                },
            },
        }) as any,
);

describe('getCountCreditProductsByTypeWorker', () => {
    const customerIds = ['123', '456'];

    it('should handle successful response with data', () => {
        const response: ThenArg<typeof fetchers.creditProductsRestV2.getCountCreditProductsByType> =
            {
                productCounts: [
                    { productType: 2, count: 5 },
                    { productType: 5, count: 3 },
                ],
            };

        return expectSaga(
            getCountCreditProductsByTypeWorker,
            getCountCreditProductsByTypeStart({ customerIds }),
        )
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getCountCreditProductsByType), response],
            ])
            .put({
                type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
                productCounts: response.productCounts,
            })
            .run();
    });

    it('should handle response with empty productCounts', () => {
        const response: ThenArg<typeof fetchers.creditProductsRestV2.getCountCreditProductsByType> =
            {
                productCounts: [],
            };

        return expectSaga(
            getCountCreditProductsByTypeWorker,
            getCountCreditProductsByTypeStart({ customerIds }),
        )
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getCountCreditProductsByType), response],
            ])
            .put({
                type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
                productCounts: [],
            })
            .run();
    });

    it('should handle fetch error', () => {
        const error = new Error('API failed');

        return expectSaga(
            getCountCreditProductsByTypeWorker,
            getCountCreditProductsByTypeStart({ customerIds }),
        )
            .provide([
                [
                    call.fn(fetchers.creditProductsRestV2.getCountCreditProductsByType),
                    throwError(error),
                ],
            ])
            .put({
                type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });

    it('should handle malformed response (no productCounts)', () => {
        const response = {} as ThenArg<
            typeof fetchers.creditProductsRestV2.getCountCreditProductsByType
        >;

        return expectSaga(
            getCountCreditProductsByTypeWorker,
            getCountCreditProductsByTypeStart({ customerIds }),
        )
            .provide([
                [call.fn(fetchers.creditProductsRestV2.getCountCreditProductsByType), response],
            ])
            .put({
                type: CREDIT_PRODUCTS_MAIN_MENU_TYPES.GET_COUNT_CREDIT_PRODUCTS_BY_TYPE_FINISH,
                productCounts: undefined,
            })
            .run();
    });
});
