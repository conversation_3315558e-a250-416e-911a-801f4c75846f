import { call, select } from 'redux-saga/effects';
import { expectSaga } from 'redux-saga-test-plan';
import { throwError } from 'redux-saga-test-plan/providers';

import { ERequestsStatusName } from '#/src/constants/credit-document-circulation';
import {
    deleteWelcomeDealByIdError,
    deleteWelcomeDealByIdFinish,
    deleteWelcomeDealByIdStart,
} from '#/src/ducks/credit-requests/actions';
import { creditRequestsListSelector } from '#/src/ducks/credit-requests/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { deleteWelcomeDealByIdWorker } from '../workers/delete-welcome-deal-by-id-worker';

jest.mock('../../utils/client-api', () => ({
    fetchers: {
        creditRequest: {
            deleteWelcomeDealById: jest.fn(() => Promise.resolve()),
        },
    },
}));

describe('deleteWelcomeDealByIdWorkerTest', () => {
    const welcomeDealId = '123';
    const initialList = [
        { id: '123', clientStatus: ERequestsStatusName.FAILURE },
        { id: '456', clientStatus: ERequestsStatusName.WORKS },
    ];

    it('should successfully delete welcome deal by ID', () => {
        const mockResponse = { id: '123' };

        return expectSaga(
            deleteWelcomeDealByIdWorker,
            deleteWelcomeDealByIdStart({ welcomeDealId }),
        )
            .provide([
                [select(creditRequestsListSelector), initialList],
                [
                    call(fetchers.creditRequest.deleteWelcomeDealById, {
                        urlParams: { welcomeDealId },
                    }),
                    mockResponse,
                ],
            ])
            .put(
                deleteWelcomeDealByIdFinish({
                    list: [{ id: '456', clientStatus: ERequestsStatusName.WORKS }],
                }),
            )
            .run();
    });

    it('should handle error while deleting welcome deal by ID', () =>
        expectSaga(deleteWelcomeDealByIdWorker, deleteWelcomeDealByIdStart({ welcomeDealId }))
            .provide([
                [select(creditRequestsListSelector), initialList],
                [
                    call(fetchers.creditRequest.deleteWelcomeDealById, {
                        urlParams: { welcomeDealId },
                    }),
                    throwError(new Error('Unexpected Error')),
                ],
            ])
            .put(
                deleteWelcomeDealByIdError(
                    new ServerResponseError(getErrorMessage(new Error('Unexpected Error'))),
                ),
            )
            .run());

    it('should not call deleteWelcomeDealByIdFinish if response.id is undefined', () => {
        const mockResponse = {};

        return expectSaga(
            deleteWelcomeDealByIdWorker,
            deleteWelcomeDealByIdStart({ welcomeDealId }),
        )
            .provide([
                [select(creditRequestsListSelector), initialList],
                [
                    call(fetchers.creditRequest.deleteWelcomeDealById, {
                        urlParams: { welcomeDealId },
                    }),
                    mockResponse,
                ],
            ])
            .not.put(deleteWelcomeDealByIdFinish({ list: initialList }))
            .run();
    });

    it('should not call deleteWelcomeDealByIdFinish if response.id is null', () => {
        const mockResponse = { id: null };

        return expectSaga(
            deleteWelcomeDealByIdWorker,
            deleteWelcomeDealByIdStart({ welcomeDealId }),
        )
            .provide([
                [select(creditRequestsListSelector), initialList],
                [
                    call(fetchers.creditRequest.deleteWelcomeDealById, {
                        urlParams: { welcomeDealId },
                    }),
                    mockResponse,
                ],
            ])
            .not.put(deleteWelcomeDealByIdFinish({ list: initialList }))
            .run();
    });
});
