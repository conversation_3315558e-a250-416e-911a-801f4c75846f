/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getCreditWidgetStatusError,
    getCreditWidgetStatusFinish,
    getCreditWidgetStatusStart,
} from '#/src/ducks/credit-widget-status/actions';
import { getCreditWidgetStatusWorker } from '#/src/sagas/workers/get-credit-widget-status-worker';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

const mockResponse = {};

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                creditRequest: { getWidgetStatus: () => Promise.resolve(mockResponse) as any },
            },
        }) as any,
);

const action = getCreditWidgetStatusStart({ limitExists: false });

describe('getCreditWidgetStatusWorkerTest', () => {
    const mockOrganizationId = 'mock-organization-id';

    it('should return empty successful response', () =>
        expectSaga(getCreditWidgetStatusWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [call(fetchers.creditRequest.getWidgetStatus), mockResponse],
            ])
            .put(getCreditWidgetStatusFinish({ response: mockResponse }))
            .run());

    it('should return 403 access error', () => {
        const accessError = { statusCode: 403, message: 'Forbidden' } as any;

        return expectSaga(getCreditWidgetStatusWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [call.fn(fetchers.creditRequest.getWidgetStatus), throwError(accessError)],
            ])
            .put(
                getCreditWidgetStatusError(
                    new ServerResponseError(EErrorMessages.ACCESS_ERROR),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });

    it('should return 500 server error', () => {
        const serverError = new Error('Server Error');

        return expectSaga(getCreditWidgetStatusWorker, action)
            .provide([
                [select(currentHeaderOrganizationEqIdSelector), mockOrganizationId],
                [call.fn(fetchers.creditRequest.getWidgetStatus), throwError(serverError)],
            ])
            .put(
                getCreditWidgetStatusError(
                    new ServerResponseError(getErrorMessage(serverError)),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });
});
