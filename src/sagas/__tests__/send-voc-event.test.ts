/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { sendVOCEvent } from '#/src/ducks/shared/actions';

import { fetchers } from '../../utils/client-api';
import { sendVOCEventWorker } from '../workers/send-voc-event-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: { csi: { sendOccuredEventInformation: () => Promise.resolve() as any } },
        }) as any,
);
describe('sendVocEvent', () => {
    it('should call without response', () =>
        expectSaga(sendVOCEventWorker, sendVOCEvent(''))
            .provide([[select(currentOrganizationEqIdSelector), 'eqId']])
            .run());

    it('should call without response if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(sendVOCEventWorker, sendVOCEvent(''))
            .provide([
                [select(currentOrganizationEqIdSelector), 'eqId'],
                [call.fn(fetchers.csi.sendOccuredEventInformation), throwError(ERROR)],
            ])
            .run();
    });
});
