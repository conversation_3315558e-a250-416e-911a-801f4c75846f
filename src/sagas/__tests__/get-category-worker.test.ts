import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type Category } from 'corp-customers-api-typescript-services/dist/category';

import { getCategoryFinish } from '#/src/ducks/organization/actions';
import { categoryInitialState, type OrganizationsState } from '#/src/ducks/organization/reducer';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { ORGANIZATION_TYPES } from '#/src/ducks/organization/types';
import { getCategoryWorker } from '#/src/sagas/workers/get-category-worker';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';
import { type ThenArg } from '../../utils/promise-helper';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({ fetchers: { mksCustomers: { getCategory: () => Promise.resolve() as any } } }) as any,
);
describe('getCategoryWorker', () => {
    const GET_CATEGORY_RESPONSE_MOCK: Category = {
        sksCode: 'ClientCategorySKS2',
        sksName: 'Отраслевые',
    };

    const INITIAL_STATE: Partial<OrganizationsState> = {
        isFetchingCategory: false,
        category: categoryInitialState,
    };

    it('should record response in state category field', () => {
        const response: ThenArg<typeof fetchers.mksCustomers.getCategory> =
            GET_CATEGORY_RESPONSE_MOCK;

        return expectSaga(getCategoryWorker, {
            type: 'GET_CATEGORY_START',
            organizationId: undefined,
        })
            .withState(INITIAL_STATE)
            .provide([
                [call.fn(fetchers.mksCustomers.getCategory), response],
                [select(currentOrganizationEqIdSelector), 'organizationId'],
            ])
            .put(getCategoryFinish(response))
            .run();
    });

    it('should create error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(getCategoryWorker, {
            type: 'GET_CATEGORY_START',
            organizationId: undefined,
        })
            .withState(INITIAL_STATE)
            .provide([[call.fn(fetchers.mksCustomers.getCategory), throwError(ERROR)]])
            .put({
                type: ORGANIZATION_TYPES.GET_CATEGORY_ERROR,
                error: new ServerResponseError(ERROR.message),
            })
            .run();
    });
});
