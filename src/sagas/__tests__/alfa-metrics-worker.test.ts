import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';

import { trackNewAlfaMetrics } from 'arui-private/lib/alfa-metrics';

import { initialState as userState } from '#/src/ducks/user/reducer';

import { trackAlfaMetrics } from '../../ducks/alfa-metrics/actions';
import { type ApplicationState } from '../../ducks/application-state';
import { type OrganizationsState } from '../../ducks/organization/reducer';
import type TMetric from '../../metrics/metric';
import { EventCategory } from '../../metrics/types';
import { alfaMetricsWorker } from '../workers/alfa-metrics-worker';

jest.mock('arui-private/lib/window', () => ({
    isBrowser: () => true,
}));

describe('alfaMetricsWorker', () => {
    const organizationState: Partial<OrganizationsState> = { current: 'XAJSOO' };
    const state: Partial<ApplicationState> = {
        organization: organizationState as OrganizationsState,
        user: userState,
    };

    const metric: TMetric = {
        category: EventCategory.overdraftInUse,
        action: 'Action > bah',
        dimensionsMapping: {},
    };

    it('should call `trackNewAlfaMetrics` if is browser and `window.sp` is defined', () => {
        (global as any).sp = jest.fn(() => ({}));

        return expectSaga(alfaMetricsWorker, trackAlfaMetrics(metric))
            .withState(state)
            .provide([[call.fn(trackNewAlfaMetrics), undefined]])
            .call.fn(trackNewAlfaMetrics)
            .run();
    });

    it('should return if `global.sp` is not defined', () => {
        (global as any).sp = undefined;

        return expectSaga(alfaMetricsWorker, trackAlfaMetrics({} as TMetric))
            .withState(state)
            .provide([[call.fn(trackNewAlfaMetrics), undefined]])
            .not.call.fn(trackNewAlfaMetrics)
            .run();
    });
});
