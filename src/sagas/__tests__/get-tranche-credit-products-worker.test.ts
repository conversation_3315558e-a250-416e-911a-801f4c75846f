import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { createCreditProduct } from '#/src/server/mocks/utils/create-credit-product';

import { ECreditProductsCodes } from '../../constants/credit-products';
import { type ApplicationState } from '../../ducks/application-state';
import { getTrancheCreditProductsStart } from '../../ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '../../ducks/credit-products/types';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { mapTranches } from '../../utils/credit-products-mappers';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { mapFromArrayToAssociativeArray } from '../../utils/object-helpers';
import { type ThenArg } from '../../utils/promise-helper';
import {
    getTrancheCreditProductsWorker,
    getTrancheCreditProductsWorkerState,
} from '../workers/get-tranche-credit-products-worker';

import { creditProductsState } from './credit-product-state';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
    fetchers: { getTrancheCreditProductsV2: () => Promise.resolve() } as any,
}));

describe('getTrancheCreditProductsWorkerTest', () => {
    const SELECT_MOCK = {
        organizationId: '',
        docNumber: '',
        pageFilter: { pageNumber: 0, perPage: 10 },
        tranchesTotalCount: 10,
        perPage: 10,
    };

    const creditProduct = createCreditProduct({
        productCode: ECreditProductsCodes.CREDIT_LINE_TRANCHE,
    }) as any; /* eslint-disable-line @typescript-eslint/no-explicit-any */

    const mappedTranches = mapTranches([creditProduct]);
    const tranchesMappedToAssociativearray = mapFromArrayToAssociativeArray(
        mappedTranches,
        'docNumber',
    );

    const testState: Partial<ApplicationState> = {
        creditProducts: creditProductsState,
    };

    it('should record tranches to store if server returns tranches', () => {
        const response: ThenArg<typeof fetchers.getTrancheCreditProductsV2> = [creditProduct];

        return expectSaga(getTrancheCreditProductsWorker, getTrancheCreditProductsStart({}))
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsV2), response],
                [call.fn(mapTranches), mappedTranches],
                [call.fn(mapFromArrayToAssociativeArray), tranchesMappedToAssociativearray],
                [select(getTrancheCreditProductsWorkerState), SELECT_MOCK],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH,
                mappedTranches: tranchesMappedToAssociativearray,
                pageFilter: SELECT_MOCK.pageFilter,
                receivedTranchesCount: 1,
                tranchesIndexedByDocNumber: { [creditProduct.docNumber ?? '']: creditProduct },
                isClosedCreditProductsTranches: false,
                isAllCreditProductsTranches: false,
                isAllReceivedCreditProductsTranches: false,
            })
            .run();
    });

    it('should throw error if server returns error', () => {
        const error = new Error();

        return expectSaga(getTrancheCreditProductsWorker, getTrancheCreditProductsStart({}))
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsV2), throwError(error)],
                [select(getTrancheCreditProductsWorkerState), SELECT_MOCK],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
                isClosedCreditProductsTranches: false,
                isAllCreditProductsTranches: false,
            })
            .run();
    });

    it('should record pageFilter.perPage equal to tranchesTotalCount if parameter all = true', () => {
        const response: ThenArg<typeof fetchers.getTrancheCreditProductsV2> = [creditProduct];

        return expectSaga(
            getTrancheCreditProductsWorker,
            getTrancheCreditProductsStart({ isFromFirstPage: true, all: true }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsV2), response],
                [call.fn(mapTranches), mappedTranches],
                [call.fn(mapFromArrayToAssociativeArray), tranchesMappedToAssociativearray],
                [select(getTrancheCreditProductsWorkerState), SELECT_MOCK],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_FINISH,
                mappedTranches: tranchesMappedToAssociativearray,
                pageFilter: { ...SELECT_MOCK.pageFilter, perPage: SELECT_MOCK.tranchesTotalCount },
                receivedTranchesCount: 1,
                tranchesIndexedByDocNumber: { [creditProduct.docNumber ?? '']: creditProduct },
                isClosedCreditProductsTranches: false,
                isAllCreditProductsTranches: false,
                isAllReceivedCreditProductsTranches: true,
            })
            .run();
    });
});
