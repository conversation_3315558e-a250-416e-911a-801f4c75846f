import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { StatementStatus } from 'corp-loan-statements-api-typescript-services';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { getStatementRequestsStart } from '#/src/ducks/statement-requests/actions';
import { STATEMENT_REQUESTS_TYPES } from '#/src/ducks/statement-requests/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { getStatementRequestsWorker } from '../workers/get-statement-requests-worker';

jest.mock(
    '../../utils/client-api.ts',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                corpLoanStatements: {
                    getStatementRequests: () => Promise.resolve() as any,
                },
            },
        }) as any,
);

describe('getStatementRequestsWorker', () => {
    const testState = {};

    it('should handle successful fetch of statement requests', () => {
        const response: ThenArg<typeof fetchers.corpLoanStatements.getStatementRequests> = {
            statementRequests: [
                { id: '1', docNumber: '123', status: StatementStatus.InProgress },
                { id: '2', docNumber: '124', status: StatementStatus.Success },
            ],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 10,
                totalPages: 1,
            },
            newStatementsCount: 5,
        };

        return expectSaga(
            getStatementRequestsWorker,
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.getStatementRequests), response],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH,
                list: response.statementRequests,
                pagesCount: 1,
                newStatementsCount: 5,
            })
            .run();
    });

    it('should handle error in fetch request', () => {
        const error = new Error('API error');

        return expectSaga(
            getStatementRequestsWorker,
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.getStatementRequests), throwError(error)],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });

    it('should handle no statement requests available', () => {
        const response: ThenArg<typeof fetchers.corpLoanStatements.getStatementRequests> = {
            statementRequests: [],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 0,
                totalPages: 0,
            },
            newStatementsCount: 0,
        };

        return expectSaga(
            getStatementRequestsWorker,
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.getStatementRequests), response],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH,
                list: [],
                pagesCount: 0,
                newStatementsCount: 0,
            })
            .run();
    });

    it('should handle missing pagination information', () => {
        const response: ThenArg<typeof fetchers.corpLoanStatements.getStatementRequests> = {
            statementRequests: [{ id: '1', docNumber: '123', status: StatementStatus.InProgress }],
            pagination: {},
            newStatementsCount: 0,
        };

        return expectSaga(
            getStatementRequestsWorker,
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.getStatementRequests), response],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH,
                list: response.statementRequests,
                pagesCount: 0,
                newStatementsCount: 0,
            })
            .run();
    });

    it('should handle incomplete or missing fields in the response', () => {
        const response: ThenArg<typeof fetchers.corpLoanStatements.getStatementRequests> = {
            statementRequests: [{ id: '1', docNumber: '123' }],
            pagination: {
                pageNumber: 1,
                pageSize: 10,
                totalElements: 1,
                totalPages: 1,
            },
            newStatementsCount: 1,
        };

        return expectSaga(
            getStatementRequestsWorker,
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: 10,
                docNumber: '123',
            }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.corpLoanStatements.getStatementRequests), response],
                [select(currentHeaderOrganizationEqIdSelector), 'organizationId'],
            ])
            .put({
                type: STATEMENT_REQUESTS_TYPES.GET_STATEMENTS_REQUESTS_FINISH,
                list: response.statementRequests,
                pagesCount: 1,
                newStatementsCount: 1,
            })
            .run();
    });
});
