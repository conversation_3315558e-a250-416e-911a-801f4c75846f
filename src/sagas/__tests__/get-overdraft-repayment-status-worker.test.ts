import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type TRepaymentResult } from 'thrift-services/services/credit_products';

import { getOverdraftRepaymentStatusStart } from '#/src/ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import { getOverdraftLimitSettingsState } from '#/src/sagas/workers/get-overdraft-limit-settings-worker';
import { getOverdraftRepaymentStatusWorker } from '#/src/sagas/workers/get-overdraft-repayment-status-worker';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { fetchers } from '../../utils/client-api';

const DEAL_ID = '1';

const overdraftLimitSettingsState: ReturnType<typeof getOverdraftLimitSettingsState> = {
    profileId: '1',
    organizationId: '2',
    docNumber: '3',
};

const getRepaymentStatusTestResponse: TRepaymentResult = {
    changeRepaymentTypeId: 1,
    repaymentType: 'EOD',
    statusCode: '200',
    creationDate: {
        seconds: 123,
    },
    approximateCompletionDate: {
        seconds: 123,
    },
};

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: {
        getChangeRepaymentStatusV2: () => Promise.resolve(),
    } as any,
}));

describe('getOverdraftRepaymentStatusWorker', () => {
    it('should fetch status', () => {
        const response: ThenArg<typeof fetchers.getChangeRepaymentStatusV2> = {
            ...getRepaymentStatusTestResponse,
        };

        return expectSaga(
            getOverdraftRepaymentStatusWorker,
            getOverdraftRepaymentStatusStart(DEAL_ID),
        )
            .provide([
                [call.fn(fetchers.getChangeRepaymentStatusV2), response],
                [select(getOverdraftLimitSettingsState), overdraftLimitSettingsState],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_FINISH,
                repaymentData: response,
                dealId: DEAL_ID,
            })
            .run();
    });

    it('should return error action if server return an error', () => {
        const ERROR = new Error('error');

        return expectSaga(
            getOverdraftRepaymentStatusWorker,
            getOverdraftRepaymentStatusStart(DEAL_ID),
        )
            .provide([
                [call.fn(fetchers.getChangeRepaymentStatusV2), throwError(ERROR)],
                [select(getOverdraftLimitSettingsState), overdraftLimitSettingsState],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_OVERDRAFT_REPAYMENT_STATUS_ERROR,
                dealId: DEAL_ID,
                error: new ServerResponseError(ERROR.message),
            })
            .run();
    });
});
