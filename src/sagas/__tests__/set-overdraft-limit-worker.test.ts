import { expectSaga } from 'redux-saga-test-plan';
import { call } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { type Amount } from 'thrift-services/entities';

import { type ApplicationState } from '../../ducks/application-state';
import { setOverdraftLimitStart } from '../../ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '../../ducks/credit-products/types';
import { type OrganizationsState } from '../../ducks/organization/reducer';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { setOverdraftLimitWorker } from '../workers/set-overdraft-limit-worker';

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({ fetchers: { setClientLimitV2: () => Promise.resolve() as any } }) as any,
);

describe('setOverdraftLimitWorkerTest', () => {
    const organizationState: Partial<OrganizationsState> = {
        current: 'U12345',
    };

    const testState: Partial<ApplicationState> = {
        user: { id: 'XA3SDX' },
        organization: organizationState as OrganizationsState,
    };

    const docNumber = 'D123';

    const limit: Amount = {
        amount: 1000000000,
        currency: {
            code: 810,
            mnemonicCode: 'RUR',
            minorUnits: 100,
            unicodeSymbol: '₽',
            fullName: 'Российский рубль',
        },
    };

    it('should set overdraft limit', () =>
        expectSaga(setOverdraftLimitWorker, setOverdraftLimitStart(docNumber, limit))
            .withState(testState)
            .provide([[call.fn(fetchers.setClientLimitV2), undefined]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_FINISH,
                docNumber,
                clientLimit: limit,
            })
            .run());

    it('should create error action if server return some error', () => {
        const error = new Error('error');

        return expectSaga(setOverdraftLimitWorker, setOverdraftLimitStart(docNumber, limit))
            .withState(testState)
            .provide([[call.fn(fetchers.setClientLimitV2), throwError(error)]])
            .put({
                type: CREDIT_PRODUCTS_TYPES.SET_OVERDRAFT_LIMIT_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
