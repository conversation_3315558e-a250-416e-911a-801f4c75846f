import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';

import {
    currentOrganizationMainMenuSelector,
    isMmbCategoryMainMenuSelector,
} from '#/src/ducks/credit-products-main-menu/selectors';
import {
    getCreditRequestsListError,
    getCreditRequestsListFinish,
    getCreditRequestsListStart,
} from '#/src/ducks/credit-requests/actions';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';
import { organizationsListSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { getCreditRequestsListWorker } from '../workers/get-credit-requests-list-worker';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: {
        corpCreditRequestRest: { getCreditRequests: () => Promise.resolve() as any } as any,
    },
}));

describe('getCreditRequestsListWorker', () => {
    const organizationMock = {
        eqId: '123',
        inn: 'test-inn',
        ogrn: 'test-ogrn',
        kppList: ['test-kpp'],
    };

    const creditRequestsResponseMock: {
        commonCreditRequestList: TCreditRequestList;
    } = {
        commonCreditRequestList: [
            {
                id: 'ABR-FW-OPSFW-WORK-LOAN MB-85642',
                type: CommonCreditRequestType.Mmb,
                createDt: 1089576000000,
                clientStatus: 'На подпись',
                description: 'Подпишите кредитный договор и получите кредит',
                isAvailableForCurrentChannel: true,
                limit: {
                    amount: 30000000,
                    currency: {
                        code: 810,
                        mnemonicCode: 'RUB',
                        minorUnits: 100,
                        unicodeSymbol: 'U+20BD',
                        fullName: 'Российский рубль',
                    },
                },
            },
        ],
    };

    it('should successfully fetching the credit requests list', () =>
        expectSaga(getCreditRequestsListWorker, getCreditRequestsListStart())
            .provide([
                [select(currentOrganizationMainMenuSelector), organizationMock],
                [select(organizationsListSelector), [organizationMock]],
                [select(isMmbCategoryMainMenuSelector), true],
                [
                    call.fn(fetchers.corpCreditRequestRest.getCreditRequests),
                    creditRequestsResponseMock,
                ],
            ])
            .put(
                getCreditRequestsListFinish({
                    list: creditRequestsResponseMock.commonCreditRequestList,
                }),
            )
            .run());

    it('should create error while fetching the credit requests list', () => {
        const error = new Error(EErrorMessages.ACCESS_ERROR);

        return expectSaga(getCreditRequestsListWorker, getCreditRequestsListStart())
            .provide([
                [select(currentOrganizationMainMenuSelector), organizationMock],
                [select(organizationsListSelector), [organizationMock]],
                [call.fn(fetchers.corpCreditRequestRest.getCreditRequests), throwError(error)],
            ])
            .put(
                getCreditRequestsListError(
                    new ServerResponseError(
                        getErrorCode(error) === 403
                            ? EErrorMessages.ACCESS_ERROR
                            : getErrorMessage(error),
                    ),
                    LOG_LEVEL.ERROR,
                ),
            )
            .run();
    });
});
