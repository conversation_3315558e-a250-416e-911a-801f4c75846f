import { type CreditProductsState } from '#/src/ducks/credit-products/reducer';
import { initialStateSelectedCreditProduct } from '#/src/ducks/credit-products/reducer/selected-credit-product';
import { ELimitStatus } from '#/src/types/overdraft';

export const creditProductsState: CreditProductsState = {
    credits: {},
    overdrafts: {},
    creditCards: {},
    tranches: {},
    creditLines: {},
    sopukLine: {},
    guaranties: {},
    guarantyLines: {},
    allProducts: {},
    selectedProduct: initialStateSelectedCreditProduct,
    overdraftsLimits: {},
    clientLimit: { clientLimit: null, error: null, isFetching: false, isFinished: false },
    isFetchingCreditProducts: false,
    isFetchingTranches: false,
    isFetchingTranchesCount: false,
    creditProductsError: null,
    isGetTrancheCreditProductsCountError: null,
    isGetTrancheCreditProductsError: null,
    isCreditProductsRecieved: false,
    isClosedCreditProducts: false,
    isGetOverdraftLimitSettingsError: null,
    isFetchingOverdraftLimitSettings: false,
    isFetchingSetOverdraftLimit: false,
    isFetchingDisallowedDatesForLoanRepayment: false,
    isAllCreditProductsTranches: false,
    isAllReceivedCreditProductsTranches: false,
    isClosedCreditProductsTranches: false,
    overdraftLimitStatus: ELimitStatus.NOT_CHANGING,
    overType: null,
    tranchesInfo: {
        pageFilter: {
            pageNumber: 0,
            perPage: 10,
        },
        totalCount: 10,
        receivedCount: 0,
        select: {
            isLoading: false,
            tranches: [],
            error: null,
        },
    },
    disallowedDatesForLoanRepayment: [],
    closestTranchePayment: {},
    overdraftChangeRepayment: {},
    overdraftsNetTurnovers: {},
};
