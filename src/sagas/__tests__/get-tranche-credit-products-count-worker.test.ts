import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';

import { type ApplicationState } from '#/src/ducks/application-state';
import { getTrancheCreditProductsCountStart } from '#/src/ducks/credit-products/actions';
import { CREDIT_PRODUCTS_TYPES } from '#/src/ducks/credit-products/types';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import {
    getTrancheCreditProductsCountWorker,
    getTrancheCreditProductsCountWorkerState,
} from '../workers/get-tranche-credit-products-count-worker';

import { creditProductsState } from './credit-product-state';

jest.mock('../../utils/client-api', (): { fetchers: Partial<typeof fetchers> } => ({
    fetchers: { getTrancheCreditProductsCountV2: () => Promise.resolve() } as any,
}));

describe('getTrancheCreditProductsCountTest', () => {
    const testState: Partial<ApplicationState> = {
        creditProducts: creditProductsState,
    };

    it('should record amount of tranches to store', () => {
        const response: ThenArg<typeof fetchers.getTrancheCreditProductsCountV2> = 0;

        return expectSaga(
            getTrancheCreditProductsCountWorker,
            getTrancheCreditProductsCountStart({ docNumber: '' }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsCountV2), response],
                [
                    select(getTrancheCreditProductsCountWorkerState),
                    { organizationId: 'UUUXXX', docNumber: 'EFSE33' },
                ],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_FINISH,
                trancheType: 'tranche',
                docNumber: '',
                tranchesCount: response,
                all: false,
            })
            .run();
    });

    it('should record zero amount of tranches to store if server returns null or undefined', () => {
        const response: ThenArg<typeof fetchers.getTrancheCreditProductsCountV2> = 0;

        return expectSaga(
            getTrancheCreditProductsCountWorker,
            getTrancheCreditProductsCountStart({ docNumber: '' }),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsCountV2), response],
                [
                    select(getTrancheCreditProductsCountWorkerState),
                    { organizationId: 'UUUXXX', docNumber: 'EFSE33' },
                ],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_FINISH,
                trancheType: 'tranche',
                docNumber: '',
                tranchesCount: 0,
                all: false,
            })
            .run();
    });

    it('should put error effect if server return error response', () => {
        const error: Error = new Error('error');

        return expectSaga(
            getTrancheCreditProductsCountWorker,
            getTrancheCreditProductsCountStart({}),
        )
            .withState(testState)
            .provide([
                [call.fn(fetchers.getTrancheCreditProductsCountV2), throwError(error)],
                [
                    select(getTrancheCreditProductsCountWorkerState),
                    { organizationId: 'UUUXXX', docNumber: 'EFSE33' },
                ],
            ])
            .put({
                type: CREDIT_PRODUCTS_TYPES.GET_TRANCHE_CREDIT_PRODUCTS_COUNT_ERROR,
                error: new ServerResponseError(error.message),
                logLevel: LOG_LEVEL.ERROR,
            })
            .run();
    });
});
