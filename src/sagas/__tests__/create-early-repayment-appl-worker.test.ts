/* eslint-disable @typescript-eslint/no-explicit-any */
import { expectSaga } from 'redux-saga-test-plan';
import { call, select } from 'redux-saga-test-plan/matchers';
import { throwError } from 'redux-saga-test-plan/providers';
import * as HEADERS from 'corporate-services/lib/constants/headers';
import { parse } from 'date-fns';

import { DATE_FORMAT } from '@alfalab/core-components/date-input';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    createEarlyRepaymentApplFinish,
    createEarlyRepaymentApplStart,
} from '#/src/ducks/credit-processing/actions';
import {
    isCalculateFeeForEarlyRepaymentSelector,
    productByDocNumberSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import {
    setRequestAlreadyCreatedErrorModalOpen,
    setSigningErrorModalOpen,
} from '#/src/ducks/early-pay/actions';
import { currentOrganizationSelector } from '#/src/ducks/organization/selectors';
import { setCurrentTime, setTimeAfterDeadlineError } from '#/src/ducks/settings/actions';
import { deadlineForAcceptingApplicationsMoscowTimeSelector } from '#/src/ducks/settings/selectors';
import { userSelector } from '#/src/ducks/user/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { fetchers } from '#/src/utils/client-api';
import { getCurrentMoscowDate, getIsDatesEqual } from '#/src/utils/date';
import { getBodyForEarlyPay, getIsTimeBeforeDeadline } from '#/src/utils/early-pay';

import { createEarlyRepaymentApplWorker } from '../workers/create-early-repayment-appl-worker';

jest.mock(
    '../../utils/client-api',
    (): { fetchers: Partial<typeof fetchers> } =>
        ({
            fetchers: {
                earlyRepaymentRest: { createEarlyRepaymentAppl: () => Promise.resolve() as any },
            },
        }) as any,
);

jest.mock('#/src/utils/date', () => {
    const actual = jest.requireActual('#/src/utils/date');

    return {
        ...actual,
        getCurrentMoscowDate: jest.fn(),
        getIsDatesEqual: jest.fn(),
    };
});

describe('createEarlyRepaymentApplWorker tests', () => {
    const action = createEarlyRepaymentApplStart({
        docNumber: '123456',
        selectedAccountNumber: 'acc-123',
        paymentType: 'full',
        paymentDate: '25.12.2027',
        debtToPay: '10000',
        interestToPay: '500',
        total: '10500',
        currency: {},
    });

    const user = { lastName: 'Ivanov', firstName: 'Ivan', middleName: 'Ivanovich' };
    const organization = { inn: '**********', name: 'ООО Тест', eqId: 'org-123' };
    const product: any = { id: 'prod-123', name: 'Кредит' };
    const deadline = { hours: 18, minutes: 0 };

    const currentTime = new Date('2025-02-05T10:30:46.000Z');
    const currentTimeISO = currentTime.toISOString();

    const applicationId = 'app-123';

    const stateMock = {
        creditProducts: {
            allProducts: {
                credit: { docNumber: '123456' },
                overdraft: { docNumber: '654321' },
            },
            selectedProduct: {
                product: {
                    credit: {},
                    overdraft: {},
                },
            },
        },
        settings: {
            deadlineForAcceptingApplicationsMoscowTime: '17:00:00',
        },
    };

    beforeEach(() => {
        (getCurrentMoscowDate as jest.Mock).mockReturnValue(currentTimeISO);
    });

    it('should successfully create an early repayment application', () => {
        const body = getBodyForEarlyPay({
            ...action.applicationData,
            product,
            name: organization.name,
            customerId: organization.eqId,
            inn: organization.inn,
            lastName: user.lastName,
            firstName: user.firstName,
            middleName: user.middleName,
            formatCurrentTime: currentTime,
            calculateFeeForEarlyRepayment: true,
        });

        return expectSaga(createEarlyRepaymentApplWorker, action)
            .withState(stateMock)
            .provide([
                [select(userSelector), user],
                [select(currentOrganizationSelector), organization],
                [select(productByDocNumberSelector), product],
                [select(isCalculateFeeForEarlyRepaymentSelector), true],
                [select(deadlineForAcceptingApplicationsMoscowTimeSelector), deadline],
                [
                    call(fetchers.earlyRepaymentRest.createEarlyRepaymentAppl, {
                        headers: {
                            [HEADERS.OPENAPI_COMPANY_ID]: organization.eqId,
                        },
                        body,
                    }),
                    applicationId,
                ],
                [
                    call(
                        getIsDatesEqual,
                        parse(action.applicationData.paymentDate, DATE_FORMAT, currentTime),
                        currentTime,
                    ),
                    true,
                ],
                [call(getIsTimeBeforeDeadline, currentTime, deadline), true],
                [call(getCurrentMoscowDate), currentTimeISO],
            ])
            .put(setCurrentTime(currentTimeISO))
            .put(createEarlyRepaymentApplFinish(applicationId))
            .put(
                trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSignModal, {
                    agreementNumber: action.applicationData.docNumber,
                    processingType: earlyRepaymentProcessingTypes.AUTO.toLowerCase(),
                    paymentType: action.applicationData.paymentType,
                }),
            )
            .run();
    });

    it('should handle request conflict error (409)', () => {
        const error = { statusCode: 409, message: 'Conflict error' } as any;

        return expectSaga(createEarlyRepaymentApplWorker, action)
            .withState(stateMock)
            .provide([
                [select(userSelector), user],
                [select(currentOrganizationSelector), organization],
                [select(productByDocNumberSelector), product],
                [call.fn(fetchers.earlyRepaymentRest.createEarlyRepaymentAppl), throwError(error)],
            ])
            .put(setRequestAlreadyCreatedErrorModalOpen(true))
            .run();
    });

    it('should handle validation error (422)', () => {
        const error = { statusCode: 422, message: 'Validation error' } as any;

        return expectSaga(createEarlyRepaymentApplWorker, action)
            .withState(stateMock)
            .provide([
                [select(userSelector), user],
                [select(currentOrganizationSelector), organization],
                [select(productByDocNumberSelector), product],
                [call.fn(fetchers.earlyRepaymentRest.createEarlyRepaymentAppl), throwError(error)],
            ])
            .put(setTimeAfterDeadlineError(true))
            .run();
    });

    it('should handle unexpected error', () =>
        expectSaga(createEarlyRepaymentApplWorker, action)
            .withState(stateMock)
            .provide([
                [select(userSelector), user],
                [select(currentOrganizationSelector), organization],
                [select(productByDocNumberSelector), product],
                [
                    call.fn(fetchers.earlyRepaymentRest.createEarlyRepaymentAppl),
                    throwError(new Error('Unexpected Error')),
                ],
            ])
            .put(setSigningErrorModalOpen(true))
            .run());
});
