import { all, call, put, select, spawn } from 'redux-saga/effects';

import { MAX_DOCUMENT_SIZE, MAX_DOCUMENTS_SIZE } from '#/src/constants/client-documents';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    type sendUnsafeDocumentsStart,
    updateUnsafeDocumentsInStore,
} from '#/src/ducks/attach-documents/actions';
import {
    borrowerCodeSelector,
    documentsSelector,
    loanMBIdSelector,
    totalSizeAttachDocumentsSelector,
} from '#/src/ducks/attach-documents/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { CLIENT_DOCUMENTS_ATTACH_METRICS } from '#/src/metrics';
import { type DocumentInfoWithFile } from '#/src/types/client-documents';
import { fetchers } from '#/src/utils/client-api';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* sendUnsafeDocumentsWorker({
    documents,
}: ReturnType<typeof sendUnsafeDocumentsStart>) {
    const loadedDocumentsSize: ReturnType<typeof totalSizeAttachDocumentsSelector> = yield select(
        totalSizeAttachDocumentsSelector,
    );
    const documentsInStore: ReturnType<typeof documentsSelector> = yield select(documentsSelector);

    yield put(
        updateUnsafeDocumentsInStore(
            documents.map((document) => ({
                id: document.id,
                name: document.name,
                type: document.type,
                size: document.size,
                uploadStatus: 'UPLOADING',
            })),
        ),
    );

    let documetsSize = 0;

    yield all(
        documents.map((document) => {
            documetsSize += document.size;
            // TODO: удалить после появления возможности удалять документы
            const sizeExceeded = loadedDocumentsSize + documetsSize > MAX_DOCUMENTS_SIZE;

            return spawn(sendUnsafeDocument, document, sizeExceeded, documentsInStore);
        }),
    );
}

function* sendUnsafeDocument(
    documentInfo: DocumentInfoWithFile,
    sizeExceeded: boolean,
    documents: ReturnType<typeof documentsSelector>,
) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );
    const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);
    const borrowerCode: ReturnType<typeof borrowerCodeSelector> =
        yield select(borrowerCodeSelector);

    try {
        if (sizeExceeded) {
            yield put(
                updateUnsafeDocumentsInStore([
                    {
                        ...documentInfo,
                        uploadStatus: 'ERROR',
                        error: 'общий размер загруженных файлов больше 20 МБ',
                        showDelete: true,
                    },
                ]),
            );

            return;
        }

        if (documentInfo.size > MAX_DOCUMENT_SIZE) {
            yield put(
                updateUnsafeDocumentsInStore([
                    {
                        ...documentInfo,
                        error: 'размер больше 5 МБ',
                        uploadStatus: 'ERROR',
                        showDelete: true,
                    },
                ]),
            );

            return;
        }

        const formData = new FormData();

        formData.append('organizationId', organizationId);
        formData.append('loanMBId', loanMBId);
        formData.append('borrowerCode', borrowerCode);
        formData.append('fileName', documentInfo.name);
        if (documentInfo.type) formData.append('fileMimeType', documentInfo.type);
        if (documentInfo.file) formData.append('file', documentInfo.file);

        const { fileId }: ThenArg<typeof fetchers.sendUnsafeFileRest> = yield call(
            fetchers.sendUnsafeFileRest,
            formData,
        );

        yield put(
            trackAlfaMetrics(CLIENT_DOCUMENTS_ATTACH_METRICS.clientDocumentAttach, {
                loanMBId,
                borrowerCode,
                mimeType: documentInfo.type,
                fileSize: documentInfo.size,
            }),
        );

        const hasDuplicateDocument = Boolean(
            Array.from(documents).find((document) => document[1].fileId === fileId),
        );

        if (hasDuplicateDocument) {
            yield put(
                updateUnsafeDocumentsInStore([
                    {
                        ...documentInfo,
                        fileId,
                        uploadStatus: 'ERROR',
                        error: 'такой документ уже загружен',
                        showDelete: true,
                    },
                ]),
            );

            return;
        }

        yield put(
            updateUnsafeDocumentsInStore([
                {
                    ...documentInfo,
                    fileId,
                    uploadStatus: 'SUCCESS',
                    showDelete: true,
                },
            ]),
        );
    } catch (error) {
        yield put(
            updateUnsafeDocumentsInStore([
                {
                    ...documentInfo,
                    uploadStatus: 'ERROR',
                    showDelete: true,
                },
            ]),
        );
    }
}
