import { call, select } from 'redux-saga/effects';

import { trackNewAlfaMetrics } from 'arui-private/lib/alfa-metrics';
import { isBrowser } from 'arui-private/lib/window';

import { userSelector } from '#/src/ducks/user/selectors';

import { type trackAlfaMetrics } from '../../ducks/alfa-metrics/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';

type TWindow = typeof window & {
    sp: (
        type: string,
        category: string,
        action: string,
        label?: string,
        property?: string,
        value?: number,
    ) => void;
};

export const getCanTrackMetrics = () => isBrowser() && !!(window as TWindow)?.sp;

export function* alfaMetricsWorker(action: ReturnType<typeof trackAlfaMetrics>) {
    if (!getCanTrackMetrics()) {
        return;
    }

    const organizationId = yield select(currentOrganizationEqIdSelector);
    const user = yield select(userSelector);

    try {
        yield call(trackNewAlfaMetrics, action.metric, organizationId, {
            userId: user.id,
            ...action.additionalData,
        });
    } catch {}
}
