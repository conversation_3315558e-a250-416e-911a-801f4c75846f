import { call, put, select } from 'redux-saga/effects';
import { PrescoringResult } from 'thrift-services/services/credit_requests';
import { type CreditsTapiServiceCreateCallbackRequestArgs } from 'thrift-services/services/credit_requestsService';

import { PRODUCT_NAMES } from '#/src/constants/credit-products';
import { organizationAddressSelector } from '#/src/ducks/addresses/selectors';
import { unexpectedAppError } from '#/src/ducks/app/actions';
import {
    type createCallbackRequest,
    setCallbackRequestStatus,
} from '#/src/ducks/credit-processing/actions';
import { CallbackRequestStatusTypes } from '#/src/ducks/credit-processing/types';
import { currentOrganizationSelector } from '#/src/ducks/organization/selectors';
import { userPhoneSelector } from '#/src/ducks/user/selectors';
import { getHasCallbackRequestAlreadyCreated } from '#/src/utils/callback-request';
import { fetchers } from '#/src/utils/client-api';

const totalOfferCallbackRequestComment =
    'Необходимо связаться с клиентом и пригласить его в отделение для оформления Безусловного предодоба по кредиту. Клиент хотел оформить кредит в интернет-банке, но у него нет КЭП для подписания документов онлайн.';

export function* createCallbackRequestWorker({
    isTotalOffer,
    productCode,
}: ReturnType<typeof createCallbackRequest>) {
    const hasCallbackRequestAlreadyCreated = getHasCallbackRequestAlreadyCreated(isTotalOffer);

    try {
        if (hasCallbackRequestAlreadyCreated) {
            yield put(
                setCallbackRequestStatus(
                    CallbackRequestStatusTypes.CALLBACK_REQUEST_HAS_ALREADY_CREATED,
                ),
            );
        } else {
            const organization: ReturnType<typeof currentOrganizationSelector> = yield select(
                currentOrganizationSelector,
            );
            const phone: ReturnType<typeof userPhoneSelector> = yield select(userPhoneSelector);
            const address: ReturnType<typeof organizationAddressSelector> = yield select(
                organizationAddressSelector,
            );

            const dataForCallbackRequest: CreditsTapiServiceCreateCallbackRequestArgs = {
                request: {
                    city: address?.address?.city ?? '',
                    phoneNumber: phone ?? '',
                    kpp: organization?.kppList[0],
                    ogrn: organization?.ogrn,
                    inn: organization?.inn,
                    organizationName: organization?.organizationName,
                    product: PRODUCT_NAMES[productCode],
                    prescoringResult: PrescoringResult.POSITIVE_SCORING_L,
                    advCode: 'NIB_LOAN_FORM_INITIATION',
                    comment: totalOfferCallbackRequestComment,
                },
            };

            yield call(fetchers.createCallbackRequest, dataForCallbackRequest);

            yield put(
                setCallbackRequestStatus(CallbackRequestStatusTypes.CALLBACK_REQUEST_CREATED),
            );
        }
    } catch (error) {
        yield put(unexpectedAppError());

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        fetchers.clientErrorLog(error);
    }
}
