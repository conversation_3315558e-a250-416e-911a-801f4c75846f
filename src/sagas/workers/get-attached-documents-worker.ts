import { call, put } from 'redux-saga/effects';

import {
    type getAttachedDocuments,
    getAttachedDocumentsError,
    initUnsafeDocumentsInStore,
} from '#/src/ducks/attach-documents/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { generateFileId } from '#/src/utils/id-generators';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getAttachedDocumentsWorker({
    organizationId,
    loanMBId,
    borrowerCode,
}: ReturnType<typeof getAttachedDocuments>) {
    try {
        const attachedDocuments: ThenArg<typeof fetchers.getAttachedFiles> = yield call(
            fetchers.getAttachedFiles,
            { organizationId, loanMBId, borrowerCode },
        );

        yield put(
            initUnsafeDocumentsInStore(
                attachedDocuments.map((document) => ({
                    id: generateFileId(),
                    fileId: document.fileId,
                    name: document.fileName || '',
                    size: document.fileSizeInBytes || 0,
                    uploadStatus: 'SUCCESS',
                    showDelete: true,
                })),
            ),
        );
    } catch (error) {
        yield put(getAttachedDocumentsError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}
