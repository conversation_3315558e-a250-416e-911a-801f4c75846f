import { call, put, select } from 'redux-saga/effects';
import qs from 'qs';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getTrancheCreditProductsCountError,
    getTrancheCreditProductsCountFinish,
    type getTrancheCreditProductsCountStart,
} from '../../ducks/credit-products/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

import { ETrancheTypes } from './get-tranche-credit-products-worker';

export function getTrancheCreditProductsCountWorkerState(state: ApplicationState) {
    return {
        organizationId: currentOrganizationEqIdSelector(state),
        docNumber: `${
            qs.parse(state.router.location.search, { ignoreQueryPrefix: true }).docNumber
        }`,
    };
}

export function* getTrancheCreditProductsCountWorker({
    docNumber,
    trancheType = ETrancheTypes.tranche,
}: ReturnType<typeof getTrancheCreditProductsCountStart>) {
    const workerState: ReturnType<typeof getTrancheCreditProductsCountWorkerState> = yield select(
        getTrancheCreditProductsCountWorkerState,
    );

    const trancheCountMethod =
        trancheType === ETrancheTypes.tranche
            ? fetchers.getTrancheCreditProductsCountV2
            : fetchers.getDealCreditProductsCountV2;

    try {
        const trancheCreditProductsCount: ThenArg<typeof trancheCountMethod> = yield call(
            trancheCountMethod,
            {
                organizationId: workerState.organizationId,
                docNumber: docNumber || workerState.docNumber,
            },
        );

        yield put(
            getTrancheCreditProductsCountFinish({
                trancheType,
                docNumber,
                all: !!docNumber,
                tranchesCount: +trancheCreditProductsCount || 0,
            }),
        );
    } catch (error) {
        yield put(
            getTrancheCreditProductsCountError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
