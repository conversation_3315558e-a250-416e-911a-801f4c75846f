import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getCreditWidgetStatusError,
    getCreditWidgetStatusFinish,
    type getCreditWidgetStatusStart,
} from '#/src/ducks/credit-widget-status/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getCreditWidgetStatusWorker(
    action: ReturnType<typeof getCreditWidgetStatusStart>,
) {
    const organizationId: string = yield select(currentHeaderOrganizationEqIdSelector);

    try {
        const query = { limitExists: action.limitExists };

        const response: ThenArg<typeof fetchers.creditRequest.getWidgetStatus> = yield call(
            fetchers.creditRequest.getWidgetStatus,
            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
                query,
            },
        );

        yield put(getCreditWidgetStatusFinish({ response }));
    } catch (error) {
        yield put(
            getCreditWidgetStatusError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
