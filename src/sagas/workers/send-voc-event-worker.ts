import { call, select } from 'redux-saga/effects';
import <PERSON><PERSON> from 'js-cookie';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { type sendVOCEvent } from '#/src/ducks/shared/actions';
import { fetchers } from '#/src/utils/client-api';

const DISABLE_VOC_EVENTS_COOKIE = 'disable_voc_events';

export function* sendVOCEventWorker({ eventType }: ReturnType<typeof sendVOCEvent>) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );

    // Пропускаем отправку VOC событий, если установлена кука disable_voc_events
    if (Cookie.get(DISABLE_VOC_EVENTS_COOKIE)) {
        return;
    }

    try {
        yield call(fetchers.csi.sendOccuredEventInformation, {
            organizationId,
            eventType,
        });
    } catch (_) {
        // Никак не обрабатываем ошибку, так как этот запрос не должен влиять на работу приложения
    }
}
