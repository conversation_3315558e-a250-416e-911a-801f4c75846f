import { call, put, select } from 'redux-saga/effects';

import {
    createModuleLoader,
    createServerStateModuleFetcher,
    executeModuleFactory,
    type FactoryModule,
} from '@alfalab/scripts-modules';

import packageJson from '#/package.json';
import { VOC_EVENTS } from '#/src/constants/voc';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    isKIBCategoryCodeSelector,
    notifyAboutAdvancedRepaySelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { sendMailKMStart, setSuccessfullySignedModalOpen } from '#/src/ducks/early-pay/actions';
import { sendVOCEvent } from '#/src/ducks/shared/actions';
import { signModuleLoadError, signModuleLoadFinish } from '#/src/ducks/sign-module/actions';
import { getSignModuleFunctionSelector } from '#/src/ducks/sign-module/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';

type SignDocumentsPayload = {
    docNumber: string;
    paymentType: string;
    processingType: string;
    payload: {
        requestId: string;
        systemName?: string;
        showResult: boolean;
        documents: Array<{
            documentId: string;
            documentType: string;
            templateId: string;
        }>;
    };
};

const loader = createModuleLoader<FactoryModule>({
    hostAppId: packageJson.name,
    moduleId: 'signModule',
    getModuleResources: createServerStateModuleFetcher({
        baseUrl: '/sign-module',
    }),
});

export function* loadSignModuleWorker() {
    try {
        const loaderResult = yield call(loader);

        const sign = yield call(
            executeModuleFactory,
            loaderResult.module,
            loaderResult.moduleResources.moduleState,
        );

        yield put(signModuleLoadFinish(sign));
    } catch (error) {
        yield put(signModuleLoadError(error));
    }
}

export function* signDocuments({
    payload,
    docNumber,
    paymentType,
    processingType,
}: SignDocumentsPayload) {
    try {
        const sign = yield select(getSignModuleFunctionSelector);

        yield call(sign, payload);

        yield put(
            trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSuccessfullySignedModal, {
                agreementNumber: docNumber,
                processingType,
                paymentType,
            }),
        );

        yield put(setSuccessfullySignedModalOpen(true));

        yield call(callEventsAfterSuccessSign);
    } catch {
        // На не нужно отлавливать здесь ошибки, потому что это делается на стороне SignModule
    }
}

function* callEventsAfterSuccessSign() {
    try {
        yield put(sendVOCEvent(VOC_EVENTS.NIB_CP_EP));

        const notifyAboutAdvancedRepay: boolean = yield select(notifyAboutAdvancedRepaySelector);
        const isKib: boolean = yield select(isKIBCategoryCodeSelector);

        if (isKib && notifyAboutAdvancedRepay) {
            yield put(sendMailKMStart());
        }
    } catch (e) {}
}
