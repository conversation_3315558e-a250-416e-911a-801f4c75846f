import { call, put, select } from 'redux-saga/effects';

import {
    setOverdraftRepaymentError,
    setOverdraftRepaymentFinish,
    type setOverdraftRepaymentStart,
} from '#/src/ducks/credit-products/actions';
import { getOverdraftLimitSettingsState } from '#/src/sagas/workers/get-overdraft-limit-settings-worker';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* setOverdraftRepaymentWorker({
    repayment,
    dealId,
}: ReturnType<typeof setOverdraftRepaymentStart>) {
    try {
        const { organizationId, docNumber }: ReturnType<typeof getOverdraftLimitSettingsState> =
            yield select(getOverdraftLimitSettingsState);

        const response: ThenArg<typeof fetchers.getClientLimitSettingPropertiesV2> = yield call(
            fetchers.getClientLimitSettingPropertiesV2,
            {
                organizationId,
                docNumber,
                repaymentType: repayment,
            },
        );

        yield put(setOverdraftRepaymentFinish(dealId, response.repaymentResult));
    } catch (error) {
        yield put(
            setOverdraftRepaymentError(dealId, new ServerResponseError(getErrorMessage(error))),
        );
    }
}
