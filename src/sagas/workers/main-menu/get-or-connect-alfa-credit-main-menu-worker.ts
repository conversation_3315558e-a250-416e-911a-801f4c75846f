import { call, put } from 'redux-saga/effects';

import {
    getOrConnectAlfaCreditMainMenuError,
    getOrConnectAlfaCreditMainMenuFinish,
    type getOrConnectAlfaCreditMainMenuStart,
} from '#/src/ducks/credit-products-main-menu/actions';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { LOG_LEVEL } from '../../../types/logger';
import { fetchers } from '../../../utils/client-api';
import { ServerResponseError } from '../../../utils/errors/server-response-error';
import { type ThenArg } from '../../../utils/promise-helper';

export function* getOrConnectAlfaCreditMainMenuWorker({
    customerIds,
}: ReturnType<typeof getOrConnectAlfaCreditMainMenuStart>) {
    try {
        const response: ThenArg<
            typeof fetchers.corpCreditDocumentCirculation.getOrConnectAlfaCredit
        > = yield call(fetchers.corpCreditDocumentCirculation.getOrConnectAlfaCredit, {
            body: { customerIds },
        });

        yield put(getOrConnectAlfaCreditMainMenuFinish(response));
    } catch (error) {
        yield put(
            getOrConnectAlfaCreditMainMenuError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
