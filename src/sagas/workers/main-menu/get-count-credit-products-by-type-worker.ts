import { call, put } from 'redux-saga/effects';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import {
    getCountCreditProductsByTypeError,
    getCountCreditProductsByTypeFinish,
    type getCountCreditProductsByTypeStart,
} from '#/src/ducks/credit-products-main-menu/actions';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { LOG_LEVEL } from '../../../types/logger';
import { fetchers } from '../../../utils/client-api';
import { ServerResponseError } from '../../../utils/errors/server-response-error';
import { type ThenArg } from '../../../utils/promise-helper';

export function* getCountCreditProductsByTypeWorker({
    customerIds,
}: ReturnType<typeof getCountCreditProductsByTypeStart>) {
    try {
        const response: ThenArg<typeof fetchers.creditProductsRestV2.getCountCreditProductsByType> =
            yield call(fetchers.creditProductsRestV2.getCountCreditProductsByType, {
                body: {
                    customerIds,
                    productTypes: [
                        ECreditProductsCodes.CREDIT_LINE,
                        ECreditProductsCodes.GUARANTY_LINE,
                    ],
                },
            });

        yield put(getCountCreditProductsByTypeFinish({ productCounts: response.productCounts }));
    } catch (error) {
        yield put(
            getCountCreditProductsByTypeError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
