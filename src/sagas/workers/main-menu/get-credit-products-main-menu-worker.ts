import { call, put, select } from 'redux-saga/effects';
import { type CreditProductsRequest } from 'corp-credit-products-api-typescript-services/dist/creditProductsRequest';
import { Status } from 'corp-credit-products-api-typescript-services/dist/status';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    EOrderByParamsFilter,
    PRODUCTS_OPTIONS,
} from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-filter/constants';
import { mapProductsFilter } from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-filter/utils';
import {
    getCreditProductsMainMenuError,
    getCreditProductsMainMenuFinish,
    type getCreditProductsMainMenuStart,
} from '#/src/ducks/credit-products-main-menu/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getCreditProductsMainMenuWorker({
    filter,
    pagination,
}: ReturnType<typeof getCreditProductsMainMenuStart>) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );
    // если в фильтре не выбраны продукты, то отправляем весь список продуктов
    const productTypes = filter.products.length
        ? filter.products.map((item) => item.key)
        : PRODUCTS_OPTIONS.map((item) => item.key);

    // если в фильтре не выбраны компании, то отправляем весь список компаний
    const cutomers = filter.company.length > 0 ? filter.company : filter.companiesOptions;
    const filterResult: CreditProductsRequest = {
        filter: {
            customers: mapProductsFilter(cutomers),
            productTypes,
            isActive: filter.status[0].key === Status.Active,
            docNumber: filter.docNumber,
        },
        sort: { sortByCustomerName: filter.sortBy[0].key === EOrderByParamsFilter.COMPANY },
        page: pagination,
    };

    try {
        const creditProducts: ThenArg<
            typeof fetchers.creditProductsRestV2.getCreditProductsMainMenu
        > = yield call(fetchers.creditProductsRestV2.getCreditProductsMainMenu, {
            headers: {
                [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
            },
            body: filterResult,
        });

        yield put(getCreditProductsMainMenuFinish(creditProducts, filter));
    } catch (error) {
        yield put(getCreditProductsMainMenuError(new ServerResponseError(getErrorMessage(error))));
    }
}
