import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    getCategoryMainMenuError,
    getCategoryMainMenuFinish,
    type getCategoryMainMenuStart,
} from '#/src/ducks/credit-products-main-menu/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getCategoryMainMenuWorker(action: ReturnType<typeof getCategoryMainMenuStart>) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );

    try {
        const category: ThenArg<typeof fetchers.mksCustomers.getCategory> = yield call(
            fetchers.mksCustomers.getCategory,
            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: action.organizationId ?? organizationId,
                },
                urlParams: { customerId: action.organizationId ?? organizationId },
            },
        );

        yield put(getCategoryMainMenuFinish(category));
    } catch (error) {
        yield put(getCategoryMainMenuError(new ServerResponseError(getErrorMessage(error))));
    }
}
