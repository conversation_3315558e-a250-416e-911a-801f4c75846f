import { call, put, select } from 'redux-saga/effects';

import {
    type removeAttachedDocument,
    removeAttachedDocumentError,
    removeUnsafeDocumentFromStore,
} from '#/src/ducks/attach-documents/actions';
import {
    borrowerCodeSelector,
    documentsSelector,
    loanMBIdSelector,
} from '#/src/ducks/attach-documents/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

export function* removeAttachedDocumentWorker({
    documentId,
}: ReturnType<typeof removeAttachedDocument>) {
    try {
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );
        const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);
        const borrowerCode: ReturnType<typeof borrowerCodeSelector> =
            yield select(borrowerCodeSelector);
        const documents: ReturnType<typeof documentsSelector> = yield select(documentsSelector);

        const document = documents.get(documentId);

        const hasDocumentError = document?.error;

        if (hasDocumentError) {
            yield put(removeUnsafeDocumentFromStore(documentId));

            return;
        }

        yield call(fetchers.removeAttachedFile, {
            organizationId,
            loanMBId,
            borrowerCode,
            fileId: document?.fileId,
        });

        yield put(removeUnsafeDocumentFromStore(documentId));
    } catch (error) {
        yield put(removeAttachedDocumentError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}
