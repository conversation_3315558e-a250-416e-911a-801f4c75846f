import { call, put, select } from 'redux-saga/effects';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    setOverdraftLimitError,
    setOverdraftLimitFinish,
    type setOverdraftLimitStart,
} from '../../ducks/credit-products/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';

function setOverdraftLimitWorkerState(state: ApplicationState) {
    return {
        profileId: state.user.id,
        organizationId: currentOrganizationEqIdSelector(state),
    };
}

export function* setOverdraftLimitWorker({
    docNumber,
    limit,
}: ReturnType<typeof setOverdraftLimitStart>) {
    try {
        const { organizationId }: ReturnType<typeof setOverdraftLimitWorkerState> = yield select(
            setOverdraftLimitWorkerState,
        );

        yield call(fetchers.setClientLimitV2, { organizationId, request: { docNumber, limit } });

        yield put(setOverdraftLimitFinish(docNumber, limit));
    } catch (error) {
        yield put(
            setOverdraftLimitError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
