import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import {
    getEarlyRepaymentProcessingTypeError,
    getEarlyRepaymentProcessingTypeFinish,
    type getEarlyRepaymentProcessingTypeStart,
} from '../../ducks/credit-processing/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function* getEarlyRepaymentProcessingTypeWorker({
    agreementNumber,
    accountNumber,
}: ReturnType<typeof getEarlyRepaymentProcessingTypeStart>) {
    try {
        const customerId: string = yield select(currentOrganizationEqIdSelector);

        const processingType: ThenArg<
            typeof fetchers.earlyRepaymentRest.getEarlyRepaymentProcessingType
        > = yield call(fetchers.earlyRepaymentRest.getEarlyRepaymentProcessingType, {
            headers: {
                [HEADERS.OPENAPI_COMPANY_ID]: [customerId],
            },
            query: {
                customerId,
                agreementNumber,
                accountNumber,
            },
        });

        yield put(getEarlyRepaymentProcessingTypeFinish({ processingType, agreementNumber }));
    } catch (error) {
        yield put(
            getEarlyRepaymentProcessingTypeError({
                error: new ServerResponseError(getErrorMessage(error)),
                logLevel: LOG_LEVEL.ERROR,
                agreementNumber,
            }),
        );
    }
}
