import { call, put, select } from 'redux-saga/effects';
import { type ResponseError } from 'corp-core-credit-products-api-typescript-services/dist/responseError';
import { type ApiGetManagersQueryParams } from 'corp-customers-api-typescript-services/dist/api-types';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getManagersError,
    getManagersFinish,
    type getManagersStart,
} from '#/src/ducks/organization/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getManagersWorker({ payload }: ReturnType<typeof getManagersStart>) {
    try {
        const customerId: string = yield select(currentHeaderOrganizationEqIdSelector);

        const codes = payload.codes.join(',') as unknown as Set<string>;

        const body: ApiGetManagersQueryParams = { codes };

        const response: ThenArg<typeof fetchers.mksCustomers.getManagers> = yield call(
            fetchers.mksCustomers.getManagers,
            {
                headers: { [HEADERS.OPENAPI_COMPANY_ID]: customerId },
                urlParams: { customerId },
                query: body,
            },
        );

        yield put(getManagersFinish(response));
    } catch (error) {
        yield put(getManagersError(error as ResponseError, LOG_LEVEL.ERROR));
    }
}
