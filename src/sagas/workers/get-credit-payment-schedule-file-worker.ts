import { call, put, select } from 'redux-saga/effects';
import { ReportFormat } from 'thrift-services/entities';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { downloadSignedBinaryFileByExtension } from '#/src/utils/binary-download';
import { fetchers } from '#/src/utils/client-api';
import { getPaymentScheduleFileErrorMessage } from '#/src/utils/payments-schedule-helpers';

import { type ApplicationState } from '../../ducks/application-state';
import {
    type getPaymentScheduleFile,
    getPaymentScheduleFileError,
    getPaymentScheduleFileStart,
    getPaymentScheduleFilFinish,
} from '../../ducks/payment-schedule/actions';
import { LOG_LEVEL } from '../../types/logger';

function getCreditPaymentScheduleFileWorkerState(state: ApplicationState) {
    return {
        organizationId: currentOrganizationEqIdSelector(state),
        contextRoot: state.settings.contextRoot,
    };
}

export function* getCreditPaymentScheduleFileWorker({
    data,
}: ReturnType<typeof getPaymentScheduleFile>) {
    const { organizationId }: ReturnType<typeof getCreditPaymentScheduleFileWorkerState> =
        yield select(getCreditPaymentScheduleFileWorkerState);

    try {
        yield put(getPaymentScheduleFileStart());
        const response: {
            body: {
                data: number[];
            };
        } = yield call(fetchers.getPaymentScheduleFileV2, {
            organizationId,
            data,
        });

        if (data.fileType) {
            downloadSignedBinaryFileByExtension(
                {
                    type: ReportFormat[data.fileType],
                    data: response.body.data,
                },
                'График платежей',
                data.fileType.toLowerCase(),
            );
        }
        yield put(getPaymentScheduleFilFinish());
    } catch (error) {
        const { text, title } = getPaymentScheduleFileErrorMessage(error);

        yield put(getPaymentScheduleFileError({ text, title }, LOG_LEVEL.ERROR));
    }
}
