import { call, put, select } from 'redux-saga/effects';

import {
    getDocumentListError,
    getDocumentListFinish,
    type getDocumentListStart,
} from '#/src/ducks/credit-document-circulation/actions';
import { documentListSelector } from '#/src/ducks/credit-document-circulation/selectors';
import { type TDocument } from '#/src/ducks/credit-document-circulation/types';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getDocumentListWorker({
    docType,
    pageFilter,
    filter,
    tabName,
}: ReturnType<typeof getDocumentListStart>) {
    try {
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );
        const documentList: ReturnType<typeof documentListSelector> =
            yield select(documentListSelector);

        const response: ThenArg<TDocument[]> = yield call(fetchers.getDocList, {
            organizationId,
            docType,
            pageFilter,
            filter,
        });

        yield put(
            getDocumentListFinish(
                {
                    ...documentList,
                    [tabName]:
                        pageFilter.pageNumber === 1
                            ? response
                            : [...(documentList?.[tabName] ?? []), ...response],
                },
                response.length === 0,
            ),
        );
    } catch (error) {
        yield put(
            getDocumentListError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
