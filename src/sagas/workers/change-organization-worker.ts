import { put } from 'redux-saga/effects';

import { resetAttachDocumentsState } from '#/src/ducks/attach-documents/actions';
import { getDocumentListFinish } from '#/src/ducks/credit-document-circulation/actions';
import { resetCreditOffersState } from '#/src/ducks/credit-offers/actions';
import {
    resetClientLimitState,
    resetCreditProductsState,
} from '#/src/ducks/credit-products/actions';
import { resetCreditProductsHeaderState } from '#/src/ducks/credit-products-header/actions';
import { resetCreditProductsMainMenuState } from '#/src/ducks/credit-products-main-menu/actions';
import { resetCreditRequestsState } from '#/src/ducks/credit-requests/actions';
import { resetCreditWidgetStatus } from '#/src/ducks/credit-widget-status/actions';
import { resetCategory, resetManagers } from '#/src/ducks/organization/actions';
import { resetStatementRequestsState } from '#/src/ducks/statement-requests/actions';
import { resetSuspensiveConditionsState } from '#/src/ducks/suspensive-conditions/actions';

export function* changeOrganizationWorker() {
    yield put(resetCreditProductsState());
    yield put(resetCreditOffersState());
    yield put(resetCreditProductsMainMenuState());
    yield put(resetAttachDocumentsState());
    yield put(resetSuspensiveConditionsState());
    yield put(resetStatementRequestsState());
    yield put(getDocumentListFinish({}));
    yield put(resetCreditProductsHeaderState());
    yield put(resetClientLimitState());
    yield put(resetCreditWidgetStatus());
    yield put(resetManagers());
    yield put(resetCategory());
    yield put(resetCreditRequestsState());
}
