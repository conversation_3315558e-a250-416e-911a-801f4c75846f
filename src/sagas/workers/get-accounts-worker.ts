import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';
import { OperationType } from 'thrift-services/services/accounts';

import { getAccountsError, getAccountsFinish } from '../../ducks/accounts/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { getErrorMessage } from '../../utils/errors/get-error-message';
import { NoAccountsError } from '../../utils/errors/no-accounts-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function* getOrganizationAccountsWorker() {
    try {
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );

        const accounts: ThenArg<typeof fetchers.mksAccounts.getByCustomerId> = yield call(
            fetchers.mksAccounts.getByCustomerId,

            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
                urlParams: { customerId: organizationId },
                query: { getBalance: true, operationType: OperationType.APAY },
            },
        );

        yield put(getAccountsFinish(accounts));
    } catch (error) {
        if (error instanceof NoAccountsError) {
            yield put(getAccountsError(error, LOG_LEVEL.WARN));

            return;
        }

        yield put(
            getAccountsError(new ServerResponseError(getErrorMessage(error)), LOG_LEVEL.ERROR),
        );
    }
}
