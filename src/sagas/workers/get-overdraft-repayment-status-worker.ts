import { call, put, select } from 'redux-saga/effects';

import {
    getOverdraftRepaymentStatusError,
    getOverdraftRepaymentStatusFinish,
    type getOverdraftRepaymentStatusStart,
} from '#/src/ducks/credit-products/actions';
import { getOverdraftLimitSettingsState } from '#/src/sagas/workers/get-overdraft-limit-settings-worker';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getOverdraftRepaymentStatusWorker({
    dealId,
}: ReturnType<typeof getOverdraftRepaymentStatusStart>) {
    try {
        const { organizationId, docNumber }: ReturnType<typeof getOverdraftLimitSettingsState> =
            yield select(getOverdraftLimitSettingsState);

        const response: ThenArg<typeof fetchers.getChangeRepaymentStatusV2> = yield call(
            fetchers.getChangeRepaymentStatusV2,
            {
                organizationId,
                docNumber,
            },
        );

        yield put(getOverdraftRepaymentStatusFinish(dealId, response));
    } catch (error) {
        yield put(
            getOverdraftRepaymentStatusError(
                dealId,
                new ServerResponseError(getErrorMessage(error)),
            ),
        );
    }
}
