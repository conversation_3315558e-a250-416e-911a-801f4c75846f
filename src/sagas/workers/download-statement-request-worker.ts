import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { DATE_FORMAT } from '@alfalab/core-components/date-input';

import { DATE_FOR_SERVICES } from '#/src/constants/date';
import { VOC_EVENTS } from '#/src/constants/voc';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { sendVOCEvent } from '#/src/ducks/shared/actions';
import {
    downloadStatementRequestError,
    downloadStatementRequestFinish,
    type downloadStatementRequestStart,
} from '#/src/ducks/statement-requests/actions';
import { statementRequestsListSelector } from '#/src/ducks/statement-requests/selectors';
import { STATEMENTS_METRICS } from '#/src/metrics';
import { LOG_LEVEL } from '#/src/types/logger';
import { downloadBinaryFile } from '#/src/utils/binary-download';
import { reformatDate } from '#/src/utils/date';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { fetchers } from '../../utils/client-api';

const getExtension = (type: string) => {
    switch (type) {
        case 'application/pdf':
            return 'pdf';
        case 'application/zip':
            return 'zip';
        default:
            return 'xls';
    }
};

export function* downloadStatementRequestWorker({
    fileId,
    fromDate,
    toDate,
    docNumber,
}: ReturnType<typeof downloadStatementRequestStart>) {
    const currentOrganizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );
    const statementRequestList: ReturnType<typeof statementRequestsListSelector> = yield select(
        statementRequestsListSelector,
    );

    try {
        yield put(trackAlfaMetrics(STATEMENTS_METRICS.clickDownloadButton, {}));

        const fetchResponse: Blob = yield call(fetchers.corpLoanStatements.getFileById, {
            headers: {
                [HEADERS.OPENAPI_COMPANY_ID]: currentOrganizationId,
            },
            query: {
                customerId: currentOrganizationId,
                fileId,
            },
        });

        const fromDateFormatted = reformatDate(fromDate, DATE_FOR_SERVICES, DATE_FORMAT);
        const toDateFormatted = reformatDate(toDate, DATE_FOR_SERVICES, DATE_FORMAT);
        const ext = getExtension(fetchResponse.type);

        downloadBinaryFile(
            fetchResponse,
            `Выписка_${docNumber}_${fromDateFormatted}-${toDateFormatted}.${ext}`,
        );

        const statementRequest = statementRequestList.map((request) => {
            if (request.id === fileId) {
                return {
                    ...request,
                    isViewed: true,
                };
            }

            return request;
        });

        yield put(downloadStatementRequestFinish({ list: statementRequest }));

        yield put(sendVOCEvent(VOC_EVENTS.NIB_CP_DS));
        yield put(trackAlfaMetrics(STATEMENTS_METRICS.successfullyDownloaded, {}));
    } catch (error) {
        yield put(
            downloadStatementRequestError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );

        yield put(
            trackAlfaMetrics(STATEMENTS_METRICS.downloadError, { error: getErrorMessage(error) }),
        );
    }
}
