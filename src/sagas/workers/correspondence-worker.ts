import { call, put, select } from 'redux-saga/effects';
import { parse } from 'date-fns';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { DATE_FORMAT } from '#/src/constants/date';
import { SIGN_MODULE_DOCUMENT_TYPES } from '#/src/constants/sign';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { setSigningErrorModalOpen } from '#/src/ducks/early-pay/actions';
import { setCurrentTime, setTimeAfterDeadlineError } from '#/src/ducks/settings/actions';
import { deadlineForAcceptingApplicationsMoscowTimeSelector } from '#/src/ducks/settings/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { getCurrentMoscowDate, getIsDatesEqual } from '#/src/utils/date';
import { getIsTimeBeforeDeadline } from '#/src/utils/early-pay';
import { getErrorCode } from '#/src/utils/errors/get-error-message';
import { type ThenArg } from '#/src/utils/promise-helper';

import { type saveLetterStart, setIsSuccessSaveLetter } from '../../ducks/correspondence/actions';
import { currentOrganizationSelector } from '../../ducks/organization/selectors';
import { fetchers } from '../../utils/client-api';

import { signDocuments } from './sign-module-worker';

export function* saveAndSignLetterWorker({
    letter,
    data: { docNumber, paymentType, paymentDate },
}: ReturnType<typeof saveLetterStart>) {
    const deadlineForAcceptingApplicationsMoscowTime: { hours: number; minutes: number } =
        yield select(deadlineForAcceptingApplicationsMoscowTimeSelector);
    const { eqId } = yield select(currentOrganizationSelector);

    try {
        const currentTime = getCurrentMoscowDate();

        yield put(setCurrentTime(currentTime));
        yield put(setIsSuccessSaveLetter(false));

        const formatCurrentTime = new Date(currentTime);
        const formatPaymentDate = parse(paymentDate, DATE_FORMAT, new Date());
        const isTodayTimeBeforeDeadline = getIsTimeBeforeDeadline(
            formatCurrentTime,
            deadlineForAcceptingApplicationsMoscowTime,
        );

        if (
            (getIsDatesEqual(formatPaymentDate, formatCurrentTime) && isTodayTimeBeforeDeadline) ||
            formatPaymentDate.valueOf() > formatCurrentTime.valueOf()
        ) {
            const messageId: ThenArg<typeof fetchers.sendLetterToBranch> = yield call(
                fetchers.sendLetterToBranch,
                {
                    ...letter,
                    organizationId: eqId,
                },
            );

            if (messageId) {
                yield put(
                    trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSignModal, {
                        agreementNumber: docNumber,
                        processingType: earlyRepaymentProcessingTypes.MANUAL.toLowerCase(),
                        paymentType,
                    }),
                );
                yield call(sendLetterGroupToSign, messageId, docNumber, paymentType);
                yield put(setIsSuccessSaveLetter(true));
            }
        } else {
            yield put(setTimeAfterDeadlineError(true));
        }
    } catch (e) {
        yield put(setSigningErrorModalOpen(true));
    }
}

export function* sendLetterGroupToSign(messageId: string, docNumber: string, paymentType: string) {
    const { eqId } = yield select(currentOrganizationSelector);

    try {
        yield call(fetchers.sendLetterGroupToSign, {
            organizationId: eqId,
            letterIds: [messageId],
        });

        yield call(signDocuments, {
            docNumber,
            paymentType,
            processingType: earlyRepaymentProcessingTypes.MANUAL.toLowerCase(),
            payload: {
                requestId: messageId,
                systemName: 'nib-messages',
                showResult: false,
                documents: [
                    {
                        documentId: messageId,
                        documentType: SIGN_MODULE_DOCUMENT_TYPES.LETTER,
                        templateId: 'default',
                    },
                ],
            },
        });
    } catch (error) {
        if (getErrorCode(error) === 500) {
            yield put(setTimeAfterDeadlineError(true));
        } else {
            yield put(setSigningErrorModalOpen(true));
        }
    }
}
