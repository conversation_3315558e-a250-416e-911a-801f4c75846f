import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';
import { parse } from 'date-fns';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { DATE_FORMAT } from '#/src/constants/date';
import { SIGN_MODULE_DOCUMENT_TYPES } from '#/src/constants/sign';
import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    createEarlyRepaymentApplFinish,
    type createEarlyRepaymentApplStart,
} from '#/src/ducks/credit-processing/actions';
import {
    isCalculateFeeForEarlyRepaymentSelector,
    productByDocNumberSelector,
} from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import {
    setRequestAlreadyCreatedErrorModalOpen,
    setSigningErrorModalOpen,
} from '#/src/ducks/early-pay/actions';
import { currentOrganizationSelector } from '#/src/ducks/organization/selectors';
import { setCurrentTime, setTimeAfterDeadlineError } from '#/src/ducks/settings/actions';
import { deadlineForAcceptingApplicationsMoscowTimeSelector } from '#/src/ducks/settings/selectors';
import { userSelector } from '#/src/ducks/user/selectors';
import { EARLY_REPAYMENT_METRICS } from '#/src/metrics';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';
import { getCurrentMoscowDate, getIsDatesEqual } from '#/src/utils/date';
import { getBodyForEarlyPay, getIsTimeBeforeDeadline } from '#/src/utils/early-pay';
import { getErrorCode } from '#/src/utils/errors/get-error-message';
import { type ThenArg } from '#/src/utils/promise-helper';

import { fetchers } from '../../utils/client-api';

import { signDocuments } from './sign-module-worker';

export function* createEarlyRepaymentApplWorker(
    action: ReturnType<typeof createEarlyRepaymentApplStart>,
) {
    const { lastName, firstName, middleName } = yield select(userSelector);
    const { inn, name, eqId } = yield select(currentOrganizationSelector);
    const product: SomeMappedProduct = yield select(productByDocNumberSelector);
    const calculateFeeForEarlyRepayment: boolean = yield select(
        isCalculateFeeForEarlyRepaymentSelector,
    );
    const deadlineForAcceptingApplicationsMoscowTime: { hours: number; minutes: number } =
        yield select(deadlineForAcceptingApplicationsMoscowTimeSelector);

    const {
        docNumber,
        selectedAccountNumber,
        paymentType,
        paymentDate,
        debtToPay,
        interestToPay,
        total,
        currency,
    } = action.applicationData;

    try {
        const currentTime = getCurrentMoscowDate();

        yield put(setCurrentTime(currentTime));

        const formatCurrentTime = new Date(currentTime);
        const formatPaymentDate = parse(paymentDate, DATE_FORMAT, new Date());
        const isTodayTimeBeforeDeadline = getIsTimeBeforeDeadline(
            formatCurrentTime,
            deadlineForAcceptingApplicationsMoscowTime,
        );

        if (
            (getIsDatesEqual(formatPaymentDate, formatCurrentTime) && isTodayTimeBeforeDeadline) ||
            formatPaymentDate.valueOf() > formatCurrentTime.valueOf()
        ) {
            const applicationId: ThenArg<
                typeof fetchers.earlyRepaymentRest.createEarlyRepaymentAppl
            > = yield call(fetchers.earlyRepaymentRest.createEarlyRepaymentAppl, {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: eqId,
                },
                body: getBodyForEarlyPay({
                    docNumber,
                    paymentType,
                    paymentDate,
                    interestToPay,
                    total,
                    currency,
                    debtToPay,
                    selectedAccountNumber,
                    product,
                    name,
                    customerId: eqId,
                    inn,
                    lastName,
                    firstName,
                    middleName,
                    formatCurrentTime,
                    calculateFeeForEarlyRepayment,
                }),
            });

            if (applicationId) {
                yield put(createEarlyRepaymentApplFinish(applicationId));
                yield put(
                    trackAlfaMetrics(EARLY_REPAYMENT_METRICS.showSignModal, {
                        agreementNumber: docNumber,
                        processingType: earlyRepaymentProcessingTypes.AUTO.toLowerCase(),
                        paymentType,
                    }),
                );

                yield call(signDocuments, {
                    docNumber,
                    paymentType,
                    processingType: earlyRepaymentProcessingTypes.AUTO.toLowerCase(),
                    payload: {
                        requestId: applicationId,
                        showResult: false,
                        documents: [
                            {
                                documentId: applicationId,
                                documentType: SIGN_MODULE_DOCUMENT_TYPES.EARLY_REPAYMENT_APPL,
                                templateId: 'default',
                            },
                        ],
                    },
                });
            }
        } else {
            yield put(setTimeAfterDeadlineError(true));
        }
    } catch (error) {
        switch (getErrorCode(error)) {
            case 409:
                yield put(setRequestAlreadyCreatedErrorModalOpen(true));
                break;
            case 422:
                yield put(setTimeAfterDeadlineError(true));
                break;
            default:
                yield put(setSigningErrorModalOpen(true));
                break;
        }
    }
}
