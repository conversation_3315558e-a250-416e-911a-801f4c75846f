import { call } from 'redux-saga/effects';

import { assertNever } from '../../types/assert-never';
import { LOG_LEVEL, type TErrorAction } from '../../types/logger';
import { fetchers } from '../../utils/client-api';

export function* logWorker({ error, logLevel = LOG_LEVEL.ERROR }: TErrorAction) {
    switch (logLevel) {
        case LOG_LEVEL.INFO:
            yield call(fetchers.clientInfoLog, error.message);
            break;
        case LOG_LEVEL.WARN:
            yield call(fetchers.clientWarnLog, error.message);
            break;
        case LOG_LEVEL.ERROR:
            yield call(fetchers.clientErrorLog, error);
            break;
        default:
            assertNever(logLevel);
    }
}
