import { call, put } from 'redux-saga/effects';

import { LOG_LEVEL } from '#/src/types/logger';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import {
    findRequestsInProgressStatisticsError,
    findRequestsInProgressStatisticsFinish,
    type findRequestsInProgressStatisticsStart,
} from '../../ducks/ufr-ak-corp-gateway-rest/actions';
import { fetchers } from '../../utils/client-api';

export function* findRequestsInProgressStatisticsWorker(
    action: ReturnType<typeof findRequestsInProgressStatisticsStart>,
) {
    try {
        const query = { cuses: action.cuses };

        const response: ThenArg<typeof fetchers.findRequestsInProgressStatistics> = yield call(
            fetchers.findRequestsInProgressStatistics,
            query,
        );

        yield put(findRequestsInProgressStatisticsFinish(response));
    } catch (error) {
        yield put(
            findRequestsInProgressStatisticsError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
