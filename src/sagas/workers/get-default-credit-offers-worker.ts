import { put, select } from 'redux-saga/effects';

import { isMmbCategoryHeaderSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    BUSINESS_AUTO_CREDIT_OFFER,
    BUSINESS_CREDIT_REFINANCING_OFFER,
    BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB_OFFER,
    type TMappedCreditOffer,
} from '#/src/utils/credit-offers-mappers';

import { getDefaultCreditOffersFinish } from '../../ducks/credit-offers/actions';

type TDefaultOffer = {
    offer: TMappedCreditOffer;
    visible: boolean;
};

export function* getDefaultCreditOffersWorker() {
    const isMmbCategoryHeader: ReturnType<typeof isMmbCategoryHeaderSelector> = yield select(
        isMmbCategoryHeaderSelector,
    );

    const defaultOffers: TDefaultOffer[] = [
        { offer: BUSINESS_CREDIT_REFINANCING_OFFER, visible: isMmbCategoryHeader },
        { offer: BUSINESS_AUTO_CREDIT_OFFER, visible: isMmbCategoryHeader },
        { offer: BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB_OFFER, visible: isMmbCategoryHeader },
    ];

    const mappedDefaultOffers = defaultOffers
        .filter(({ visible }) => !!visible)
        .map(({ offer }) => offer);

    yield put(getDefaultCreditOffersFinish(mappedDefaultOffers));
}
