import { call, put, select } from 'redux-saga/effects';
import { type ClientLimitResponse } from 'corp-core-credit-products-api-typescript-services/dist/clientLimitResponse';
import { type ResponseError } from 'corp-core-credit-products-api-typescript-services/dist/responseError';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    getClientLimitError,
    getClientLimitFinish,
    type getClientLimitStart,
} from '#/src/ducks/credit-products/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorCode } from '#/src/utils/errors/get-error-message';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getClientLimitWorker(action: ReturnType<typeof getClientLimitStart>) {
    const organizationId: string = yield select(currentHeaderOrganizationEqIdSelector);

    try {
        const query = action.payload.kpp ? { kpp: action.payload.kpp } : {};

        const response: ThenArg<typeof fetchers.coreCreditProductsRestV2.getClientLimit> =
            yield call(fetchers.coreCreditProductsRestV2.getClientLimit, {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
                urlParams: { inn: action.payload.inn },
                query,
            });

        yield put(getClientLimitFinish(response));
    } catch (error) {
        if (getErrorCode(error) === 403) {
            yield put(getClientLimitFinish({} as ClientLimitResponse));
        } else {
            yield put(getClientLimitError(error as ResponseError, LOG_LEVEL.ERROR));
        }
    }
}
