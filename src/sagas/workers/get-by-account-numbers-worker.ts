import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    getByAccountNumbersError,
    getByAccountNumbersFinish,
    type getByAccountNumbersStart,
} from '../../ducks/accounts/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { getErrorMessage } from '../../utils/errors/get-error-message';
import { NoAccountsError } from '../../utils/errors/no-accounts-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function* getByAccountNumbersWorker({
    accountNumbers,
    organizationId,
}: ReturnType<typeof getByAccountNumbersStart>) {
    try {
        const currentOrganizationId: ReturnType<typeof currentOrganizationEqIdSelector> =
            yield select(currentOrganizationEqIdSelector);

        if (accountNumbers?.length) {
            const accounts: ThenArg<typeof fetchers.mksAccounts.getByAccountNumbers> = yield call(
                fetchers.mksAccounts.getByAccountNumbers,

                {
                    headers: {
                        [HEADERS.OPENAPI_COMPANY_ID]: organizationId ?? currentOrganizationId,
                    },
                    body: {
                        accountNumbers,
                        getBalance: true,
                        operationType: 'SERV',
                    },
                },
            );

            yield put(getByAccountNumbersFinish(accounts));
        } else {
            yield put(getByAccountNumbersFinish([]));
        }
    } catch (error) {
        if (error instanceof NoAccountsError) {
            yield put(getByAccountNumbersError(error, LOG_LEVEL.WARN));

            return;
        }

        yield put(
            getByAccountNumbersError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
