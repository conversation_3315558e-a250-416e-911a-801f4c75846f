import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    getCategoryHeaderError,
    getCategoryHeaderFinish,
    type getCategoryHeaderStart,
} from '#/src/ducks/credit-products-header/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getCategoryHeaderWorker({
    organizationId,
}: ReturnType<typeof getCategoryHeaderStart>) {
    const currentOrganizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );

    const customerId = organizationId ?? currentOrganizationId;

    try {
        const category: ThenArg<typeof fetchers.mksCustomers.getCategory> = yield call(
            fetchers.mksCustomers.getCategory,
            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: customerId,
                },
                urlParams: { customerId },
            },
        );

        yield put(getCategoryHeaderFinish(category));
    } catch (error) {
        yield put(getCategoryHeaderError(new ServerResponseError(getErrorMessage(error))));
    }
}
