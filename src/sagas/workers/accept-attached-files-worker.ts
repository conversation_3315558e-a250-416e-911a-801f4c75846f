import { call, put, select } from 'redux-saga/effects';

import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import {
    acceptAttachedFilesError,
    acceptAttachedFilesFinish,
    setClientDocumentsRequestStatus,
} from '#/src/ducks/attach-documents/actions';
import {
    borrowerCodeSelector,
    loanMBIdSelector,
    totalSizeAttachDocumentsSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    currentOrganizationEqIdSelector,
    getCurrentOrganizationNameSelector,
} from '#/src/ducks/organization/selectors';
import { CLIENT_DOCUMENTS_ATTACH_METRICS } from '#/src/metrics';
import { EClientDocumentsRequestStatuses } from '#/src/types/client-documents';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

export function* acceptAttachedFilesWorker() {
    try {
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );
        const organizationName: ReturnType<typeof getCurrentOrganizationNameSelector> =
            yield select(getCurrentOrganizationNameSelector);
        const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);
        const borrowerCode: ReturnType<typeof borrowerCodeSelector> =
            yield select(borrowerCodeSelector);
        const totalFilesSize: ReturnType<typeof totalSizeAttachDocumentsSelector> = yield select(
            totalSizeAttachDocumentsSelector,
        );

        yield call(fetchers.acceptAttachedFiles, {
            organizationId,
            organizationName,
            loanMBId,
            borrowerCode,
        });

        yield put(
            trackAlfaMetrics(CLIENT_DOCUMENTS_ATTACH_METRICS.sendClientDocuments, {
                loanMBId,
                borrowerCode,
                totalFilesSize,
            }),
        );

        yield put(setClientDocumentsRequestStatus(EClientDocumentsRequestStatuses.CHECK));

        yield put(acceptAttachedFilesFinish());
    } catch (error) {
        yield put(
            acceptAttachedFilesError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
