import { call, put, select } from 'redux-saga/effects';
import qs from 'qs';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getOverdraftLimitSettingsError,
    getOverdraftLimitSettingsFinish,
} from '../../ducks/credit-products/actions';
import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function getOverdraftLimitSettingsState(state: ApplicationState) {
    return {
        profileId: state.user.id,
        organizationId: currentOrganizationEqIdSelector(state),
        docNumber: `${
            qs.parse(state.router.location.search, { ignoreQueryPrefix: true }).docNumber
        }`,
    };
}

export function* getOverdraftLimitSettingsWorker() {
    try {
        const { organizationId, docNumber }: ReturnType<typeof getOverdraftLimitSettingsState> =
            yield select(getOverdraftLimitSettingsState);

        const overdraftLimitSettings: ThenArg<typeof fetchers.getClientLimitSettingPropertiesV2> =
            yield call(fetchers.getClientLimitSettingPropertiesV2, { organizationId, docNumber });

        yield put(
            getOverdraftLimitSettingsFinish({
                docNumber,
                maxClientLimit: overdraftLimitSettings.maxLimit,
                hasClientLimitSetting: overdraftLimitSettings.hasClientLimitSetting,
                minClientLimit: overdraftLimitSettings.minLimit,
                repaymentResult: overdraftLimitSettings.repaymentResult,
                overdraftLimitRulesUrl: overdraftLimitSettings.overdraftLimitRulesUrl,
            }),
        );
    } catch (error) {
        yield put(
            getOverdraftLimitSettingsError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
