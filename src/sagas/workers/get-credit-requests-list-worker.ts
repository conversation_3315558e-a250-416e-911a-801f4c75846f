import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentOrganizationMainMenuSelector } from '#/src/ducks/credit-products-main-menu/selectors';
import {
    getCreditRequestsListError,
    getCreditRequestsListFinish,
    type getCreditRequestsListStart,
} from '#/src/ducks/credit-requests/actions';
import { organizationsListSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getCreditRequestsListWorker({
    organizationId,
}: ReturnType<typeof getCreditRequestsListStart>) {
    try {
        const currentOrganizationMainMenu: ReturnType<typeof currentOrganizationMainMenuSelector> =
            yield select(currentOrganizationMainMenuSelector);
        const organizationsList: ReturnType<typeof organizationsListSelector> =
            yield select(organizationsListSelector);
        const organization = organizationId
            ? organizationsList.find((value) => value.eqId === organizationId)
            : currentOrganizationMainMenu;

        const response: ThenArg<typeof fetchers.corpCreditRequestRest.getCreditRequests> =
            yield call(fetchers.corpCreditRequestRest.getCreditRequests, {
                headers: { [HEADERS.OPENAPI_COMPANY_ID]: organization?.eqId },
                query: {
                    organizationId: organization?.eqId ?? '',
                    inn: organization?.inn ?? '',
                    ogrn: organization?.ogrn,
                    kpp: organization?.kppList[0],
                },
            });

        yield put(
            getCreditRequestsListFinish({
                list: response.commonCreditRequestList ?? [],
            }),
        );
    } catch (error) {
        yield put(
            getCreditRequestsListError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
