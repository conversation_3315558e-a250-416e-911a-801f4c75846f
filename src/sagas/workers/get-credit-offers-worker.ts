import { call, put, select } from 'redux-saga/effects';
import {
    type CreditOfferFilter,
    RequestedOfferType,
    TotalOfferFilter,
} from 'thrift-services/services/credit_offers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';

import { getCreditOffersError, getCreditOffersFinish } from '../../ducks/credit-offers/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { mapCreditOffers } from '../../utils/credit-offers-mappers';
import { NoProductsError } from '../../utils/errors/no-products-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function* getCreditOffersWorker() {
    const organizationId: string = yield select(currentHeaderOrganizationEqIdSelector);

    const filter: CreditOfferFilter = {
        type: RequestedOfferType.ALL,
        totalOfferFilter: TotalOfferFilter.CHECK_CLIENT,
    };

    try {
        const response: ThenArg<typeof fetchers.getCreditOffers> = yield call(
            fetchers.getCreditOffers,
            { organizationId, filter },
        );

        if (!response.offers?.length) {
            throw new NoProductsError();
        }

        const mappedCreditOffers = mapCreditOffers(response.offers);

        yield put(getCreditOffersFinish(mappedCreditOffers, response.offers));
    } catch (error) {
        if (error instanceof NoProductsError) {
            yield put(getCreditOffersError(error, LOG_LEVEL.WARN));

            return;
        }

        yield put(
            getCreditOffersError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
