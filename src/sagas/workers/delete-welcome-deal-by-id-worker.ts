import { call, put, select } from 'redux-saga/effects';

import {
    deleteWelcomeDealByIdError,
    deleteWelcomeDealByIdFinish,
    type deleteWelcomeDealByIdStart,
} from '#/src/ducks/credit-requests/actions';
import { creditRequestsListSelector } from '#/src/ducks/credit-requests/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* deleteWelcomeDealByIdWorker({
    welcomeDealId,
}: ReturnType<typeof deleteWelcomeDealByIdStart>) {
    const creditRequestsList: ReturnType<typeof creditRequestsListSelector> = yield select(
        creditRequestsListSelector,
    );

    try {
        const response: ThenArg<typeof fetchers.creditRequest.deleteWelcomeDealById> = yield call(
            fetchers.creditRequest.deleteWelcomeDealById,
            {
                urlParams: { welcomeDealId },
            },
        );

        if (response.id) {
            const list = creditRequestsList.filter((item) => item.id !== response.id);

            yield put(deleteWelcomeDealByIdFinish({ list }));
        }
    } catch (error) {
        yield put(deleteWelcomeDealByIdError(new ServerResponseError(getErrorMessage(error))));
    }
}
