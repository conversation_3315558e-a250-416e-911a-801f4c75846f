import { all, call, put, select } from 'redux-saga/effects';
import {
    type AgreementFilterRequest,
    type CreditProduct,
    type CreditProductProjectionRequest,
} from 'corp-core-credit-products-api-typescript-services';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { ECreditProductsDealStatuses } from '#/src/constants/credit-products';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getCreditProductsError,
    getCreditProductsFinish,
    type GetCreditProductsFinishParam,
    type getCreditProductsStart,
} from '../../ducks/credit-products/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import {
    mapCreditCards,
    mapCreditLines,
    mapCredits,
    mapGuaranties,
    mapGuarantyLines,
    mapOverdrafts,
    mapServerCreditProducts,
    mapSopukLine,
    sortServerCreditProducts,
    type TCreditProductsReturnType,
} from '../../utils/credit-products-mappers';
import { NoProductsError } from '../../utils/errors/no-products-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { mapFromArrayToAssociativeArray } from '../../utils/object-helpers';
import { type ThenArg } from '../../utils/promise-helper';

function getCreditProductsWorkerState(state: ApplicationState) {
    return { organizationId: currentOrganizationEqIdSelector(state) };
}

export function* getCreditProductsWorker({
    docNumber,
    withFault,
    withDocs,
    withClosed,
    withPaymentStatus,
}: ReturnType<typeof getCreditProductsStart>) {
    const { organizationId }: ReturnType<typeof getCreditProductsWorkerState> = yield select(
        getCreditProductsWorkerState,
    );

    const filter: AgreementFilterRequest = {
        docNumber,
        statuses: withClosed
            ? [ECreditProductsDealStatuses.L]
            : [ECreditProductsDealStatuses.A, ECreditProductsDealStatuses.X],
    };

    const projection: CreditProductProjectionRequest = {
        withFault,
        withDocs,
        withClosed,
        withActions: true,
        withSuspensiveConditionsDeadlineStatus: false,
        withPaymentStatus,
    };

    try {
        const creditProducts: ThenArg<typeof fetchers.coreCreditProductsRestV2.getCreditProducts> =
            yield call(fetchers.coreCreditProductsRestV2.getCreditProducts, {
                body: {
                    projection,
                    filter,
                },
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
            });

        if (!creditProducts.length) {
            throw new NoProductsError();
        }

        const sortedCreditProducts: CreditProduct[] = yield call(
            sortServerCreditProducts,
            creditProducts,
        );

        const mappedCreditProducts: TCreditProductsReturnType = yield call(
            mapServerCreditProducts,
            sortedCreditProducts,
        );

        if (Array.isArray(mappedCreditProducts)) {
            throw new NoProductsError();
        }

        const {
            mappedOverdrafts,
            mappedCredits,
            mappedCreditCards,
            mappedCreditLines,
            mappedGuaranties,
            mappedGuarantyLines,
            allProductsIndexedByDocNumber,
            mappedSopukLine,
        }: GetCreditProductsFinishParam = yield all({
            mappedOverdrafts: call(
                mapFromArrayToAssociativeArray,
                yield call(mapOverdrafts, mappedCreditProducts?.overdrafts || []),
                'docNumber',
            ),
            mappedCreditCards: call(
                mapFromArrayToAssociativeArray,
                yield call(mapCreditCards, mappedCreditProducts?.creditCards || []),
                'docNumber',
            ),
            mappedCredits: call(
                mapFromArrayToAssociativeArray,
                yield call(mapCredits, mappedCreditProducts?.credits || []),
                'docNumber',
            ),
            mappedCreditLines: call(
                mapFromArrayToAssociativeArray,
                yield call(mapCreditLines, mappedCreditProducts?.creditLines || []),
                'docNumber',
            ),
            mappedSopukLine: call(
                mapFromArrayToAssociativeArray,
                yield call(mapSopukLine, mappedCreditProducts?.sopukLine || []),
                'docNumber',
            ),
            mappedGuaranties: call(
                mapFromArrayToAssociativeArray,
                yield call(mapGuaranties, mappedCreditProducts?.guaranties || []),
                'docNumber',
            ),
            mappedGuarantyLines: call(
                mapFromArrayToAssociativeArray,
                yield call(mapGuarantyLines, mappedCreditProducts?.guarantyLines || []),
                'docNumber',
            ),
            allProductsIndexedByDocNumber: call(
                mapFromArrayToAssociativeArray,
                creditProducts,
                'docNumber',
            ),
        });

        yield put(
            getCreditProductsFinish({
                mappedCredits,
                mappedOverdrafts,
                mappedCreditCards,
                mappedCreditLines,
                mappedSopukLine,
                mappedGuaranties,
                mappedGuarantyLines,
                allProductsIndexedByDocNumber,
                withClosed: !!withClosed,
            }),
        );
    } catch (error) {
        if (error instanceof NoProductsError) {
            yield put(getCreditProductsError(error, LOG_LEVEL.WARN));

            return;
        }

        yield put(
            getCreditProductsError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
