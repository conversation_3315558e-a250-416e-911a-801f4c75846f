import { call, put, select } from 'redux-saga/effects';

import {
    getOverdraftNetTurnoverError,
    getOverdraftNetTurnoverFinish,
    type getOverdraftNetTurnoverStart,
} from '#/src/ducks/credit-products/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getOverdraftNetTurnoverDataWorker({
    docNumber,
    accountNumber,
}: ReturnType<typeof getOverdraftNetTurnoverStart>) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );

    try {
        const netTurnover: ThenArg<typeof fetchers.getOverTurnV2> = yield call(
            fetchers.getOverTurnV2,
            {
                organizationId,
                docNumber,
                accountNumber,
            },
        );

        yield put(getOverdraftNetTurnoverFinish(docNumber, netTurnover));
    } catch (error) {
        yield put(
            getOverdraftNetTurnoverError(
                docNumber,
                new ServerResponseError(getErrorMessage(error)),
            ),
        );
    }
}
