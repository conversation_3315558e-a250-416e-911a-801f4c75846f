import { <PERSON>uff<PERSON> } from 'buffer';

import { call, put, select } from 'redux-saga/effects';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import {
    checkSignedDocumentsError,
    checkSignedDocumentsFinish,
    type checkSignedDocumentsStart,
} from '#/src/ducks/signed-documents/actions';
import { type SignedDocument } from '#/src/types/signed-document';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getSignedDocumentsWorker(action: ReturnType<typeof checkSignedDocumentsStart>) {
    const pinEq = yield select(currentOrganizationEqIdSelector);
    const { docs } = action;

    try {
        const documents: ThenArg<typeof fetchers.getProductDocumentsV2> = yield call(
            fetchers.getProductDocumentsV2,
            { pinEq, docs },
        );

        const mappedDocuments: SignedDocument[] = documents.map((document) => {
            const body = Buffer.from(document?.body ?? '').toJSON();

            return {
                ...document,
                body,
            };
        });

        yield put(checkSignedDocumentsFinish(mappedDocuments));
    } catch (error) {
        yield put(checkSignedDocumentsError(new ServerResponseError(getErrorMessage(error))));
    }
}
