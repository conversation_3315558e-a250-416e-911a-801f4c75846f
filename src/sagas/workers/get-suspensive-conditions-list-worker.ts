import { call, put, select } from 'redux-saga/effects';
import { DealType, Status } from 'corp-credit-products-api-typescript-services';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import {
    getSuspensiveConditionsError,
    getSuspensiveConditionsFinish,
    type getSuspensiveConditionsStart,
} from '#/src/ducks/suspensive-conditions/actions';
import { suspensiveConditionsSelector } from '#/src/ducks/suspensive-conditions/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getSuspensiveConditionsListWorker({
    dealId,
    dealType = DealType.All,
    status = Status.All,
    pageNumber,
    pageSize,
    docNumber,
}: ReturnType<typeof getSuspensiveConditionsStart>) {
    try {
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );
        const suspensiveConditions: ReturnType<typeof suspensiveConditionsSelector> = yield select(
            suspensiveConditionsSelector,
        );

        const response: ThenArg<typeof fetchers.creditProductsRestV2.getSuspensiveConditions> =
            yield call(fetchers.creditProductsRestV2.getSuspensiveConditions, {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
                query: {
                    customerId: organizationId,
                    dealId: Number(dealId),
                    dealType,
                    status,
                    pageNumber,
                    pageSize,
                    docNumber,
                },
            });

        const list = response?.suspensiveConditions || [];
        const pagesCount = response?.pagination?.totalPages || 0;
        const isVisible =
            status === Status.All && dealType === DealType.All
                ? !!pagesCount
                : suspensiveConditions.isVisible;

        yield put(
            getSuspensiveConditionsFinish({
                list,
                isVisible,
                pagesCount,
            }),
        );
    } catch (error) {
        yield put(
            getSuspensiveConditionsError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
