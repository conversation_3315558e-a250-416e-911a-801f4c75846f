import { put, select } from 'redux-saga/effects';
import { push } from 'connected-react-router';
import qs from 'qs';

import { ECreditOffersLandingLinks } from '#/src/constants/credit-offers';
import {
    ECreditOffersProductCodes,
    getIsCanRedirectToCreditForms,
} from '#/src/constants/credit-products';
import { PATHS } from '#/src/constants/routing';
import {
    type goCreditTrancheApp,
    type goLanding,
    type goToCreditFormsApp,
    type goToDashboardAccount,
    type goToPaymentsSchedulePage,
    type initExternalRedirect,
} from '#/src/ducks/app/actions';
import { type ApplicationState } from '#/src/ducks/application-state';
import {
    isOnlineSigningAvailableSelector,
    loanMBIdSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    goToCreditFormsParamsOfferSelector,
    goToCreditFormsParamsQuestionnaireCustomerSelector,
} from '#/src/ducks/common-selectors/redirect-selectors';
import {
    businessCreditCardIdSelector,
    expressOverdraftIdSelector,
} from '#/src/ducks/credit-offers/selectors';
import { type goCreditProductPage, type goTranchePage } from '#/src/ducks/credit-products/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { fetchers } from '#/src/utils/client-api';
import { sanitizeUrlParameters } from '#/src/utils/url-parameters';

export function routingWorkersState(state: ApplicationState) {
    return {
        linkToDashboard: state.settings.redirect.dashboard,
        linkToCreditTrancheApp: state.settings.redirect.creditTrancheApp,
        linkToCreditFormsApp: state.settings.redirect.creditFormsApp,
        linkToLandings: state.settings.redirect.landings,
        linkToOverformAgreement: state.settings.redirect.overformAgreementContextRoot,
        contextRoot: state.settings.contextRoot,
        expressOverdraftId: expressOverdraftIdSelector(state),
        businessCreditCardId: businessCreditCardIdSelector(state),
        currentOrganizationEqId: currentOrganizationEqIdSelector(state),
        currentHeaderOrganizationEqId: currentHeaderOrganizationEqIdSelector(state),
    };
}

export function* goCreditProductPageWorker(action: ReturnType<typeof goCreditProductPage>) {
    const { currentOrganizationEqId }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    yield put(
        push(
            `${action.path}${qs.stringify(
                {
                    docNumber: action.docNumber,
                    customerId: action.customerId ?? currentOrganizationEqId,
                    tab: action.tab,
                },
                { addQueryPrefix: true },
            )}`,
        ),
    );
}

export function* goTranchePageWorker(action: ReturnType<typeof goTranchePage>) {
    const { trancheNumber, docNumber, path } = action;

    const { currentOrganizationEqId }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    yield put(
        push(
            `${path}${qs.stringify(
                {
                    docNumber,
                    trancheNumber,
                    customerId: action.customerId ?? currentOrganizationEqId,
                    tab: action.tab,
                },
                { addQueryPrefix: true },
            )}`,
        ),
    );
}

export function* goToHomeWorker() {
    yield put(push(PATHS.MAIN_PAGE));
}

export function* goToCreditOffersWorker() {
    yield put(push(PATHS.CREDIT_OFFERS));
}

export function* goToPaymentsScheduleWorker({
    path,
    docNumber,
    trancheNumber,
}: ReturnType<typeof goToPaymentsSchedulePage>) {
    yield put(
        push(`${path}${qs.stringify({ docNumber, trancheNumber }, { addQueryPrefix: true })}`),
    );
}

export function* goToAttachDocumentsPageWorker() {
    yield put(push(PATHS.ATTACH_DOCUMENTS));
}

const SESSION_STORAGE_QUESTIONNAIRE_KEY = 'questionnaireCustomer';
const SESSION_STORAGE_OFFER_KEY = 'offer';

export function* goToCreditFormsAppWorker({
    offerType,
    organizationId,
    id,
}: ReturnType<typeof goToCreditFormsApp>) {
    const {
        linkToCreditFormsApp,
        currentHeaderOrganizationEqId,
    }: ReturnType<typeof routingWorkersState> = yield select(routingWorkersState);

    if (
        organizationId === currentHeaderOrganizationEqId &&
        !!offerType &&
        getIsCanRedirectToCreditForms(offerType)
    ) {
        try {
            const goToCreditFormsParamsQuestionnaireCustomer: ReturnType<
                typeof goToCreditFormsParamsQuestionnaireCustomerSelector
            > = yield select(goToCreditFormsParamsQuestionnaireCustomerSelector, offerType);
            const goToCreditFormsParamsOffer: ReturnType<
                typeof goToCreditFormsParamsOfferSelector
            > = yield select(goToCreditFormsParamsOfferSelector, offerType);

            sessionStorage.setItem(
                SESSION_STORAGE_QUESTIONNAIRE_KEY,
                JSON.stringify(goToCreditFormsParamsQuestionnaireCustomer),
            );
            sessionStorage.setItem(
                SESSION_STORAGE_OFFER_KEY,
                JSON.stringify(goToCreditFormsParamsOffer),
            );
        } catch (error) {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            fetchers.clientErrorLog(error);
        }
    }

    const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);
    const isOnlineSigningAvailable: ReturnType<typeof isOnlineSigningAvailableSelector> =
        yield select(isOnlineSigningAvailableSelector);

    window.location.assign(
        `${linkToCreditFormsApp}${qs.stringify(
            {
                ...(!!organizationId && !!offerType
                    ? { productCode: ECreditOffersProductCodes[offerType] }
                    : {}),
                ...((isOnlineSigningAvailable && !!loanMBId) || !!id
                    ? { loanMBId: id ?? loanMBId }
                    : {}),
            },
            { addQueryPrefix: true },
        )}`,
    );
}

export function* goToDashboardWorker() {
    const { linkToDashboard }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    window.location.assign(`${linkToDashboard}`);
}

export function* goToDashboardAccountWorker(action: ReturnType<typeof goToDashboardAccount>) {
    const { accountNumber } = action;
    const { linkToDashboard }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    window.location.assign(`${linkToDashboard}/account/${accountNumber}`);
}

export function* goCreditTrancheAppWorker({ docNumber }: ReturnType<typeof goCreditTrancheApp>) {
    const { linkToCreditTrancheApp }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    window.location.assign(
        `${linkToCreditTrancheApp}${qs.stringify(
            {
                docNumber,
            },
            { addQueryPrefix: true },
        )}`,
    );
}

const SESSION_STORAGE_CAMPAIGN_KEY = 'acceptedCampaign';

export function* goLandingWorker({ landingType, campaignCode }: ReturnType<typeof goLanding>) {
    const { linkToLandings }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    if (campaignCode) {
        try {
            sessionStorage.setItem(SESSION_STORAGE_CAMPAIGN_KEY, JSON.stringify(campaignCode));
        } catch (e) {
            // ignore it
        }
    }

    window.location.assign(`${linkToLandings}/${[ECreditOffersLandingLinks[landingType]]}`);
}

export function* goToOveformAgreement() {
    const { linkToOverformAgreement, expressOverdraftId }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    window.location.assign(
        `${linkToOverformAgreement}/${qs.stringify(
            {
                variantId: expressOverdraftId,
            },
            { addQueryPrefix: true },
        )}`,
    );
}

export function* goToBusinessCreditCard() {
    const {
        linkToOverformAgreement,
        businessCreditCardId,
    }: ReturnType<typeof routingWorkersState> = yield select(routingWorkersState);

    window.location.assign(
        `${linkToOverformAgreement}/business-credit-card/${qs.stringify(
            {
                variantId: businessCreditCardId,
            },
            { addQueryPrefix: true },
        )}`,
    );
}

export function* initExternalRedirectWorker({
    link,
    withOrganizationId,
    addContextRoot,
    parameters,
}: ReturnType<typeof initExternalRedirect>) {
    const { contextRoot, currentOrganizationEqId }: ReturnType<typeof routingWorkersState> =
        yield select(routingWorkersState);

    let path = `${addContextRoot ? contextRoot : ''}${link}`;

    const additionalParameters =
        withOrganizationId && !!currentOrganizationEqId
            ? { customerId: currentOrganizationEqId, ...parameters }
            : parameters;

    const sanitizedParameters = sanitizeUrlParameters(additionalParameters);

    path += qs.stringify(sanitizedParameters, {
        addQueryPrefix: true,
    });

    window.location.assign(path);
}
