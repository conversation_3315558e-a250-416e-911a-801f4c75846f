import { call, put, select } from 'redux-saga/effects';
import { type RunDecisionMakerCheckResponse } from 'corp-role-model-mks-permissions-api-typescript-services';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    runDecisionMakerCheckError,
    runDecisionMakerCheckFinish,
} from '#/src/ducks/mks-permissions/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';

export function* runDecisionMakerCheckWorker() {
    try {
        const organizationId: string = yield select(currentOrganizationEqIdSelector);

        const fetchResponse: RunDecisionMakerCheckResponse = yield call(
            fetchers.mksPermissions.runDecisionMakerCheck,
            {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
            },
        );

        yield put(runDecisionMakerCheckFinish(fetchResponse));
    } catch (error) {
        yield put(runDecisionMakerCheckError(new ServerResponseError(getErrorMessage(error))));
    }
}
