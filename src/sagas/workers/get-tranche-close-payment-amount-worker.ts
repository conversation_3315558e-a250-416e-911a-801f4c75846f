import { call, put } from 'redux-saga/effects';

import {
    getTrancheClosePaymentAmountError,
    getTrancheClosePaymentAmountFinish,
    type getTrancheClosePaymentAmountStart,
} from '#/src/ducks/credit-products/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getTrancheClosePaymentAmountWorker({
    docNumber,
    organizationId,
    projection,
}: ReturnType<typeof getTrancheClosePaymentAmountStart>) {
    try {
        const response: ThenArg<typeof fetchers.getClosePaymentAmountV2> = yield call(
            fetchers.getClosePaymentAmountV2,
            {
                docNumber,
                organizationId,
                projection,
            },
        );

        yield put(getTrancheClosePaymentAmountFinish({ [docNumber]: response }));
    } catch (error) {
        yield put(
            getTrancheClosePaymentAmountError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
        yield put(getTrancheClosePaymentAmountFinish({}));
    }
}
