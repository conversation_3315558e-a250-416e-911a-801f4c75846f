import { call, put } from 'redux-saga/effects';
import { TRequestedPaymentsTypeV2 } from 'thrift-services/services/credit_products_v2';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import {
    getPayedPaymentScheduleError,
    getPayedPaymentScheduleFinish,
} from '../../ducks/payment-schedule/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

export function* getPayedPaymentsScheduleWorker({
    organizationId,
    docNumber,
}: {
    organizationId: string | undefined;
    docNumber: string;
}) {
    try {
        const payedCreditPaymentsSchedule: ThenArg<typeof fetchers.getPaymentScheduleV2> =
            yield call(fetchers.getPaymentScheduleV2, {
                organizationId,
                filter: {
                    docNumber,
                    paymentsType: TRequestedPaymentsTypeV2.PAYED_ONLY,
                },
            });

        yield put(getPayedPaymentScheduleFinish(payedCreditPaymentsSchedule));
    } catch (error) {
        yield put(
            getPayedPaymentScheduleError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
