import { call, put, select } from 'redux-saga/effects';

import {
    getActiveCreditCaseError,
    getActiveCreditCaseFinish,
    type getActiveCreditCaseStart,
} from '#/src/ducks/attach-documents/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getActiveCreditCaseWorker({
    organizationId,
}: ReturnType<typeof getActiveCreditCaseStart>) {
    try {
        const currentHeaderOrganizationEqId: ReturnType<
            typeof currentHeaderOrganizationEqIdSelector
        > = yield select(currentHeaderOrganizationEqIdSelector);

        const response: ThenArg<typeof fetchers.getActiveCreditCase> = yield call(
            fetchers.getActiveCreditCase,
            {
                organizationId: organizationId ?? currentHeaderOrganizationEqId,
            },
        );

        yield put(getActiveCreditCaseFinish(response));
    } catch (error) {
        yield put(
            getActiveCreditCaseError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
