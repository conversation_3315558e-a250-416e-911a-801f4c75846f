import { call, put, select } from 'redux-saga/effects';

import { loanMBIdSelector } from '#/src/ducks/attach-documents/selectors';
import {
    getDocumentError,
    getDocumentFinish,
    type getDocumentStart,
} from '#/src/ducks/documents/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { downloadBinaryFileByExtension } from '#/src/utils/binary-download';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getDocumentWorker({
    documentType,
    documentName,
    participantCode,
}: ReturnType<typeof getDocumentStart>) {
    const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
        currentOrganizationEqIdSelector,
    );
    const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);

    try {
        const fileAsBlob: ThenArg<typeof fetchers.getDocument> = yield call(fetchers.getDocument, {
            documentType,
            participantCode,
            organizationId,
            loanMBId: loanMBId || 'loanMBId',
        });

        downloadBinaryFileByExtension(fileAsBlob, documentName, 'pdf');

        yield put(getDocumentFinish(documentType, participantCode));
    } catch (error) {
        yield put(getDocumentError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}
