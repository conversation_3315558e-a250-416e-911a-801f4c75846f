import { call, put, select, take } from 'redux-saga/effects';
import formatDate from 'date-fns/format';

import { DATE_FOR_SERVICES } from '#/src/constants/date';
import { earlyRepaymentProcessingTypeSelector } from '#/src/ducks/credit-processing/selectors';
import { CREDIT_PROCESSING_TYPES } from '#/src/ducks/credit-processing/types';
import {
    getDisallowedDatesForLoanRepaymentError,
    getDisallowedDatesForLoanRepaymentFinish,
    type getDisallowedDatesForLoanRepaymentStart,
} from '#/src/ducks/credit-products/actions';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getDisallowedDatesForLoanRepaymentWorker(
    action: ReturnType<typeof getDisallowedDatesForLoanRepaymentStart>,
) {
    try {
        const { endDate } = action;
        const currentTime: ReturnType<typeof currentTimeSelector> =
            yield select(currentTimeSelector);
        let earlyRepaymentProcessingType: ReturnType<typeof earlyRepaymentProcessingTypeSelector> =
            yield select(earlyRepaymentProcessingTypeSelector);

        if (!earlyRepaymentProcessingType) {
            yield take([
                CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_FINISH,
                CREDIT_PROCESSING_TYPES.GET_EARLY_REPAYMENT_PROCESSING_TYPE_ERROR,
            ]);
            earlyRepaymentProcessingType = yield select(earlyRepaymentProcessingTypeSelector);
        }

        const dates: ThenArg<typeof fetchers.getNonBusinessDays> = yield call(
            fetchers.getNonBusinessDays,
            {
                filter: {
                    currencyCodes: ['RUR'],
                    period: {
                        fromDate: formatDate(currentTime, DATE_FOR_SERVICES),
                        toDate: endDate,
                    },
                },
            },
        );

        yield put(
            getDisallowedDatesForLoanRepaymentFinish(
                dates
                    .filter((item) => !!item?.date?.seconds)
                    .map((item) => item?.date?.seconds || 0),
            ),
        );
    } catch (error) {
        yield put(
            getDisallowedDatesForLoanRepaymentError(
                new ServerResponseError(getErrorMessage(error)),
            ),
        );
    }
}
