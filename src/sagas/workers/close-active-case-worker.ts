import { call, put, select } from 'redux-saga/effects';

import { unexpectedAppError } from '#/src/ducks/app/actions';
import { loanMBIdSelector } from '#/src/ducks/attach-documents/selectors';
import {
    closeActiveCaseError,
    closeActiveCaseSuccess,
} from '#/src/ducks/credit-processing/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

const REJECTION_CLIENT = '3';

export function* closeActiveCaseWorker() {
    try {
        const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);
        const organizationId: ReturnType<typeof currentOrganizationEqIdSelector> = yield select(
            currentOrganizationEqIdSelector,
        );

        const response: ThenArg<typeof fetchers.closeCase> = yield call(fetchers.closeCase, {
            organizationId,
            application: {
                id: loanMBId,
                closeReasonId: REJECTION_CLIENT,
            },
        });

        if (response.loanMBId) {
            yield put(closeActiveCaseSuccess());
        }
    } catch (error) {
        yield put(unexpectedAppError());
        yield put(closeActiveCaseError(new ServerResponseError(), LOG_LEVEL.ERROR));
    }
}
