import { call, put, select } from 'redux-saga/effects';

import { LOANMB_ERRORS } from '#/src/constants/loanmb-errors';
import { PlatformId } from '#/src/constants/platform-ids';
import { goToCreditFormsApp, unexpectedAppError } from '#/src/ducks/app/actions';
import { loanMBIdSelector, platformIdSelector } from '#/src/ducks/attach-documents/selectors';
import {
    type moveCaseToStage,
    setMangerWorksWithAppModalVisible,
} from '#/src/ducks/credit-processing/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { checkErrorCode } from '#/src/utils/check-loanmb-error';
import { fetchers } from '#/src/utils/client-api';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* moveCaseToStageWorker({
    actionType,
    organizationId,
}: ReturnType<typeof moveCaseToStage>) {
    try {
        const platformId: ReturnType<typeof platformIdSelector> = yield select(platformIdSelector);

        if (platformId !== PlatformId.sfa) {
            yield put(goToCreditFormsApp({ organizationId }));

            return;
        }

        const currentOrganizationId: string = yield select(currentHeaderOrganizationEqIdSelector);
        const loanMBId: ReturnType<typeof loanMBIdSelector> = yield select(loanMBIdSelector);

        const response: ThenArg<typeof fetchers.moveCaseToStage> = yield call(
            fetchers.moveCaseToStage,
            {
                organizationId: organizationId ?? currentOrganizationId,
                loanMBId,
                actionType,
            },
        );

        const errorList = response?.errorList;

        const managerWorksWithApplication =
            errorList?.length &&
            checkErrorCode(errorList, LOANMB_ERRORS.MANAGER_WORKS_WITH_APPLICATION);

        if (managerWorksWithApplication) {
            yield put(setMangerWorksWithAppModalVisible(true));

            return;
        }
        if (errorList?.length) {
            throw new Error('moveCaseToStage return error');
        } else {
            yield put(goToCreditFormsApp({ organizationId }));
        }
    } catch (error) {
        yield put(unexpectedAppError());

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        fetchers.clientErrorLog(error);
    }
}
