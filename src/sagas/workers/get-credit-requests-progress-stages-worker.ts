import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getProgressStagesError,
    getProgressStagesFinish,
    type getProgressStagesStart,
} from '#/src/ducks/credit-requests/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

export function* getProgressStagesWorker({ requestId }: ReturnType<typeof getProgressStagesStart>) {
    try {
        const customerId: string = yield select(currentHeaderOrganizationEqIdSelector);

        const response: ThenArg<typeof fetchers.statusModel.getApplicationProgressStages> =
            yield call(fetchers.statusModel.getApplicationProgressStages, {
                headers: { [HEADERS.OPENAPI_COMPANY_ID]: customerId },
                urlParams: { id: requestId },
            });

        yield put(getProgressStagesFinish(response));
    } catch (error) {
        yield put(
            getProgressStagesError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
