import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    getStatementRequestsError,
    getStatementRequestsFinish,
    type getStatementRequestsStart,
} from '#/src/ducks/statement-requests/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';
import { type ThenArg } from '../../utils/promise-helper';

export function* getStatementRequestsWorker({
    pageNumber,
    pageSize,
    docNumber,
}: ReturnType<typeof getStatementRequestsStart>) {
    try {
        const organizationId: string = yield select(currentHeaderOrganizationEqIdSelector);

        const response: ThenArg<typeof fetchers.corpLoanStatements.getStatementRequests> =
            yield call(fetchers.corpLoanStatements.getStatementRequests, {
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
                query: {
                    customerId: organizationId,
                    pageNumber,
                    pageSize,
                    docNumber,
                },
            });

        const list = response.statementRequests || [];
        const pagesCount = response?.pagination?.totalPages || 0;
        const newStatementsCount = response?.newStatementsCount || 0;

        yield put(
            getStatementRequestsFinish({
                list,
                pagesCount,
                newStatementsCount,
            }),
        );
    } catch (error) {
        yield put(
            getStatementRequestsError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
