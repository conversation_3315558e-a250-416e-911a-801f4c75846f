import { call, put, select } from 'redux-saga/effects';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    type getGuarantyPaymentSchedule,
    getPaymentScheduleError,
    getPaymentScheduleFinish,
} from '../../ducks/payment-schedule/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

function getPaymentScheduleWorkerState(state: ApplicationState) {
    return { organizationId: currentOrganizationEqIdSelector(state) };
}

export function* getGuarantyPaymentScheduleWorker({
    docNumber,
}: ReturnType<typeof getGuarantyPaymentSchedule>) {
    const { organizationId }: ReturnType<typeof getPaymentScheduleWorkerState> = yield select(
        getPaymentScheduleWorkerState,
    );

    try {
        const guarantyPayments: ThenArg<typeof fetchers.getPaymentScheduleV2> = yield call(
            fetchers.getGuarantyPaymentScheduleV2,
            {
                organizationId,
                docNumber,
            },
        );

        yield put(getPaymentScheduleFinish(guarantyPayments));
    } catch (error) {
        yield put(
            getPaymentScheduleError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
