import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import {
    updateStatementRequestsViewError,
    updateStatementRequestsViewFinish,
    type updateStatementRequestsViewStart,
} from '#/src/ducks/statement-requests/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import { fetchers } from '../../utils/client-api';

export function* updateStatementRequestsViewWorker({
    docNumber,
}: ReturnType<typeof updateStatementRequestsViewStart>) {
    try {
        const organizationId: string = yield select(currentHeaderOrganizationEqIdSelector);

        yield call(fetchers.corpLoanStatements.updateStatementRequestsView, {
            headers: {
                [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
            },
            query: {
                customerId: organizationId,
                docNumber,
            },
        });

        yield put(updateStatementRequestsViewFinish({ newStatementsCount: 0 }));
    } catch (error) {
        yield put(
            updateStatementRequestsViewError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
