import { call, put, select } from 'redux-saga/effects';
import {
    type AgreementFilterRequest,
    type CreditProduct,
    type CreditProductProjectionRequest,
} from 'corp-core-credit-products-api-typescript-services';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorCode, getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getSelectedCreditProductError,
    getSelectedCreditProductFinish,
    type getSelectedCreditProductStart,
} from '../../ducks/credit-products/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import {
    mapCreditLines,
    mapCredits,
    mapGuaranties,
    mapGuarantyLines,
    mapOverdrafts,
    mapServerCreditProducts,
    mapSopukLine,
    type TCreditProductsReturnType,
} from '../../utils/credit-products-mappers';
import { NoProductsError } from '../../utils/errors/no-products-error';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { type ThenArg } from '../../utils/promise-helper';

function getCreditProductsWorkerState(state: ApplicationState) {
    return { organizationId: currentOrganizationEqIdSelector(state) };
}

const getMappedProduct = (product: CreditProduct) => {
    const defaultActiveProduct = {
        overdraft: null,
        credit: null,
        creditLine: null,
        sopukLine: null,
        guarantie: null,
        guarantyLine: null,
    };

    switch (product?.requisites?.product) {
        case ECreditProductsCodes.BUSINESS_CREDIT:
            return { ...defaultActiveProduct, credit: mapCredits([product])[0] };
        case ECreditProductsCodes.OVERDRAFT:
            return { ...defaultActiveProduct, overdraft: mapOverdrafts([product])[0] };
        case ECreditProductsCodes.CREDIT_LINE:
            return { ...defaultActiveProduct, creditLine: mapCreditLines([product])[0] };
        case ECreditProductsCodes.GUARANTY:
            return { ...defaultActiveProduct, guarantie: mapGuaranties([product])[0] };
        case ECreditProductsCodes.GUARANTY_LINE:
            return { ...defaultActiveProduct, guarantyLine: mapGuarantyLines([product])[0] };
        case ECreditProductsCodes.SOPUK:
            return { ...defaultActiveProduct, sopukLine: mapSopukLine([product])[0] };
        default:
            return defaultActiveProduct;
    }
};

export function* getSelectedCreditProducts({
    docNumber,
}: ReturnType<typeof getSelectedCreditProductStart>) {
    const { organizationId }: ReturnType<typeof getCreditProductsWorkerState> = yield select(
        getCreditProductsWorkerState,
    );

    const filter: AgreementFilterRequest = {
        docNumber,
    };

    const projection: CreditProductProjectionRequest = {
        withFault: true,
        withDocs: true,
        withClosed: true,
        withActions: true,
        withSuspensiveConditionsDeadlineStatus: true,
    };

    try {
        const creditProducts: ThenArg<typeof fetchers.coreCreditProductsRestV2.getCreditProducts> =
            yield call(fetchers.coreCreditProductsRestV2.getCreditProducts, {
                body: {
                    projection,
                    filter,
                },
                headers: {
                    [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                },
            });

        if (!creditProducts.length) {
            throw new NoProductsError();
        }

        const mappedCreditProducts: TCreditProductsReturnType = yield call(
            mapServerCreditProducts,
            creditProducts,
        );

        if (Array.isArray(mappedCreditProducts)) {
            throw new NoProductsError();
        }

        const mappedProduct = getMappedProduct(creditProducts[0]);

        if (mappedProduct) {
            yield put(getSelectedCreditProductFinish(mappedProduct));
        }
    } catch (error) {
        if (error instanceof NoProductsError) {
            yield put(getSelectedCreditProductError(error, LOG_LEVEL.WARN));

            return;
        }

        yield put(
            getSelectedCreditProductError(
                new ServerResponseError(
                    getErrorCode(error) === 403
                        ? EErrorMessages.ACCESS_ERROR
                        : getErrorMessage(error),
                ),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
