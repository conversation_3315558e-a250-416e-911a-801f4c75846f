import { call, put, select } from 'redux-saga/effects';
import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';
import { type TAgreementFilter } from 'thrift-services/services/credit_products';
import { type TCreditProductProjectionV2 } from 'thrift-services/services/credit_products_v2';

import { ECreditProductsDealStatuses } from '#/src/constants/credit-products';
import { type PageFilterState } from '#/src/ducks/credit-products/reducer/tranches-info/page-filter';
import { parentDocNumberSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { type ApplicationState } from '../../ducks/application-state';
import {
    getTrancheCreditProductsError,
    getTrancheCreditProductsFinish,
    type getTrancheCreditProductsStart,
} from '../../ducks/credit-products/actions';
import { LOG_LEVEL } from '../../types/logger';
import { fetchers } from '../../utils/client-api';
import { mapTranches } from '../../utils/credit-products-mappers';
import { ServerResponseError } from '../../utils/errors/server-response-error';
import { mapFromArrayToAssociativeArray } from '../../utils/object-helpers';

export enum ETrancheTypes {
    tranche = 'tranche',
    deal = 'deal',
}

export function getTrancheCreditProductsWorkerState(state: ApplicationState): {
    organizationId: string;
    docNumber: string;
    pageFilter: PageFilterState;
    filter?: TAgreementFilter;
    tranchesTotalCount: number;
    perPage: number;
    projection: TCreditProductProjectionV2;
} {
    return {
        organizationId: currentOrganizationEqIdSelector(state),
        docNumber: parentDocNumberSelector(state),
        pageFilter: state.creditProducts.tranchesInfo.pageFilter,
        tranchesTotalCount: state.creditProducts.tranchesInfo.totalCount,
        perPage: state.settings.tranchesPerPage,
        projection: {
            withFault: true,
            withDocs: true,
            withSuspensiveConditionsDeadlineStatus: true,
            withActions: true,
        },
    };
}

type TWorkerParams = ReturnType<typeof getTrancheCreditProductsStart>;

export function* getTrancheCreditProductsWorker({
    docNumber,
    trancheType,
    isFromFirstPage = true,
    all = false,
    isClosed = false,
    isAll = false,
}: TWorkerParams) {
    const workerState: ReturnType<typeof getTrancheCreditProductsWorkerState> = yield select(
        getTrancheCreditProductsWorkerState,
    );

    try {
        const trancheMethod =
            trancheType === ETrancheTypes.tranche
                ? fetchers.getTrancheCreditProductsV2
                : fetchers.getDealCreditProductsV2;

        if (isFromFirstPage) {
            workerState.pageFilter.pageNumber = 0;
        }

        if (isClosed || isAll) {
            workerState.projection.withClosed = true;
        }

        if (isAll) {
            workerState.filter = {
                statuses: [
                    ECreditProductsDealStatuses.A,
                    ECreditProductsDealStatuses.X,
                    ECreditProductsDealStatuses.L,
                ],
            };
        } else if (isClosed) {
            workerState.filter = { statuses: [ECreditProductsDealStatuses.L] };
        } else {
            workerState.filter = {
                statuses: [ECreditProductsDealStatuses.A, ECreditProductsDealStatuses.X],
            };
        }

        workerState.pageFilter.perPage = all ? workerState.tranchesTotalCount : workerState.perPage;

        // TODO: вернуть ThenArg<typeof trancheMethod> после перехода методов с трифтов на рест
        const trancheCreditProducts: CreditProduct[] = yield call(trancheMethod, {
            organizationId: workerState.organizationId,
            docNumber: docNumber || workerState.docNumber,
            pageFilter: workerState.pageFilter,
            projection: workerState.projection,
            filter: workerState.filter,
        });

        const receivedTranchesCount = trancheCreditProducts.length;

        const tranchesIndexedByDocNumber = mapFromArrayToAssociativeArray(
            trancheCreditProducts,
            'docNumber',
        );

        const mappedTranches = mapTranches(trancheCreditProducts, trancheType);

        const mappedTrancheCreditProducts = mapFromArrayToAssociativeArray(
            mappedTranches,
            'docNumber',
        );

        yield put(
            getTrancheCreditProductsFinish({
                mappedTranches: mappedTrancheCreditProducts,
                pageFilter: workerState.pageFilter,
                receivedTranchesCount,
                tranchesIndexedByDocNumber,
                isClosedCreditProductsTranches: isClosed,
                isAllCreditProductsTranches: isAll,
                isAllReceivedCreditProductsTranches: receivedTranchesCount === 0 || all,
            }),
        );
    } catch (error) {
        yield put(
            getTrancheCreditProductsError({
                error: new ServerResponseError(getErrorMessage(error)),
                logLevel: LOG_LEVEL.ERROR,
                isClosedCreditProductsTranches: isClosed,
                isAllCreditProductsTranches: isAll,
            }),
        );
    }
}
