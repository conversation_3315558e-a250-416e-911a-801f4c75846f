import { call, put, select } from 'redux-saga/effects';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import {
    createStatementRequestError,
    createStatementRequestFinish,
    type createStatementRequestStart,
} from '#/src/ducks/statement-requests/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';

import {
    currentOrganizationEqIdSelector,
    getCurrentOrganizationShortNameSelector,
} from '../../ducks/organization/selectors';
import { fetchers } from '../../utils/client-api';

export function* createStatementRequestWorker({
    docNumber,
    fromDate,
    toDate,
    format,
    selectedTranches,
    withSignature,
    organizationId,
}: ReturnType<typeof createStatementRequestStart>) {
    try {
        const currentOrganizationId: ReturnType<typeof currentOrganizationEqIdSelector> =
            yield select(currentOrganizationEqIdSelector);

        const currentOrganizationShortName: ReturnType<
            typeof getCurrentOrganizationShortNameSelector
        > = yield select(getCurrentOrganizationShortNameSelector);

        yield call(fetchers.corpLoanStatements.createStatementRequest, {
            headers: {
                [HEADERS.OPENAPI_COMPANY_ID]: organizationId ?? currentOrganizationId,
            },
            body: {
                customer: {
                    id: organizationId ?? currentOrganizationId,
                    name: currentOrganizationShortName,
                },
                docNumber,
                filter: {
                    period: {
                        fromDate,
                        toDate,
                    },
                    subsetProductsInfo:
                        selectedTranches.length > 0
                            ? {
                                  numbers: selectedTranches,
                              }
                            : undefined,
                },
                format,
                withSignature: !!withSignature,
            },
        });

        yield put(createStatementRequestFinish());
    } catch (error) {
        yield put(
            createStatementRequestError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
