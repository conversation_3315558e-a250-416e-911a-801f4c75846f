import { call, put, select } from 'redux-saga/effects';

import { getErrorMessage } from '#/src/utils/errors/get-error-message';

import { currentOrganizationEqIdSelector } from '../../ducks/organization/selectors';
import {
    getPaymentScheduleError,
    getPaymentScheduleFinish,
    type getPaymentScheduleStart,
} from '../../ducks/payment-schedule/actions';
import { LOG_LEVEL } from '../../types/logger';
import { ServerResponseError } from '../../utils/errors/server-response-error';

import { getFuturePaymentsScheduleWorker } from './get-future-payments-schedule-worker';
import { getPayedPaymentsScheduleWorker } from './get-payed-payments-schedule-worker';

export function* getPaymentScheduleWorker({
    docNumber,
}: ReturnType<typeof getPaymentScheduleStart>) {
    const organizationId: string = yield select(currentOrganizationEqIdSelector);

    try {
        yield call(getPayedPaymentsScheduleWorker, { docNumber, organizationId });
        yield call(getFuturePaymentsScheduleWorker, { docNumber, organizationId });

        yield put(getPaymentScheduleFinish());
    } catch (error) {
        yield put(
            getPaymentScheduleError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
