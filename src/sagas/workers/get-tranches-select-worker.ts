import { call, put } from 'redux-saga/effects';
import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';
import * as HEADERS from 'corporate-services/lib/constants/headers';

import { getByAccountNumbersFinish } from '#/src/ducks/accounts/actions';
import {
    getTranchesSelectError,
    getTranchesSelectFinish,
    type getTranchesSelectStart,
} from '#/src/ducks/credit-products/tranches-select/actions';
import { LOG_LEVEL } from '#/src/types/logger';
import { fetchers } from '#/src/utils/client-api';
import { mapTranches } from '#/src/utils/credit-products-mappers';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { ServerResponseError } from '#/src/utils/errors/server-response-error';
import { type ThenArg } from '#/src/utils/promise-helper';

import { ETrancheTypes } from './get-tranche-credit-products-worker';

export function* getTranchesSelectWorker({
    organizationId,
    docNumber,
    trancheType,
}: ReturnType<typeof getTranchesSelectStart>) {
    try {
        const isTranche = trancheType === ETrancheTypes.tranche;

        const trancheMethod = isTranche
            ? fetchers.getTrancheCreditProductsV2
            : fetchers.getDealCreditProductsV2;

        const trancheCountMethod = isTranche
            ? fetchers.getTrancheCreditProductsCountV2
            : fetchers.getDealCreditProductsCountV2;

        const tranchesCount: ThenArg<typeof trancheCountMethod> = yield call(trancheCountMethod, {
            organizationId,
            docNumber,
            withClosed: true,
        });

        // TODO: вернуть ThenArg<typeof trancheMethod> после перехода методов с трифтов на рест
        const tranches: CreditProduct[] = yield call(trancheMethod, {
            organizationId,
            docNumber,
            pageFilter: {
                pageNumber: 0,
                perPage: tranchesCount,
            },
            projection: {
                withFault: false,
                withDocs: true,
                withClosed: true,
            },
        });

        const mappedTranches = mapTranches(tranches, trancheType);

        if (mappedTranches?.length) {
            const accountNumbers = Array.from(
                new Set(
                    mappedTranches
                        .map((tranche) => tranche.servicingAccounts ?? [])
                        .flat()
                        .map((account) => account?.number ?? ''),
                ),
            );

            if (accountNumbers?.length) {
                const accounts: ThenArg<typeof fetchers.mksAccounts.getByAccountNumbers> =
                    yield call(fetchers.mksAccounts.getByAccountNumbers, {
                        headers: {
                            [HEADERS.OPENAPI_COMPANY_ID]: organizationId,
                        },
                        body: {
                            accountNumbers,
                            getBalance: true,
                            operationType: 'SERV',
                        },
                    });

                // Получаем транши с доступом к счету по которым возможно сформировать выписку
                const filteredTranches = mappedTranches.filter(
                    (tranche) =>
                        !!accounts.find(
                            (account) =>
                                account?.mainInfo?.number ===
                                tranche?.servicingAccounts?.[0]?.number,
                        )?.mainInfo?.openDate,
                );

                yield put(getByAccountNumbersFinish(accounts));
                yield put(getTranchesSelectFinish(filteredTranches));
            } else {
                yield put(getByAccountNumbersFinish([]));
                yield put(getTranchesSelectFinish(mappedTranches));
            }
        } else {
            yield put(getTranchesSelectFinish(mappedTranches));
        }
    } catch (error) {
        yield put(
            getTranchesSelectError(
                new ServerResponseError(getErrorMessage(error)),
                LOG_LEVEL.ERROR,
            ),
        );
    }
}
