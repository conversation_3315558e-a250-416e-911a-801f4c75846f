/* eslint-disable @typescript-eslint/no-explicit-any */
import { type Action } from 'redux';
import <PERSON><PERSON> from 'js-cookie';

import { type Dispatch } from 'arui-private/types';

import { CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG } from '../components/change-organization-modal/change-organization-modal';
import { APP_TYPES } from '../ducks/app/types';
import { type ApplicationState } from '../ducks/application-state';
import { setChangeOrganizationModalVisible } from '../ducks/organization/actions';
import { ORGANIZATION_TYPES } from '../ducks/organization/types';

const EXTERNAL_REDIRECT_ACTION_TYPES = [
    APP_TYPES.GO_TRANCHE_APP,
    APP_TYPES.GO_TO_DASHBOARD_ACCOUNT,
    APP_TYPES.GO_TO_DASHBOARD,
    APP_TYPES.GO_TO_CREDIT_FORMS_APP,
    APP_TYPES.GO_LANDING,
    APP_TYPES.GO_TO_BUSINESS_CREDIT_CARD,
    APP_TYPES.GO_TO_OVERFORM_AGREEMENT,
    APP_TYPES.INIT_EXTERNAL_REDIRECT,
];

type TAction = Action & {
    organizationId?: string;
    [CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG]?: boolean;
};

/**
 * Мидлвара для хранения выбранной организации в куках.
 * Для корректной работы необходимо продиспатчить экшн такого вида:
 *
 * {
 *     type: 'CHANGE_ORGANIZATION',
 *     organizationId: 'XXXXXX'
 * }
 *
 * Так же реагирует на редирект-экшены в другие сервисы,
 * чтобы отображать модалку для изменения текущей организации
 *
 * @returns {Object} State
 */

export default ({ getState }: { getState: () => ApplicationState }) =>
    (next: Dispatch) =>
    (action: TAction) => {
        if (
            (action.type === ORGANIZATION_TYPES.CHANGE_ORGANIZATION ||
                !!action[CHANGE_ORGANIZATION_MODAL_CONTINUE_FLAG]) &&
            !!action.organizationId
        ) {
            Cookie.set('organizationId', action.organizationId);

            if (
                !!Cookie.get('holdingGroupId') &&
                action.type !== ORGANIZATION_TYPES.CHANGE_ORGANIZATION
            ) {
                Cookie.remove('holdingGroupId');
            }

            return next(action);
        }

        if (EXTERNAL_REDIRECT_ACTION_TYPES.includes(action.type)) {
            const state = getState();

            const targetOrganization = state.organization.list.find(
                (organization) => organization.eqId === action.organizationId,
            );

            if (
                (state.organization.current !== action.organizationId ||
                    !!Cookie.get('holdingGroupId')) &&
                !!targetOrganization
            ) {
                return next(
                    setChangeOrganizationModalVisible({
                        isVisible: true,
                        organizationId: targetOrganization.eqId,
                        organizationName: targetOrganization.organizationName.shortName,
                        action,
                    }),
                );
            }

            return next(action);
        }

        return next(action);
    };
