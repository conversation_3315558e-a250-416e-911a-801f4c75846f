export const UNAUTHORIZED_REQUEST = 'UNAUTHORIZED_REQUEST';

const defaultAction = {
    type: UNAUTHORIZED_REQUEST,
};

/**
 * Мидлвара для обработки ошибок авторизации.
 * Для использования необходимо, чтобы все AC при ошибке авторизации генерировали action вида:
 *
 * {
 *     type: 'ANY_TYPE',
 *     error: [{ status: 401 }]
 * }
 *
 * Это соответствует ответу от корпоративных апишек http://git/projects/EF/repos/services/browse/src/lib/api-factory.ts
 * Главное - пробросить ответ в action.error
 *
 * @param  {Object} [unauthorizedAction] action, который будет продиспатчен в случае ошибки авторизации.
 * @returns {Function} Redux middleware
 */
export default function handleUnauthorizedMiddleware(unauthorizedAction = defaultAction) {
    return () => (next) => (action) => {
        if (action.error?.[0] && action.error[0].status === 401) {
            next(unauthorizedAction);
        }

        return next(action);
    };
}
