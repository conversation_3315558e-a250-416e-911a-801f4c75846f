import React from 'react';
import { type SuspensiveCondition } from 'corp-credit-products-api-typescript-services';

import { Button } from '@alfalab/core-components/button';
import { IconButton } from '@alfalab/core-components/icon-button';
import { ModalDesktop } from '@alfalab/core-components/modal/desktop';
import { ModalMobile } from '@alfalab/core-components/modal/mobile';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';
import ChevronLeftMIcon from '@alfalab/icons-glyph/ChevronLeftMIcon';
import ChevronRightMIcon from '@alfalab/icons-glyph/ChevronRightMIcon';

import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';
import { useGetSuspensiveConditionInfo } from '#/src/utils/hooks/use-get-suspensive-condition-info';

import { cn } from './suspensive-conditions';

type TProps = {
    condition: SuspensiveCondition;
    product: SomeMappedProduct;
    handleSwitchInfoModal: (x: number) => void;
    handleCloseInfoModal: () => void;
    handleOnSendDocumentsClick: () => void;
};

export const SuspensiveConditionModal = ({
    condition,
    product,
    handleSwitchInfoModal,
    handleCloseInfoModal,
    handleOnSendDocumentsClick,
}: TProps) => {
    const [isMobile] = useMatchMedia('--mobile');
    const [isTablet] = useMatchMedia('--tablet');

    const { status, statusLabel, completeLabelDate, typeLable, subTitleLabel, description } =
        useGetSuspensiveConditionInfo(condition, product);

    const Component = isMobile ? ModalMobile : ModalDesktop;

    return (
        <Component size={800} open={true}>
            <Component.Header
                leftAddons={
                    <div>
                        <IconButton
                            size={32}
                            icon={<ChevronLeftMIcon />}
                            onClick={() => handleSwitchInfoModal(-1)}
                            dataTestId='left-chevron'
                        />
                        <IconButton
                            size={32}
                            icon={<ChevronRightMIcon />}
                            onClick={() => handleSwitchInfoModal(1)}
                            dataTestId='right-chevron'
                        />
                    </div>
                }
                hasCloser={true}
                onClose={handleCloseInfoModal}
            >
                <div className={cn('status-container')} data-test-id='status'>
                    <Typography.Text
                        className={cn(status?.toLowerCase())}
                        tag='div'
                        view='primary-medium'
                    >
                        {statusLabel}
                    </Typography.Text>
                    <Typography.Text
                        className={cn(status?.toLowerCase())}
                        tag='div'
                        view='primary-medium'
                        weight='medium'
                    >
                        {completeLabelDate}
                    </Typography.Text>
                </div>
                <Typography.Title dataTestId='type' tag='h1' view='medium'>
                    {typeLable}
                </Typography.Title>
                <Typography.Text
                    dataTestId='subTitle'
                    tag='div'
                    color='secondary'
                    view='primary-medium'
                >
                    {subTitleLabel}
                </Typography.Text>
            </Component.Header>
            <Component.Content>
                <div className={cn('modal-content')}>
                    <Typography.Text tag='div' view='primary-medium' dataTestId='description'>
                        {description}
                    </Typography.Text>
                </div>
            </Component.Content>

            <Component.Footer layout={isMobile ? 'column' : undefined}>
                <Button
                    block={isMobile || isTablet}
                    onClick={handleOnSendDocumentsClick}
                    view='primary'
                    size={48}
                    dataTestId='send-documents'
                >
                    Отправить документы
                </Button>
                <Button
                    dataTestId='close-button'
                    block={isMobile || isTablet}
                    size={48}
                    onClick={handleCloseInfoModal}
                >
                    Закрыть
                </Button>
            </Component.Footer>
        </Component>
    );
};
