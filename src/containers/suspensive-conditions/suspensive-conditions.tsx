import React, { type ComponentProps, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';
import { DealType } from 'corp-credit-products-api-typescript-services';

import { Button } from '@alfalab/core-components/button';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { type Pagination } from '@alfalab/core-components/pagination';
import { type OptionShape } from '@alfalab/core-components/select/typings';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Table } from '@alfalab/core-components/table';
import { Tag } from '@alfalab/core-components/tag';
import { Typography } from '@alfalab/core-components/typography';
import { ExclamationCompactMIcon } from '@alfalab/icons-glyph/ExclamationCompactMIcon';
import MagnifierMIcon from '@alfalab/icons-glyph/MagnifierMIcon';

import ErrorState from '#/src/components/ui/error-state';
import { FilterComponent } from '#/src/components/ui/filter-component/filter-component';
import { ECreditProducts } from '#/src/constants/credit-products';
import { ESuspensiveConditionsStatuses } from '#/src/constants/suspensive-conditions';
import { VOC_EVENTS } from '#/src/constants/voc';
import { initExternalRedirect } from '#/src/ducks/app/actions';
import { isCreditProductsFetchingSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { externalRedirectAlfaCreditRequestDocumentSelector } from '#/src/ducks/settings/selectors';
import { sendVOCEvent } from '#/src/ducks/shared/actions';
import { getSuspensiveConditionsStart } from '#/src/ducks/suspensive-conditions/actions';
import {
    isSuspensiveConditionsFetchingSelector,
    pagesCountSuspensiveConditionsSelector,
    suspensiveConditionsErrorSelector,
    suspensiveConditionsListSelector,
} from '#/src/ducks/suspensive-conditions/selectors';
import { SUSPENSIVE_CONDITIONS_METRICS } from '#/src/metrics';
import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { SuspensiveConditionsItem } from '../suspensive-conditions-item';

import { SuspensiveConditionModal } from './suspensive-condition-modal';

import './suspensive-conditions.css';

export const cn = createCn('suspensive-conditions');

type Props = {
    product: SomeMappedProduct;
};

type DealTypeFilters = {
    current: boolean;
    subset: boolean;
};

const options = Object.entries(ESuspensiveConditionsStatuses).map(([key, value]) => ({
    key: value.toLocaleLowerCase(),
    content: value,
    value: key,
}));
const possiblePerPage = [10, 25, 50];

export const defaultStatusOptions = [options[0]] as OptionShape[];

const getDealTypeFromFilters = (filters: DealTypeFilters) => {
    if (filters.current && filters.subset) {
        return DealType.All;
    }
    if (filters.current && !filters.subset) {
        return DealType.Current;
    }
    if (!filters.current && filters.subset) {
        return DealType.SubsetDeals;
    }

    return DealType.All;
};

const SUSPENSIVE_CONDITIONS_FILTER_BUTTONS: Partial<
    Record<ECreditProducts | ETrancheTypes, string[]>
> = {
    [ECreditProducts.CREDIT_LINE]: ['Кредитная линия', 'Транши'],
    [ECreditProducts.SOPUK]: ['Рамочный договор', 'Кредиты'],
    [ECreditProducts.GUARANTY_LINE]: ['Гарантийная линия', 'Гарантии'],
    [ECreditProducts.OVERDRAFT]: ['Овердрафт', 'Транши'],
};

export const SuspensiveConditions = ({ product }: Props) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();
    const [isMobile] = useMatchMedia('--mobile');
    const [isTablet] = useMatchMedia('--tablet-m');
    const suspensiveConditionsList = useSelector(suspensiveConditionsListSelector);
    const pagesCountSuspensiveConditions = useSelector(pagesCountSuspensiveConditionsSelector);
    const isSuspensiveConditionsFetching = useSelector(isSuspensiveConditionsFetchingSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const suspensiveConditionsError = useSelector(suspensiveConditionsErrorSelector);
    const externalRedirectAlfaCreditRequestDocument = useSelector(
        externalRedirectAlfaCreditRequestDocumentSelector,
    );
    const currentCustomerId = useSelector(currentOrganizationEqIdSelector);

    const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
    const [statusOptions, setStatusOptions] = useState<OptionShape[] | null>(null);
    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(possiblePerPage[0]);
    const [filters, setFilters] = useState<DealTypeFilters>({
        subset: false,
        current: false,
    });

    const isFetching = isCreditProductsFetching || isSuspensiveConditionsFetching;

    const filterButtons = product?.type
        ? SUSPENSIVE_CONDITIONS_FILTER_BUTTONS[product.type] || []
        : [];

    const handleOnSendDocumentsClick = () => {
        dispatch(
            initExternalRedirect({
                link: externalRedirectAlfaCreditRequestDocument,
                addContextRoot: false,
                withOrganizationId: true,
                organizationId: currentCustomerId,
                parameters: {
                    dealId: product?.dealId || '',
                    dealDocNumber: product?.docNumber || '',
                    customerId: currentCustomerId,
                },
            }),
        );

        if (trackAlfaMetrics && (!!product?.productCode || !!product?.docNumber)) {
            trackAlfaMetrics(SUSPENSIVE_CONDITIONS_METRICS.clickSendDocuments, {
                productCode: product.productCode,
                docNumber: product.docNumber,
            });
        }
    };

    const handleOnPageChange: React.ComponentProps<typeof Pagination>['onPageChange'] = (index) => {
        dispatch(
            getSuspensiveConditionsStart({
                dealId: product?.dealId || '',
                dealType: getDealTypeFromFilters(filters),
                status: statusOptions?.[0]?.value,
                pageNumber: index + 1,
                pageSize,
                docNumber: product?.docNumber || '',
            }),
        );

        setPageIndex(index);
    };

    const handleOnPerPageChange: React.ComponentProps<
        typeof Table.Pagination
    >['onPerPageChange'] = (perPage) => {
        dispatch(
            getSuspensiveConditionsStart({
                dealId: product?.dealId || '',
                dealType: getDealTypeFromFilters(filters),
                status: statusOptions?.[0]?.value,
                pageNumber: 1,
                pageSize: perPage,
                docNumber: product?.docNumber || '',
            }),
        );

        setPageIndex(0);
        setPageSize(perPage);
    };

    const handleOnStatusChange: ComponentProps<typeof FilterComponent>['onChange'] = (
        selectedMultiple,
    ) => {
        dispatch(
            getSuspensiveConditionsStart({
                dealId: product?.dealId || '',
                dealType: getDealTypeFromFilters(filters),
                status: selectedMultiple?.[0]?.value,
                pageNumber: 1,
                pageSize,
                docNumber: product?.docNumber || '',
            }),
        );

        setStatusOptions(selectedMultiple);
        setPageIndex(0);
    };

    const handleOnTagClear = () => {
        handleOnStatusChange([]);
    };

    const handleOnReload = () => {
        setFilters({
            current: false,
            subset: false,
        });
        setPageIndex(0);
        setStatusOptions(null);

        dispatch(
            getSuspensiveConditionsStart({
                dealId: product?.dealId || '',
                dealType: DealType.All,
                status: defaultStatusOptions?.[0]?.value,
                pageNumber: 1,
                pageSize,
                docNumber: product?.docNumber || '',
            }),
        );
    };

    const handleOnTagChange = (
        _: React.MouseEvent<HTMLButtonElement, MouseEvent>,
        payload: { checked: boolean; name?: string },
    ) => {
        const key = payload.name;
        const { checked } = payload;

        if (key) {
            const newFilters = {
                ...filters,
                [key]: checked,
            };

            dispatch(
                getSuspensiveConditionsStart({
                    dealId: product?.dealId || '',
                    status: statusOptions?.[0]?.value,
                    dealType: getDealTypeFromFilters(newFilters),
                    pageNumber: 1,
                    pageSize,
                    docNumber: product?.docNumber || '',
                }),
            );

            setPageIndex(0);
            setFilters(newFilters);
        }
    };

    if (!!suspensiveConditionsError && !isFetching) {
        return (
            <ErrorState
                className={cn('container')}
                icon={<ExclamationCompactMIcon width={28} height={28} />}
                title='Не  получилось загрузить список отлагательных условий'
                text='Уже исправляем. Попробуйте ещё раз или зайдите позже'
                textForButton='Попробовать ещё раз'
                height={350}
                onButtonClick={handleOnReload}
                isRedesigned={true}
            />
        );
    }

    const handleSwitchInfoModal = (index: number) => {
        const newIndex = (selectedIndex || 0) + index;

        if (newIndex < 0) {
            setSelectedIndex(suspensiveConditionsList.length - 1);
        } else if (newIndex > suspensiveConditionsList.length - 1) {
            setSelectedIndex(0);
        } else {
            setSelectedIndex(newIndex);
        }

        switch (product?.type) {
            case ECreditProducts.CREDIT_LINE:
                dispatch(sendVOCEvent(VOC_EVENTS.NIB_CP_CLM));
                break;
            case ECreditProducts.GUARANTY_LINE:
                dispatch(sendVOCEvent(VOC_EVENTS.NIB_CP_GLM));
                break;
            case ECreditProducts.GUARANTY:
                dispatch(sendVOCEvent(VOC_EVENTS.NIB_CP_GSM));
                break;
            case ECreditProducts.BUSINESS_CREDIT:
                dispatch(sendVOCEvent(VOC_EVENTS.NIB_CP_CSM));
                break;
            case ETrancheTypes.tranche:
                dispatch(sendVOCEvent(VOC_EVENTS.NIB_CP_LSM));
                break;
        }
    };

    const handleCloseInfoModal = () => {
        setSelectedIndex(null);
    };

    return (
        <div className={cn()} data-test-id='suspensive-conditions'>
            <div className={cn('header')}>
                <div className={cn('filters-container')}>
                    <div className={cn('filters')}>
                        <FilterComponent
                            label='Статус'
                            showClear={true}
                            multiple={false}
                            options={options}
                            selected={statusOptions ?? []}
                            onClear={handleOnTagClear}
                            onChange={handleOnStatusChange}
                            dataTestId='status-tag'
                            className={cn('status')}
                        />
                        {filterButtons.length > 0 && (
                            <React.Fragment>
                                <Tag
                                    name='current'
                                    checked={filters.current}
                                    onClick={handleOnTagChange}
                                    view='filled'
                                    shape='rectangular'
                                    size='xxs'
                                    dataTestId={filterButtons[0]}
                                >
                                    {filterButtons[0]}
                                </Tag>
                                <Tag
                                    name='subset'
                                    checked={filters.subset}
                                    onClick={handleOnTagChange}
                                    view='filled'
                                    shape='rectangular'
                                    size='xxs'
                                    dataTestId={filterButtons[1]}
                                >
                                    {filterButtons[1]}
                                </Tag>
                            </React.Fragment>
                        )}
                    </div>
                </div>
                <Button
                    view='secondary'
                    size={isTablet ? 32 : 40}
                    onClick={handleOnSendDocumentsClick}
                    className={cn('send-documents')}
                    dataTestId='suspensive-conditions__send-documents'
                >
                    Отправить документы
                </Button>
            </div>
            <div className={cn('container')}>
                {isFetching ? (
                    <React.Fragment>
                        <Skeleton visible={true} className={cn('skeleton')} />
                        <Skeleton visible={true} className={cn('skeleton')} />
                        <Skeleton visible={true} className={cn('skeleton')} />
                    </React.Fragment>
                ) : (
                    <React.Fragment>
                        {!suspensiveConditionsList.length && (
                            <ErrorState
                                className={cn('container')}
                                icon={<MagnifierMIcon width={28} height={28} />}
                                title='Ничего не нашлось'
                                text={
                                    <Typography.Text color='primary' view='primary-medium'>
                                        Попробуйте сбросить фильтры и&nbsp;поискать еще раз
                                    </Typography.Text>
                                }
                                textForButton='Сбросить фильтры'
                                onButtonClick={handleOnReload}
                                isRedesigned={true}
                            />
                        )}
                        {suspensiveConditionsList.map((condition, index) => (
                            <SuspensiveConditionsItem
                                key={condition?.suspensiveConditionId}
                                condition={condition}
                                product={product}
                                dataTestId={`suspensive-conditions__item-${index}`}
                                onClick={handleSwitchInfoModal}
                                selectedIndex={index}
                            />
                        ))}
                        {pagesCountSuspensiveConditions > 1 && (
                            <Table.Pagination
                                className={cn('pagination')}
                                activePadding={isMobile ? 0 : 2}
                                perPage={pageSize}
                                possiblePerPage={possiblePerPage}
                                currentPageIndex={pageIndex}
                                pagesCount={pagesCountSuspensiveConditions}
                                onPageChange={handleOnPageChange}
                                onPerPageChange={handleOnPerPageChange}
                            />
                        )}
                    </React.Fragment>
                )}
            </div>
            {selectedIndex !== null && (
                <SuspensiveConditionModal
                    condition={suspensiveConditionsList[selectedIndex]}
                    product={product}
                    handleSwitchInfoModal={handleSwitchInfoModal}
                    handleOnSendDocumentsClick={handleOnSendDocumentsClick}
                    handleCloseInfoModal={handleCloseInfoModal}
                />
            )}
        </div>
    );
};
