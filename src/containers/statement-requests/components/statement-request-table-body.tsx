import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { type StatementRequest } from 'corp-loan-statements-api-typescript-services';
import formatDate from 'date-fns/format';

import { IconButton } from '@alfalab/core-components/icon-button';
import { Indicator } from '@alfalab/core-components/indicator';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Notification, type NotificationProps } from '@alfalab/core-components/notification';
import { Status } from '@alfalab/core-components/status';
import { Table } from '@alfalab/core-components/table';
import { Tooltip } from '@alfalab/core-components/tooltip';
import { ArrowDownLineDownSIcon } from '@alfalab/icons-glyph/ArrowDownLineDownSIcon';
import { NBSP } from 'arui-private/lib/formatters';

import { DATE_FORMAT } from '#/src/constants/date';
import { downloadStatementRequestStart } from '#/src/ducks/statement-requests/actions';
import { downloadStatementRequestStatusSelector } from '#/src/ducks/statement-requests/selectors';

import { cn } from '../statement-requests';

enum EStatusTexts {
    SUCCESS = 'Готово',
    FAILED = 'Ошибка',
    IN_PROGRESS = 'В работе',
}

enum EStatusColors {
    SUCCESS = 'green',
    FAILED = 'red',
    IN_PROGRESS = 'blue',
}

const getTrancheNumbers = ({
    trancheNums,
    maxTrancheNumbersLength,
}: {
    trancheNums: string[];
    maxTrancheNumbersLength: number;
}) => {
    if (trancheNums.length > maxTrancheNumbersLength) {
        const result = [];

        for (let i = 0; i < maxTrancheNumbersLength; i++) {
            result.push(trancheNums[i]);
        }

        return result.join(', ');
    }

    return trancheNums.join(', ');
};

const getTooltipText = ({ trancheNums, sliceNum }: { trancheNums: string[]; sliceNum: number }) =>
    trancheNums.slice(sliceNum).join(', ');

const CustomNotification = ({
    condition,
    title,
    text,
    badge,
    autoCloseDelay,
    contentClassName,
}: {
    condition: boolean;
    title: string;
    text: string;
    badge: NotificationProps['badge'];
    autoCloseDelay?: NotificationProps['autoCloseDelay'];
    contentClassName?: NotificationProps['contentClassName'];
}) => {
    const [notificationVisible, setNotificationVisible] = useState(false);

    useEffect(() => {
        setNotificationVisible(condition);
    }, [condition]);

    return (
        <Notification
            visible={notificationVisible}
            badge={badge}
            title={title}
            onClose={() => setNotificationVisible(false)}
            onCloseTimeout={() => setNotificationVisible(false)}
            autoCloseDelay={autoCloseDelay}
            contentClassName={contentClassName}
        >
            {text}
        </Notification>
    );
};

const StatementRequestsTableBody = ({
    statementRequestsList,
    docNumber,
}: {
    statementRequestsList: StatementRequest[];
    docNumber?: string;
}) => {
    const [isDownloadStart, setIsDownloadStart] = useState(false);
    const downloadStatementRequestStatus = useSelector(downloadStatementRequestStatusSelector);
    const dispatch = useDispatch();
    const [isTablet] = useMatchMedia('--tablet');

    const handleStatementRequestDownload = ({
        fileId,
        fromDate,
        toDate,
    }: {
        fileId: string;
        fromDate: string;
        toDate: string;
    }) => {
        setIsDownloadStart(true);

        dispatch(
            downloadStatementRequestStart({
                fileId,
                fromDate,
                toDate,
                docNumber: docNumber ?? '',
            }),
        );
    };

    useEffect(() => {
        if (
            !downloadStatementRequestStatus.isFetching &&
            downloadStatementRequestStatus.error === null
        ) {
            setIsDownloadStart(false);
        }
    }, [downloadStatementRequestStatus]);

    return (
        <React.Fragment>
            <CustomNotification
                condition={downloadStatementRequestStatus.isFetching}
                title='Выписка скачивается'
                text='Это займёт несколько секунд'
                badge='neutral-operation'
            />

            <CustomNotification
                condition={downloadStatementRequestStatus.error !== null}
                title='Не получилось скачать выписку'
                text='Попробуйте ещё раз'
                badge='negative'
                autoCloseDelay={5000}
                contentClassName={cn('error-notification')}
            />
            <Table.TBody>
                {statementRequestsList.map((statement) => {
                    const isCreditLine = statement.productType === 'CREDIT_LINE';
                    const isSopuk = statement.productType === 'SOPUK';
                    const isStatusSuccess = statement.status === 'SUCCESS';
                    const maxTrancheNumbersLength = isTablet ? 1 : 3;

                    const renderDownloadIconButton = () => (
                        <IconButton
                            view='primary'
                            size={32}
                            icon={ArrowDownLineDownSIcon}
                            transparentBg={true}
                            onClick={() =>
                                handleStatementRequestDownload({
                                    fileId: statement.id ?? '',
                                    fromDate: statement.period?.fromDate ?? '',
                                    toDate: statement.period?.toDate ?? '',
                                })
                            }
                            disabled={statement.status === 'SUCCESS' ? isDownloadStart : true}
                            dataTestId='statement-download'
                        />
                    );

                    return (
                        <Table.TRow key={statement.id}>
                            <Table.TCell width='24px'>
                                {isStatusSuccess && !statement.isViewed && <Indicator view='red' />}
                            </Table.TCell>
                            <Table.TCell>
                                {formatDate(
                                    new Date((statement.createDate ?? 0) * 1000),
                                    DATE_FORMAT,
                                )}
                            </Table.TCell>
                            {isCreditLine || isSopuk ? (
                                <Table.TCell>
                                    <div className={cn('tranche-numbers-wrapper')}>
                                        {getTrancheNumbers({
                                            trancheNums:
                                                statement.subsetProductsInfo?.numbers ?? [],
                                            maxTrancheNumbersLength,
                                        })}

                                        {!!statement.subsetProductsInfo &&
                                            statement.subsetProductsInfo.numbers.length >
                                                maxTrancheNumbersLength && (
                                                <Tooltip
                                                    position='top'
                                                    content={getTooltipText({
                                                        trancheNums:
                                                            statement.subsetProductsInfo?.numbers ??
                                                            [],
                                                        sliceNum: maxTrancheNumbersLength,
                                                    })}
                                                    trigger='hover'
                                                >
                                                    <Indicator
                                                        value={
                                                            <span>
                                                                +
                                                                {statement.subsetProductsInfo
                                                                    .numbers.length -
                                                                    maxTrancheNumbersLength}
                                                            </span>
                                                        }
                                                        view='grey'
                                                        dataTestId='statement-indicator'
                                                    />
                                                </Tooltip>
                                            )}
                                    </div>
                                </Table.TCell>
                            ) : (
                                <React.Fragment />
                            )}
                            <Table.TCell className={cn('nowrap')}>
                                {formatDate(
                                    new Date(statement.period ? statement.period.fromDate : ''),
                                    DATE_FORMAT,
                                )}
                                {NBSP}-{NBSP}
                                {formatDate(
                                    new Date(statement.period ? statement.period.toDate : ''),
                                    DATE_FORMAT,
                                )}
                            </Table.TCell>
                            <Table.TCell className={cn('status-cell')}>
                                <Status
                                    shape='rounded'
                                    color={
                                        EStatusColors[
                                            statement.status as keyof typeof EStatusColors
                                        ]
                                    }
                                >
                                    {EStatusTexts[statement.status as keyof typeof EStatusTexts]}
                                </Status>
                            </Table.TCell>
                            <Table.TCell align='center' width='52px'>
                                {isTablet ? (
                                    renderDownloadIconButton()
                                ) : (
                                    <Tooltip
                                        content='Скачать'
                                        position='top'
                                        trigger='hover'
                                        view='hint'
                                    >
                                        {renderDownloadIconButton()}
                                    </Tooltip>
                                )}
                            </Table.TCell>
                        </Table.TRow>
                    );
                })}
            </Table.TBody>
        </React.Fragment>
    );
};

export { StatementRequestsTableBody };
