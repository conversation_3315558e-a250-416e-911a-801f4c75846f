import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Text } from '@alfalab/core-components/typography/text';
import ContainerMIcon from '@alfalab/icons-glyph/ContainerMIcon';

import ErrorState from '#/src/components/ui/error-state';
import PrintOperationsSidebar from '#/src/components/ui/print-operations-sidebar/print-operations-sidebar';
import { type PrintOperationsData } from '#/src/components/ui/print-operations-sidebar/types';
import { type ECreditProducts } from '#/src/constants/credit-products';
import {
    createStatementRequestFinish,
    createStatementRequestStart,
} from '#/src/ducks/statement-requests/actions';
import {
    isErrorCreateFetchingRequestSelector,
    isFetchingCreateStatementRequestSelector,
} from '#/src/ducks/statement-requests/selectors';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';
import { useDispatchedAction } from '#/src/utils/hooks/use-dispatched-action';
import { getTypeOfPrintOperationsSidebar } from '#/src/utils/print-operations-sidebar-helpers';

const EmptyStatementRequestsList = ({ product }: { product: SomeMappedProduct }) => {
    const dispatch = useDispatch();
    const [isVisiblePrintOperationsSidebar, setVisiblePrintOperationsSidebar] = useState(false);
    const isFetchingCreateStatementRequest = useSelector(isFetchingCreateStatementRequestSelector);
    const isErrorCreateFetchingRequest = useSelector(isErrorCreateFetchingRequestSelector);
    const handleCreateStatementRequestFinish = useDispatchedAction(createStatementRequestFinish);

    const handlePrintOperationsSidebarSubmit = ({
        format,
        fromDate,
        toDate,
        selectedTranches,
        withSignature,
    }: PrintOperationsData) => {
        dispatch(
            createStatementRequestStart({
                docNumber: product?.docNumber || '',
                fromDate,
                toDate,
                format,
                selectedTranches,
                withSignature,
            }),
        );

        setVisiblePrintOperationsSidebar(false);
    };

    const togglePrintOperationsSidebar = useCallback(() => {
        setVisiblePrintOperationsSidebar((isVisible) => !isVisible);
    }, []);

    return (
        <React.Fragment>
            <ErrorState
                icon={<ContainerMIcon />}
                title='Пока пусто'
                text={
                    <Text color='primary' view='primary-medium'>
                        Создайте выписку и она здесь появится
                    </Text>
                }
                height={350}
                textForButton='Создать выписку'
                onButtonClick={() => {
                    setVisiblePrintOperationsSidebar(true);
                }}
                isRedesigned={true}
            />

            <PrintOperationsSidebar
                isFetching={isFetchingCreateStatementRequest}
                isError={isErrorCreateFetchingRequest}
                docNumber={product?.docNumber}
                servicingAccounts={product?.servicingAccounts}
                type={getTypeOfPrintOperationsSidebar((product?.type ?? '') as ECreditProducts)}
                startDate={product?.requisites?.fromDate}
                isVisible={isVisiblePrintOperationsSidebar}
                onClose={togglePrintOperationsSidebar}
                onCloseNotification={handleCreateStatementRequestFinish}
                onSubmit={handlePrintOperationsSidebarSubmit}
            />
        </React.Fragment>
    );
};

export { EmptyStatementRequestsList };
