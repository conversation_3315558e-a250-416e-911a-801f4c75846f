import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Skeleton } from '@alfalab/core-components/skeleton';
import { Table } from '@alfalab/core-components/table';
import MobilePhoneLineMIcon from '@alfalab/icons-glyph/MobilePhoneLineMIcon';
import ScrewdriverPaintBrushMIcon from '@alfalab/icons-glyph/ScrewdriverPaintBrushMIcon';

import { AdaptiveEmptyState } from '#/src/components/ui/adaptive-empty-state';
import ErrorState from '#/src/components/ui/error-state';
import { isCreditProductsFetchingSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import {
    getStatementRequestsStart,
    updateStatementRequestsViewStart,
} from '#/src/ducks/statement-requests/actions';
import {
    isStatementRequestsFetchingSelector,
    pagesCountStatementRequestsSelector,
    statementRequestsErrorSelector,
    statementRequestsListSelector,
} from '#/src/ducks/statement-requests/selectors';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';

import { EmptyStatementRequestsList } from './components/empty-statement-requests-list';
import { StatementRequestsTableBody } from './components/statement-request-table-body';
import { StatementRequestsTableHead } from './components/statement-request-table-head';

import './statement-requests.css';

export const cn = createCn('statement-requests');

type TProps = {
    product: SomeMappedProduct;
};

const possiblePerPage = [10, 25, 50];

const StatementRequests = ({ product }: TProps) => {
    const [pageIndex, setPageIndex] = useState(0);
    const [pageSize, setPageSize] = useState(possiblePerPage[0]);
    const dispatch = useDispatch();
    const [isMobile] = useMatchMedia('--mobile');
    const statementRequestsList = useSelector(statementRequestsListSelector);
    const statementRequestsError = useSelector(statementRequestsErrorSelector);
    const isStatementRequestsFetching = useSelector(isStatementRequestsFetchingSelector);
    const isCreditProductsFetching = useSelector(isCreditProductsFetchingSelector);
    const pagesCountStatementRequests = useSelector(pagesCountStatementRequestsSelector);
    const isFetching = isCreditProductsFetching || isStatementRequestsFetching;

    const isHaveUnviewedRequests = useMemo(
        () => statementRequestsList.some((request) => !request.isViewed),
        [statementRequestsList],
    );

    const handleOnPageChange: React.ComponentProps<typeof Table.Pagination>['onPageChange'] = (
        index,
    ) => {
        dispatch(
            getStatementRequestsStart({
                pageNumber: index + 1,
                pageSize,
                docNumber: product?.docNumber || '',
            }),
        );

        setPageIndex(index);
    };

    const handleOnPerPageChange: React.ComponentProps<
        typeof Table.Pagination
    >['onPerPageChange'] = (perPage) => {
        dispatch(
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize: perPage,
                docNumber: product?.docNumber || '',
            }),
        );

        setPageIndex(0);
        setPageSize(perPage);
    };

    const handleOnReload = () => {
        setPageIndex(0);

        dispatch(
            getStatementRequestsStart({
                pageNumber: 1,
                pageSize,
                docNumber: product?.docNumber || '',
            }),
        );
    };

    useEffect(() => {
        if (isHaveUnviewedRequests) {
            dispatch(
                updateStatementRequestsViewStart({
                    docNumber: product?.docNumber || '',
                }),
            );
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isHaveUnviewedRequests]);

    if (isFetching) {
        return <Skeleton visible={true} className={cn('skeleton')} />;
    }

    if (!!statementRequestsError && !isFetching) {
        return (
            <ErrorState
                icon={<ScrewdriverPaintBrushMIcon />}
                title='Не  получилось загрузить'
                text='Уже исправляем. Попробуйте ещё раз или зайдите позже'
                textForButton='Попробовать ещё раз'
                height={350}
                onButtonClick={handleOnReload}
                isRedesigned={true}
            />
        );
    }

    if (isMobile) {
        return (
            <AdaptiveEmptyState
                text='Пока работать с заявлениями и получать кредиты можно только с компьютера'
                title='Недоступно в мобильной версии'
                icon={<MobilePhoneLineMIcon />}
            />
        );
    }

    if (statementRequestsList.length === 0) {
        return <EmptyStatementRequestsList product={product} />;
    }

    return (
        <Table
            className={cn()}
            pagination={
                statementRequestsList.length > possiblePerPage[0] && (
                    <Table.Pagination
                        perPage={pageSize}
                        possiblePerPage={possiblePerPage}
                        currentPageIndex={pageIndex}
                        pagesCount={pagesCountStatementRequests}
                        onPageChange={handleOnPageChange}
                        onPerPageChange={handleOnPerPageChange}
                    />
                )
            }
        >
            <StatementRequestsTableHead productType={product?.type} />

            <StatementRequestsTableBody
                statementRequestsList={statementRequestsList}
                docNumber={product?.docNumber}
            />
        </Table>
    );
};

export { StatementRequests };
