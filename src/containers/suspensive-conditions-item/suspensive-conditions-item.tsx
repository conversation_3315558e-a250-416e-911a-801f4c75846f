import React from 'react';
import { createCn } from 'bem-react-classname';
import { type SuspensiveCondition } from 'corp-credit-products-api-typescript-services';

import { Button } from '@alfalab/core-components/button';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';
import { BackgroundPlate, BackgroundPlateView } from 'arui-private/background-plate';

import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';
import { useGetSuspensiveConditionInfo } from '#/src/utils/hooks/use-get-suspensive-condition-info';

import './suspensive-conditions-item.css';

const cn = createCn('suspensive-conditions-item');

type TProps = {
    condition: SuspensiveCondition;
    product: SomeMappedProduct;
    dataTestId?: string;
    onClick: (payload: number) => void;
    selectedIndex: number;
};

export const SuspensiveConditionsItem = React.memo(
    ({ condition, onClick, product, dataTestId, selectedIndex }: TProps) => {
        const [isMobile] = useMatchMedia('--mobile');
        const [isTablet] = useMatchMedia('--tablet-m');
        const { statusLabel, status, completeLabelDate, typeLable, subTitleLabel, description } =
            useGetSuspensiveConditionInfo(condition, product);

        const isLongDescription = description.length > 180;

        const handleClick = () => {
            onClick(selectedIndex);
        };

        return (
            <BackgroundPlate
                view={isTablet ? BackgroundPlateView.Primary : BackgroundPlateView.Secondary}
                className={cn()}
                data-test-id={dataTestId}
            >
                <div className={cn('header')}>
                    <Typography.Text view='caps' weight='bold' color='secondary'>
                        {subTitleLabel}
                    </Typography.Text>
                    {statusLabel}
                </div>
                <div className={cn('card-container')}>
                    <div>
                        <div className={cn('title')}>
                            {!!typeLable && (
                                <Typography.Title
                                    color='primary'
                                    tag='h1'
                                    view='small'
                                    font='system'
                                >
                                    {typeLable}
                                </Typography.Title>
                            )}
                            {!!completeLabelDate && (
                                <Typography.Text
                                    className={cn(status?.toLowerCase())}
                                    view='primary-small'
                                >
                                    {completeLabelDate}
                                </Typography.Text>
                            )}
                        </div>
                        <Typography.Text
                            className={cn('description')}
                            view='primary-small'
                            color='secondary'
                        >
                            {isLongDescription ? `${description.slice(0, 180)}...` : description}
                        </Typography.Text>
                    </div>
                    <Button
                        dataTestId='more-button'
                        block={isMobile}
                        onClick={handleClick}
                        size={isTablet ? 48 : 40}
                        view='secondary'
                    >
                        Подробнее
                    </Button>
                </div>
            </BackgroundPlate>
        );
    },
);
