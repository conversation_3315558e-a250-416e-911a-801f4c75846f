import { type Amount as TAmount } from 'thrift-services/entities';
import { type TSopukSummary } from 'thrift-services/services/credit_products';

import { type OptionShape } from '@alfalab/core-components/select/typings';

const CURRENCIES = {
    RUR: 'Рубли',
    USD: 'Доллары',
    EUR: 'Евро',
    CNY: 'Юани',
    HKD: 'Гонконгские доллары',
};

type Row = {
    title: string;
    totalOverdueLoans: TAmount;
    totalOverdueInterests: TAmount;
    totalFines: TAmount;
    totalOverdueDebts: TAmount;
};

export const prepareSopukOverdueData = (
    data: TSopukSummary | undefined,
    withLowerCase?: boolean,
): Row[] => {
    if (data === undefined) {
        return [];
    }

    const currencyMap = new Map<string, Row>();

    data.totalOverdueLoansByCurrency?.forEach(({ amount, currency }) => {
        if (currency?.mnemonicCode) {
            currencyMap.set(currency.mnemonicCode, {
                title: withLowerCase
                    ? CURRENCIES[currency?.mnemonicCode as keyof typeof CURRENCIES].toLowerCase()
                    : CURRENCIES[currency?.mnemonicCode as keyof typeof CURRENCIES],
                totalOverdueLoans: { amount: amount ?? 0, currency },
                totalOverdueInterests: {},
                totalFines: {},
                totalOverdueDebts: {},
            });
        }
    });

    data.totalOverdueInterestsByCurrency?.forEach(({ amount, currency }) => {
        if (currency?.mnemonicCode) {
            const currencyData = currencyMap.get(currency.mnemonicCode) || {
                title: CURRENCIES[currency?.mnemonicCode as keyof typeof CURRENCIES],
                totalOverdueLoans: {},
                totalOverdueInterests: {},
                totalFines: {},
                totalOverdueDebts: {},
            };

            currencyData.totalOverdueInterests = { amount: amount ?? 0, currency };

            currencyMap.set(currency.mnemonicCode, currencyData);
        }
    });

    data.totalFinesByCurrency?.forEach(({ amount, currency }) => {
        if (currency?.mnemonicCode) {
            const currencyData = currencyMap.get(currency.mnemonicCode) || {
                title: CURRENCIES[currency?.mnemonicCode as keyof typeof CURRENCIES],
                totalOverdueLoans: {},
                totalOverdueInterests: {},
                totalFines: {},
                totalOverdueDebts: {},
            };

            currencyData.totalFines = { amount: amount ?? 0, currency };
            currencyMap.set(currency.mnemonicCode, currencyData);
        }
    });

    data.totalOverdueDebtsByCurrency?.forEach(({ amount, currency }) => {
        if (currency?.mnemonicCode) {
            const currencyData = currencyMap.get(currency.mnemonicCode) || {
                title: CURRENCIES[currency?.mnemonicCode as keyof typeof CURRENCIES],
                totalOverdueLoans: {},
                totalOverdueInterests: {},
                totalFines: {},
                totalOverdueDebts: {},
            };

            currencyData.totalOverdueDebts = { amount: amount ?? 0, currency };
            currencyMap.set(currency.mnemonicCode, currencyData);
        }
    });

    return Array.from(currencyMap.values()).filter((row) => row.totalOverdueDebts.amount !== 0);
};

export const getSopukLinePickerOptions = (totalDebtArray: TAmount[]) => {
    const LABELS_BY_CURRENCIES = {
        RUR: 'в рублях',
        USD: 'в долларах',
        EUR: 'в евро',
        CNY: 'в юанях',
        HKD: 'в гонконгских долларах',
    };

    const result: OptionShape[] = totalDebtArray.map((item) => {
        const mnemonicCode = item?.currency?.mnemonicCode || '';
        const keyLabel = LABELS_BY_CURRENCIES[mnemonicCode as keyof typeof LABELS_BY_CURRENCIES];

        return { key: keyLabel ?? '', value: item };
    });

    return result;
};
