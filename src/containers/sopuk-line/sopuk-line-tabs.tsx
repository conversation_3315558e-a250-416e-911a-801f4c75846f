import React from 'react';
import { useSelector } from 'react-redux';

import { Indicator } from '@alfalab/core-components/indicator';
import { Tab, Tabs } from '@alfalab/core-components/tabs';
import { type SelectedId } from '@alfalab/core-components/tabs/typings';

import { isAlfaCreditWidgetButtonVisibleSelector } from '#/src/ducks/credit-products-main-menu/selectors';
import { newStatementsCountSelector } from '#/src/ducks/statement-requests/selectors';
import {
    isSuspensiveConditionsAvailableSelector,
    isSuspensiveConditionsVisibleSelector,
} from '#/src/ducks/suspensive-conditions/selectors';
import { type SomeMappedProduct } from '#/src/utils/credit-products-mappers';

import { ETabs } from './sopuk-line';

type TProps = {
    product: SomeMappedProduct;
    onChange: React.ComponentProps<typeof Tabs>['onChange'];
    tab: SelectedId;
};

export const SopukLineTabs = ({ product, tab, onChange }: TProps) => {
    const isAlfaCreditWidgetButtonVisible = useSelector(isAlfaCreditWidgetButtonVisibleSelector);
    const isSuspensiveConditionsVisible = useSelector(isSuspensiveConditionsVisibleSelector);
    const isSuspensiveConditionsAvailable = useSelector(isSuspensiveConditionsAvailableSelector);

    const isHiddenAlfaCreditTabs = !isAlfaCreditWidgetButtonVisible;

    const notificationsCount = product?.suspensiveConditionsDeadlineInfo?.notificationsCount;
    const statementsCount = useSelector(newStatementsCountSelector);
    const isHiddenSuspensiveConditionsTab =
        !isSuspensiveConditionsAvailable || !isSuspensiveConditionsVisible;

    const isStatementsTabVisible = product?.isStatementAllowed;

    return (
        <Tabs
            tagView='filled'
            size='xs'
            scrollable={true}
            selectedId={tab}
            onChange={onChange}
            dataTestId='sopuk-line-tabs'
        >
            <Tab
                title='Кредиты'
                id={ETabs.credits}
                dataTestId={`sopuk-line-tab__${ETabs.credits}`}
                keepMounted={true}
            />
            <Tab
                hidden={isHiddenAlfaCreditTabs}
                title='Заявления на кредиты'
                id={ETabs.creditStatements}
                dataTestId={`sopuk-line-tab__${ETabs.creditStatements}`}
            />
            <Tab
                hidden={isHiddenAlfaCreditTabs}
                title='Отправка документов'
                id={ETabs.sendDocuments}
                dataTestId={`sopuk-line-tab__${ETabs.sendDocuments}`}
            />
            <Tab
                hidden={isHiddenSuspensiveConditionsTab}
                title='Отлагательные условия'
                id={ETabs.suspensiveConditions}
                dataTestId={`sopuk-line-tab__${ETabs.suspensiveConditions}`}
                rightAddons={
                    notificationsCount ? (
                        <Indicator height={20} view='red' value={notificationsCount} />
                    ) : null
                }
                keepMounted={true}
            />
            <Tab
                id={ETabs.statementRequests}
                title='Выписки'
                hidden={!isStatementsTabVisible}
                dataTestId={`sopuk-line-tab__${ETabs.statementRequests}`}
                rightAddons={
                    statementsCount > 0 ? (
                        <Indicator height={20} view='red' value={statementsCount} />
                    ) : null
                }
                keepMounted={true}
            />
        </Tabs>
    );
};
