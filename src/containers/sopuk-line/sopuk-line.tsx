import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router';
import { createCn } from 'bem-react-classname';
import qs from 'qs';

import { type Tabs } from '@alfalab/core-components/tabs';
import { type SelectedId } from '@alfalab/core-components/tabs/typings';

import { ErrorStateForCreditProduct } from '#/src/components/error-state-for-credit-product';
import CommonHeading from '#/src/components/ui/common-heading';
import { NBSP } from '#/src/constants/unicode-symbols';
import { selectedProductSelector } from '#/src/ducks/credit-products/selectors/credit-line.selectors';
import { getCurrentOrganizationShortNameSelector } from '#/src/ducks/organization/selectors';
import { checkSignedDocumentsStart } from '#/src/ducks/signed-documents/actions';
import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';
import { useHoldingControls } from '#/src/utils/hooks/use-holding-controls';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import ProductPageGrid from '../../components/ui/product-page-grid';
import SectionSpin from '../../components/ui/section-spin';
import { isAppFetchingSelector } from '../../ducks/app/selectors';
import {
    getSelectedCreditProductStart,
    getTrancheCreditProductsStart,
} from '../../ducks/credit-products/actions';
import { SOPUK_LINE_IN_USE_METRICS } from '../../metrics';
import { checkIsFaultyCreditProduct } from '../../utils/credit-products-mappers';
import ProductPageWrapper from '../product-page-wrapper';

import { SopukLineDebtPane } from './sopuk-line-debt-pane';
import { SopukLineTabInfo } from './sopuk-line-tab-info';
import { SopukLineTabs } from './sopuk-line-tabs';

import './sopuk-line.css';

export const cn = createCn('sopuk-line');

export enum ETabs {
    credits = 'credits',
    sendDocuments = 'sendDocuments',
    creditStatements = 'creditStatements',
    suspensiveConditions = 'suspensiveConditions',
    statementRequests = 'statementRequests',
}

export const SopukLine = () => {
    const location = useLocation<{ docNumber: string }>();
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const { isShowCompanyName } = useHoldingControls();

    const currentOrganizationName = useSelector(getCurrentOrganizationShortNameSelector);
    const selectedProduct = useSelector(selectedProductSelector);
    const currentDocNumber = qs.parse(location.search, { ignoreQueryPrefix: true })
        ?.docNumber as string;

    const { sopukLine } = selectedProduct.product;
    const isAppFetching = useSelector(isAppFetchingSelector);
    const isSelectedProductFetching = selectedProduct.isFetching;
    const [currentTab, setCurrentTab] = useState<SelectedId>(
        ETabs[qs.parse(location.search, { ignoreQueryPrefix: true })?.tab as ETabs] ||
            ETabs.credits,
    );

    const docNumber = qs.parse(location.search, { ignoreQueryPrefix: true })?.docNumber as string;

    const isFetching = isAppFetching || isSelectedProductFetching;

    const handlePageDidMount = () => {
        if (sopukLine?.productDocs?.length) {
            dispatch(checkSignedDocumentsStart(sopukLine.productDocs));
        }
    };

    const tabClickHandler: React.ComponentProps<typeof Tabs>['onChange'] = (_, { selectedId }) => {
        trackAlfaMetrics(SOPUK_LINE_IN_USE_METRICS.tabClick, {
            agreementNumber: sopukLine?.docNumber,
            tabName: selectedId,
        });

        setCurrentTab(selectedId);
    };

    useEffect(() => {
        dispatch(
            getTrancheCreditProductsStart({ trancheType: ETrancheTypes.deal, isFromTab: true }),
        );

        if (docNumber) {
            dispatch(getSelectedCreditProductStart(docNumber));
            trackAlfaMetrics(SOPUK_LINE_IN_USE_METRICS.renderSopukPage, {
                agreementNumber: docNumber,
            });
        }
    }, [dispatch, docNumber, trackAlfaMetrics]);

    const renderPage = () => {
        if (isFetching) {
            return <SectionSpin />;
        }

        if (!sopukLine || checkIsFaultyCreditProduct(sopukLine)) {
            const currentErrorHeading = currentDocNumber ? ` №${NBSP}${currentDocNumber}` : '';

            return (
                <ErrorStateForCreditProduct
                    heading={
                        <CommonHeading
                            heading={`Рамочный договор${currentErrorHeading}`}
                            subheading={isShowCompanyName ? currentOrganizationName : undefined}
                        />
                    }
                />
            );
        }

        return (
            <ProductPageGrid
                productInfo={sopukLine}
                onPageDidMountHandler={handlePageDidMount}
                subHeading={
                    <CommonHeading
                        heading={`Рамочный договор №${NBSP}${sopukLine?.docNumber}`}
                        subheading={isShowCompanyName ? currentOrganizationName : undefined}
                    />
                }
                debtPane={<SopukLineDebtPane sopukLine={sopukLine} />}
                tabs={
                    <SopukLineTabs
                        product={sopukLine}
                        onChange={tabClickHandler}
                        tab={currentTab}
                    />
                }
                tabInfo={<SopukLineTabInfo sopukLine={sopukLine} tab={currentTab} />}
            />
        );
    };

    return <ProductPageWrapper>{renderPage()}</ProductPageWrapper>;
};
