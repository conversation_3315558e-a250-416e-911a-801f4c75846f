import React from 'react';
import { useSelector } from 'react-redux';

import { type SelectedId } from '@alfalab/core-components/tabs/typings';

import { ECreditStatusName, EDocTypes } from '#/src/constants/credit-document-circulation';
import { NBSP } from '#/src/constants/unicode-symbols';
import { type ProductByType } from '#/src/ducks/credit-products/reducer/selected-credit-product';
import {
    hasAlfaCreditCreditRequestViewRightsSelector,
    hasAlfaCreditDocumentRequestViewRightsSelector,
} from '#/src/ducks/mks-permissions/selectors';
import { externalRedirectAlfaCreditRequestCreditSelector } from '#/src/ducks/settings/selectors';
import { checkIsFaultyCreditProduct } from '#/src/utils/credit-products-mappers';

import { DocumentsList } from '../documents-list';
import { SopukDeals } from '../sopuk-line-deals/sopuk-line-deals';
import { StatementRequests } from '../statement-requests/statement-requests';
import { SuspensiveConditions } from '../suspensive-conditions';

import { ETabs } from './sopuk-line';

type Props = {
    tab: SelectedId;
    sopukLine: ProductByType['sopukLine'];
};

export const SopukLineTabInfo = ({ sopukLine, tab }: Props) => {
    const hasAlfaCreditDocumentRequestViewRights = useSelector(
        hasAlfaCreditDocumentRequestViewRightsSelector,
    );
    const externalRedirectAlfaCreditRequestCredit = useSelector(
        externalRedirectAlfaCreditRequestCreditSelector,
    );
    const hasAlfaCreditCreditRequestViewRights = useSelector(
        hasAlfaCreditCreditRequestViewRightsSelector,
    );

    const isTrancheAllowedMMB = !!sopukLine?.isTrancheAllowedMMB;
    const isTrancheAllowed = !!sopukLine?.isTrancheAllowed;

    if (checkIsFaultyCreditProduct(sopukLine)) {
        return null;
    }

    switch (tab) {
        case ETabs.credits:
            return (
                <SopukDeals
                    sopukDocNumber={sopukLine?.docNumber}
                    isTrancheAllowedMMB={isTrancheAllowedMMB}
                    isTrancheAllowed={isTrancheAllowed}
                    dealId={sopukLine?.dealId}
                />
            );
        case ETabs.sendDocuments:
            return (
                <DocumentsList
                    tabName={`sopuk-line__${ETabs.sendDocuments}`}
                    tableTitles={['дата', '№ заявления', 'статус']}
                    docType={EDocTypes.SEND_DOCUMENTS}
                    buttonText='Отправить документы в банк'
                    dealDocNumber={sopukLine?.docNumber}
                    dealId={sopukLine?.dealId}
                    viewRights={hasAlfaCreditDocumentRequestViewRights}
                    parameters={{
                        dealDocNumber: sopukLine?.docNumber || '',
                        dealId: sopukLine?.dealId || '',
                    }}
                />
            );
        case ETabs.creditStatements:
            return (
                <DocumentsList
                    tabName={`sopuk-line__${ETabs.creditStatements}`}
                    docType={EDocTypes.TRANCHES_STATEMENTS}
                    dealDocNumber={sopukLine?.docNumber}
                    dealId={sopukLine?.dealId}
                    documentStatuses={ECreditStatusName}
                    categoryDocLabel='У вас пока нет заявлений'
                    docNumberText={`Заявление на кредит по договору №${NBSP}${sopukLine.docNumber}`}
                    viewRights={hasAlfaCreditCreditRequestViewRights}
                    externalRedirectLink={externalRedirectAlfaCreditRequestCredit}
                    parameters={{
                        dealDocNumber: sopukLine?.docNumber || '',
                        dealId: sopukLine?.dealId || '',
                    }}
                />
            );
        case ETabs.suspensiveConditions:
            return <SuspensiveConditions product={sopukLine} />;
        case ETabs.statementRequests:
            return <StatementRequests product={sopukLine} />;
        default:
            return null;
    }
};
