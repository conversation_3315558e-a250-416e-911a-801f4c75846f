import { type Amount as TAmount } from 'thrift-services/entities';
import { type TSopukSummary } from 'thrift-services/services/credit_products';

import { type OptionShape } from '@alfalab/core-components/select/typings';

import { getSopukLinePickerOptions, prepareSopukOverdueData } from './utils';

describe('utils', () => {
    describe('prepareSopukOverdueData', () => {
        it('should return an empty array if data is undefined', () => {
            expect(prepareSopukOverdueData(undefined)).toEqual([]);
        });

        it('should prepare data correctly', () => {
            const data: TSopukSummary = {
                totalOverdueLoansByCurrency: [{ amount: 100, currency: { mnemonicCode: 'RUR' } }],
                totalOverdueInterestsByCurrency: [
                    { amount: 50, currency: { mnemonicCode: 'RUR' } },
                ],
                totalFinesByCurrency: [{ amount: 10, currency: { mnemonicCode: 'RUR' } }],
                totalOverdueDebtsByCurrency: [{ amount: 5, currency: { mnemonicCode: 'RUR' } }],
            };

            const expectedResult = [
                {
                    title: 'Рубли',
                    totalOverdueLoans: { amount: 100, currency: { mnemonicCode: 'RUR' } },
                    totalOverdueInterests: { amount: 50, currency: { mnemonicCode: 'RUR' } },
                    totalFines: { amount: 10, currency: { mnemonicCode: 'RUR' } },
                    totalOverdueDebts: { amount: 5, currency: { mnemonicCode: 'RUR' } },
                },
            ];

            expect(prepareSopukOverdueData(data)).toEqual(expectedResult);
        });

        it('should handle lowercase option', () => {
            const data: TSopukSummary = {
                totalOverdueLoansByCurrency: [{ amount: 100, currency: { mnemonicCode: 'USD' } }],
                totalOverdueInterestsByCurrency: [],
                totalFinesByCurrency: [],
                totalOverdueDebtsByCurrency: [],
            };

            const expectedResult = [
                {
                    title: 'доллары',
                    totalOverdueLoans: { amount: 100, currency: { mnemonicCode: 'USD' } },
                    totalOverdueInterests: {},
                    totalFines: {},
                    totalOverdueDebts: {},
                },
            ];

            expect(prepareSopukOverdueData(data, true)).toEqual(expectedResult);
        });
    });

    describe('getSopukLinePickerOptions', () => {
        it('should return options correctly', () => {
            const totalDebtArray: TAmount[] = [
                { amount: 100, currency: { mnemonicCode: 'RUR' } },
                { amount: 200, currency: { mnemonicCode: 'USD' } },
            ];

            const expectedResult: OptionShape[] = [
                { key: 'в рублях', value: totalDebtArray[0] },
                { key: 'в долларах', value: totalDebtArray[1] },
            ];

            expect(getSopukLinePickerOptions(totalDebtArray)).toEqual(expectedResult);
        });

        it('should handle missing currency mnemonicCode gracefully', () => {
            const totalDebtArray: TAmount[] = [{ amount: 100, currency: {} }];

            const expectedResult: OptionShape[] = [{ key: '', value: totalDebtArray[0] }];

            expect(getSopukLinePickerOptions(totalDebtArray)).toEqual(expectedResult);
        });
    });
});
