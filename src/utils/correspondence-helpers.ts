import {
    type Letter,
    LettersDirection,
    LetterStatusId,
} from 'thrift-services/services/correspondence';

import { type EarlyPayData } from '#/src/types/early-pay-sidebar';

const CATEGORY = '30';
const DEFAULT_BRANCH_NUMBER = '0000';

export function getEarlyPaymentLetter(data: EarlyPayData, type = 'credit'): Letter {
    const {
        docNumber,
        fromDate,
        paymentType,
        selectedAccountNumber,
        paymentDate,
        debtToPay,
        interestToPay,
        total,
        currency,
        branchNumber,
        currentOrganizationEqId,
    } = data;

    const bodyFirstLine =
        type === 'credit'
            ? `Прошу осуществить досрочное погашение кредита по договору № ${docNumber} от ${fromDate}`
            : `Прошу осуществить досрочное погашение транша № ${docNumber}`;

    return {
        subject:
            type === 'credit'
                ? `Досрочное погашение кредита по договору № ${docNumber} от ${fromDate}. Клиент ${currentOrganizationEqId}`
                : `Досрочное погашение транша № ${docNumber}. Клиент ${currentOrganizationEqId}`,
        direction: LettersDirection.OUT,
        body: `${bodyFirstLine}
                Тип погашения: ${paymentType === 'full' ? 'Полное' : 'Частичное'}
                Номер счета для погашения: Расчетный счет № ${selectedAccountNumber}
                Дата погашения: ${paymentDate}
                Сумма основного долга: ${debtToPay} ${currency.mnemonicCode}
                Сумма процентов: ${interestToPay} ${currency.mnemonicCode}
                Общая сумма: ${total} ${currency.mnemonicCode}`,
        category: {
            id: CATEGORY,
        },
        status: LetterStatusId.ON_SIGNING,
        branch: {
            number: branchNumber || DEFAULT_BRANCH_NUMBER,
        },
    };
}
