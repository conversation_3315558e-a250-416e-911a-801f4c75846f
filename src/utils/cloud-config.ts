import { patchLocalConfig } from '@corp-front/cloud-config/patch-local-config';
import { type IConfig } from 'config';

import packageJson from '../../package.json';

export const formatConfigByValues = ({
    config,
    values = {
        SERVICE_HOST: process.env.HOST || '',
        PROJECT_KEY: process.env.PROJECT_NAME || packageJson['alfa:meta'].name,
    },
}: {
    config: {
        [k: string]: string;
    };
    values?: {
        [k: string]: string;
    };
}) =>
    JSON.parse(
        Object.entries(values).reduce(
            (acc, [key, value]) => acc.replace(new RegExp(`..${key}.`, 'g'), value),
            JSON.stringify(config),
        ),
    );

export const pathConfig = async (config: IConfig) => {
    await patchLocalConfig(config);

    config.util.extendDeep(config, formatConfigByValues({ config: config.util.toObject() }));
};
