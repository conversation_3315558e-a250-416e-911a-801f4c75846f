import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

import { getIsInsideRange } from './math';

export const getIsCreditSumInsideCreditAmount = (
    { minimumAmount, maximumAmount }: TMappedCreditOffer,
    creditSum: number,
) =>
    minimumAmount && maximumAmount
        ? getIsInsideRange(creditSum, [minimumAmount?.amount || 0, maximumAmount?.amount || 0])
        : false;
