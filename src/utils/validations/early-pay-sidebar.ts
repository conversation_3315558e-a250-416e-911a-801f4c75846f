import { type Amount } from 'thrift-services/entities';
import { type UnixEpoch } from 'thrift-services/utils';

import { DATE_FORMAT } from '#/src/constants/date';
import { NBSP } from '#/src/constants/unicode-symbols';
import { earlyPaymentTypes } from '#/src/types/early-pay-sidebar';

import { dateToCustomFormat, parseDateFromAny } from '../date';
import { convertAmountToNumber, convertAmountToString } from '../number-helpers';

export function getPaymentDateValidationErrors(
    currentTime: Date,
    paymentDate: string,
    offDays: number[],
    isAutomaticProcessingType: boolean,
    paymentType: earlyPaymentTypes,
    closestFuturePaymentDate: UnixEpoch,
) {
    const today = currentTime;

    today.setHours(0, 0, 0, 0);

    const paymentDateParsed = parseDateFromAny(currentTime, paymentDate, DATE_FORMAT);

    const validationErrors: string[] = [];

    if (!paymentDate) {
        validationErrors.push('Необходимо заполнить поле');
    }

    if (offDays.includes(paymentDateParsed.getTime())) {
        validationErrors.push('Необходимо выбрать рабочий день');
    }

    if (paymentDateParsed < today) {
        validationErrors.push('Дата платежа должна быть не ранее сегодняшней');
    }

    if (
        isAutomaticProcessingType &&
        paymentType === earlyPaymentTypes.PARTIAL &&
        paymentDateParsed > parseDateFromAny(currentTime, closestFuturePaymentDate)
    ) {
        validationErrors.push(
            `Дата платежа должна быть не${NBSP}позже даты
            ближайшего платежа по${NBSP}графику
            (${dateToCustomFormat(currentTime, closestFuturePaymentDate, DATE_FORMAT)})`,
        );
    }

    return validationErrors;
}

export function getDebtToPayValidationErrors(
    debtToPay: number,
    interestToPay: number,
    isAutomaticProcessingType: boolean,
    paymentType: earlyPaymentTypes,
    totalLoanSum: Amount,
) {
    const validationErrors: string[] = [];

    if (paymentType === earlyPaymentTypes.PARTIAL && !debtToPay && !interestToPay) {
        validationErrors.push(
            isAutomaticProcessingType
                ? 'Необходимо заполнить поле'
                : 'Необходимо заполнить одно из полей',
        );
    }

    if (
        paymentType === earlyPaymentTypes.PARTIAL &&
        debtToPay >= convertAmountToNumber(totalLoanSum)
    ) {
        validationErrors.push(`Максимальная сумма досрочного погашения должна быть менее
            ${convertAmountToString(totalLoanSum, true)}${NBSP}${
                totalLoanSum.currency?.unicodeSymbol || ''
            }`);
    }

    return validationErrors;
}

export function getInterestToPayValidationErrors(
    interestToPay: number,
    debtToPay: number,
    paymentType: earlyPaymentTypes,
) {
    const validationErrors: string[] = [];

    if (paymentType === earlyPaymentTypes.PARTIAL && !debtToPay && !interestToPay) {
        validationErrors.push('Необходимо заполнить одно из полей');
    }

    return validationErrors;
}

export function getSelectedAccountNumberValidationErrors(selectedAccountNumber: string[]) {
    const validationErrors: string[] = [];

    if (!selectedAccountNumber.filter(Boolean).length) {
        validationErrors.push('Необходимо выбрать счет');
    }

    return validationErrors;
}
