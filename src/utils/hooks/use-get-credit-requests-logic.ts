import { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    CommonCreditRequestType,
    RedirectListItemType,
} from 'corp-credit-request-api-typescript-services';

import { ERequestsStatusName } from '#/src/constants/credit-document-circulation';
import { ECreditOffersProductType, EProductCodes } from '#/src/constants/credit-products';
import { EXTERNAL_PATHS } from '#/src/constants/routing';
import { goToCreditFormsApp, initExternalRedirect } from '#/src/ducks/app/actions';
import { isOnlineSigningAvailableSelector } from '#/src/ducks/attach-documents/selectors';
import { setOnlineSigningChannel } from '#/src/ducks/credit-processing/actions';
import { deleteWelcomeDealByIdStart } from '#/src/ducks/credit-requests/actions';
import { creditRequestsFiltersSelector } from '#/src/ducks/credit-requests/selectors';
import {
    type TCreditRequestList,
    type TCreditRequestListItem,
} from '#/src/ducks/credit-requests/types';
import { CREDITSB_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

export const useGetCreditRequests = ({
    creditRequestsList,
}: {
    creditRequestsList: TCreditRequestList;
}) => {
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const isOnlineSigningAvailable = useSelector(isOnlineSigningAvailableSelector);
    const { company } = useSelector(creditRequestsFiltersSelector);

    const [isModalVisible, setIsModalVisible] = useState(false);
    const [selectedRow, setSelectedRow] = useState<TCreditRequestListItem>({});

    const currentCompany = company?.[0];

    const handleMmbRedirect = useCallback(
        ({ productCode }: { productCode?: string }) => {
            const offerType =
                productCode && productCode in ECreditOffersProductType
                    ? ECreditOffersProductType[productCode as EProductCodes]
                    : ECreditOffersProductType[EProductCodes.BP11];

            if (isOnlineSigningAvailable) {
                dispatch(setOnlineSigningChannel({ organizationId: currentCompany?.key }));
            } else {
                dispatch(goToCreditFormsApp({ offerType, organizationId: currentCompany?.key }));
            }
        },
        [dispatch, currentCompany?.key, isOnlineSigningAvailable],
    );

    const handleRowClick = useCallback(
        (id: string) => {
            const currentRow = creditRequestsList.find(
                (elem: TCreditRequestListItem) => elem.id === id,
            );

            if (!currentRow) return;

            setSelectedRow(currentRow);

            const { type, redirect, clientStatus } = currentRow;
            const redirectCard = redirect?.find(
                (redirectItem) => redirectItem.type === RedirectListItemType.Card,
            );
            const isMmb = type === CommonCreditRequestType.Mmb;
            const isWelcome =
                type === CommonCreditRequestType.Welcome &&
                clientStatus === ERequestsStatusName.FAILURE;

            if (isMmb || isWelcome) {
                setIsModalVisible(!isModalVisible);
            } else {
                if (!redirectCard) return;

                dispatch(
                    initExternalRedirect({
                        link: `${redirectCard.redirectLink}`,
                        addContextRoot: false,
                        organizationId: currentCompany?.key,
                    }),
                );
            }

            trackAlfaMetrics(CREDITSB_METRICS.clickRequestDocument, {
                clientStatus: currentRow.clientStatus,
                description: currentRow.description,
                isAvailableForCurrentChannel: currentRow.isAvailableForCurrentChannel,
                type: currentRow.type,
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [creditRequestsList, isModalVisible, trackAlfaMetrics],
    );

    const handleDescriptionClick = useCallback(
        (rowId: string | undefined) => {
            const selectedData = creditRequestsList.find(
                (elem: TCreditRequestListItem) => elem.id === rowId,
            );

            if (!selectedData) return;

            const { clientStatus, limit, redirect, type, productCode, description } = selectedData;

            const redirectLinkData = redirect?.find(
                (redirectItem) => redirectItem.type === RedirectListItemType.Main,
            );

            const isMmb = type === CommonCreditRequestType.Mmb;
            const MMBRedirectLink = '/credit-forms';
            const redirectUrl = isMmb ? MMBRedirectLink : redirectLinkData?.redirectLink;
            const isRedirectLinkExist = Boolean(redirectUrl);

            const handlePrimaryRedirect = () => {
                dispatch(
                    initExternalRedirect({
                        link: `${redirectLinkData?.redirectLink}`,
                        addContextRoot: false,
                        organizationId: currentCompany?.key,
                    }),
                );
            };

            if (isMmb) {
                handleMmbRedirect({ productCode });
            } else if (isRedirectLinkExist) {
                handlePrimaryRedirect();
            }

            trackAlfaMetrics(CREDITSB_METRICS.clickRequestDescription, {
                creditSBLists: 'CreditSBLists',
                url: `${redirectUrl}`,
                urlName: 'Нажатие на поле описания объекта списка',
                sum: limit?.amount,
                status: clientStatus || '',
                description,
            });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [creditRequestsList, handleMmbRedirect, trackAlfaMetrics],
    );

    const handleCancelModal = useCallback(() => {
        const { isAvailableForCurrentChannel, clientStatus, description, type } = selectedRow;

        setIsModalVisible(false);

        trackAlfaMetrics(CREDITSB_METRICS.closeRequestModalWindow, {
            clientStatus,
            description,
            isAvailableForCurrentChannel,
            type,
        });
    }, [selectedRow, trackAlfaMetrics]);

    const handleContinueModal = useCallback(() => {
        const { isAvailableForCurrentChannel, clientStatus, description, type, productCode } =
            selectedRow;

        const shouldRedirectMmb = isAvailableForCurrentChannel;
        const isMmb = type === CommonCreditRequestType.Mmb;
        const isWelcome =
            type === CommonCreditRequestType.Welcome &&
            clientStatus === ERequestsStatusName.FAILURE;

        if (isMmb) {
            if (!shouldRedirectMmb) return;

            handleMmbRedirect({ productCode });
        }

        if (isWelcome) {
            dispatch(
                initExternalRedirect({
                    link: EXTERNAL_PATHS.GOSSUPPORT,
                    addContextRoot: false,
                }),
            );
        }

        setIsModalVisible(false);

        trackAlfaMetrics(CREDITSB_METRICS.clickActionRequestModalWindow, {
            clientStatus,
            description,
            isAvailableForCurrentChannel,
            type,
        });
    }, [dispatch, handleMmbRedirect, selectedRow, trackAlfaMetrics]);

    const handleDeleteWelcome = useCallback(() => {
        const { id, type, clientStatus } = selectedRow;
        const isWelcome =
            type === CommonCreditRequestType.Welcome &&
            clientStatus === ERequestsStatusName.FAILURE;

        if (isWelcome && id) {
            dispatch(
                deleteWelcomeDealByIdStart({
                    welcomeDealId: id,
                }),
            );
        }

        setIsModalVisible(false);
    }, [dispatch, selectedRow]);

    useEffect(() => {
        if (isModalVisible && trackAlfaMetrics) {
            trackAlfaMetrics(CREDITSB_METRICS.showRequestModalWindow, {
                clientStatus: selectedRow.clientStatus,
                description: selectedRow.description,
                isAvailableForCurrentChannel: selectedRow.isAvailableForCurrentChannel,
                type: selectedRow.type,
            });
        }
    }, [isModalVisible, selectedRow, trackAlfaMetrics]);

    return {
        isModalVisible,
        selectedRow,
        handleMmbRedirect,
        handleRowClick,
        handleCancelModal,
        handleContinueModal,
        handleDeleteWelcome,
        handleDescriptionClick,
        setIsModalVisible,
    };
};
