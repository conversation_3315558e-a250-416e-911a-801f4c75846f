import { type RefObject, useEffect, useRef } from 'react';

export const useOutsideClick = (callback: () => void, refFilter: RefObject<HTMLDivElement>) => {
    const ref = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent | TouchEvent) => {
            if (refFilter.current?.contains(event.target as Node)) {
                return;
            }
            if (ref.current && !ref.current.contains(event.target as Node)) {
                callback();
            }
        };

        document.addEventListener('mouseup', handleClickOutside);
        document.addEventListener('touchend', handleClickOutside);

        return () => {
            document.removeEventListener('mouseup', handleClickOutside);
            document.removeEventListener('touchend', handleClickOutside);
        };
    }, [callback, refFilter]);

    return ref;
};
