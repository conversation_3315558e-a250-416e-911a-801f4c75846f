import { type MutableRefObject } from 'react';
import { useHistory, useLocation } from 'react-router';
import qs from 'qs';

import { HEIGHT_HEADER } from '#/src/constants/layout-values';

export const useScrollToRefWithAnchor = (
    ref: MutableRefObject<HTMLDivElement | null>,
    targetAnchor = '',
) => {
    const location = useLocation();
    const history = useHistory();
    const { anchor, ...otherQuery } = qs.parse(location.search, { ignoreQueryPrefix: true });
    const needScrollToRef = anchor === targetAnchor;

    return () => {
        if (needScrollToRef && ref?.current) {
            const topY = ref.current.getBoundingClientRect().top + window.scrollY - HEIGHT_HEADER;

            window.scrollTo(0, topY);
            const newQueryString = qs.stringify(otherQuery, { addQueryPrefix: true });

            history.replace(location.pathname + newQueryString);
        }
    };
};
