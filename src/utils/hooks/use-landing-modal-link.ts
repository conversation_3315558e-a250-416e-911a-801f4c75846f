import { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { getEventBus, NIB_EVENT_BUS_KEY } from '@corp-front/client-event-bus';

import { DIGITAL_SALES_LANDING_MODAL_SET_LANDING_DATA_EVENT_NAME } from '#/src/constants/digital-sales';
import { digitalSalesLandingsSelector } from '#/src/ducks/settings/selectors';

import { type TMappedCreditOffer } from '../credit-offers-mappers';

export function useLandingModalLink<T>(
    productInfo: TMappedCreditOffer,
    callback?: (data: T) => void,
) {
    const digitalSalesLandings = useSelector(digitalSalesLandingsSelector);

    const { type, campaignCode = '' } = productInfo;

    const onLandingModalLinkClick = useCallback(
        (e: T) => {
            const landingId = digitalSalesLandings[type] || digitalSalesLandings[campaignCode];

            if (landingId) {
                const eventBus = getEventBus(NIB_EVENT_BUS_KEY);

                eventBus?.dispatchEvent(DIGITAL_SALES_LANDING_MODAL_SET_LANDING_DATA_EVENT_NAME, {
                    landingId,
                });
            } else {
                callback?.(e);
            }
        },
        [callback, campaignCode, digitalSalesLandings, type],
    );

    return onLandingModalLinkClick;
}
