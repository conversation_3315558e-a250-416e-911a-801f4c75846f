import { useCallback } from 'react';
import { useSelector } from 'react-redux';

import { trackNewAlfaMetrics } from 'arui-private/lib/alfa-metrics';

import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { userSelector } from '#/src/ducks/user/selectors';
import type TMetric from '#/src/metrics/metric';
import { getCanTrackMetrics } from '#/src/sagas/workers/alfa-metrics-worker';

import { useHoldingMetrics } from './use-holding-metrics';

export function useTrackAlfaMetrics() {
    const organizationId = useSelector(currentOrganizationEqIdSelector);
    const user = useSelector(userSelector);

    const { holdingData, holdingDimensions } = useHoldingMetrics();

    const trackAlfaMetrics = useCallback(
        <
            Metric extends TMetric,
            AdditionalParams extends Metric['dimensionsMapping'] extends Record<infer Key, any>
                ? [Key] extends [never]
                    ? undefined
                    : Record<keyof Metric['dimensionsMapping'], any>
                : never,
        >(
            metric: Metric,
            additionalData: AdditionalParams,
        ) => {
            if (!getCanTrackMetrics()) return;

            trackNewAlfaMetrics(
                {
                    ...metric,
                    dimensionsMapping: {
                        ...holdingDimensions,
                        ...metric.dimensionsMapping,
                    },
                },
                organizationId,
                {
                    userId: user.id,
                    ...holdingData,
                    ...additionalData,
                },
            );
        },
        [holdingData, holdingDimensions, organizationId, user.id],
    );

    return trackAlfaMetrics;
}
