import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Tab, Tabs } from '@alfalab/core-components/tabs';

import { getTrancheCreditProductsStart } from '#/src/ducks/credit-products/actions';
import {
    isAllCreditProductsTranchesSelector,
    isAllReceivedCreditProductsTranchesSelector,
    isClosedCreditProductsTranchesSelector,
    isCreditProductsTranchesFetchingSelector,
} from '#/src/ducks/credit-products/selectors/tranches.selectors';

import './use-tranches-tabs.css';

const cn = createCn('tranches-tabs');

enum ETabs {
    all = 'all',
    opened = 'opened',
    closed = 'closed',
}

type TProps = {
    errorText: string;
};

export const useTranchesTabs = ({ errorText }: TProps) => {
    const isAllCreditProductsTranches = useSelector(isAllCreditProductsTranchesSelector);
    const isClosedCreditProductsTranches = useSelector(isClosedCreditProductsTranchesSelector);
    const isFetchingTranches = useSelector(isCreditProductsTranchesFetchingSelector);
    const isAllReceivedCreditProductsTranches = useSelector(
        isAllReceivedCreditProductsTranchesSelector,
    );

    const [currentTab, setCurrentTab] = useState<ETabs>(
        isAllCreditProductsTranches
            ? ETabs.all
            : isClosedCreditProductsTranches
              ? ETabs.closed
              : ETabs.opened,
    );

    const dispatch = useDispatch();

    useEffect(() => {
        setCurrentTab(
            isAllCreditProductsTranches
                ? ETabs.all
                : isClosedCreditProductsTranches
                  ? ETabs.closed
                  : ETabs.opened,
        );
    }, [isAllCreditProductsTranches, isClosedCreditProductsTranches]);

    const getTitleForErrorState = () => {
        switch (currentTab) {
            case ETabs.all:
                return `У вас нет ${errorText}`;
            case ETabs.opened:
                return `У вас нет действующих ${errorText}`;
            case ETabs.closed:
                return `У вас нет закрытых ${errorText}`;
            default:
        }
    };

    const handleScrollReachListEnd = useCallback(() => {
        if (!isFetchingTranches && !isAllReceivedCreditProductsTranches) {
            dispatch(
                getTrancheCreditProductsStart({
                    isFromFirstPage: false,
                    isAll: isAllCreditProductsTranches,
                    isClosed: isClosedCreditProductsTranches,
                }),
            );
        }

        return null;
    }, [
        dispatch,
        isAllCreditProductsTranches,
        isAllReceivedCreditProductsTranches,
        isClosedCreditProductsTranches,
        isFetchingTranches,
    ]);

    const tabClickHandler = useCallback(
        (_, { selectedId }) => {
            setCurrentTab(selectedId);

            switch (selectedId) {
                case ETabs.all:
                    dispatch(getTrancheCreditProductsStart({ isAll: true, isFromTab: true }));
                    break;
                case ETabs.opened:
                    dispatch(getTrancheCreditProductsStart({ isFromTab: true }));
                    break;
                case ETabs.closed:
                    dispatch(
                        getTrancheCreditProductsStart({
                            isClosed: true,
                            isFromTab: true,
                        }),
                    );
                    break;
                default:
            }
        },
        [dispatch],
    );

    const renderTabs = () => (
        <Tabs
            selectedId={currentTab}
            onChange={tabClickHandler}
            size='xs'
            scrollable={true}
            className={cn()}
            tagView='filled'
            view='secondary'
        >
            <Tab disabled={isFetchingTranches} title='Все' id={ETabs.all} />
            <Tab disabled={isFetchingTranches} title='Действующие' id={ETabs.opened} />
            <Tab disabled={isFetchingTranches} title='Закрытые' id={ETabs.closed} />
        </Tabs>
    );

    return {
        getTitleForErrorState,
        handleScrollReachListEnd,
        renderTabs,
        isFetchingTranches,
    };
};
