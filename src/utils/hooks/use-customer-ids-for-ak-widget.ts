import { useSelector } from 'react-redux';
import { type AlfaCreditStatusResponse } from 'corp-credit-document-circulation-api-typescript-services';

import { type OptionShape } from '@alfalab/core-components/select/typings';

import { alfaCreditStatusSelector } from '#/src/ducks/credit-products-main-menu/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';

import { useHoldingControls } from './use-holding-controls';

const getCustomerIdsForGroupWithAkConnect = (
    options: OptionShape[],
    checks: AlfaCreditStatusResponse['checks'],
) => {
    // Функция, в которой мы проверяем группу компаний на прохождение проверки на подключение к АК
    const result: string[] = [];

    // Проходим по всем компаниям, смотрим, какие из них прошли проверку
    options.forEach((item) => {
        const match = checks?.find((check) => check.customerId === item.key);

        if (match && match.check === 'OK') {
            result.push(item.key);
        }
    });

    return result;
};

const useCustomerIdsForAkWidget = () => {
    const { isHolding, isShowCompanyFilter, options } = useHoldingControls();

    const organizationId = useSelector(currentOrganizationEqIdSelector);
    const alfaCreditStatus = useSelector(alfaCreditStatusSelector);

    if (!alfaCreditStatus) {
        return [];
    }

    if (!isHolding) {
        // Если у нас старое меню, в таком случае нам нужно передать все компании,
        // которые проходят проверку и дают "OK"
        const customerIds = alfaCreditStatus?.checks
            ?.filter((item) => item.check === 'OK')
            .map((item) => item.customerId);

        return customerIds ?? [];
    }

    if (!isShowCompanyFilter) {
        // Если у нас новое меню и выбрана не группа, а одна компания,
        // то в таком случае нам нужно передать одну компанию, если она прошла проверку
        const customer = alfaCreditStatus?.checks?.find(
            (check) => check.customerId === organizationId,
        );

        return [customer && customer.check === 'OK' ? customer.customerId : ''];
    }

    // Если у нас новое меню и выбрана группа, в таком случае нам нужно
    // передать компании, которые прошли проверку и дают "OK"
    return getCustomerIdsForGroupWithAkConnect(options, alfaCreditStatus?.checks);
};

export { useCustomerIdsForAkWidget };
