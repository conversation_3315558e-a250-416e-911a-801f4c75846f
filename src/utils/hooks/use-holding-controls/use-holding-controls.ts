import { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { type OptionShape } from '@alfalab/core-components/select/typings';

import { isMmbCategoryMainMenuSelector } from '#/src/ducks/credit-products-main-menu/selectors';
import {
    holdingGroupIdSelector,
    holdingGroupOrganizationsSelector,
    holdingGroupSelector,
} from '#/src/ducks/holding-group/selectors';
import {
    currentOrganizationEqIdSelector,
    organizationsListSelector,
} from '#/src/ducks/organization/selectors';
import { isHoldingGroupSharedSidePanelSelector } from '#/src/ducks/shared/selectors';
import { type TCustomCustomer } from '#/src/types/organization';

const sortOptionsByContent = (a: OptionShape, b: OptionShape) =>
    (a?.value ?? '').localeCompare(b?.value ?? '');

const organizationToOption = (organization: TCustomCustomer): OptionShape => ({
    key: organization?.eqId ?? '',
    value: organization?.organizationName?.shortName ?? '',
    content: organization?.organizationName?.shortName ?? '',
});

const getSortedOptions = (
    organizations: TCustomCustomer[],
    filterPredicate: (org: TCustomCustomer) => boolean = () => true,
) => organizations.filter(filterPredicate).map(organizationToOption).sort(sortOptionsByContent);

export const useHoldingControls = () => {
    const isHolding = useSelector(isHoldingGroupSharedSidePanelSelector);
    const isMmbCategoryMainMenu = useSelector(isMmbCategoryMainMenuSelector);
    const organizationId = useSelector(currentOrganizationEqIdSelector);
    const organizationsList = useSelector(organizationsListSelector);

    const value = useSelector(holdingGroupSelector);
    const holdingGroupId = useSelector(holdingGroupIdSelector);
    const holdingGroupOrganizations = useSelector(holdingGroupOrganizationsSelector);

    const isGroup = !!holdingGroupId;
    const isShowCompanyFilter = isGroup || (!isHolding && !isMmbCategoryMainMenu);
    const isShowCompanyName = isHolding ? isGroup : true;

    const sortedOptions = useMemo(
        () =>
            isGroup
                ? getSortedOptions(
                      organizationsList,
                      (org) => !!holdingGroupOrganizations?.includes(org.eqId),
                  )
                : getSortedOptions(organizationsList),
        [isGroup, holdingGroupOrganizations, organizationsList],
    );

    const initialCompanyValue = useMemo(() => {
        const selectedOption = sortedOptions.find((option) => option.key === organizationId);

        return [selectedOption ?? sortedOptions[0]];
    }, [organizationId, sortedOptions]);

    const options = isGroup || !isHolding ? sortedOptions : initialCompanyValue;

    return {
        value,
        options,
        isShowCompanyFilter,
        isShowCompanyName,
        isHolding,
        isGroup,
        initialCompanyValue,
    };
};
