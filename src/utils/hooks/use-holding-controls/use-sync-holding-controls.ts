import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
    type HoldingControlsProps,
    useHoldingControls as useHoldingControlsBase,
} from '@corp-front/holding-controls';
import isEqual from 'lodash/isEqual';

import { resetHoldingGroup, setHoldingGroup } from '#/src/ducks/holding-group/actions';
import { holdingGroupOrganizationsSelector } from '#/src/ducks/holding-group/selectors';

import packageJson from '../../../../package.json';

/**
 * Хук для синхронизации данных `useHoldingControls` из `@corp-front/holding-controls` с Redux
 * */

export const useSyncHoldingControls = (props?: Partial<HoldingControlsProps>) => {
    const dispatch = useDispatch();
    const holdingGroupOrganizations = useSelector(holdingGroupOrganizationsSelector);

    const value = useHoldingControlsBase({
        projectName: packageJson.name,
        ...props,
    });

    const groupId = value?.groupInfo?.groupId;
    const groupOrganizationsIds = value?.groupOrganizationsIds;

    useEffect(() => {
        if (!isEqual(holdingGroupOrganizations, groupOrganizationsIds)) {
            if (groupId) {
                dispatch(setHoldingGroup(value));
            } else {
                dispatch(resetHoldingGroup());
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [groupId, groupOrganizationsIds]);

    return value;
};
