import qs from 'qs';

import { ECreditProducts } from '#/src/constants/credit-products';
import { getCurrentPathByProductType } from '#/src/containers/main-page/main-page-products/main-page-credit-products/products-view/utils';

export const useHrefForProductViewItem = ({
    docNumber,
    customerId,
    dashboard,
    account,
    type,
}: {
    docNumber: string;
    customerId: string;
    dashboard: string;
    account: string | undefined;
    type: ECreditProducts;
}) => {
    if (type === ECreditProducts.CREDIT_CARD && !!account) return `${dashboard}/account/${account}`;

    const path = getCurrentPathByProductType(type) ?? '';
    const href = window.location.href.replace(/\/$/, '');

    return `${href}${path}${qs.stringify(
        {
            docNumber,
            customerId,
        },
        { addQueryPrefix: true },
    )}`;
};
