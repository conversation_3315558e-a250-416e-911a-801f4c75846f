import { useSelector } from 'react-redux';

import {
    alfaCreditStatusSelector,
    isMmbCategoryMainMenuSelector,
} from '#/src/ducks/credit-products-main-menu/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';

import { useHoldingControls } from './use-holding-controls';

const useIsVisibleAlfaCreditWidgetMainMenuButton = () => {
    const { isHolding, isShowCompanyFilter, options } = useHoldingControls();

    const alfaCreditStatus = useSelector(alfaCreditStatusSelector);
    const organizationId = useSelector(currentOrganizationEqIdSelector);
    const isMmbCategoryMainMenu = useSelector(isMmbCategoryMainMenuSelector);

    if (!alfaCreditStatus || (isMmbCategoryMainMenu && !isHolding)) {
        return false;
    }

    if (!isHolding) {
        // Если у нас старое меню, в таком случае нам нужно отобразить вкладку,
        // если хотя бы одна компания прошла проверку на подключение к АК
        if (alfaCreditStatus.generalSuccessful) return true;

        const result = alfaCreditStatus.checks?.some(({ check }) => check === 'OK');

        return !!result;
    }

    if (isShowCompanyFilter) {
        // Если у нас новое меню и выбрана группа, в таком случае нам нужно отобразить вкладку,
        // если хотя бы одна компания из этой группы прошла проверку
        const result = alfaCreditStatus.checks?.some(
            (item) =>
                options.some((option) => option.key === item.customerId) && item.check === 'OK',
        );

        return !!result;
    }

    // Если у нас новое меню и выбрана не группа, а одна компания,
    // то в таком случае нам нужно отобразить вкладку, если эта компания прошла проверку
    const result = alfaCreditStatus.checks?.find(
        (item) => item.customerId === organizationId && item.check === 'OK',
    );

    return !!result;
};

export { useIsVisibleAlfaCreditWidgetMainMenuButton };
