import { useSelector } from 'react-redux';
import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';
import { accountsSelector } from '#/src/ducks/accounts/selectors';
import { isEarlyRepaymentRequestAlreadyCreatedSelector } from '#/src/ducks/credit-processing/selectors';
import { earlyRepaymentButtonStateSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { isPaymentScheduleFetchingSelector } from '#/src/ducks/payment-schedule/selectors';

import { getEarlyRepaymentButtonState } from '../early-pay';

export const useIsEarlyRepaymentButtonDisabled = (tranche: CreditProduct) => {
    const creditProductEarlyRepaymentButtonState = useSelector(earlyRepaymentButtonStateSelector);
    const accounts = useSelector(accountsSelector);
    const trancheEarlyRepaymentButtonState = getEarlyRepaymentButtonState(tranche);
    const isEarlyRepaymentRequestAlreadyCreated = useSelector(
        isEarlyRepaymentRequestAlreadyCreatedSelector(tranche?.docNumber ?? ''),
    );
    const isCreditPaymentsFetching = useSelector(isPaymentScheduleFetchingSelector);

    return (
        trancheEarlyRepaymentButtonState !== earlyRepaymentButtonStates.enabled ||
        creditProductEarlyRepaymentButtonState !== earlyRepaymentButtonStates.enabled ||
        isEarlyRepaymentRequestAlreadyCreated ||
        !accounts.length ||
        isCreditPaymentsFetching
    );
};
