import { useCallback, useEffect, useState } from 'react';
import {
    type AbstractAppEventBus,
    type AbstractKnownEventTypes,
    getEventBus,
    NIB_EVENT_BUS_KEY,
    type NibEventTypes,
} from '@corp-front/client-event-bus';

export function useEventBusValue<
    EventTypes extends AbstractKnownEventTypes,
    Event extends keyof EventTypes,
>(eventName: Event, eventBus?: AbstractAppEventBus<EventTypes>): EventTypes[Event] | undefined {
    const [nibEventBus, setNibEventBus] = useState<AbstractAppEventBus<EventTypes> | undefined>(
        eventBus,
    );
    const [lastValue, setLastValue] = useState<EventTypes[Event] | undefined>();

    const eventCallback = useCallback((event: CustomEvent<EventTypes[Event]>) => {
        setLastValue(event.detail);
    }, []);

    useEffect(() => {
        if (eventBus) {
            setLastValue(eventBus.getLastEventDetail?.(eventName));
        } else {
            const newNibEventBus = getEventBus(NIB_EVENT_BUS_KEY);

            setNibEventBus(newNibEventBus);
            setLastValue(newNibEventBus?.getLastEventDetail?.(eventName as keyof NibEventTypes));
        }
    }, [eventBus, eventName]);

    useEffect(() => {
        nibEventBus?.addEventListener(eventName as keyof EventTypes, eventCallback);

        return () => {
            nibEventBus?.removeEventListener(eventName as keyof EventTypes, eventCallback);
        };
    }, [nibEventBus, eventName, eventCallback]);

    return lastValue;
}
