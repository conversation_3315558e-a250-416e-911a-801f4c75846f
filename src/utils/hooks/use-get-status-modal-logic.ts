import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';
import { fromUnixTime } from 'date-fns';

import {
    EProgressStageType,
    ERequestsStatusName,
} from '#/src/constants/credit-document-circulation';
import { DATE_FORMAT } from '#/src/constants/date';
import { getProgressStagesStart } from '#/src/ducks/credit-requests/actions';
import {
    isAvailableProgressStagesSelector,
    progressStagesFetchingSelector,
    singleProductStageSelector,
    sortedProgressStagesSelector,
} from '#/src/ducks/credit-requests/selectors';
import { type TCreditRequestListItem } from '#/src/ducks/credit-requests/types';
import { CREDITSB_METRICS } from '#/src/metrics';
import { getShortRequestNumber } from '#/src/utils/credit-request';
import { dateToCustomFormat } from '#/src/utils/date';

import { useTrackAlfaMetrics } from './use-track-alfa-metrics';

export const useGetStatusModalLogic = (data: TCreditRequestListItem, currentTime: Date) => {
    const { id, type, clientStatus, createDt, description, isAvailableForCurrentChannel } = data;
    const dispatch = useDispatch();
    const trackAlfaMetrics = useTrackAlfaMetrics();

    const isFetching = useSelector(progressStagesFetchingSelector);
    const isAvailableProgressStages = useSelector(isAvailableProgressStagesSelector);
    const singleProgressStage = useSelector(singleProductStageSelector);
    const progressStages = useSelector(sortedProgressStagesSelector);

    const currentActiveStep = useMemo(
        () =>
            Math.max(
                1,
                progressStages.findIndex(
                    (stage) => stage.progressStage === EProgressStageType.CURRENT,
                ) + 1,
            ),
        [progressStages],
    );

    const currentProgressStage = progressStages.find(
        (stage) => stage.progressStage === EProgressStageType.CURRENT,
    );
    const isLastStage = currentActiveStep === progressStages.length;
    const isCreditRequestApproved = isAvailableProgressStages && isLastStage;
    const isWelcome =
        type === CommonCreditRequestType.Welcome && clientStatus === ERequestsStatusName.FAILURE;
    const shortRequestNumber = getShortRequestNumber(id, type);
    const formattedDate = useMemo(
        () =>
            createDt
                ? dateToCustomFormat(currentTime, fromUnixTime(Number(createDt)), DATE_FORMAT)
                : null,
        [createDt, currentTime],
    );

    const handleStepPositive = (): boolean => {
        if (isLastStage) {
            return true;
        }

        return false;
    };

    useEffect(() => {
        if (id && !isWelcome) {
            dispatch(getProgressStagesStart(id));
        }
    }, [dispatch, id, isWelcome]);

    useEffect(() => {
        if (!isFetching && isAvailableProgressStages && trackAlfaMetrics) {
            trackAlfaMetrics(CREDITSB_METRICS.showProgressBarInfo, {
                clientStatus: currentProgressStage?.status?.clientStatusName || clientStatus,
                description: currentProgressStage?.status?.clientDescription || description,
                isAvailableForCurrentChannel,
                type,
                stageName: currentProgressStage?.stageName,
                sla: currentProgressStage?.sla,
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isAvailableProgressStages, isFetching, currentProgressStage, trackAlfaMetrics]);

    return {
        isFetching,
        isAvailableProgressStages,
        singleProgressStage,
        progressStages,
        currentActiveStep,
        isCreditRequestApproved,
        shortRequestNumber,
        formattedDate,
        clientStatus,
        description,
        handleStepPositive,
    };
};
