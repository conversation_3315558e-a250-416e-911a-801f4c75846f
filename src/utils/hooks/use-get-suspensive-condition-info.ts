import { useSelector } from 'react-redux';
import {
    MonitoringOperationStatus,
    type SuspensiveCondition,
} from 'corp-credit-products-api-typescript-services';

import { DATE_FORMAT } from '@alfalab/core-components/date-input';
import { useMatchMedia } from '@alfalab/core-components/mq';

import { ESuspensiveConditionType } from '#/src/constants/suspensive-conditions';
import { currentTimeSelector } from '#/src/ducks/settings/selectors';
import {
    getLabelByProductType,
    getSuspensiveConditionStatus,
} from '#/src/view-utils/suspensive-conditions';

import { type SomeMappedProduct } from '../credit-products-mappers';
import { dateToCustomFormat, unixEpochToMilliseconds } from '../date';

const getCompleteLabel = (status: MonitoringOperationStatus | undefined) => {
    switch (status) {
        case MonitoringOperationStatus.Executed:
            return 'Выполнено';
        case MonitoringOperationStatus.Violated:
            return 'Срок истёк';
        default:
            return 'Выполнить до';
    }
};

export const useGetSuspensiveConditionInfo = (
    condition: SuspensiveCondition,
    product: SomeMappedProduct,
) => {
    const currentTime = useSelector(currentTimeSelector);
    const [isTablet] = useMatchMedia('--tablet-m');

    const { status, planDate, description = '', expirationDays = 0, type } = condition;

    const productType = product?.type ? getLabelByProductType(product.type) : '';
    const docNumber = product?.docNumber ? `№${product.docNumber}` : null;
    const completeLabel = getCompleteLabel(status);

    const statusLabel = status
        ? getSuspensiveConditionStatus({
              status,
              expirationDays,
              isTablet,
          })
        : null;

    const completeLabelDate = planDate
        ? `${completeLabel} ${dateToCustomFormat(
              currentTime,
              unixEpochToMilliseconds({ seconds: planDate }),
              DATE_FORMAT,
          )}`
        : null;

    const typeLable = ESuspensiveConditionType[type as keyof typeof ESuspensiveConditionType];

    const subTitleLabel = `${productType} ${docNumber}`;

    return {
        subTitleLabel,
        typeLable,
        completeLabelDate,
        statusLabel,
        description,
        status,
    };
};
