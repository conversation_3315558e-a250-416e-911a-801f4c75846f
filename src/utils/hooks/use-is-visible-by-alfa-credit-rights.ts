import { useSelector } from 'react-redux';

import { EUserRights } from '#/src/constants/rights';
import {
    currentOrganizationSelector,
    organizationsListSelector,
} from '#/src/ducks/organization/selectors';

import { useHoldingControls } from './use-holding-controls';

// Права, которые нам надо проверить
const requiredKeys = [
    EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW,
    EUserRights.ALFACREDIT_BANK_REQUEST_VIEW,
    EUserRights.ALFACREDIT_CREDIT_REQUEST_VIEW,
    EUserRights.ALFACREDIT_GUARANTEE_REQUEST_VIEW,
] as const;

type RequiredRightKey = (typeof requiredKeys)[number];
type ResultType = Record<RequiredRightKey, boolean>;

const getMonoResult = (organizationsList: ReturnType<typeof organizationsListSelector>) => {
    const result = {} as ResultType;

    // Если у нас старое меню, в таком случае нам нужно по правам вернуть true,
    // если хотя бы одна компания прошла проверку на подключение к АК
    requiredKeys.forEach((key) => {
        result[key] = organizationsList.some((org) => org?.rights?.[key]?.successful === true);
    });

    return result;
};

const getGroupHoldingResult = ({
    options,
    organizationsList,
}: {
    options: ReturnType<typeof useHoldingControls>['options'];
    organizationsList: ReturnType<typeof organizationsListSelector>;
}) => {
    // Собираем id организаций из группы
    const groupKeys = new Set(options.map((opt) => opt.key));

    // Фильтруем список организаций, оставляя только те, которые есть в группе
    const groupOrganizations = organizationsList.filter((org) => groupKeys.has(org.eqId));

    const result = {} as ResultType;

    // Если у нас новое меню и выбрана группа, в таком случае нам нужно по правам вернуть true,
    // если хотя бы одна организация из этой группы прошла проверку
    requiredKeys.forEach((key) => {
        result[key] = groupOrganizations.some((org) => org?.rights?.[key]?.successful === true);
    });

    return result;
};

const getMonoHoldingResult = ({
    organization,
}: {
    organization: ReturnType<typeof currentOrganizationSelector>;
}) => {
    const result = {} as ResultType;

    // Если у нас новое меню и выбрана не группа, а одна компания,
    // то в таком случае нам нужно отобразить вкладку, если эта компания прошла проверку
    requiredKeys.forEach((key) => {
        result[key] = !!organization?.rights?.[key]?.successful;
    });

    return result;
};

const useIsVisibleByAlfaCreditRights = () => {
    const { isHolding, isShowCompanyFilter, options } = useHoldingControls();
    const organizationsList = useSelector(organizationsListSelector);
    const currentOrganization = useSelector(currentOrganizationSelector);

    if (!isHolding) {
        return getMonoResult(organizationsList);
    }

    if (isShowCompanyFilter) {
        return getGroupHoldingResult({
            options,
            organizationsList,
        });
    }

    return getMonoHoldingResult({
        organization: currentOrganization,
    });
};

export { useIsVisibleByAlfaCreditRights };
