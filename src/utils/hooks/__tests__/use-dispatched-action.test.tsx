import { useDispatch } from 'react-redux';
import { renderHook } from '@testing-library/react';

import { useDispatchedAction } from '../use-dispatched-action';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
}));

describe('useDispatchedAction', () => {
    it('должен вызывать действие с переданными аргументами', () => {
        const dispatch = jest.fn();

        (useDispatch as jest.Mock).mockReturnValue(dispatch);

        const action = jest.fn();
        const { result } = renderHook(() => useDispatchedAction(action));

        const dispatchedAction = result.current;

        const args = [1, 2, 3];

        dispatchedAction(...args);

        expect(action).toHaveBeenCalledWith(...args);
        expect(dispatch).toHaveBeenCalledWith(action(...args));
    });

    it('должен вызывать callback, если он передан', () => {
        const dispatch = jest.fn();

        (useDispatch as jest.Mock).mockReturnValue(dispatch);

        const action = jest.fn();
        const callback = jest.fn();

        const { result } = renderHook(() => useDispatchedAction(action, { callback }));

        const dispatchedAction = result.current;

        const args = [1, 2, 3];

        dispatchedAction(...args);

        expect(callback).toHaveBeenCalledWith(...args);
    });
});
