import { act, renderHook } from '@testing-library/react';

import { useSwitcher } from '../use-switcher';

describe('useSwitcher', () => {
    it('должен возвращать значение по умолчанию', () => {
        const { result } = renderHook(() => useSwitcher());

        expect(result.current[0]).toBe(false);
    });

    it('должен включаться', () => {
        const { result } = renderHook(() => useSwitcher());
        const [, on] = result.current;

        act(() => {
            on();
        });

        expect(result.current[0]).toBe(true);
    });

    it('должен выключаться', () => {
        const { result } = renderHook(() => useSwitcher(true));
        const [, , off] = result.current;

        act(() => {
            off();
        });

        expect(result.current[0]).toBe(false);
    });

    it('должен переключаться', () => {
        const { result } = renderHook(() => useSwitcher());
        const [, , , toggle] = result.current;

        act(() => {
            toggle();
        });

        expect(result.current[0]).toBe(true);

        act(() => {
            toggle();
        });

        expect(result.current[0]).toBe(false);
    });
});
