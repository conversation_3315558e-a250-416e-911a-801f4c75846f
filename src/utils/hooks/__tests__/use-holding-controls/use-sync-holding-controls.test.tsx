import { useDispatch, useSelector } from 'react-redux';
import {
    type HoldingControlsProps,
    useHoldingControls as useHoldingControlsBase,
} from '@corp-front/holding-controls';
import { renderHook } from '@testing-library/react';

import { resetHoldingGroup, setHoldingGroup } from '#/src/ducks/holding-group/actions';
import { holdingGroupOrganizationsSelector } from '#/src/ducks/holding-group/selectors';

import packageJson from '../../../../../package.json';
import { useSyncHoldingControls } from '../../use-holding-controls';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('@corp-front/holding-controls', () => ({ useHoldingControls: jest.fn() }));

const mockUseDispatch = useDispatch as jest.MockedFunction<typeof useDispatch>;
const mockUseSelector = useSelector as jest.MockedFunction<typeof useSelector>;
const mockUseHoldingControlsBase = useHoldingControlsBase as jest.MockedFunction<
    typeof useHoldingControlsBase
>;

type MockValue = ReturnType<typeof useHoldingControlsBase>;
type GroupInfo = { groupId?: string; groupName?: string };

const createMockValue = (
    groupInfo: GroupInfo = {},
    overrides: Partial<MockValue> = {},
): MockValue => ({
    selectProps: {
        onChange: jest.fn(),
        selected: [],
        options: [],
        multiple: false,
    },
    groupOrganizationsIds: [],
    groupInfo,
    ...overrides,
});

describe(useSyncHoldingControls.name, () => {
    let dispatchMock: jest.Mock;

    beforeEach(() => {
        dispatchMock = jest.fn();
        mockUseDispatch.mockReturnValue(dispatchMock);
        mockUseSelector.mockImplementation((selector) => {
            if (selector === holdingGroupOrganizationsSelector) {
                return [];
            }

            return undefined;
        });
        mockUseHoldingControlsBase.mockReturnValue(createMockValue());
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should call useHoldingControlsBase with project name and props', () => {
        const props: Partial<HoldingControlsProps> = { shouldFireOnFirstChange: true };

        renderHook(() => useSyncHoldingControls(props));

        expect(mockUseHoldingControlsBase).toHaveBeenCalledWith({
            projectName: packageJson.name,
            ...props,
        });
    });

    describe('dispatch behavior', () => {
        it('should dispatch setHoldingGroup when groupId exists and organizations changed', () => {
            const mockValue = createMockValue(
                { groupId: '123' },
                {
                    groupInfo: {
                        groupId: 'test',
                    },
                    groupOrganizationsIds: ['org1', 'org2'],
                },
            );

            mockUseSelector.mockImplementation((selector) => {
                if (selector === holdingGroupOrganizationsSelector) {
                    return ['org1'];
                }

                return undefined;
            });

            mockUseHoldingControlsBase.mockReturnValue(mockValue);

            renderHook(() => useSyncHoldingControls());
            expect(dispatchMock).toHaveBeenCalledWith(setHoldingGroup(mockValue));
        });

        it('should dispatch resetHoldingGroup when groupId does not exist and organizations changed', () => {
            const mockValue = createMockValue(
                {},
                {
                    groupOrganizationsIds: ['org1', 'org2'],
                },
            );

            mockUseSelector.mockImplementation((selector) => {
                if (selector === holdingGroupOrganizationsSelector) {
                    return ['org1'];
                }

                return undefined;
            });

            mockUseHoldingControlsBase.mockReturnValue(mockValue);

            renderHook(() => useSyncHoldingControls());
            expect(dispatchMock).toHaveBeenCalledWith(resetHoldingGroup());
        });

        it('should not dispatch when organizations are the same', () => {
            const mockValue = createMockValue(
                { groupId: '123' },
                {
                    groupOrganizationsIds: ['org1', 'org2'],
                },
            );

            mockUseSelector.mockImplementation((selector) => {
                if (selector === holdingGroupOrganizationsSelector) {
                    return ['org1', 'org2'];
                }

                return undefined;
            });

            mockUseHoldingControlsBase.mockReturnValue(mockValue);

            renderHook(() => useSyncHoldingControls());
            expect(dispatchMock).not.toHaveBeenCalled();
        });
    });

    describe('group ID changes', () => {
        it('should update holding group when groupId and organizations change', () => {
            const initial = createMockValue(
                { groupId: '123' },
                {
                    groupOrganizationsIds: ['org1'],
                },
            );
            const updated = createMockValue(
                { groupId: '456' },
                {
                    groupOrganizationsIds: ['org2'],
                },
            );

            mockUseSelector.mockImplementation((selector) => {
                if (selector === holdingGroupOrganizationsSelector) {
                    return [];
                }

                return undefined;
            });

            mockUseHoldingControlsBase.mockReturnValueOnce(initial).mockReturnValueOnce(updated);

            const { rerender } = renderHook(() => useSyncHoldingControls());

            expect(dispatchMock).toHaveBeenCalledWith(setHoldingGroup(initial));

            dispatchMock.mockClear();
            rerender();
            expect(dispatchMock).toHaveBeenCalledWith(setHoldingGroup(updated));
        });

        it('should handle groupId change to null with organization changes', () => {
            const initial = createMockValue(
                { groupId: '123' },
                {
                    groupOrganizationsIds: ['org1'],
                },
            );
            const updated = createMockValue(
                {},
                {
                    groupOrganizationsIds: ['org2'],
                },
            );

            mockUseSelector.mockImplementation((selector) => {
                if (selector === holdingGroupOrganizationsSelector) {
                    return [];
                }

                return undefined;
            });

            mockUseHoldingControlsBase.mockReturnValueOnce(initial).mockReturnValueOnce(updated);

            const { rerender } = renderHook(() => useSyncHoldingControls());

            expect(dispatchMock).toHaveBeenCalledWith(setHoldingGroup(initial));

            dispatchMock.mockClear();
            rerender();
            expect(dispatchMock).toHaveBeenCalledWith(resetHoldingGroup());
        });
    });

    it('should return value from useHoldingControlsBase', () => {
        const mockValue = createMockValue({ groupId: '123' });

        mockUseHoldingControlsBase.mockReturnValue(mockValue);

        const { result } = renderHook(() => useSyncHoldingControls());

        expect(result.current).toEqual(mockValue);
    });
});
