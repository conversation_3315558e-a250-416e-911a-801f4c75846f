import { renderHook } from '@testing-library/react';

import { ECreditProducts } from '#/src/constants/credit-products';

import { useHrefForProductViewItem } from '../use-href-for-product-view-item';

const setWindowHref = (url: string) => {
    delete (window as any).location;
    (window as any).location = new URL(url);
};

describe('useHrefForProductViewItem', () => {
    beforeEach(() => {
        setWindowHref('http://localhost:8080/');
    });

    it('should return href with path and query params for CREDIT_LINE', () => {
        const { result } = renderHook(() =>
            useHrefForProductViewItem({
                docNumber: '075A2L',
                customerId: 'UABITA',
                dashboard: '/dashboard',
                account: undefined,
                type: ECreditProducts.CREDIT_LINE,
            }),
        );

        expect(result.current).toBe(
            'http://localhost:8080/credit-line?docNumber=075A2L&customerId=UABITA',
        );
    });

    it('should return href with /account path for CREDIT_CARD with account', () => {
        const { result } = renderHook(() =>
            useHrefForProductViewItem({
                docNumber: 'ignored',
                customerId: 'ignored',
                dashboard: '/dashboard',
                account: '*********',
                type: ECreditProducts.CREDIT_CARD,
            }),
        );

        expect(result.current).toBe('/dashboard/account/*********');
    });

    it('should return base url + path with query even if trailing slash is missing in href', () => {
        setWindowHref('http://localhost:8080');
        const { result } = renderHook(() =>
            useHrefForProductViewItem({
                docNumber: '000XYZ',
                customerId: 'FOO',
                dashboard: '/dashboard',
                account: undefined,
                type: ECreditProducts.OVERDRAFT,
            }),
        );

        expect(result.current).toBe(
            'http://localhost:8080/overdraft?docNumber=000XYZ&customerId=FOO',
        );
    });
});
