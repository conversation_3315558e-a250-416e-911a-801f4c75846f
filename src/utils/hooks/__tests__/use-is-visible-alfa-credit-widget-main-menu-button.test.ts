import { useSelector } from 'react-redux';
import { renderHook } from '@testing-library/react';

import { alfaCreditStatusSelector } from '#/src/ducks/credit-products-main-menu/selectors';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { useHoldingControls } from '#/src/utils/hooks/use-holding-controls';
import { useIsVisibleAlfaCreditWidgetMainMenuButton } from '#/src/utils/hooks/use-is-visible-alfa-credit-widget-main-menu-button';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/credit-products-main-menu/selectors', () => ({
    alfaCreditStatusSelector: jest.fn(),
}));

jest.mock('#/src/ducks/organization/selectors', () => ({
    currentOrganizationEqIdSelector: jest.fn(),
}));

jest.mock('#/src/utils/hooks/use-holding-controls', () => ({
    useHoldingControls: jest.fn(),
}));

describe('useIsVisibleAlfaCreditWidgetMainMenuButton', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return false if alfaCreditStatus is falsy', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector) return null;

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(false);
    });

    it('should return true if alfaCreditStatus.generalSuccessful is true and isHolding is false', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector) return { generalSuccessful: true };

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(true);
    });

    it('should return true if any check in alfaCreditStatus.checks has check === "OK" and isHolding is false', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector) return { checks: [{ check: 'OK' }] };

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(true);
    });

    it('should return true if isShowCompanyFilter is true and a group option passes the check', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector)
                return { checks: [{ customerId: '1', check: 'OK' }] };

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: true,
            options: [{ key: '1' }],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(true);
    });

    it('should return true if a single company passes the check when isShowCompanyFilter is false', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector)
                return { checks: [{ customerId: '2', check: 'OK' }] };
            if (selector === currentOrganizationEqIdSelector) return '2';

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(true);
    });

    it('should return false if no checks pass and no conditions are met', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === alfaCreditStatusSelector)
                return { checks: [{ customerId: '3', check: 'FAIL' }] };
            if (selector === currentOrganizationEqIdSelector) return '2';

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleAlfaCreditWidgetMainMenuButton());

        expect(result.current).toBe(false);
    });
});
