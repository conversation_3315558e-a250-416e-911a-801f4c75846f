import * as Redux from 'react-redux';
import { renderHook } from '@testing-library/react';

import { useCustomerIdsForAkWidget } from '../use-customer-ids-for-ak-widget';
import { useHoldingControls } from '../use-holding-controls';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('../use-holding-controls', () => ({
    useHoldingControls: jest.fn(),
}));

describe('useCustomerIdsForAkWidget', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return customerIds for non-holding menu when check is OK', () => {
        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: true,
            options: [],
        });

        (Redux.useSelector as jest.Mock)
            .mockReturnValueOnce('orgId') // currentOrganizationEqIdSelector
            .mockReturnValueOnce({
                checks: [
                    { customerId: 'id1', check: 'OK' },
                    { customerId: 'id2', check: 'FAIL' },
                    { customerId: 'id3', check: 'OK' },
                ],
            });

        const { result } = renderHook(() => useCustomerIdsForAkWidget());

        expect(result.current).toEqual(['id1', 'id3']);
    });

    it('should return customerId for holding menu and one company check is OK', () => {
        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: false,
            options: [],
        });

        (Redux.useSelector as jest.Mock)
            .mockReturnValueOnce('orgId') // currentOrganizationEqIdSelector
            .mockReturnValueOnce({
                checks: [{ customerId: 'orgId', check: 'OK' }],
            });

        const { result } = renderHook(() => useCustomerIdsForAkWidget());

        expect(result.current).toEqual(['orgId']);
    });

    it('should return empty customerId when holding and company check is not OK', () => {
        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: false,
            options: [],
        });

        (Redux.useSelector as jest.Mock)
            .mockReturnValueOnce('orgId') // currentOrganizationEqIdSelector
            .mockReturnValueOnce({
                checks: [{ customerId: 'orgId', check: 'FAIL' }],
            });

        const { result } = renderHook(() => useCustomerIdsForAkWidget());

        expect(result.current).toEqual(['']);
    });

    it('should return customerIds for holding menu with group and OK checks', () => {
        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: true,
            options: [
                { key: 'id1', content: 'Company 1' },
                { key: 'id2', content: 'Company 2' },
            ],
        });

        (Redux.useSelector as jest.Mock)
            .mockReturnValueOnce('orgId') // currentOrganizationEqIdSelector
            .mockReturnValueOnce({
                checks: [
                    { customerId: 'id1', check: 'OK' },
                    { customerId: 'id2', check: 'FAIL' },
                    { customerId: 'id3', check: 'OK' },
                ],
            });

        const { result } = renderHook(() => useCustomerIdsForAkWidget());

        expect(result.current).toEqual(['id1']);
    });

    it('should return empty array when no checks are OK', () => {
        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: true,
            options: [
                { key: 'id1', content: 'Company 1' },
                { key: 'id2', content: 'Company 2' },
            ],
        });

        (Redux.useSelector as jest.Mock)
            .mockReturnValueOnce('orgId') // currentOrganizationEqIdSelector
            .mockReturnValueOnce({
                checks: [
                    { customerId: 'id1', check: 'FAIL' },
                    { customerId: 'id2', check: 'FAIL' },
                ],
            });

        const { result } = renderHook(() => useCustomerIdsForAkWidget());

        expect(result.current).toEqual([]);
    });
});
