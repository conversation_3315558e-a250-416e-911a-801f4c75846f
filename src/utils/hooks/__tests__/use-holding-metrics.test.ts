import { renderHook } from '@testing-library/react';

import { useHoldingControls } from '../use-holding-controls';
import { useHoldingMetrics } from '../use-holding-metrics';

jest.mock('../use-holding-controls');

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/credit-products-main-menu/selectors', () => ({
    creditProductsMainMenuSelector: jest.fn(),
}));

const mockUseHoldingControls = useHoldingControls as jest.MockedFunction<typeof useHoldingControls>;

describe(useHoldingMetrics.name, () => {
    beforeEach(() => {
        mockUseHoldingControls.mockReturnValue({
            isHolding: false,
            options: [],
            value: {
                selectProps: {
                    selected: [],
                    options: [],
                    multiple: false,
                    onChange: () => {},
                },
                groupOrganizationsIds: [],
                groupInfo: {
                    groupName: undefined,
                    groupId: undefined,
                },
            },
            isShowCompanyFilter: false,
            isGroup: false,
            initialCompanyValue: [],
            isShowCompanyName: false,
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return off status when holding is disabled', () => {
        const { result } = renderHook(() => useHoldingMetrics());

        expect(result.current.holdingData).toEqual({
            holdingModeStatus: 'off',
        });
    });

    it('should return correct metrics when holding is enabled', () => {
        const mockOptions = Array.from({ length: 40 }, (_, i) => ({
            key: `upin${i}`,
            label: `Company ${i}`,
            content: `Content ${i}`,
        }));

        mockUseHoldingControls.mockReturnValue({
            isHolding: true,
            options: mockOptions,
            value: {
                selectProps: {
                    selected: mockOptions,
                    options: [],
                    multiple: false,
                    onChange: () => {},
                },
                groupOrganizationsIds: [],
                groupInfo: {
                    groupName: undefined,
                    groupId: undefined,
                },
            },
            isShowCompanyFilter: true,
            isGroup: true,
            initialCompanyValue: [],
            isShowCompanyName: false,
        });

        const { result } = renderHook(() => useHoldingMetrics());

        expect(result.current.holdingData).toEqual({
            holdingModeStatus: 'on',
            holdingCompanyCount: '40',
            selectedCompaniesCount: '40',
            selectedUpinsPart1: mockOptions
                .slice(0, 36)
                .map((o) => o.key)
                .join(','),
            selectedUpinsPart2: mockOptions
                .slice(36, 40)
                .map((o) => o.key)
                .join(','),
        });
    });

    it('should handle edge cases correctly', () => {
        mockUseHoldingControls.mockReturnValue({
            isHolding: true,
            options: [],
            value: {
                selectProps: {
                    selected: [],
                    options: [],
                    multiple: false,
                    onChange: () => {},
                },
                groupOrganizationsIds: [],
                groupInfo: {
                    groupName: undefined,
                    groupId: undefined,
                },
            },
            isShowCompanyFilter: true,
            isGroup: true,
            initialCompanyValue: [],
            isShowCompanyName: false,
        });

        const { result, rerender } = renderHook(() => useHoldingMetrics());

        expect(result.current.holdingData).toEqual({
            holdingModeStatus: 'on',
            holdingCompanyCount: '0',
            selectedCompaniesCount: '0',
            selectedUpinsPart1: '',
            selectedUpinsPart2: '',
        });

        const options = Array.from({ length: 10 }, (_, i) => ({
            key: `upin${i}`,
            label: `Company ${i}`,
            content: `Content ${i}`,
        }));

        mockUseHoldingControls.mockReturnValue({
            isHolding: true,
            options,
            value: {
                selectProps: {
                    selected: [],
                    options: [],
                    multiple: false,
                    onChange: () => {},
                },
                groupOrganizationsIds: [],
                groupInfo: {
                    groupName: undefined,
                    groupId: undefined,
                },
            },
            isShowCompanyFilter: true,
            isGroup: true,
            initialCompanyValue: [],
            isShowCompanyName: false,
        });

        rerender();

        expect(result.current.holdingData).toEqual({
            holdingModeStatus: 'on',
            holdingCompanyCount: '10',
            selectedCompaniesCount: '10',
            selectedUpinsPart1: options.map(({ key }) => key).join(','),
            selectedUpinsPart2: '',
        });
    });

    it('should memoize results properly', () => {
        const options = Array.from({ length: 10 }, (_, i) => ({
            key: `upin${i}`,
            label: `Company ${i}`,
            content: `Content ${i}`,
        }));

        const selectProps = {
            selected: [],
            options: [],
            multiple: false,
            onChange: () => {},
        };

        const initialProps = {
            isHolding: true,
            options,
            value: {
                selectProps,
                groupOrganizationsIds: [],
                groupInfo: {
                    groupName: undefined,
                    groupId: undefined,
                },
            },
            isShowCompanyFilter: true,
            isGroup: true,
            initialCompanyValue: [],
            isShowCompanyName: false,
        };

        mockUseHoldingControls.mockReturnValue(initialProps);
        const { result, rerender } = renderHook(() => useHoldingMetrics());
        const firstResult = result.current;

        mockUseHoldingControls.mockReturnValue(initialProps);
        rerender();

        expect(result.current).toStrictEqual(firstResult);

        const changedOptions = [...options, { key: 'new', label: 'New', content: 'New' }];

        mockUseHoldingControls.mockReturnValue({
            ...initialProps,
            options: changedOptions,
            isShowCompanyName: false,
        });

        rerender();

        expect(result.current).not.toBe(firstResult);
        expect(result.current.holdingData.holdingCompanyCount).toBe('11');
    });

    it('should split upins into chunks correctly', () => {
        const testCases = [
            {
                input: Array.from({ length: 70 }, (_, i) => `UPIN${i}`),
                expected: { part1: 36, part2: 34 }, // 70 = 36 + 34
            },
            {
                input: Array.from({ length: 100 }, (_, i) => `C${i}`),
                expected: { part1: 36, part2: 36 }, // 100 = 36 + 36 + 28
            },
            {
                input: [],
                expected: { part1: 0, part2: 0 },
            },
        ];

        testCases.forEach(({ input, expected }) => {
            mockUseHoldingControls.mockReturnValue({
                isHolding: true,
                options: input.map((key) => ({ key, label: key, content: key })),
                value: {
                    selectProps: {
                        selected: [],
                        options: [],
                        multiple: false,
                        onChange: () => {},
                    },
                    groupOrganizationsIds: [],
                    groupInfo: {
                        groupName: undefined,
                        groupId: undefined,
                    },
                },
                isShowCompanyFilter: true,
                isGroup: true,
                initialCompanyValue: [],
                isShowCompanyName: false,
            });

            const { result } = renderHook(() => useHoldingMetrics());

            const part1 = result?.current?.holdingData?.selectedUpinsPart1
                ?.split(',')
                .filter(Boolean);
            const part2 = result?.current?.holdingData?.selectedUpinsPart2
                ?.split(',')
                .filter(Boolean);

            expect(part1?.length).toBe(expected.part1);
            expect(part2?.length).toBe(expected.part2);
        });
    });
});
