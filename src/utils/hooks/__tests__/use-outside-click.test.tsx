import React from 'react';
import { fireEvent, render } from '@testing-library/react';

import { useOutsideClick } from '#/src/utils/hooks/use-outside-click'; // for better assertions

describe('useOutsideClick', () => {
    const TestComponent = ({ callback }: { callback: () => void }) => {
        const divRef = { current: null };
        const ref = useOutsideClick(callback, divRef);

        return (
            <div>
                <div ref={divRef}>
                    <div ref={ref} data-testid='inside'>
                        Inside Element
                    </div>
                </div>
                <div data-testid='outside'>Outside Element</div>
            </div>
        );
    };

    it('should call callback when clicking outside the element', () => {
        const callback = jest.fn();
        const { getByTestId } = render(<TestComponent callback={callback} />);

        fireEvent.mouseUp(getByTestId('outside'));
        expect(callback).toHaveBeenCalledTimes(1);

        fireEvent.touchEnd(getByTestId('outside'));
        expect(callback).toHaveBeenCalledTimes(2);
    });

    it('should not call callback when clicking inside the element', () => {
        const callback = jest.fn();
        const { getByTestId } = render(<TestComponent callback={callback} />);

        fireEvent.mouseUp(getByTestId('inside'));
        expect(callback).not.toHaveBeenCalled();

        fireEvent.touchEnd(getByTestId('inside'));
        expect(callback).not.toHaveBeenCalled();
    });
});
