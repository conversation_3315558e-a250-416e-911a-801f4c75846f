import { useDispatch, useSelector } from 'react-redux';
import { renderHook } from '@testing-library/react';
import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';
import { type ProgressStageResponseDto } from 'crmmb-status-model-core-api-typescript-services';

import { getProgressStagesStart } from '#/src/ducks/credit-requests/actions';
import {
    type TCreditRequestListItem,
    type TCreditRequestProgressStagesResponse,
} from '#/src/ducks/credit-requests/types';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { useGetStatusModalLogic } from '../use-get-status-modal-logic';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/credit-requests/actions', () => ({
    getProgressStagesStart: jest.fn(),
}));

jest.mock('#/src/utils/hooks/use-track-alfa-metrics', () => ({
    useTrackAlfaMetrics: jest.fn(),
}));

describe('useGetStatusModalLogic', () => {
    let dispatch: jest.Mock;
    let trackAlfaMetrics: jest.Mock;
    let data: TCreditRequestListItem;
    let currentTime: Date;
    const mockSingleProgressStagesResponse: ProgressStageResponseDto = {
        stageOrder: 1,
        stageName: 'В АРХИВЕ',
        progressStage: 'CURRENT',
        sla: undefined,
        status: {
            clientStatusName: 'В АРХИВЕ',
            clientDescription: 'Заявка закрыта',
        },
    };
    const mockProgressStagesResponse: TCreditRequestProgressStagesResponse = [
        {
            stageOrder: 100,
            stageName: 'ЗАЯВКА НА КРЕДИТ',
            progressStage: 'DONE',
            sla: undefined,
            status: undefined,
        },
        {
            stageOrder: 200,
            stageName: 'РАССМОТРЕНИЕ ЗАЯВКИ',
            progressStage: 'DONE',
            sla: undefined,
            status: undefined,
        },
        {
            stageOrder: 300,
            stageName: 'КРЕДИТНОЕ ПРЕДЛОЖЕНИЕ',
            progressStage: 'DONE',
            sla: undefined,
            status: undefined,
        },
        {
            stageOrder: 400,
            stageName: 'ПОДТВЕРЖДЕНИЕ УСЛОВИЙ',
            progressStage: 'DONE',
            sla: undefined,
            status: undefined,
        },
        {
            stageOrder: 500,
            stageName: 'ПОДПИСАНИЕ',
            progressStage: 'DONE',
            sla: undefined,
            status: undefined,
        },
        {
            stageOrder: 600,
            stageName: 'КРЕДИТ ОФОРМЛЕН',
            progressStage: 'CURRENT',
            sla: new Date('2025-07-24T07:24:24.777Z'),
            status: {
                clientStatusName: 'ОДОБРЕНА',
                clientDescription:
                    'Сегодня кредит появится в ваших продуктах — им можно пользоваться сразу',
            },
        },
    ];

    beforeEach(() => {
        dispatch = jest.fn();
        (useDispatch as jest.Mock).mockReturnValue(dispatch);
        (useTrackAlfaMetrics as jest.Mock).mockReturnValue(trackAlfaMetrics);

        data = {
            id: 'd527bc3c-1ca7-40f6-9b06-05ef4ba061a4',
            type: CommonCreditRequestType.Mmb,
            createDt: 1731569318,
            description: 'Не смогли до вас дозвониться. Попробуйте оставить заявку еще раз',
            clientStatus: 'ОТКЛОНЕНА',
            isAvailableForCurrentChannel: false,
            productName: 'Заявка на кредит',
            productCode: 'LP_PLEDGE',
            redirect: [],
        };
        currentTime = new Date();
    });

    it('должен запрашивать прогресс-бар при монтировании', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isFetching
            .mockReturnValueOnce(true) // isAvailableProgressStages
            .mockReturnValueOnce(null) // singleProductStage
            .mockReturnValueOnce(mockProgressStagesResponse); // progressStages

        renderHook(() => useGetStatusModalLogic(data, currentTime));
        expect(dispatch).toHaveBeenCalledWith(
            getProgressStagesStart('d527bc3c-1ca7-40f6-9b06-05ef4ba061a4'),
        );
    });

    it('должен запрашивать прогресс-бар при монтировании при отсутствии id', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isFetching
            .mockReturnValueOnce(true) // isAvailableProgressStages
            .mockReturnValueOnce(null) // singleProductStage
            .mockReturnValueOnce(mockProgressStagesResponse); // progressStages

        renderHook(() => useGetStatusModalLogic({ ...data, id: undefined }, currentTime));
        expect(dispatch).not.toHaveBeenCalledWith(
            getProgressStagesStart('d527bc3c-1ca7-40f6-9b06-05ef4ba061a4'),
        );
    });

    it('должен корректно возвращать значения', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isFetching
            .mockReturnValueOnce(true) // isAvailableProgressStages
            .mockReturnValueOnce(null) // singleProductStage
            .mockReturnValueOnce(mockProgressStagesResponse); // progressStages

        const { result } = renderHook(() => useGetStatusModalLogic(data, currentTime));

        expect(result.current.isFetching).toBe(false);
        expect(result.current.isAvailableProgressStages).toBe(true);
        expect(result.current.singleProgressStage).toBe(null);
        expect(result.current.progressStages.length).toBe(6);
        expect(result.current.currentActiveStep).toBe(6);
        expect(result.current.isCreditRequestApproved).toBe(true);
        expect(result.current.shortRequestNumber).toBe('М‐**61a4');
        expect(result.current.formattedDate).toBe('14.11.2024');
        expect(result.current.clientStatus).toBe('ОТКЛОНЕНА');
        expect(result.current.description).toBe(
            'Не смогли до вас дозвониться. Попробуйте оставить заявку еще раз',
        );
    });

    it('должен возвращать true из handleStepPositive при последнем этапе', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isFetching
            .mockReturnValueOnce(true) // isAvailableProgressStages
            .mockReturnValueOnce(null) // singleProductStage
            .mockReturnValueOnce(mockProgressStagesResponse); // progressStages

        const { result } = renderHook(() => useGetStatusModalLogic(data, currentTime));

        expect(result.current.handleStepPositive()).toBe(true);
    });

    it('должен возвращать корректное значение для одиночного прогресс-бара', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isFetching
            .mockReturnValueOnce(true) // isAvailableProgressStages
            .mockReturnValueOnce(mockSingleProgressStagesResponse) // singleProductStage
            .mockReturnValueOnce([mockSingleProgressStagesResponse]); // progressStages

        const { result } = renderHook(() => useGetStatusModalLogic(data, currentTime));

        expect(result.current.isFetching).toBe(false);
        expect(result.current.isAvailableProgressStages).toBe(true);
        expect(result.current.singleProgressStage).toBe(mockSingleProgressStagesResponse);
        expect(result.current.progressStages.length).toBe(1);
        expect(result.current.currentActiveStep).toBe(1);
        expect(result.current.isCreditRequestApproved).toBe(true);
        expect(result.current.shortRequestNumber).toBe('М‐**61a4');
        expect(result.current.formattedDate).toBe('14.11.2024');
        expect(result.current.clientStatus).toBe('ОТКЛОНЕНА');
        expect(result.current.description).toBe(
            'Не смогли до вас дозвониться. Попробуйте оставить заявку еще раз',
        );
    });
});
