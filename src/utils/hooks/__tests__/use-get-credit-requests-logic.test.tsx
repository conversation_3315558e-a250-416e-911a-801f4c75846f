import { useDispatch, useSelector } from 'react-redux';
import { act, renderHook } from '@testing-library/react';
import {
    CommonCreditRequestType,
    RedirectListItemType,
} from 'corp-credit-request-api-typescript-services';

import { Status } from '#/src/constants/credit-document-circulation';
import { ECreditOffers } from '#/src/constants/credit-offers';
import { EProductCodes } from '#/src/constants/credit-products';
import { goToCreditFormsApp, initExternalRedirect } from '#/src/ducks/app/actions';
import { CREDITSB_METRICS } from '#/src/metrics';
import { useTrackAlfaMetrics } from '#/src/utils/hooks/use-track-alfa-metrics';

import { useGetCreditRequests } from '../use-get-credit-requests-logic';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/app/actions', () => ({
    initExternalRedirect: jest.fn(),
    goToCreditFormsApp: jest.fn(),
}));

jest.mock('#/src/ducks/credit-processing/actions', () => ({
    setOnlineSigningChannel: jest.fn(),
}));

jest.mock('#/src/utils/hooks/use-track-alfa-metrics', () => ({
    useTrackAlfaMetrics: jest.fn(),
}));

describe('useGetCreditRequests', () => {
    let dispatch: jest.Mock;
    let trackAlfaMetrics: jest.Mock;
    const setupSelectorMock = () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isOnlineSigningAvailableSelector
            .mockReturnValueOnce({
                status: Status.Active,
                products: [],
                company: [{ key: 'test-company' }], // creditRequestsFiltersSelector
            });
    };

    beforeEach(() => {
        jest.clearAllMocks();
        dispatch = jest.fn();
        trackAlfaMetrics = jest.fn();
        (useDispatch as jest.Mock).mockReturnValue(dispatch);
        (useTrackAlfaMetrics as jest.Mock).mockReturnValue(trackAlfaMetrics);
        setupSelectorMock();
    });

    const creditRequestsListMMB = [
        {
            id: 'ABR-FW-OPSFW-WORK-LOAN MB-85642',
            type: CommonCreditRequestType.Mmb,
            createDt: 1089576000000,
            clientStatus: 'На подпись',
            description: 'Подпишите кредитный договор и получите кредит',
            isAvailableForCurrentChannel: true,
            limit: {
                amount: 30000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUB',
                    minorUnits: 100,
                    unicodeSymbol: 'U+20BD',
                    fullName: 'Российский рубль',
                },
            },
        },
    ];

    const creditRequestsListSB = [
        {
            id: '016c14f7-dfd3-4c3a-908c-2764695869c1',
            type: CommonCreditRequestType.Limit,
            lmLimitId: undefined,
            createDt: 1738241300,
            description: 'Сохранили заявку — заполните её до конца',
            clientStatus: 'Черновик',
            isAvailableForCurrentChannel: undefined,
            productName: 'Заявка на кредит',
            productCode: undefined,
            productInfo: undefined,
            limit: {
                amount: 2000000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
            redirect: [
                {
                    redirectLink:
                        '/creditsb/016c14f7-dfd3-4c3a-908c-2764695869c1?utm_source=button5',
                    linkText: undefined,
                    type: RedirectListItemType.Card,
                },
                {
                    redirectLink:
                        '/creditsb/016c14f7-dfd3-4c3a-908c-2764695869c1/edit?step=conditions&utm_source=button5',
                    linkText: 'заполните её',
                    type: RedirectListItemType.Main,
                },
            ],
        },
    ];

    it('должен обработать клик по строке и переключить видимость модального окна', () => {
        const { result } = renderHook(() =>
            useGetCreditRequests({ creditRequestsList: creditRequestsListMMB }),
        );

        expect(result.current.selectedRow).toEqual({});
        expect(result.current.isModalVisible).toBe(false);

        act(() => {
            result.current.handleRowClick('ABR-FW-OPSFW-WORK-LOAN MB-85642');
            setupSelectorMock();
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(
            CREDITSB_METRICS.clickRequestDocument,
            expect.objectContaining({
                clientStatus: 'На подпись',
                description: 'Подпишите кредитный договор и получите кредит',
                type: 'MMB',
            }),
        );
        expect(result.current.selectedRow).toEqual(creditRequestsListMMB[0]);
        expect(result.current.isModalVisible).toBe(true);
    });

    it('должен выполнить внешний редирект для заявок, отличных от Mmb', () => {
        const { result } = renderHook(() =>
            useGetCreditRequests({ creditRequestsList: creditRequestsListSB }),
        );

        act(() => {
            result.current.handleRowClick('016c14f7-dfd3-4c3a-908c-2764695869c1');
            setupSelectorMock();
        });

        expect(dispatch).toHaveBeenCalled();
        expect(initExternalRedirect).toHaveBeenCalledWith({
            addContextRoot: false,
            link: '/creditsb/016c14f7-dfd3-4c3a-908c-2764695869c1?utm_source=button5',
            organizationId: 'test-company',
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(CREDITSB_METRICS.clickRequestDocument, {
            clientStatus: 'Черновик',
            description: 'Сохранили заявку — заполните её до конца',
            isAvailableForCurrentChannel: undefined,
            type: 'LIMIT',
        });
    });

    it('должен обработать клик по описанию для заявок Mmb', () => {
        const { result } = renderHook(() =>
            useGetCreditRequests({ creditRequestsList: creditRequestsListMMB }),
        );

        act(() => {
            result.current.handleDescriptionClick('ABR-FW-OPSFW-WORK-LOAN MB-85642');
        });

        expect(initExternalRedirect).not.toHaveBeenCalled();

        expect(dispatch).toHaveBeenCalled();

        expect(goToCreditFormsApp).toHaveBeenCalledWith({
            offerType: ECreditOffers.BUSINESS_CREDIT,
            organizationId: 'test-company',
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(CREDITSB_METRICS.clickRequestDescription, {
            status: 'На подпись',
            description: 'Подпишите кредитный договор и получите кредит',
            creditSBLists: 'CreditSBLists',
            url: '/credit-forms',
            urlName: 'Нажатие на поле описания объекта списка',
            sum: 30000000,
        });
    });

    it('должен обработать клик по описанию для заявок, отличных от Mmb', () => {
        const { result } = renderHook(() =>
            useGetCreditRequests({ creditRequestsList: creditRequestsListSB }),
        );

        act(() => {
            result.current.handleDescriptionClick('016c14f7-dfd3-4c3a-908c-2764695869c1');
        });

        expect(initExternalRedirect).toHaveBeenCalledWith({
            addContextRoot: false,
            link: '/creditsb/016c14f7-dfd3-4c3a-908c-2764695869c1/edit?step=conditions&utm_source=button5',
            organizationId: 'test-company',
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(CREDITSB_METRICS.clickRequestDescription, {
            status: 'Черновик',
            description: 'Сохранили заявку — заполните её до конца',
            creditSBLists: 'CreditSBLists',
            url: '/creditsb/016c14f7-dfd3-4c3a-908c-2764695869c1/edit?step=conditions&utm_source=button5',
            urlName: 'Нажатие на поле описания объекта списка',
            sum: 2000000000,
        });
    });

    it('должен выполнить редирект в e2e при вызове handleMmbRedirect', () => {
        const { result } = renderHook(() => useGetCreditRequests({ creditRequestsList: [] }));

        act(() => {
            result.current.handleMmbRedirect({ productCode: EProductCodes.BP11 });
        });

        expect(dispatch).toHaveBeenCalled();

        expect(goToCreditFormsApp).toHaveBeenCalledWith({
            offerType: ECreditOffers.BUSINESS_CREDIT,
            organizationId: 'test-company',
        });
    });

    it('должен обработать клик по закрытию модального окна', () => {
        const { result } = renderHook(() => useGetCreditRequests({ creditRequestsList: [] }));

        act(() => {
            result.current.handleCancelModal();
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(
            CREDITSB_METRICS.closeRequestModalWindow,
            expect.any(Object),
        );
    });

    it('должен обработать клик по кнопке действия в модальном окне', () => {
        const { result } = renderHook(() =>
            useGetCreditRequests({ creditRequestsList: creditRequestsListMMB }),
        );

        act(() => {
            result.current.handleRowClick('ABR-FW-OPSFW-WORK-LOAN MB-85642');
            setupSelectorMock();
        });

        expect(result.current.selectedRow).toEqual(creditRequestsListMMB[0]);
        expect(result.current.isModalVisible).toBe(true);

        trackAlfaMetrics(CREDITSB_METRICS.clickRequestDocument, {
            clientStatus: 'На подпись',
            description: 'Подпишите кредитный договор и получите кредит',
            isAvailableForCurrentChannel: true,
            type: 'MMB',
        });

        act(() => {
            result.current.handleContinueModal();
            setupSelectorMock();
        });

        expect(result.current.isModalVisible).toBe(false);

        expect(dispatch).toHaveBeenCalled();

        expect(goToCreditFormsApp).toHaveBeenCalledWith({
            offerType: ECreditOffers.BUSINESS_CREDIT,
            organizationId: 'test-company',
        });

        expect(trackAlfaMetrics).toHaveBeenCalledWith(
            CREDITSB_METRICS.clickActionRequestModalWindow,
            {
                clientStatus: 'На подпись',
                description: 'Подпишите кредитный договор и получите кредит',
                isAvailableForCurrentChannel: true,
                type: 'MMB',
            },
        );
    });
});
