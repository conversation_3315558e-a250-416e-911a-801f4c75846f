import { useSelector } from 'react-redux';
import { act, renderHook } from '@testing-library/react';

import { ECreditOffers } from '#/src/constants/credit-offers';
import { digitalSalesLandingsSelector } from '#/src/ducks/settings/selectors';
import { useLandingModalLink } from '#/src/utils/hooks/use-landing-modal-link';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/settings/selectors', () => ({
    digitalSalesLandingsSelector: jest.fn(),
}));

describe('useLandingModalLink', () => {
    const mockCallback = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should call callback if no landingId is found', () => {
        const mockProductInfo = {
            titlesVariants: {
                long: 'Sales banner long',
                short: 'Sales banner short',
            },
            type: ECreditOffers.BUSINESS_CREDIT,
            expiryDate: {
                seconds: 18142142,
            },
        };
        const mockDigitalSalesLandings = {};

        (useSelector as jest.Mock).mockImplementation(() => mockDigitalSalesLandings);
        (digitalSalesLandingsSelector as jest.Mock).mockReturnValue(mockDigitalSalesLandings);

        const { result } = renderHook(() => useLandingModalLink(mockProductInfo, mockCallback));

        const eventData = { someData: 'test' };

        act(() => {
            result.current(eventData);
        });

        expect(mockCallback).toHaveBeenCalledWith(eventData);
    });

    it('should handle callback being undefined', () => {
        const mockProductInfo = {
            titlesVariants: {
                long: 'Sales banner long',
                short: 'Sales banner short',
            },
            type: ECreditOffers.BUSINESS_CREDIT,
            expiryDate: {
                seconds: 18142142,
            },
        };
        const mockDigitalSalesLandings = {};

        (useSelector as jest.Mock).mockImplementation(() => mockDigitalSalesLandings);
        (digitalSalesLandingsSelector as jest.Mock).mockReturnValue(mockDigitalSalesLandings);

        const { result } = renderHook(() => useLandingModalLink(mockProductInfo));

        const eventData = { someData: 'test' };

        act(() => {
            result.current(eventData);
        });

        expect(mockCallback).not.toHaveBeenCalled();
    });
});
