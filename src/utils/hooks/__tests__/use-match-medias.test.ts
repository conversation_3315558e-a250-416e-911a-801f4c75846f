import { renderHook } from '@testing-library/react';

import { useMatchMedias } from '#/src/utils/hooks/use-match-medias';

jest.mock('@alfalab/core-components/mq/utils', () => {
    const mockMatches = jest.fn();

    return {
        getMatchMedia: jest.fn((query) => ({
            media: query,
            matches: mockMatches(),
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
        })),
        releaseMatchMedia: jest.fn(),
    };
});

const { getMatchMedia } = jest.requireMock('@alfalab/core-components/mq/utils');

describe('useMatchMedias', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return the default value if no queries are provided', () => {
        const { result } = renderHook(() => useMatchMedias([]));

        expect(result.current).toBe(false);
    });

    it('should return false if no query matches', () => {
        getMatchMedia.mockImplementation(() => ({
            media: '',
            matches: false,
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
        }));

        const { result } = renderHook(() =>
            useMatchMedias(['(max-width: 768px)', '(max-width: 1024px)']),
        );

        expect(result.current).toBe(false);
    });
});
