import { useDispatch, useSelector } from 'react-redux';
import { act, renderHook } from '@testing-library/react';

import { getTrancheCreditProductsStart } from '#/src/ducks/credit-products/actions';

import { useTranchesTabs } from '../use-tranches-tabs';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/credit-products/actions', () => ({
    getTrancheCreditProductsStart: jest.fn(),
}));

describe('useTranchesTabs', () => {
    let dispatch: jest.Mock;

    beforeEach(() => {
        dispatch = jest.fn();
        (useDispatch as jest.Mock).mockReturnValue(dispatch);
    });

    it('should select the correct tab based on selector values', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(true) // isAllCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isClosedCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isCreditProductsTranchesFetchingSelector
            .mockReturnValueOnce(false); // isAllReceivedCreditProductsTranchesSelector

        const { result } = renderHook(() => useTranchesTabs({ errorText: 'кредитных продуктов' }));

        expect(result.current.isFetchingTranches).toBe(false);
        expect(result.current.getTitleForErrorState()).toBe('У вас нет кредитных продуктов');
    });

    it('should dispatch the correct action when a tab is clicked', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isAllCreditProductsTranchesSelector
            .mockReturnValueOnce(true) // isClosedCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isCreditProductsTranchesFetchingSelector
            .mockReturnValueOnce(false); // isAllReceivedCreditProductsTranchesSelector

        const { result } = renderHook(() => useTranchesTabs({ errorText: 'кредитных продуктов' }));

        // Выбираем "Все"
        act(() => {
            result.current.renderTabs().props.onChange(null, { selectedId: 'all' });
        });

        expect(dispatch).toHaveBeenCalledWith(
            getTrancheCreditProductsStart({ isAll: true, isFromTab: true }),
        );

        // Выбираем "Закрытые"
        act(() => {
            result.current.renderTabs().props.onChange(null, { selectedId: 'closed' });
        });

        expect(dispatch).toHaveBeenCalledWith(
            getTrancheCreditProductsStart({ isClosed: true, isFromTab: true }),
        );
    });

    it('should not fetch more data if already fetching', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(false) // isAllCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isClosedCreditProductsTranchesSelector
            .mockReturnValueOnce(true) // isCreditProductsTranchesFetchingSelector
            .mockReturnValueOnce(false); // isAllReceivedCreditProductsTranchesSelector

        const { result } = renderHook(() => useTranchesTabs({ errorText: 'кредитных продуктов' }));

        act(() => {
            result.current.handleScrollReachListEnd();
        });

        expect(dispatch).not.toHaveBeenCalled();
    });

    it('should fetch more data when not fetching and the end of the list is reached', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(true) // isAllCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isClosedCreditProductsTranchesSelector
            .mockReturnValueOnce(false) // isCreditProductsTranchesFetchingSelector
            .mockReturnValueOnce(false); // isAllReceivedCreditProductsTranchesSelector

        const { result } = renderHook(() => useTranchesTabs({ errorText: 'кредитных продуктов' }));

        act(() => {
            result.current.handleScrollReachListEnd();
        });

        expect(dispatch).toHaveBeenCalledWith(
            getTrancheCreditProductsStart({
                isFromFirstPage: false,
                isAll: true,
                isClosed: false,
            }),
        );
    });
});
