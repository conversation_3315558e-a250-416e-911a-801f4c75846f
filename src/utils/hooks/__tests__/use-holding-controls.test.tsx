import * as Redux from 'react-redux';
import { renderHook } from '@testing-library/react';

import { useHoldingControls } from '../use-holding-controls';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

describe(useHoldingControls.name, () => {
    it('returns correct options when groupInfo groupId is defined', () => {
        jest.spyOn(Redux, 'useSelector')
            .mockReturnValueOnce(true)
            .mockReturnValueOnce(true)
            .mockReturnValueOnce('')
            .mockReturnValueOnce([
                { eqId: 'id1', organizationName: { shortName: 'Short name 1' } },
                { eqId: 'id2', organizationName: { shortName: 'Short name 2' } },
            ])
            .mockReturnValueOnce({});

        const { result } = renderHook(() => useHoldingControls());

        expect(result.current.value).toBeTruthy();
        expect(result.current.isShowCompanyFilter).toBeFalsy();
        expect(result.current.options).toEqual([
            { key: 'id1', content: 'Short name 1', value: 'Short name 1' },
        ]);
    });

    it('returns undefined value and correct options when isHolding is false', () => {
        jest.spyOn(Redux, 'useSelector')
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce('')
            .mockReturnValueOnce([
                { eqId: 'id1', organizationName: { shortName: 'Short name 1' } },
                { eqId: 'id2', organizationName: { shortName: 'Short name 2' } },
            ]);

        const { result } = renderHook(() => useHoldingControls());

        expect(result.current.value).toBeUndefined();
        expect(result.current.isShowCompanyFilter).toBeTruthy();
        expect(result.current.options).toEqual([
            { key: 'id1', content: 'Short name 1', value: 'Short name 1' },
            { key: 'id2', content: 'Short name 2', value: 'Short name 2' },
        ]);
    });

    it('returns value and empty options when organizationsList is empty', () => {
        jest.spyOn(Redux, 'useSelector')
            .mockReturnValueOnce(true)
            .mockReturnValueOnce(true)
            .mockReturnValueOnce('')
            .mockReturnValueOnce([])
            .mockReturnValueOnce({});

        const { result } = renderHook(() => useHoldingControls());

        expect(result.current.value).toBeTruthy();
        expect(result.current.isShowCompanyFilter).toBeFalsy();
        expect(result.current.options).toEqual([]);
    });
});
