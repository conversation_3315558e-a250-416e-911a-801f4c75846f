import { getEventBus } from '@corp-front/client-event-bus';
import { act, renderHook } from '@testing-library/react';

import { useEventBusValue } from '../use-event-bus-value';

jest.mock('@corp-front/client-event-bus', () => ({
    getEventBus: jest.fn(),
    NIB_EVENT_BUS_KEY: 'test-key',
}));

describe(useEventBusValue.name, () => {
    let mockEventBus: any;

    beforeEach(() => {
        mockEventBus = {
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            getLastEventDetail: jest.fn(),
        };

        (getEventBus as jest.Mock).mockReturnValue(mockEventBus);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('должен инициализироваться с последним значением события', () => {
        const testEvent = 'test-event';
        const testValue = { data: 'test' };

        mockEventBus.getLastEventDetail.mockReturnValue(testValue);

        const { result } = renderHook(() => useEventBusValue(testEvent));

        expect(result.current).toBe(testValue);
        expect(mockEventBus.getLastEventDetail).toHaveBeenCalledWith(testEvent);
    });

    it('должен обновлять значение при получении нового события', () => {
        const testEvent = 'test-event';
        const initialValue = { data: 'initial' };
        const newValue = { data: 'new' };

        mockEventBus.getLastEventDetail.mockReturnValue(initialValue);

        const { result } = renderHook(() => useEventBusValue(testEvent));

        act(() => {
            const eventCallback = mockEventBus.addEventListener.mock.calls[0][1];

            eventCallback({ detail: newValue });
        });

        expect(result.current).toBe(newValue);
    });

    it('должен очищать подписку на события при размонтировании', () => {
        const testEvent = 'test-event';
        const { unmount } = renderHook(() => useEventBusValue(testEvent));

        unmount();

        expect(mockEventBus.removeEventListener).toHaveBeenCalledWith(
            testEvent,
            expect.any(Function),
        );
    });

    it('должен использовать переданный eventBus вместо получения нового', () => {
        const testEvent = 'test-event';
        const customEventBus = {
            addEventListener: jest.fn(),
            removeEventListener: jest.fn(),
            getLastEventDetail: jest.fn().mockReturnValue({ data: 'custom' }),
            addEventListenerAndGetLast: jest.fn(),
            dispatchEvent: jest.fn(),
        };

        const { result } = renderHook(() => useEventBusValue(testEvent, customEventBus));

        expect(result.current).toEqual({ data: 'custom' });
        expect(getEventBus).not.toHaveBeenCalled();
        expect(customEventBus.getLastEventDetail).toHaveBeenCalledWith(testEvent);
    });

    it('должен возвращать undefined если eventBus не найден', () => {
        (getEventBus as jest.Mock).mockReturnValue(undefined);
        const testEvent = 'test-event';

        const { result } = renderHook(() => useEventBusValue(testEvent));

        expect(result.current).toBeUndefined();
    });
});
