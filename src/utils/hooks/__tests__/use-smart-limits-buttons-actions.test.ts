import { useDispatch, useSelector } from 'react-redux';
import { renderHook } from '@testing-library/react';

import { BUTTON_MARKER_PARAM_KEY, ButtonMarkers } from '#/src/constants/button-markers';
import { initExternalRedirect } from '#/src/ducks/app/actions';
import {
    externalRedirectAlfaCreditRequestCreditSelector,
    externalRedirectAlfaCreditRequestGuaranteeSelector,
    externalRedirectCreditSBSelector,
} from '#/src/ducks/settings/selectors';
import { useSmartLimitsButtonsActions } from '#/src/utils/hooks/use-smart-limits-buttons-actions';

jest.mock('react-redux', () => ({
    useDispatch: jest.fn(),
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/app/actions', () => ({
    initExternalRedirect: jest.fn(),
}));

jest.mock('#/src/ducks/settings/selectors', () => ({
    externalRedirectAlfaCreditRequestCreditSelector: jest.fn(),
    externalRedirectAlfaCreditRequestGuaranteeSelector: jest.fn(),
    externalRedirectCreditSBSelector: jest.fn(),
}));

describe('useSmartLimitsButtonsActions', () => {
    const mockDispatch = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
        (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    });

    it('should call initExternalRedirect with tranche link when redirectNewTranche is called', () => {
        const trancheLink = 'https://alfabank.ru/ak/request/credit';

        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === externalRedirectAlfaCreditRequestCreditSelector) return trancheLink;

            return null;
        });

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.redirectNewTranche();

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({ link: trancheLink, addContextRoot: false }),
        );
    });

    it('should call initExternalRedirect with guarantee link when redirectNewGuarantee is called', () => {
        const guaranteeLink = 'https://alfabank.ru/ak/request/guarantee';

        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === externalRedirectAlfaCreditRequestGuaranteeSelector)
                return guaranteeLink;

            return null;
        });

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.redirectNewGuarantee();

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({ link: guaranteeLink, addContextRoot: false }),
        );
    });

    it('should call initExternalRedirect with creditSB link and parameters when redirectNewLimit is called', () => {
        const creditSBLink = 'https://alfabank.ru/credit-sb';

        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === externalRedirectCreditSBSelector) return creditSBLink;

            return null;
        });

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.redirectNewLimit();

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({
                link: creditSBLink,
                addContextRoot: false,
                parameters: {
                    [BUTTON_MARKER_PARAM_KEY]: ButtonMarkers.BUTTON_8,
                },
            }),
        );
    });

    it('should call initExternalRedirect with formatted link and parameters when redirectNewProduct is called', () => {
        const creditSBLink = 'https://alfabank.ru/credit-sb';

        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === externalRedirectCreditSBSelector) return creditSBLink;

            return null;
        });

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.redirectNewProduct(123);

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({
                link: `${creditSBLink}/deal`,
                addContextRoot: false,
                parameters: {
                    lmLimitId: '123',
                    [BUTTON_MARKER_PARAM_KEY]: ButtonMarkers.BUTTON_8,
                },
            }),
        );
    });

    it('should call initExternalRedirect with formatted link and parameters when redirectViewDetails is called with LIMIT type', () => {
        const creditSBLink = 'https://alfabank.ru/creditsb';

        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === externalRedirectCreditSBSelector) return creditSBLink;

            return null;
        });

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.redirectViewDetails('LIMIT', '456');

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({
                link: `${creditSBLink}/456`,
                addContextRoot: false,
                parameters: {
                    [BUTTON_MARKER_PARAM_KEY]: ButtonMarkers.BUTTON_8,
                },
            }),
        );
    });

    it('should scroll to tab when goToTab is called', () => {
        Object.defineProperty(window, 'scrollY', {
            writable: true,
            value: 50,
        });

        const tabElement = {
            click: jest.fn(),
            getBoundingClientRect: jest.fn(() => ({ top: 100 })),
        } as unknown as HTMLElement;

        window.scrollTo = jest.fn();

        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.goToTab(tabElement);

        expect(tabElement.click).toHaveBeenCalled();
        expect(window.scrollTo).toHaveBeenCalledWith({
            top: 90, // 50 (scrollY) + 100 (elementRect.top) - 60 (headerOffset)
            behavior: 'smooth',
        });
    });

    it('should do nothing if goToTab is called with null', () => {
        const { result } = renderHook(() => useSmartLimitsButtonsActions());

        result.current.goToTab(null);

        expect(window.scrollTo).not.toHaveBeenCalled();
    });
});
