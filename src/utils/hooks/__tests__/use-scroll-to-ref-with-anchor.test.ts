/* eslint-disable @typescript-eslint/no-explicit-any */
import { useHistory, useLocation } from 'react-router';
import { renderHook } from '@testing-library/react';
import qs from 'qs';

import { HEIGHT_HEADER } from '#/src/constants/layout-values';
import { useScrollToRefWithAnchor } from '#/src/utils/hooks/use-scroll-to-ref-with-anchor';

jest.mock('react-router', () => ({
    useHistory: jest.fn(),
    useLocation: jest.fn(),
}));

jest.mock('qs', () => ({
    parse: jest.fn(),
    stringify: jest.fn(),
}));

describe('useScrollToRefWithAnchor', () => {
    const mockRef = {
        current: {
            getBoundingClientRect: jest.fn(),
        },
    };

    const mockLocation = {
        pathname: '/test',
        search: '?anchor=testAnchor',
    };

    const mockHistory = {
        replace: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        (useLocation as jest.Mock).mockReturnValue(mockLocation);
        (useHistory as jest.Mock).mockReturnValue(mockHistory);

        global.scrollTo = jest.fn();
    });

    it('should scroll to ref and update query string if anchor matches targetAnchor', () => {
        const topOffset = 100;

        if (mockRef.current?.getBoundingClientRect) {
            mockRef.current.getBoundingClientRect.mockReturnValue({
                top: topOffset,
            });
        }
        (qs.parse as jest.Mock).mockReturnValue({ anchor: 'testAnchor', otherParam: 'value' });
        (qs.stringify as jest.Mock).mockReturnValue('?otherParam=value');

        const { result } = renderHook(() => useScrollToRefWithAnchor(mockRef as any, 'testAnchor'));

        result.current();

        const expectedScrollTop = topOffset + window.scrollY - HEIGHT_HEADER;

        expect(window.scrollTo).toHaveBeenCalledWith(0, expectedScrollTop);

        expect(mockHistory.replace).toHaveBeenCalledWith('/test?otherParam=value');
    });

    it('should not scroll or update query string if anchor does not match targetAnchor', () => {
        (qs.parse as jest.Mock).mockReturnValue({ anchor: 'differentAnchor', otherParam: 'value' });

        const { result } = renderHook(() => useScrollToRefWithAnchor(mockRef as any, 'testAnchor'));

        result.current();

        expect(window.scrollTo).not.toHaveBeenCalled();

        expect(mockHistory.replace).not.toHaveBeenCalled();
    });

    it('should not scroll if ref is null', () => {
        (qs.parse as jest.Mock).mockReturnValue({ anchor: 'testAnchor', otherParam: 'value' });

        const { result } = renderHook(() =>
            useScrollToRefWithAnchor({ current: null }, 'testAnchor'),
        );

        result.current();

        expect(window.scrollTo).not.toHaveBeenCalled();

        expect(mockHistory.replace).not.toHaveBeenCalled();
    });

    it('should handle empty query string gracefully', () => {
        (useLocation as jest.Mock).mockReturnValue({ pathname: '/test', search: '' });
        (qs.parse as jest.Mock).mockReturnValue({});

        const { result } = renderHook(() => useScrollToRefWithAnchor(mockRef as any, 'testAnchor'));

        result.current();

        expect(window.scrollTo).not.toHaveBeenCalled();

        expect(mockHistory.replace).not.toHaveBeenCalled();
    });

    it('should work with a query string containing multiple parameters', () => {
        (qs.parse as jest.Mock).mockReturnValue({
            anchor: 'testAnchor',
            param1: 'value1',
            param2: 'value2',
        });
        (qs.stringify as jest.Mock).mockReturnValue('?param1=value1&param2=value2');

        const topOffset = 200;

        if (mockRef.current?.getBoundingClientRect) {
            mockRef.current.getBoundingClientRect.mockReturnValue({
                top: topOffset,
            });
        }

        const { result } = renderHook(() => useScrollToRefWithAnchor(mockRef as any, 'testAnchor'));

        result.current();

        const expectedScrollTop = topOffset + window.scrollY - HEIGHT_HEADER;

        expect(window.scrollTo).toHaveBeenCalledWith(0, expectedScrollTop);

        expect(mockHistory.replace).toHaveBeenCalledWith('/test?param1=value1&param2=value2');
    });
});
