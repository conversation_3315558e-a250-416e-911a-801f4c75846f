import { useSelector } from 'react-redux';
import { renderHook } from '@testing-library/react';

import { EUserRights } from '#/src/constants/rights';
import {
    currentOrganizationSelector,
    organizationsListSelector,
} from '#/src/ducks/organization/selectors';
import { useHoldingControls } from '#/src/utils/hooks/use-holding-controls';

import { useIsVisibleByAlfaCreditRights } from '../use-is-visible-by-alfa-credit-rights';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('#/src/ducks/organization/selectors', () => ({
    currentOrganizationSelector: jest.fn(),
    organizationsListSelector: jest.fn(),
}));

jest.mock('#/src/utils/hooks/use-holding-controls', () => ({
    useHoldingControls: jest.fn(),
}));

describe('useIsVisibleByAlfaCreditRights', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('returns true for all rights if at least one organization has successful right (old menu)', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === organizationsListSelector) {
                return [
                    {
                        eqId: '123',
                        rights: {
                            [EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW]: { successful: true },
                            [EUserRights.ALFACREDIT_BANK_REQUEST_VIEW]: { successful: true },
                            [EUserRights.ALFACREDIT_CREDIT_REQUEST_VIEW]: { successful: true },
                            [EUserRights.ALFACREDIT_GUARANTEE_REQUEST_VIEW]: { successful: true },
                        },
                    },
                ];
            }

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleByAlfaCreditRights());

        expect(result.current).toEqual({
            ALFACREDIT_DOCUMENT_REQUEST_VIEW: true,
            ALFACREDIT_BANK_REQUEST_VIEW: true,
            ALFACREDIT_CREDIT_REQUEST_VIEW: true,
            ALFACREDIT_GUARANTEE_REQUEST_VIEW: true,
        });
    });

    it('returns correct rights based on filtered group (group selected)', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === organizationsListSelector) {
                return [
                    {
                        eqId: 'A1',
                        rights: {
                            [EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW]: { successful: true },
                        },
                    },
                    {
                        eqId: 'B2',
                        rights: {
                            [EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW]: { successful: false },
                        },
                    },
                ];
            }

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: true,
            options: [{ key: 'A1' }],
        });

        const { result } = renderHook(() => useIsVisibleByAlfaCreditRights());

        expect(result.current).toEqual({
            ALFACREDIT_DOCUMENT_REQUEST_VIEW: true,
            ALFACREDIT_BANK_REQUEST_VIEW: false,
            ALFACREDIT_CREDIT_REQUEST_VIEW: false,
            ALFACREDIT_GUARANTEE_REQUEST_VIEW: false,
        });
    });

    it('returns rights of a single organization (new menu, not group)', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === currentOrganizationSelector) {
                return {
                    eqId: 'XYZ',
                    rights: {
                        [EUserRights.ALFACREDIT_DOCUMENT_REQUEST_VIEW]: { successful: true },
                        [EUserRights.ALFACREDIT_CREDIT_REQUEST_VIEW]: { successful: true },
                    },
                };
            }

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: true,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleByAlfaCreditRights());

        expect(result.current).toEqual({
            ALFACREDIT_DOCUMENT_REQUEST_VIEW: true,
            ALFACREDIT_BANK_REQUEST_VIEW: false,
            ALFACREDIT_CREDIT_REQUEST_VIEW: true,
            ALFACREDIT_GUARANTEE_REQUEST_VIEW: false,
        });
    });

    it('returns all false when no organizations and isHolding is false (old menu)', () => {
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector === organizationsListSelector) {
                return [];
            }

            return undefined;
        });

        (useHoldingControls as jest.Mock).mockReturnValue({
            isHolding: false,
            isShowCompanyFilter: false,
            options: [],
        });

        const { result } = renderHook(() => useIsVisibleByAlfaCreditRights());

        expect(result.current).toEqual({
            ALFACREDIT_DOCUMENT_REQUEST_VIEW: false,
            ALFACREDIT_BANK_REQUEST_VIEW: false,
            ALFACREDIT_CREDIT_REQUEST_VIEW: false,
            ALFACREDIT_GUARANTEE_REQUEST_VIEW: false,
        });
    });
});
