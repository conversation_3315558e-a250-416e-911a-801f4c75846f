import { useSelector } from 'react-redux';
import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';

import { useIsEarlyRepaymentButtonDisabled } from '../use-is-early-repayment-button-disabled';

const hasOverdue = {
    debts: {
        loan: {
            overdueDebt: {
                amount: 200,
            },
        },
        interest: {
            overdueInterest: {
                amount: 300,
            },
        },
    },
    summary: {
        totalFine: {
            amount: 50,
        },
    },
};

const trancheEnabled = {
    debts: {
        interest: {
            interest: {
                amount: 500,
            },
        },
    },
    summary: {},
};

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

describe('useIsEarlyRepaymentButtonDisabled', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return true, because tranche has overdue', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.enabled)
            .mockReturnValueOnce(true)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(hasOverdue as CreditProduct);

        expect(isDisabled).toBe(true);
    });
    it('should return true, because creditProductEarlyRepaymentButtonState is not enabled', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.disabled)
            .mockReturnValueOnce(true)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(trancheEnabled as CreditProduct);

        expect(isDisabled).toBe(true);
    });
    it('should return true, because accountsSelector is empty', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.enabled)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(trancheEnabled as CreditProduct);

        expect(isDisabled).toBe(true);
    });
    it('should return true, because isEarlyRepaymentRequestAlreadyCreatedSelector is true', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.enabled)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(true)
            .mockReturnValueOnce(false);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(trancheEnabled as CreditProduct);

        expect(isDisabled).toBe(true);
    });
    it('should return true, because isPaymentScheduleFetchingSelector is true', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.enabled)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(true);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(trancheEnabled as CreditProduct);

        expect(isDisabled).toBe(true);
    });
    it('should return false, because all conditions are valid', () => {
        (useSelector as jest.Mock)
            .mockReturnValueOnce(earlyRepaymentButtonStates.enabled)
            .mockReturnValueOnce([{}])
            .mockReturnValueOnce(false)
            .mockReturnValueOnce(false);
        const isDisabled = useIsEarlyRepaymentButtonDisabled(trancheEnabled as CreditProduct);

        expect(isDisabled).toBe(false);
    });
});
