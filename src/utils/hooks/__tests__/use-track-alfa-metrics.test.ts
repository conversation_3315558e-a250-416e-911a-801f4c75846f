import { useSelector } from 'react-redux';
import { act, renderHook } from '@testing-library/react';

import { trackNewAlfaMetrics } from 'arui-private/lib/alfa-metrics';

import type TMetric from '#/src/metrics/metric';
import { EventCategory } from '#/src/metrics/types';
import { getCanTrackMetrics } from '#/src/sagas/workers/alfa-metrics-worker';

import { useHoldingMetrics } from '../use-holding-metrics';
import { useTrackAlfaMetrics } from '../use-track-alfa-metrics';

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

jest.mock('#/src/sagas/workers/alfa-metrics-worker', () => ({
    getCanTrackMetrics: jest.fn(),
}));

jest.mock('arui-private/lib/alfa-metrics', () => ({
    trackNewAlfaMetrics: jest.fn(),
}));

jest.mock('../use-holding-metrics', () => ({
    useHoldingMetrics: jest.fn(),
}));

describe(useTrackAlfaMetrics.name, () => {
    beforeEach(() => {
        jest.clearAllMocks();
        (useSelector as jest.Mock).mockImplementation((selector) => {
            if (selector.name === 'userSelector') return { id: 'user-id' };
        });
        (getCanTrackMetrics as jest.Mock).mockReturnValue(true);
        (useHoldingMetrics as jest.Mock).mockReturnValue({
            holdingData: {},
            holdingDimensions: {},
        });
    });

    it('should track metrics when conditions are met', () => {
        const { result } = renderHook(() => useTrackAlfaMetrics());

        const metric: TMetric = {
            dimensionsMapping: { key: '2' },
            category: EventCategory.startPage,
            action: '',
        };
        const additionalData = { key: 'additional-value' };

        act(() => result.current(metric, additionalData));

        expect(getCanTrackMetrics).toHaveBeenCalled();
        expect(trackNewAlfaMetrics).toHaveBeenCalledWith(metric, undefined, {
            userId: 'user-id',
            ...additionalData,
        });
    });

    it('should not track metrics when getCanTrackMetrics returns false', () => {
        (getCanTrackMetrics as jest.Mock).mockReturnValue(false);

        const { result } = renderHook(() => useTrackAlfaMetrics());

        const metric: TMetric = {
            dimensionsMapping: { key: '2' },
            category: EventCategory.startPage,
            action: '',
        };
        const additionalData = { key: 'additional-value' };

        act(() => result.current(metric, additionalData));

        expect(getCanTrackMetrics).toHaveBeenCalled();
        expect(trackNewAlfaMetrics).not.toHaveBeenCalled();
    });
});
