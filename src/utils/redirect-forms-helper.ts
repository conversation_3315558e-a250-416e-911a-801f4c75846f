import { type Dispatch } from 'redux';

import { ECreditOffers, ECreditOffersCampaignCode } from '#/src/constants/credit-offers';
import { EXTERNAL_PATHS } from '#/src/constants/routing';
import {
    goLanding,
    goToBusinessCreditCard,
    goToCreditFormsApp,
    goToOverformAgreement,
    initExternalRedirect,
} from '#/src/ducks/app/actions';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

export const redirectForms = ({
    offer,
    dispatch,
    organizationId,
}: {
    offer: TMappedCreditOffer;
    dispatch: Dispatch;
    organizationId?: string;
}) => {
    if (offer) {
        switch (offer.type) {
            case ECreditOffers.EXPRESS_OVERDRAFT_OFFER:
                if (offer.campaignCode === ECreditOffersCampaignCode.OVR1) {
                    dispatch(
                        goLanding({
                            landingType: offer.type,
                            campaignCode: offer.campaignCode,
                            organizationId,
                        }),
                    );
                } else if (offer.campaignCode === ECreditOffersCampaignCode.LP_OVER_UO) {
                    dispatch(goToCreditFormsApp({ offerType: offer.type, organizationId }));
                } else {
                    dispatch(goToOverformAgreement({ organizationId }));
                }
                break;
            case ECreditOffers.ALFA_BUSINESS_CREDIT_CARD:
                dispatch(goToCreditFormsApp({ offerType: offer.type, organizationId }));
                break;
            case ECreditOffers.BUSINESS_CREDIT_CARD:
                if (
                    [
                        ECreditOffersCampaignCode.BN_CC_SB,
                        ECreditOffersCampaignCode.LP_CC_OW,
                        ECreditOffersCampaignCode.LP_CC_OW_UO,
                        ECreditOffersCampaignCode.LOAN_BUS_SM,
                    ].includes(offer.campaignCode as ECreditOffersCampaignCode)
                ) {
                    dispatch(goToCreditFormsApp({ offerType: offer.type, organizationId }));
                } else {
                    dispatch(goToBusinessCreditCard({ organizationId }));
                }
                break;
            case ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT:
            case ECreditOffers.CREDIT_LIMIT:
                dispatch(
                    initExternalRedirect({
                        link: EXTERNAL_PATHS.GOSSUPPORT,
                        addContextRoot: false,
                        organizationId,
                    }),
                );
                break;
            case ECreditOffers.LEASING:
                dispatch(
                    goLanding({
                        landingType: offer.type,
                        campaignCode: offer.campaignCode as ECreditOffersCampaignCode,
                        organizationId,
                    }),
                );
                break;
            default:
                dispatch(goToCreditFormsApp({ offerType: offer.type, organizationId }));
        }
    }
};
