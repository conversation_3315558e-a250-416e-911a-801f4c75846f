import { CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';

import { type OptionShape as GroupingOption } from '@alfalab/core-components/select/typings';

import { AST, DASH } from '#/src/constants/unicode-symbols';
import {
    type TCreditRequestList,
    type TCreditRequestListItem,
} from '#/src/ducks/credit-requests/types';

type ButtonTexts = [string | null, string | null];

const REQUEST_MARKERS = {
    [CommonCreditRequestType.Mmb]: 'М',
    [CommonCreditRequestType.Deal]: 'С',
    [CommonCreditRequestType.Limit]: 'С',
    [CommonCreditRequestType.Welcome]: 'С',
};

export const getShortRequestNumber = (
    id: string | undefined,
    type?: CommonCreditRequestType,
): string => {
    if (!id || !type) {
        return '';
    }

    const shortRequestMarker = REQUEST_MARKERS[type] ?? '';

    return `${shortRequestMarker}${DASH}${AST}${AST}${id.slice(-4)}`;
};

export const getStatusModalActionButtonTexts = (
    isAvailableForCurrentChannel: boolean | undefined,
    clientStatus: string | null | undefined,
) => {
    if (!clientStatus) {
        return [null, null];
    }

    const searchedStatus = clientStatus.toLocaleLowerCase();

    const availableStatusMap: Record<string, ButtonTexts> = {
        application_not_approved: [null, 'Понятно'],
        call_failed: ['Оставить заявку', null],
        черновик: ['Продолжить оформление', null],
        'на обработке': ['Продолжить оформление', null],
        'на подпись': ['Подписать', null],
        'согласование условий': ['Согласовать условия', null],
        'в работе': ['Заполнить данные', null],
        'выбор продукта': ['Выбрать предложение', null],
        'подтверждение условий': ['Продолжить оформление', null],
    };

    const unavailableStatusMap: Record<string, ButtonTexts> = {
        черновик: [null, 'Понятно'],
        'на обработке': [null, 'Понятно'],
        'на подпись': [null, 'Понятно'],
        'согласование условий': [null, 'Понятно'],
        'в работе': [null, 'Понятно'],
        'выбор продукта': [null, 'Понятно'],
        'подтверждение условий': [null, 'Понятно'],
    };

    const commonStatusMap: Record<string, ButtonTexts> = {
        reject_bank: [null, 'Понятно'],
        отклонена: [null, 'Понятно'],
        completed: ['Новая заявка', 'Мои продукты'],
        исполнена: ['Новая заявка', 'Мои продукты'],
        одобрена: ['Новая заявка', 'Мои продукты'],
        application_closed: [null, 'Понятно'],
        application_closed_data_errors: [null, 'Понятно'],
        application_closed_other_request: [null, 'Понятно'],
        'в архиве': [null, 'Понятно'],
        call_unsuccessful: ['Заполнить данные', null],
        call_rescheduled: ['Заполнить данные', null],
        'требуются данные': ['Заполнить данные', null],
        application_accepted: ['Заполнить данные', null],
        manager_assigning: ['Заполнить данные', null],
        application_transferred: [null, 'Понятно'],
        'готовим документы': [null, 'Понятно'],
        'отлагательные условия': [null, 'Понятно'],
    };

    const statusMap = isAvailableForCurrentChannel ? availableStatusMap : unavailableStatusMap;

    return statusMap[searchedStatus] || commonStatusMap[searchedStatus] || [null, null];
};

export const groupByField = ({
    list,
    groupBy: { key, value },
    handleCustomTitle,
}: {
    list: TCreditRequestList;
    groupBy: GroupingOption;
    handleCustomTitle: (value: unknown) => string;
}) =>
    Object.entries(
        list.reduce(
            (result, item) => {
                const groupTitle =
                    handleCustomTitle(item[key as keyof TCreditRequestListItem]) ?? value;

                if (!result[groupTitle]) {
                    result[groupTitle] = [];
                }
                result[groupTitle].push(item);

                return result;
            },
            {} as Record<string, TCreditRequestList>,
        ),
    );
