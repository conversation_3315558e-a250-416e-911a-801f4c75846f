import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';
import {
    type TAvailableActionsV2,
    TSuspensiveConditionsDeadlineStatusV2,
} from 'thrift-services/services/credit_products_v2';

import { type TranchesMappedForSelect } from '#/src/components/ui/tranches-select/types';

import {
    ECreditCardUniversalCode,
    ECreditProductActions,
    ECreditProducts,
    ECreditProductsCodes,
    ECreditProductsDealStatuses,
    isProductWithDailyRate,
} from '../constants/credit-products';
import { EOverdueStatus } from '../constants/overdue-status';
import { type CreditProductTranche } from '../ducks/credit-products/credit-products-types';
import { ETrancheTypes } from '../sagas/workers/get-tranche-credit-products-worker';

import { isAmountLessOrEqual } from './amount';
import { mapFromArrayToAssociativeArray } from './object-helpers';

type CreditProductsCategoryName =
    | 'credits'
    | 'overdrafts'
    | 'creditCards'
    | 'creditLines'
    | 'guaranties'
    | 'guarantyLines'
    | 'sopukLine';

const compareStatuses = {
    [TSuspensiveConditionsDeadlineStatusV2.VIOLATED]: 1,
    [TSuspensiveConditionsDeadlineStatusV2.APPROACHING]: 2,
    [TSuspensiveConditionsDeadlineStatusV2.UNDEFINED]: 3,
};

export function sortServerCreditProducts(serverCreditProducts: CreditProduct[]) {
    return serverCreditProducts
        .sort(
            (productA, productB) =>
                (productB.requisites?.fromDate?.seconds || 0) -
                (productA.requisites?.fromDate?.seconds || 0),
        )
        .sort((productA, productB) => {
            const suspensiveConditionsDeadlineStatusA =
                productA?.requisites?.suspensiveConditionsDeadlineInfo
                    ?.suspensiveConditionsDeadlineStatus ||
                TSuspensiveConditionsDeadlineStatusV2.UNDEFINED;
            const suspensiveConditionsDeadlineStatusB =
                productB?.requisites?.suspensiveConditionsDeadlineInfo
                    ?.suspensiveConditionsDeadlineStatus ||
                TSuspensiveConditionsDeadlineStatusV2.UNDEFINED;

            return (
                compareStatuses[suspensiveConditionsDeadlineStatusA] -
                compareStatuses[suspensiveConditionsDeadlineStatusB]
            );
        });
}

export function mapServerCreditProducts(serverCreditProducts: CreditProduct[]) {
    if (!serverCreditProducts) {
        return null;
    }
    if (typeof serverCreditProducts !== 'object' || !serverCreditProducts.map) {
        throw new TypeError('Wrong type!');
    }
    if (!serverCreditProducts.length) {
        return [];
    }

    return serverCreditProducts.reduce<Record<CreditProductsCategoryName, CreditProduct[]>>(
        (accumulator, product) => {
            if (product) {
                switch (product?.requisites?.product) {
                    case ECreditProductsCodes.BUSINESS_CREDIT:
                        accumulator.credits.push(product);
                        break;
                    case ECreditProductsCodes.OVERDRAFT:
                        if (
                            product?.requisites?.productCode &&
                            product.requisites.productCode.includes(
                                ECreditCardUniversalCode.OVERNZUC,
                            )
                        ) {
                            accumulator.creditCards.push(product);
                            break;
                        }
                        accumulator.overdrafts.push(product);
                        break;
                    case ECreditProductsCodes.CREDIT_LINE:
                        accumulator.creditLines.push(product);
                        break;
                    case ECreditProductsCodes.GUARANTY:
                        accumulator.guaranties.push(product);
                        break;
                    case ECreditProductsCodes.GUARANTY_LINE:
                        accumulator.guarantyLines.push(product);
                        break;
                    case ECreditProductsCodes.SOPUK:
                        accumulator.sopukLine.push(product);
                        break;
                }
            }

            return accumulator;
        },
        {
            credits: [],
            overdrafts: [],
            creditCards: [],
            creditLines: [],
            guaranties: [],
            guarantyLines: [],
            sopukLine: [],
        },
    );
}

export const mapCreditProductActions = (actions: TAvailableActionsV2[]) => {
    const actionsHash = mapFromArrayToAssociativeArray(actions, 'action');

    return {
        actionsHash,
        isCreditAllowed: !!actionsHash[ECreditProductActions.GET_CREDIT],
        isTrancheAllowed: !!actionsHash[ECreditProductActions.GET_TRANCHE],
        isTrancheAllowedMMB: !!actionsHash[ECreditProductActions.GET_TRANCHE_MMB],
        isGuaranteeAllowed: !!actionsHash[ECreditProductActions.GET_GUARANTEE],
        isStatementAllowed: !!actionsHash[ECreditProductActions.GET_STATEMENT],
        isDetailsAllowed: !!actionsHash[ECreditProductActions.GET_DETAILS],
    };
};

export const mapBaseCreditProduct = (product: CreditProduct) => {
    const overdueStatus =
        (product.summary?.overdueDays || 0) > 0 ? EOverdueStatus.OVERDUE : EOverdueStatus.AVAILABLE;
    const shortAccountNumber = product.requisites?.account
        ? product.requisites.account.slice(-4)
        : '';
    const isSignatureVisible = [
        product?.summary?.totalFine,
        product?.debts?.loan?.overdueDebt,
        product?.debts?.interest?.overdueInterest,
    ].every(isAmountLessOrEqual(0));
    const isClosedDeal = product.requisites?.dealStatus === ECreditProductsDealStatuses.L;
    const isDailyRate = isProductWithDailyRate(product.requisites?.productCode);

    return {
        ...product,
        ...mapCreditProductActions(product?.actions || []),
        fault: false as const,
        docNumber: product.docNumber,
        dealId: product.dealId || '',
        productCode: product.requisites?.productCode,
        totalToPay: product.summary?.totalToPay,
        debt: product.debts?.loan?.debt,
        interest: product.debts?.interest?.interest,
        debtRate: product.rate?.debtRate,
        debtRateDaily: isDailyRate ? product.rate?.debtRateDaily : null,
        overdueDays: product.summary?.overdueDays,
        accountNumber: product.requisites?.account || '',
        repaymentAccount: product.requisites?.repaymentAccount || '',
        payDebtTillDate: product.debts?.loan?.payDebtTillDate,
        payInterestTillDate: product.debts?.interest?.payInterestTillDate,
        hasCreditHolidaysFZ: product.frontParams?.hasCreditHolidaysFZ,
        hasPrepaymentAvailability: product.frontParams?.hasPrepaymentAvailability,
        isNotReadyScheduleOfPromoDeal: product.frontParams?.isNotReadyScheduleOfPromoDeal,
        dateDealClose: product.requisites?.dateDealClose,
        totalDebt: product.summary?.totalDebt,
        totalLoanSumToPay: product.summary?.totalLoanSumToPay,
        totalInterestSumToPay: product.summary?.totalInterestSumToPay,
        totalInterestOverdue: product.summary?.totalInterestOverdue,
        totalLoanSum: product.summary?.totalLoanSum,
        sum: product.requisites?.sum,
        overdueDebt: product.debts?.loan?.overdueDebt,
        overdueInterest: product.debts?.interest?.overdueInterest,
        interestToPay: product.debts?.interest?.interestToPay,
        debtToPay: product.debts?.loan?.debtToPay,
        fineDebt: product.debts?.loan?.fineDebt,
        fineInterest: product.debts?.interest?.fineInterest,
        totalFine: product.summary?.totalFine,
        dailyFine: product.summary?.dailyFine,
        availableAmount: product.availableAmount,
        totalOverdue: product.summary?.totalOverdue,
        totalOverdueAndFine: product.summary?.totalOverdueAndFine,
        fromDate: product.requisites?.fromDate,
        toDate: product.requisites?.toDate,
        limit: product.requisites?.limit,
        actualDate: product.actualDate,
        overdueDebtRate: product.rate?.overdueDebtRate,
        trancheDeadline: product.requisites?.trancheDeadline,
        clientLimit: product.requisites?.clientLimit,
        maxLimit: product.requisites?.maxLimit,
        hasClientLimit: product.frontParams?.hasClientLimit,
        hasDealDocs: product.frontParams?.hasDealDocs,
        beneficiary: product.requisites?.beneficiary,
        feeToPay: product.debts?.fee?.feeToPay,
        payFeeTillDate: product.debts?.fee?.payFeeTillDate,
        demandToPay: product.debts?.guaranteeRequirements?.demandToPay,
        payDemandTillDate: product.debts?.guaranteeRequirements?.payDemandTillDate,
        insuranceDate: product.requisites?.insuranceDate,
        feeRate: product.rate?.feeRate,
        suspensiveConditionsDeadlineInfo: product.requisites?.suspensiveConditionsDeadlineInfo,
        daysBeforeAdvancedRepay: product.requisites?.daysBeforeAdvancedRepay,
        overdueStatus,
        shortAccountNumber,
        isSignatureVisible,
        isClosedDeal,
        isAnnuityScheduleType: product.requisites?.isAnnuityScheduleType,
    };
};

export const mapFaultyCreditProduct = (product: CreditProduct, productType: ECreditProducts) =>
    product
        ? {
              ...mapBaseCreditProduct(product),
              fault: true as const,
              type: productType,
          }
        : null;

export const mapCreditProduct = (product: CreditProduct, type: ECreditProducts) =>
    product?.frontParams?.fault
        ? mapFaultyCreditProduct(product, type)
        : { ...mapBaseCreditProduct(product), type };

export const mapBaseCreditCard = (creditCard: CreditProduct) =>
    creditCard && !creditCard.frontParams?.fault
        ? {
              ...mapBaseCreditProduct(creditCard),
              type: ECreditProducts.CREDIT_CARD,
              accountNumber: creditCard.requisites?.account,
          }
        : mapFaultyCreditProduct(creditCard, ECreditProducts.CREDIT_CARD);

export const mapBaseTranche = (tranche: CreditProductTranche, type: ETrancheTypes) => ({
    ...mapBaseCreditProduct(tranche),
    isExpanded: tranche?.isExpanded || false,
    totalInterestSumToPay: tranche.summary?.totalInterestSumToPay,
    totalLoanSumToPay: tranche.summary?.totalLoanSumToPay,
    totalLoanSum: tranche.summary?.totalLoanSum,
    totalInterestOverdue: tranche.summary?.totalInterestOverdue,
    fineDebt: tranche.debts?.loan?.fineDebt,
    fineInterest: tranche.debts?.interest?.fineInterest,
    overdueDebt: tranche.debts?.loan?.overdueDebt,
    overdueInterest: tranche.debts?.interest?.overdueInterest,
    overdueDebtRate: tranche.rate?.overdueDebtRate,
    interestToPay: tranche.debts?.interest?.interestToPay,
    debtToPay: tranche.debts?.loan?.debtToPay,
    totalFine: tranche.summary?.totalFine,
    sum: tranche.requisites?.sum,
    totalDebt: tranche.summary?.totalDebt,
    fromDate: tranche.requisites?.fromDate,
    toDate: tranche.requisites?.toDate,
    issueDate: tranche.requisites?.issueDate,
    beneficiary: tranche.requisites?.beneficiary,
    feeRate: tranche.rate?.feeRate,
    feeToPay: tranche.debts?.fee?.feeToPay,
    payFeeTillDate: tranche.debts?.fee?.payFeeTillDate,
    demandToPay: tranche.debts?.guaranteeRequirements?.demandToPay,
    payDemandTillDate: tranche.debts?.guaranteeRequirements?.payDemandTillDate,
    insuranceDate: tranche.requisites?.insuranceDate,
    suspensiveConditionsDeadlineInfo: tranche.requisites?.suspensiveConditionsDeadlineInfo,
    isClosedTranche: tranche.requisites?.dealStatus === ECreditProductsDealStatuses.L,
    type,
});

type FaultyCreditProduct = ReturnType<typeof mapFaultyCreditProduct>;

export type MappedOverdraft = ReturnType<typeof mapCreditProduct>;
export type MappedCreditCard = ReturnType<typeof mapBaseCreditCard>;
export type MappedCredit = ReturnType<typeof mapCreditProduct>;
export type MappedCreditLine = ReturnType<typeof mapCreditProduct>;
export type MappedSopukLine = ReturnType<typeof mapCreditProduct>;
export type MappedGuaranty = ReturnType<typeof mapCreditProduct>;
export type MappedGuarantyLine = ReturnType<typeof mapCreditProduct>;
export type MappedTranche = ReturnType<typeof mapBaseTranche>;

export type SomeMappedProduct =
    | MappedOverdraft
    | MappedCredit
    | MappedCreditLine
    | MappedSopukLine
    | MappedGuaranty
    | MappedGuarantyLine
    | MappedTranche;

export function checkIsFaultyCreditProduct(
    product: SomeMappedProduct,
): product is FaultyCreditProduct {
    return !product || ('fault' in product && !!product.fault);
}

export const mapOverdrafts: (overdrafts: CreditProduct[]) => MappedOverdraft[] = (overdrafts) =>
    overdrafts.map((overdraft) => mapCreditProduct(overdraft, ECreditProducts.OVERDRAFT));
export const mapCreditCards: (creditCards: CreditProduct[]) => MappedCreditCard[] = (creditCards) =>
    creditCards.map((creditCard) => mapBaseCreditCard(creditCard));
export const mapCredits: (credits: CreditProduct[]) => MappedCredit[] = (credits) =>
    credits.map((credit) => mapCreditProduct(credit, ECreditProducts.BUSINESS_CREDIT));
export const mapCreditLines: (creditLines: CreditProduct[]) => MappedCreditLine[] = (creditLines) =>
    creditLines.map((creditLine) => mapCreditProduct(creditLine, ECreditProducts.CREDIT_LINE));
export const mapSopukLine: (creditLines: CreditProduct[]) => MappedSopukLine[] = (sopukLines) =>
    sopukLines.map((sopukLine) => mapCreditProduct(sopukLine, ECreditProducts.SOPUK));
export const mapGuaranties: (guaranties: CreditProduct[]) => MappedGuaranty[] = (guaranties) =>
    guaranties.map((guaranty) => mapCreditProduct(guaranty, ECreditProducts.GUARANTY));
export const mapGuarantyLines: (guarantyLines: CreditProduct[]) => MappedGuarantyLine[] = (
    guarantyLines,
) =>
    guarantyLines.map((guarantyLine) =>
        mapCreditProduct(guarantyLine, ECreditProducts.GUARANTY_LINE),
    );

export const mapTranches: (
    tranches: CreditProductTranche[],
    type?: ETrancheTypes,
) => MappedTranche[] = (tranches, type = ETrancheTypes.tranche) =>
    tranches
        .filter((tranche) => !tranche.frontParams?.fault)
        .map((tranche) => mapBaseTranche(tranche, type));

/**
 * Транши для селекбокса, имеют метку закрыт - если транш был закрыт
 * @param tranches array tranches
 * @returns TranchesMappedForSelect object with keys options - массив опций,
 *  allInDifferentState - boolean есть и открытые и закрытые
 */
export const mapTranchesToSelectOptions = (
    tranches: ReturnType<typeof mapTranches>,
): TranchesMappedForSelect => {
    let atLeastOneClosed = false;
    let atLeastOneOpen = false;

    const options = tranches.map((tranche) => {
        const content = [tranche?.docNumber];
        const isClosed = tranche?.requisites?.dealStatus === ECreditProductsDealStatuses.L;

        if (isClosed) {
            atLeastOneClosed = true;
            content.push('закрыт');
        } else {
            atLeastOneOpen = true;
        }

        return {
            key: tranche?.docNumber || '',
            content: content.join(' - '),
            value: {
                docNumber: tranche?.docNumber || '',
                sum: tranche?.requisites?.sum,
                isClosed,
                toLowerCase: tranche?.docNumber?.toLowerCase,
                fromDate: tranche?.requisites?.fromDate,
                toDate: tranche?.requisites?.toDate,
                dealStatus: tranche?.requisites?.dealStatus,
            },
        };
    });

    return { options, allInDifferentState: atLeastOneClosed && atLeastOneOpen };
};

export type TCreditProductsReturnType = ReturnType<typeof mapServerCreditProducts>;
export type TMappedCredits = ReturnType<typeof mapCredits>;
export type TMappedCredit = MappedCredit;
export type TMappedOverdrafts = ReturnType<typeof mapOverdrafts>;
export type TMappedCreditLines = ReturnType<typeof mapCreditLines>;
export type TMappedSopuks = ReturnType<typeof mapSopukLine>;
export type TMapGuaranties = ReturnType<typeof mapGuaranties>;
export type TMapGuarantyLines = ReturnType<typeof mapGuarantyLines>;
