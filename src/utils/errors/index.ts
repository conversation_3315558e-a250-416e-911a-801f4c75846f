import { type NoAccountsError } from './no-accounts-error';
import { type NoProductsError } from './no-products-error';
import { type ServerResponseError } from './server-response-error';

export type CreditProductErrors = ServerResponseError | NoProductsError;
export type CreditOffersErrors = ServerResponseError | NoProductsError;
export type AccountsErrors = ServerResponseError | NoAccountsError;
export type CreditProcessingErrors = ServerResponseError;
export type CreditDocumentCirculationErrors = ServerResponseError;
export type CurrentTimeErrors = ServerResponseError;
export type SubjectFeaturesErrors = ServerResponseError;
export type CategoryErrors = ServerResponseError;
export type EarlyPayErrors = ServerResponseError;
export type CreditWidgetStatusErrors = ServerResponseError;
