import partition from 'lodash/partition';
import { type Amount as TAmount } from 'thrift-services/entities';
import { type CreditOffer, type TCreditCardVariant } from 'thrift-services/services/credit_offers';
import { type UnixEpoch } from 'thrift-services/utils';

import { EProductCodes } from '#/src/constants/credit-products';

import {
    ECreditOffers,
    ECreditOffersCampaignCode,
    preapprovedOverdraftCampaignCodes,
} from '../constants/credit-offers';
import { NBSP } from '../constants/unicode-symbols';
import { DEFAULT_AMOUNT } from '../view-utils/credit-pane';

export function mapCreditOffers(offers: CreditOffer[]): TMappedCreditOffers {
    const [PRE_APPROVED_OFFERS, PRE_UNAPPROVED_OFFERS] = partition(
        offers,
        (offer) => !!offer.type?.preApproved,
    );

    return {
        preApproved: PRE_APPROVED_OFFERS.map((offer) => mapCreditOffer(offer)).filter(
            (offer) => !!offer,
        ), // mapCreditOffer по дефолту возвращает null. Эта проверка нужна, чтобы приложение не крашилось, если придет неизвестный продукт
        offers: PRE_UNAPPROVED_OFFERS.map((offer) => mapCreditOffer(offer)).filter(
            (offer) => !!offer,
        ),
    };
}

function mapCreditOffer(offer: CreditOffer): TMappedCreditOffer | null {
    switch (offer.type?.id) {
        case ECreditOffers.BUSINESS_CREDIT:
            if (offer.type?.campaignCode === ECreditOffersCampaignCode.LP_LOAN_KPN) {
                return mapCreditLimitOffer(offer);
            }

            return mapBusinessCreditOffer(offer);
        case ECreditOffers.BUSINESS_CREDIT_CARD:
            return mapBusinessCreditCardOffer(offer);
        case ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE:
            return mapBusinessWithPledgeCreditOffer(offer);
        case ECreditOffers.OVERDRAFT:
            return mapOverdraftOffer(offer);
        case ECreditOffers.GUARANTY:
            return mapGuarantyOffer(offer);
        case ECreditOffers.BUSINESS_MORTGAGE:
            return mapBusinessMortgageOffer(offer);
        case ECreditOffers.REFILLABLE_CREDIT_LINE:
            return mapRefillableCreditLineOffer(offer);
        case ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE:
            return mapRefillableCreditLineOffer(offer, { widthPledge: true });
        case ECreditOffers.BUSINESS_FACTORING:
            return mapBusinessFactoringOffer(offer);
        case ECreditOffers.LEASING:
            return mapLeasingOffer(offer);
        case ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT:
            if (offer.type?.campaignCode === ECreditOffersCampaignCode.PODDERJKA_SB) {
                return mapBusinessCreditWithStateSupportOffer(offer);
            }

            return null;
        default:
            return null;
    }
}

function mapLeasingOffer(offer: CreditOffer): TMappedCreditOffer {
    return offer.type?.preApproved
        ? {
              titlesVariants: {
                  short: 'Лизинг',
                  long: 'Лизинг',
              },
              type: ECreditOffers.LEASING as const,
              maximumTerm: offer.variants?.[0]?.maximumTerm,
              maximumAmount: offer.variants?.[0]?.maximumAmount,
              minimumAmount: offer.variants?.[0]?.minimumAmount,
              yearRatePct: offer.variants?.[0]?.yearRatePct,
              id: offer.variants?.[0]?.id,
              code: offer.id?.code,
              landingVersion: offer.variants?.[0]?.landingVersion,
              maximumLimit: offer.variants?.[0]?.maximumLimit,
              expiryDate: {
                  seconds: offer.expiryDate ? offer.expiryDate.seconds : 0,
              },
              visibleForUser: offer.type.visibleForUser,
              campaignCode: offer.type.campaignCode,
              preApproved: offer.type.preApproved,
              loanPurpose: offer.variants?.[0]?.loanPurpose,
          }
        : {
              titlesVariants: {
                  short: 'Лизинг',
                  long: 'Лизинг',
              },
              type: ECreditOffers.LEASING as const,
              yearRatePct: offer.variants?.[0]?.yearRatePct,
              expiryDate: {
                  seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
              },
              maximumTerm: offer.variants?.[0]?.maximumTerm,
              maximumAmount: offer.variants?.[0]?.maximumAmount,
              minimumAmount: offer.variants?.[0]?.minimumAmount,
              landingVersion: offer.variants?.[0]?.landingVersion,
              maximumLimit: offer.variants?.[0]?.maximumLimit,
              id: offer.variants?.[0]?.id,
              code: offer.id?.code,
              text: 'Экспресс-решение для клиентов банка',
              visibleForUser: offer.type?.visibleForUser,
              campaignCode: offer.type?.campaignCode,
              preApproved: offer.type?.preApproved,
              loanPurpose: offer.variants?.[0]?.loanPurpose,
          };
}

function mapCreditLimitOffer(offer: CreditOffer): TMappedCreditOffer {
    return {
        titlesVariants: {
            short: 'Кредитный лимит для бизнеса',
            long: 'Кредитный лимит для бизнеса',
        },
        type: ECreditOffers.CREDIT_LIMIT as const,
        text: 'Мы уже одобрили лимит на развитие вашего дела — осталось выбрать, на что тратить',
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
        },
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
    };
}

function mapBusinessCreditWithStateSupportOffer(offer: CreditOffer): TMappedCreditOffer {
    return {
        titlesVariants: {
            short: 'Кредит для поддержки и развития бизнеса',
            long: 'Кредит для поддержки и развития бизнеса',
        },
        type: ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT as const,
        text: `Кредиты для${NBSP}бизнеса`,
        description: `Специальные программы для${NBSP}субъектов малого и${NBSP}среднего бизнеса, ИТ,${NBSP}агропрома, производителей и${NBSP}экспортеров. Получите деньги на${NBSP}развитие бизнеса или${NBSP}операционные расходы`,
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate.seconds : 0,
        },
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: false,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
    };
}

function mapOverdraftOffer(offer: CreditOffer): TMappedCreditOffer | null {
    const creditLineOffer = {
        titlesVariants: {
            short: 'Овердрафт',
            long: 'Овердрафт',
        },
        text: `Возможность уходить в${NBSP}минус по${NBSP}счёту и${NBSP}пользоваться деньгами банка, когда нужно`,
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate.seconds : 0,
        },
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        productCode: EProductCodes.UP03,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
    };

    const isPreapprovedOverdraftCampaignCode = preapprovedOverdraftCampaignCodes.includes(
        offer.type?.campaignCode as ECreditOffersCampaignCode,
    );

    return offer.type?.preApproved && !isPreapprovedOverdraftCampaignCode
        ? {
              ...creditLineOffer,
              type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER as const,
              finalRate: offer.finalRate,
          }
        : {
              ...creditLineOffer,
              type: ECreditOffers.OVERDRAFT as const,
              productCode: EProductCodes.UP03,
              finalRate: offer.finalRate,
          };
}

function mapBusinessCreditOffer(offer: CreditOffer): TMappedCreditOffer {
    return {
        titlesVariants: {
            short: 'Кредит',
            long: 'Кредит для бизнеса',
        },
        type: ECreditOffers.BUSINESS_CREDIT as const,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
        },
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        text: `Кредит одной суммой на пополнение оборотных средств и${NBSP}инвестиционные цели`,
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        productCode: EProductCodes.BP11,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
        finalRate: offer.finalRate,
    };
}

function mapBusinessCreditCardOffer(offer: CreditOffer): TMappedCreditOffer {
    const creditLineOffer = {
        titlesVariants: {
            short: 'Карта Альфа-Бизнес Кредит',
            long: 'Карта Альфа-Бизнес Кредит',
        },
        type: ECreditOffers.BUSINESS_CREDIT_CARD as const,
        text: `Не ставьте бизнес на паузу${NBSP}- оплачивайте расходы кредитной картой`,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
        },
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        creditCardVariant: offer.variants?.[0]?.creditCardVariant,
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        productCode: EProductCodes.UC04,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
        finalRate: offer.finalRate,
    };

    return offer.type?.preApproved && offer.type?.campaignCode === ECreditOffersCampaignCode.LP_CC1
        ? {
              ...creditLineOffer,
              type: ECreditOffers.BUSINESS_CREDIT_CARD as const,
          }
        : {
              ...creditLineOffer,
              type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD as const,
          };
}

function mapBusinessWithPledgeCreditOffer(offer: CreditOffer): TMappedCreditOffer {
    return {
        titlesVariants: {
            short: 'Кредит',
            long: 'Кредит для бизнеса',
        },
        type: ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE as const,
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
        },
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        text: `Кредит одной суммой на пополнение оборотных средств и${NBSP}инвестиционные цели`,
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        productCode: EProductCodes.UF99,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
    };
}

function mapGuarantyOffer(offer: CreditOffer): TMappedCreditOffer | null {
    return offer.type?.preApproved
        ? null
        : {
              titlesVariants: {
                  long: 'Банковская гарантия',
                  short: 'Гарантия',
              },
              type: ECreditOffers.GUARANTY as const,
              yearRatePct: offer.variants?.[0]?.yearRatePct,
              expiryDate: {
                  seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
              },
              maximumTerm: offer.variants?.[0]?.maximumTerm,
              maximumAmount: offer.variants?.[0]?.maximumAmount,
              minimumAmount: offer.variants?.[0]?.minimumAmount,
              oneTimeComissionPct: offer.variants?.[0]?.oneTimeComissionPct,
              landingVersion: offer.variants?.[0]?.landingVersion,
              maximumLimit: offer.variants?.[0]?.maximumLimit,
              text: `Обеспечение исполнения${NBSP}обязательств`,
              visibleForUser: offer.type?.visibleForUser,
              campaignCode: offer.type?.campaignCode,
              preApproved: offer.type?.preApproved,
              loanPurpose: offer.variants?.[0]?.loanPurpose,
          };
}

export const BANK_GUARANTEE_FOR_MEDIUM_BUSINESS_OFFER: TMappedCreditOffer = {
    titlesVariants: {
        long: 'Банковская гарантия',
        short: 'Гарантия',
    },
    type: ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS as const,
    expiryDate: {
        seconds: 0,
    },
    text: 'Обеспечение исполнения обязательств',
    visibleForUser: true,
};

export const FACTORING_X5_OFFER: TMappedCreditOffer = {
    titlesVariants: {
        long: 'Финансирование дебиторской задолженности',
        short: 'Финансирование дебиторской задолженности',
    },
    text: 'Для клиентов X5 retail Group',
    type: ECreditOffers.FACTORING_X5 as const,
    visibleForUser: true,
    expiryDate: {
        seconds: 0,
    },
};

export const BUSINESS_CREDIT_REFINANCING_OFFER: TMappedCreditOffer = {
    titlesVariants: {
        long: 'Рефинансирование кредитов для бизнеса',
        short: 'Рефинансирование кредитов для бизнеса',
    },
    text: 'Под залог недвижимости или поручительство госкорпораций',
    type: ECreditOffers.BUSINESS_CREDIT_REFINANCING as const,
    visibleForUser: true,
    maximumAmount: {
        ...DEFAULT_AMOUNT,
        amount: ***********,
    },
    maximumLimit: {
        ...DEFAULT_AMOUNT,
        amount: ***********,
    },
    yearRatePct: 25.5,
    maximumTerm: 120,
    expiryDate: {
        seconds: 0,
    },
};

export const BUSINESS_AUTO_CREDIT_OFFER: TMappedCreditOffer = {
    titlesVariants: {
        long: 'Автокредит для бизнеса',
        short: 'Автокредит для бизнеса',
    },
    text: 'Под залог приобретаемого транспорта и спецтехники',
    type: ECreditOffers.BUSINESS_AUTO_CREDIT as const,
    visibleForUser: true,
    maximumAmount: {
        ...DEFAULT_AMOUNT,
        amount: 5000000000,
    },
    maximumLimit: {
        ...DEFAULT_AMOUNT,
        amount: 5000000000,
    },
    yearRatePct: 27.5,
    maximumTerm: 120,
    expiryDate: {
        seconds: 0,
    },
};

export const BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB_OFFER: TMappedCreditOffer = {
    titlesVariants: {
        long: 'Кредит с господдержкой',
        short: 'Кредит с господдержкой',
    },
    text: 'Кредит с поручительством Корпорации МСП и скидкой на ставку',
    type: ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB as const,
    visibleForUser: true,
    maximumAmount: {
        ...DEFAULT_AMOUNT,
        amount: 300000000,
    },
    maximumLimit: {
        ...DEFAULT_AMOUNT,
        amount: 300000000,
    },
    yearRatePct: 27.5,
    maximumTerm: 60,
    expiryDate: {
        seconds: 0,
    },
};

function mapBusinessMortgageOffer(offer: CreditOffer): TMappedCreditOffer | null {
    return offer.type?.preApproved
        ? null
        : {
              titlesVariants: {
                  long: 'Бизнес-ипотека',
                  short: 'Ипотека',
              },
              type: ECreditOffers.BUSINESS_MORTGAGE as const,
              yearRatePct: offer.variants?.[0]?.yearRatePct,
              expiryDate: {
                  seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
              },
              maximumTerm: offer.variants?.[0]?.maximumTerm,
              maximumAmount: offer.variants?.[0]?.maximumAmount,
              minimumAmount: offer.variants?.[0]?.minimumAmount,
              oneTimeComissionPct: offer.variants?.[0]?.oneTimeComissionPct,
              landingVersion: offer.variants?.[0]?.landingVersion,
              maximumLimit: offer.variants?.[0]?.maximumLimit,
              text: 'Покупка жилой или коммерческой недвижимости с аккредитивом',
              visibleForUser: offer.type?.visibleForUser,
              campaignCode: offer.type?.campaignCode,
              preApproved: offer.type?.preApproved,
              loanPurpose: offer.variants?.[0]?.loanPurpose,
              productCode: EProductCodes.UF05,
          };
}

function mapRefillableCreditLineOffer(
    offer: CreditOffer,
    options?: { widthPledge: boolean },
): TMappedCreditOffer | null {
    const creditLineOffer = {
        titlesVariants: {
            long: options?.widthPledge
                ? 'Возобновляемая кредитная линия c залогом'
                : 'Возобновляемая кредитная линия',
            short: 'Кредитная линия',
        },
        yearRatePct: offer.variants?.[0]?.yearRatePct,
        expiryDate: {
            seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
        },
        maximumTerm: offer.variants?.[0]?.maximumTerm,
        maximumAmount: offer.variants?.[0]?.maximumAmount,
        minimumAmount: offer.variants?.[0]?.minimumAmount,
        oneTimeComissionPct: offer.variants?.[0]?.oneTimeComissionPct,
        landingVersion: offer.variants?.[0]?.landingVersion,
        maximumLimit: offer.variants?.[0]?.maximumLimit,
        text: options?.widthPledge
            ? 'На оплату периодических расходов. Лимит возвращается после погашения использованной части займа'
            : 'Возможность брать транши на любые суммы в пределах лимита. При погашении транша лимит восстанавливается',
        visibleForUser: offer.type?.visibleForUser,
        campaignCode: offer.type?.campaignCode,
        preApproved: offer.type?.preApproved,
        loanPurpose: offer.variants?.[0]?.loanPurpose,
        productCode: EProductCodes.VMS2,
        id: offer.variants?.[0]?.id,
        code: offer.id?.code,
        ewsId: offer.ewsId,
        approvalProbability: offer.approvalProbability,
        clientRating: offer.clientRating,
        ratingModel: offer.ratingModel,
        finalRate: offer.finalRate,
    };

    return options?.widthPledge
        ? {
              ...creditLineOffer,
              type: ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE as const,
          }
        : {
              ...creditLineOffer,
              type: ECreditOffers.REFILLABLE_CREDIT_LINE as const,
          };
}

function mapBusinessFactoringOffer(offer: CreditOffer): TMappedCreditOffer | null {
    return offer.type?.preApproved
        ? null
        : {
              titlesVariants: {
                  long: 'Финансирование дебиторской задолженности',
                  short: 'Финансирование дебиторской задолженности',
              },
              type: ECreditOffers.BUSINESS_FACTORING as const,
              yearRatePct: offer.variants?.[0]?.yearRatePct,
              expiryDate: {
                  seconds: offer.expiryDate ? offer.expiryDate?.seconds : 0,
              },
              maximumTerm: offer.variants?.[0]?.maximumTerm,
              maximumAmount: offer.variants?.[0]?.maximumAmount,
              minimumAmount: offer.variants?.[0]?.minimumAmount,
              oneTimeComissionPct: offer.variants?.[0]?.oneTimeComissionPct,
              landingVersion: offer.variants?.[0]?.landingVersion,
              maximumLimit: offer.variants?.[0]?.maximumLimit,
              text: `Услуги для производителей и${NBSP}поставщиков`,
              visibleForUser: offer.type?.visibleForUser,
              campaignCode: offer.type?.campaignCode,
              preApproved: offer.type?.preApproved,
              loanPurpose: offer.variants?.[0]?.loanPurpose,
          };
}

export type TMappedCreditOffer = {
    titlesVariants: {
        long: string;
        short: string;
    };
    type: ECreditOffers;
    yearRatePct?: number;
    isOverformAvailable?: boolean;
    expiryDate: UnixEpoch;
    maximumTerm?: number; // NOTE: в месяцах
    maximumAmount?: TAmount;
    minimumAmount?: TAmount;
    maximumLimit?: TAmount;
    oneTimeComissionPct?: number;
    landingVersion?: number;
    creditCardVariant?: TCreditCardVariant;
    id?: string;
    code?: string;
    text?: string;
    description?: string;
    visibleForUser?: boolean;
    campaignCode?: string;
    preApproved?: boolean;
    loanPurpose?: string[];
    productCode?: EProductCodes;
    additionalOffer?: TMappedCreditOffer;
    ewsId?: string;
    approvalProbability?: number;
    clientRating?: string;
    ratingModel?: string;
    finalRate?: number;
};

export type TMappedCreditOffers = {
    offers: TMappedCreditOffer[];
    preApproved: TMappedCreditOffer[];
};
