import { type Amount } from 'thrift-services/entities';

import { MILLION, THOUSAND } from '../constants/number';

export function convertAmountToNumber(value: Amount): number | null {
    return value?.amount && value?.currency?.minorUnits
        ? value.amount / value.currency.minorUnits
        : null;
}

export function convertAmountToText(value?: Amount | number): string {
    if (typeof value === 'number') {
        return value.toLocaleString('ru-RU', {
            style: 'currency',
            currency: 'rub',
            minimumFractionDigits: 0,
        });
    }

    if (value?.amount && value?.currency) {
        const { currency } = value;

        const amount = value.currency.minorUnits ? value.amount / value.currency.minorUnits : 0;

        if (amount / MILLION >= 1) {
            const splitAmount = (amount / MILLION).toString().split('.');
            const first = splitAmount[0];
            const second = splitAmount[1] ? `.${splitAmount[1].slice(0, 2)}` : '';

            return `${first}${second} млн ${currency.unicodeSymbol}`;
        }

        if (amount / THOUSAND >= 1) {
            return `${(amount / THOUSAND).toString().split('.')[0]} тыс ${currency.unicodeSymbol}`;
        }

        return `${amount} ${currency.unicodeSymbol}`;
    }

    return '';
}

export function isAmountGreaterThanZero(value?: Amount): boolean {
    if (value) {
        return value.amount !== undefined && value.amount > 0;
    }

    return false;
}

export const isAmountLessOrEqual =
    (to: number) =>
    (amount?: Amount): boolean =>
        (amount?.amount || 0) <= to;
