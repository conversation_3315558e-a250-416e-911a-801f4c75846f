import { Buffer } from 'buffer';

import { type SignedDocument } from '#/src/types/signed-document';

export function downloadBinaryFile(fileAsBlob: Blob, name: string) {
    const url = window.URL.createObjectURL(fileAsBlob);
    const link = document.createElement('a');

    link.download = name;
    link.href = url;

    document.body.appendChild(link);
    link.click();

    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
}

export function downloadBinaryFileByExtension(
    fileAsBlob: Blob,
    name: string,
    fileExtension: string,
) {
    const url = window.URL.createObjectURL(fileAsBlob);
    const link = document.createElement('a');

    link.download = `${name}.${fileExtension}`;
    link.href = url;

    document.body.appendChild(link);
    link.click();

    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
}

export function downloadSignedBinaryFileByExtension(
    content: SignedDocument['body'],
    name: string,
    fileExtension: string,
) {
    const buffer = Buffer.from(content.data);
    const blob = new Blob([buffer.buffer], { type: 'application/vnd.ms-excel' });

    downloadBinaryFileByExtension(blob, name, fileExtension);
}
