import { type Amount } from 'thrift-services/entities';

import { convertAmountToText, isAmountLessOrEqual } from '#/src/utils/amount';

const getAmount = (amount = 10000): Amount => ({
    amount,
    currency: {
        code: 810,
        mnemonicCode: 'RUR',
        minorUnits: 100,
        unicodeSymbol: '₽',
        fullName: 'Российский рубль',
    },
});

describe('amount', () => {
    const amount = getAmount();

    it('should return empty string if has not amount', () => {
        expect(convertAmountToText()).toEqual('');
    });

    it('should return text', () => {
        expect(convertAmountToText(amount)).toEqual('100 ₽');
    });

    it('should return text with digit', () => {
        amount.amount = 1000000;
        expect(convertAmountToText(amount)).toEqual('10 тыс ₽');

        amount.amount = 100000000;
        expect(convertAmountToText(amount)).toEqual('1 млн ₽');
    });

    it('should return text amount with dot', () => {
        amount.amount = 110000000;
        expect(convertAmountToText(amount)).toEqual('1.1 млн ₽');
    });
});

describe('isAmountLessOrEqual', () => {
    const isAmountLessOrEqual0 = isAmountLessOrEqual(0);

    it('truthy for undefined', () => {
        expect(isAmountLessOrEqual0()).toEqual(true);
    });
    it('falsy for > 0', () => {
        expect(isAmountLessOrEqual0(getAmount(1))).toEqual(false);
    });
    it('truthy for < 0', () => {
        expect(isAmountLessOrEqual0(getAmount(-1))).toEqual(true);
    });
    it('truthy 0', () => {
        expect(isAmountLessOrEqual0(getAmount(0))).toEqual(true);
    });
});
