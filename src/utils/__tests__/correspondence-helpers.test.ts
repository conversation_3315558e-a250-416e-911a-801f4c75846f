import {
    type Letter,
    LettersDirection,
    LetterStatusId,
} from 'thrift-services/services/correspondence';

import { type EarlyPayData, earlyPaymentTypes } from '#/src/types/early-pay-sidebar';
import { getEarlyPaymentLetter } from '#/src/utils/correspondence-helpers';

describe('correspondence helpers', () => {
    const data: EarlyPayData = {
        docNumber: '1234',
        fromDate: '11.11.1111',
        paymentType: earlyPaymentTypes.FULL,
        selectedAccountNumber: '1111111',
        paymentDate: '22.22.2222',
        debtToPay: '1234567',
        interestToPay: '54321',
        total: '2222222',
        currency: {
            mnemonicCode: '11111',
        },
        branchNumber: '123',
        currentOrganizationEqId: 'XWERA123',
    };

    const getLetter = (bodyLetter: Record<string, string>) => {
        let body: Record<string, string> = bodyLetter;

        return (
            options: { bodyLetter?: Record<string, string>; subject?: string } = {},
        ): Letter => {
            body = Object.assign(body, options.bodyLetter);

            return {
                subject: options.subject,
                direction: LettersDirection.OUT,
                body: `${body.firstLine}
                ${body.secondLine}
                ${body.body}`,
                category: {
                    id: '30',
                },
                status: LetterStatusId.ON_SIGNING,
                branch: {
                    number: data.branchNumber,
                },
            };
        };
    };

    const bodyLetter: Record<string, string> = {
        firstLine: `Прошу осуществить досрочное погашение кредита по договору № ${data.docNumber} от ${data.fromDate}`,
        secondLine: 'Тип погашения: Полное',
        body: `Номер счета для погашения: Расчетный счет № ${data.selectedAccountNumber}
                Дата погашения: ${data.paymentDate}
                Сумма основного долга: ${data.debtToPay} ${data.currency.mnemonicCode}
                Сумма процентов: ${data.interestToPay} ${data.currency.mnemonicCode}
                Общая сумма: ${data.total} ${data.currency.mnemonicCode}`,
    };

    const letter = getLetter(bodyLetter);

    it('should return letter for type credit', () => {
        const expected = letter({
            subject: `Досрочное погашение кредита по договору № ${data.docNumber} от ${data.fromDate}. Клиент ${data.currentOrganizationEqId}`,
        });

        expect(getEarlyPaymentLetter(data)).toEqual(expected);
    });

    it('should return letter for other type', () => {
        const expected = letter({
            bodyLetter: {
                firstLine: `Прошу осуществить досрочное погашение транша № ${data.docNumber}`,
            },
            subject: `Досрочное погашение транша № ${data.docNumber}. Клиент ${data.currentOrganizationEqId}`,
        });

        expect(getEarlyPaymentLetter(data, 'other')).toEqual(expected);
    });

    it('should return letter with paymentTyp === partial', () => {
        data.paymentType = earlyPaymentTypes.PARTIAL;
        const expected = letter({
            bodyLetter: {
                secondLine: 'Тип погашения: Частичное',
            },
            subject: `Досрочное погашение транша № ${data.docNumber}. Клиент ${data.currentOrganizationEqId}`,
        });

        expect(getEarlyPaymentLetter(data, 'other')).toEqual(expected);
    });
});
