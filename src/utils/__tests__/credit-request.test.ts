import { type CommonCreditRequestType } from 'corp-credit-request-api-typescript-services';

import { AST, DASH } from '#/src/constants/unicode-symbols';
import { type TCreditRequestList } from '#/src/ducks/credit-requests/types';

import {
    getShortRequestNumber,
    getStatusModalActionButtonTexts,
    groupByField,
} from '../credit-request';

describe('getShortRequestNumber', () => {
    it.each([
        { id: undefined, type: '', expected: '' },
        { id: undefined, type: 'DEAL', expected: '' },
        { id: '', type: 'MMB', expected: '' },
        { id: '', type: 'LIMIT', expected: '' },
        { id: '', type: 'WELCOME', expected: '' },
        { id: '1234', type: '', expected: '' },
        { id: 'mock_id_12345678', type: 'MMB', expected: `М${DASH}${AST}${AST}5678` },
        { id: 'mock_id_87654321', type: 'DEAL', expected: `С${DASH}${AST}${AST}4321` },
        { id: 'mock_id_24681357', type: 'WELCOME', expected: `С${DASH}${AST}${AST}1357` },
        { id: '123', type: 'MMB', expected: `М${DASH}${AST}${AST}123` },
        { id: '123', type: 'DEAL', expected: `С${DASH}${AST}${AST}123` },
        { id: '123', type: 'WELCOME', expected: `С${DASH}${AST}${AST}123` },
        { id: '1234', type: 'MMB', expected: `М${DASH}${AST}${AST}1234` },
        { id: '1234', type: 'LIMIT', expected: `С${DASH}${AST}${AST}1234` },
        { id: '1234', type: 'WELCOME', expected: `С${DASH}${AST}${AST}1234` },
    ])('returns $expected for id=$id and type=$type', ({ id, type, expected }) => {
        expect(getShortRequestNumber(id, type as CommonCreditRequestType)).toBe(expected);
    });
});

describe('getStatusModalActionButtonTexts', () => {
    it.each([
        [true, 'черновик', ['Продолжить оформление', null]],
        [true, 'на подпись', ['Подписать', null]],
        [false, 'черновик', [null, 'Понятно']],
        [false, 'в работе', [null, 'Понятно']],
        [true, 'REJECT_BANK', [null, 'Понятно']],
        [false, 'COMPLETED', ['Новая заявка', 'Мои продукты']],
        [true, 'CALL_UNSUCCESSFUL', ['Заполнить данные', null]],
        [false, 'call_rescheduled', ['Заполнить данные', null]],
        [true, 'Черновик', ['Продолжить оформление', null]],
        [false, 'В РАБОТЕ', [null, 'Понятно']],
        [true, 'unknown_status_1', [null, null]],
        [false, 'unknown_status_2', [null, null]],
        [true, null, [null, null]],
        [false, '', [null, null]],
    ])(
        'should return correct button texts for isAvailableForCurrentChannel=%s and clientStatus="%s"',
        (isAvailableForCurrentChannel, clientStatus, expected) => {
            expect(
                getStatusModalActionButtonTexts(isAvailableForCurrentChannel, clientStatus),
            ).toEqual(expected);
        },
    );

    it('should default to commonStatusMap when isAvailableForCurrentChannel is undefined', () => {
        expect(getStatusModalActionButtonTexts(undefined, 'COMPLETED')).toEqual([
            'Новая заявка',
            'Мои продукты',
        ]);
    });
});

describe('groupByField', () => {
    const sampleList: TCreditRequestList = [
        {
            id: '8a7fa445-8d58-4fa1-97fa-bf5f5fa2492a',
            type: undefined,
            lmLimitId: 211857817,
            createDt: 1726737288,
            description: '',
            clientStatus: 'pending',
            isAvailableForCurrentChannel: false,
            productName: 'Product A',
            limit: {
                amount: 2000000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
        },
        {
            id: '2b7fa445-8d58-4fa1-97fa-bf5f5fa2492a',
            type: undefined,
            lmLimitId: 211857818,
            createDt: 1726737289,
            description: '',
            clientStatus: 'completed',
            isAvailableForCurrentChannel: false,
            productName: 'Product B',
            limit: {
                amount: 1000000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            },
        },
    ];

    const handleCustomTitle = (value: unknown) => (value ? String(value) : 'Unknown');

    it('should group items by the specified key and custom title', () => {
        const groupBy = { key: 'clientStatus', content: 'Статус', value: 'Без названия' };
        const grouped = groupByField({ list: sampleList, groupBy, handleCustomTitle });

        expect(grouped).toEqual([
            ['pending', [sampleList[0]]],
            ['completed', [sampleList[1]]],
        ]);
    });

    it('should use "Unknown" when group key value is null or undefined', () => {
        const groupBy = { key: 'type', value: 'Unknown', content: '' };
        const grouped = groupByField({ list: sampleList, groupBy, handleCustomTitle });

        expect(grouped).toEqual([['Unknown', sampleList]]);
    });

    it('should group items by numeric keys correctly', () => {
        const groupBy = { key: 'createDt', value: 'Unknown', content: '' };
        const grouped = groupByField({ list: sampleList, groupBy, handleCustomTitle });

        expect(grouped).toEqual([
            ['1726737288', [sampleList[0]]],
            ['1726737289', [sampleList[1]]],
        ]);
    });

    it('should return an empty array when the list is empty', () => {
        const groupBy = { key: 'clientStatus', value: 'Unknown', content: '' };
        const grouped = groupByField({ list: [], groupBy, handleCustomTitle });

        expect(grouped).toEqual([]);
    });
});
