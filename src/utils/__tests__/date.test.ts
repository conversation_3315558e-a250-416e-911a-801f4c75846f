import parse from 'date-fns/parse';
import { type UnixEpoch } from 'thrift-services/utils';

import { DATE_FORMAT } from '#/src/constants/date';
import { earlyPaymentTypes } from '#/src/types/early-pay-sidebar';

import {
    getIsLessThanDaysLeft,
    getMoscowDate,
    getMoscowTimeZone,
    parseDateFromAny,
    toNativeDate,
    toUnixEpoch,
} from '../date';
import { calculateMaxDate, calculateMinDate, getClosestFuturePaymentDate } from '../early-pay';

describe('date', () => {
    describe('getMoscowDate', () => {
        test('should have the same date', () => {
            const date = new Proxy(new Date(2014, 10, 10, 23, 50), {
                get: (target: any, parameter) => {
                    if (parameter === 'getTimezoneOffset') {
                        return () => -4 * 60; // offset +4 hours
                    }

                    return parameter in target ? target[parameter].bind(target) : undefined;
                },
            });

            expect(getMoscowDate(date).getDate()).toBe(10);
        });

        test('should have next date', () => {
            const date = new Proxy(new Date(2014, 10, 10, 23, 50), {
                get: (target: any, parameter) => {
                    if (parameter === 'getTimezoneOffset') {
                        return () => -2 * 60; // offset +2 hours
                    }

                    return parameter in target ? target[parameter].bind(target) : undefined;
                },
            });

            expect(getMoscowDate(date).getDate()).toBe(11);
        });
    });
    describe('getClosestFuturePaymentDate', () => {
        const futurePayments = [
            { paymentDate: { seconds: 1551301200 } }, // Thu Feb 28 2019 00:00:00 GMT+0300 (GMT+03:00)
            { paymentDate: { seconds: 1553720400 } }, // Thu Mar 28 2019 00:00:00 GMT+0300 (GMT+03:00)
            { paymentDate: { seconds: 1556485200 } }, // Mon Apr 29 2019 00:00:00 GMT+0300 (GMT+03:00)
        ];
        const fallbackDate: UnixEpoch = { seconds: 1611873058 }; // Fri Jan 29 2021 01:30:58 GMT+0300 (GMT+03:00)

        test('should return first payment date when current date earlier than first payment', () => {
            const date = new Date(2019, 1, 10, 23, 50);

            expect(getClosestFuturePaymentDate(date, futurePayments, fallbackDate)).toBe(
                futurePayments[0].paymentDate,
            );
        });
        test('should return payment date after current date', () => {
            const date = new Date(2019, 2, 10, 23, 50);

            expect(getClosestFuturePaymentDate(date, futurePayments, fallbackDate)).toBe(
                futurePayments[1].paymentDate,
            );
        });
        test('should return fallback date if current date is after all payments', () => {
            const date = new Date(2020, 2, 10, 23, 50);

            expect(getClosestFuturePaymentDate(date, futurePayments, fallbackDate)).toBe(
                fallbackDate,
            );
        });
    });

    describe('toUnixEpoch', () => {
        const dateString = '2022-06-01';

        expect(toUnixEpoch({ dateString })).toEqual({
            seconds: new Date(2022, 5, 1).getTime() / 1000,
        });
    });

    describe(toNativeDate.name, () => {
        it('should return native format data', () => {
            const date = '05.05.2019';
            const result = parse(date, DATE_FORMAT, new Date());

            expect(toNativeDate(date)?.getSeconds()).toEqual(result.getSeconds());
        });

        it('should return null if date is undefined', () => {
            const date = undefined;

            expect(toNativeDate(date)).toEqual(null);
        });

        it('should return null if date is not correct match format', () => {
            const date = 'Hello';

            expect(toNativeDate(date)).toEqual(null);
        });
    });

    describe(calculateMinDate.name, () => {
        const currentTime = new Date('2023-11-07');
        const maxDate = new Date('2023-11-10').valueOf();

        it('calculates minDate when no adjustments are needed', () => {
            const daysBeforeAdvancedRepay = 0;
            const isTodayTimeAfterDeadline = false;
            const isAutomaticProcessingType = true;
            const isMmbCategory = false;
            const isOffDay = (_: number) => false;

            const result = calculateMinDate({
                currentTime,
                maxDate,
                daysBeforeAdvancedRepay,
                isTodayTimeAfterDeadline,
                isAutomaticProcessingType,
                isMmbCategory,
                isOffDay,
            });

            expect(result).toBe(currentTime.valueOf());
        });

        it('calculates minDate when isTodayTimeAfterDeadline is true', () => {
            const daysBeforeAdvancedRepay = 0;
            const isTodayTimeAfterDeadline = true;
            const isAutomaticProcessingType = true;
            const isMmbCategory = false;
            const isOffDay = (_: number) => false;

            const result = calculateMinDate({
                currentTime,
                maxDate,
                daysBeforeAdvancedRepay,
                isTodayTimeAfterDeadline,
                isAutomaticProcessingType,
                isMmbCategory,
                isOffDay,
            });

            const expectedDate = new Date('2023-11-08').valueOf();

            expect(result).toBe(expectedDate);
        });

        it('calculates minDate when maxDate is an off day', () => {
            const daysBeforeAdvancedRepay = 0;
            const isTodayTimeAfterDeadline = false;
            const isAutomaticProcessingType = true;
            const isMmbCategory = false;
            const isOffDay = (date: number) => date === maxDate;

            const result = calculateMinDate({
                currentTime,
                maxDate,
                daysBeforeAdvancedRepay,
                isTodayTimeAfterDeadline,
                isAutomaticProcessingType,
                isMmbCategory,
                isOffDay,
            });

            expect(result).toBe(currentTime.valueOf());
        });

        it('calculates minDate with daysBeforeAdvancedRepay', () => {
            const daysBeforeAdvancedRepay = 3;
            const isTodayTimeAfterDeadline = false;
            const isAutomaticProcessingType = true;
            const isMmbCategory = false;
            const isOffDay = (_: number) => false;

            const result = calculateMinDate({
                currentTime,
                maxDate,
                daysBeforeAdvancedRepay,
                isTodayTimeAfterDeadline,
                isAutomaticProcessingType,
                isMmbCategory,
                isOffDay,
            });

            const expectedDate = new Date('2023-11-10').valueOf();

            expect(result).toBe(expectedDate);
        });
    });

    describe(calculateMaxDate.name, () => {
        const closestFuturePaymentDate = { seconds: 1636251600 };
        const currentTime = new Date('2023-11-07');
        const toDate = toUnixEpoch({ dateString: '2023-11-10' });

        it('calculates maxDate when paymentType is PARTIAL', () => {
            const isAutomaticProcessingType = true;
            const paymentType = earlyPaymentTypes.PARTIAL;

            const result = calculateMaxDate({
                isAutomaticProcessingType,
                paymentType,
                closestFuturePaymentDate,
                currentTime,
                toDate,
            });

            const expectedMaxDate = getMoscowTimeZone(
                getMoscowDate(new Date(1636251600 * 1000)),
            ).valueOf();

            expect(result).toBe(expectedMaxDate);
        });

        it('calculates maxDate when paymentType is FULL', () => {
            const isAutomaticProcessingType = false;
            const paymentType = earlyPaymentTypes.FULL;

            const result = calculateMaxDate({
                isAutomaticProcessingType,
                paymentType,
                closestFuturePaymentDate,
                currentTime,
                toDate,
            });

            const expectedMaxDate = getMoscowTimeZone(
                getMoscowDate(new Date(parseDateFromAny(currentTime, toDate).valueOf())),
            ).valueOf();

            expect(result).toBe(expectedMaxDate);
        });
    });

    describe(getIsLessThanDaysLeft.name, () => {
        const currentDate = new Date('2024-07-31').getTime();

        it('should return true if the target date is within 1 day from the current date', () => {
            const targetDate: UnixEpoch = { seconds: new Date('2024-08-01').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(currentDate, targetDate)).toBe(true);
        });

        it('should return false if the target date is more than 1 day from the current date', () => {
            const targetDate: UnixEpoch = { seconds: new Date('2024-08-02').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(currentDate, targetDate)).toBe(false);
        });

        it('should return true if the target date is within the specified number of days', () => {
            const targetDate: UnixEpoch = { seconds: new Date('2024-08-02').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(currentDate, targetDate, 3)).toBe(true);
        });

        it('should return false if the target date is not within the specified number of days', () => {
            const targetDate: UnixEpoch = { seconds: new Date('2024-08-05').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(currentDate, targetDate, 3)).toBe(false);
        });

        it('should handle leap year correctly', () => {
            const leapYearDate = new Date('2024-02-28').getTime();
            const targetDate: UnixEpoch = { seconds: new Date('2024-03-01').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(leapYearDate, targetDate, 1)).toBe(false);
        });

        it('should handle non-leap year correctly', () => {
            const nonLeapYearDate = new Date('2023-02-28').getTime();
            const targetDate: UnixEpoch = { seconds: new Date('2023-03-01').getTime() / 1000 };

            expect(getIsLessThanDaysLeft(nonLeapYearDate, targetDate, 1)).toBe(true);
        });
    });
});
