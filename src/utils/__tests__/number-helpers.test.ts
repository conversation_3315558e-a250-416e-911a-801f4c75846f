import { type Amount } from 'thrift-services/entities';

import {
    convertAmountToNumber,
    convertAmountToString,
    convertNumberToAmount,
    convertStringToAmount,
    convertStringToNumber,
    divideNumberByMinorUnits,
    getPercentage,
} from '#/src/utils/number-helpers';

describe('number helpers', () => {
    const amount: Amount = {
        amount: 100,
        currency: {
            minorUnits: 1,
        },
    };

    describe('getPercentage', () => {
        it('should return percentage', () => {
            const nominator: Amount = {
                ...amount,
                amount: 20,
            };

            expect(getPercentage(nominator, amount)).toEqual(20);
        });
    });

    describe('convertNumberToAmount', () => {
        it('should convert number to amount', () => {
            expect(convertNumberToAmount(100)).toEqual({
                amount: 10000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            });
        });

        it('should convert number to amount with custom currency', () => {
            expect(convertNumberToAmount(1, { minorUnits: 1 })).toEqual({
                amount: 100,
                currency: {
                    minorUnits: 1,
                },
            });
        });
    });

    describe('convertAmountToNumber', () => {
        it('should convert amount to number', () => {
            expect(convertAmountToNumber(amount)).toEqual(100);
        });
    });

    describe('divideNumberByMinorUnits', () => {
        it('should return 0 if args have not a number', () => {
            expect(divideNumberByMinorUnits(null, 1)).toEqual(0);
        });

        it('should return number', () => {
            expect(divideNumberByMinorUnits(100, 3)).toEqual(33.33);
        });
    });

    describe('convertStringToNumber', () => {
        it('should convert simple string to number', () => {
            expect(convertStringToNumber('100 000')).toEqual(100000);
        });

        it('should convert simple with coma to number', () => {
            expect(convertStringToNumber('100,324')).toEqual(100.324);
        });
    });

    describe('convertStringToAmount', () => {
        it('should convert string to amount', () => {
            expect(convertStringToAmount('100 000', { minorUnits: 1000 })).toEqual({
                amount: 10000000,
                currency: {
                    minorUnits: 1000,
                },
            });
        });

        it('should convert string to amount without currency', () => {
            expect(convertStringToAmount('100 000')).toEqual({
                amount: 10000000,
                currency: {
                    code: 810,
                    mnemonicCode: 'RUR',
                    minorUnits: 100,
                    unicodeSymbol: '₽',
                    fullName: 'Российский рубль',
                },
            });
        });
    });

    describe('convertAmountToString', () => {
        it('should convert amount to string', () => {
            expect(convertAmountToString(amount)).toEqual('100.00');
        });
    });
});
