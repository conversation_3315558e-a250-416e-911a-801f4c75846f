import { getUnixTime, parse } from 'date-fns';

import { type TrancheOption } from '#/src/components/ui/tranches-select/types';
import { DATE_FORMAT } from '#/src/constants/date';

import { type MappedTranche } from '../credit-products-mappers';
import { toUnixEpoch } from '../date';
import { getEarliestTrancheOpenDate, hasActiveTrancheOutOfDateRange } from '../statements';

describe(getEarliestTrancheOpenDate.name, () => {
    beforeEach(() => {
        jest.useFakeTimers().setSystemTime(new Date('2024-29-07'));
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it('returns undefined for an empty array', () => {
        const tranches = [] as MappedTranche[];
        const result = getEarliestTrancheOpenDate({ accounts: [], tranches });

        expect(result).toStrictEqual({ seconds: 0 });
    });

    it('returns the earliest openDate when only one valid openDate is present', () => {
        const tranches = [
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {},
        ] as unknown as MappedTranche[];

        const result = getEarliestTrancheOpenDate({
            accounts: [
                {
                    mainInfo: { openDate: '2021-09-15' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
            ],
            tranches,
        });

        expect(result).toEqual(
            toUnixEpoch({
                dateString: '2021-09-15',
            }),
        );
    });

    it('returns the earliest openDate among valid openDates', () => {
        const tranches = [
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {},
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
        ] as unknown as MappedTranche[];

        const result = getEarliestTrancheOpenDate({
            accounts: [
                {
                    mainInfo: { openDate: '2021-09-15' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
                {
                    mainInfo: { openDate: '2021-09-27' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
                {
                    mainInfo: { openDate: '2021-09-04' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
            ],
            tranches,
        });

        expect(result).toEqual(
            toUnixEpoch({
                dateString: '2021-09-04',
            }),
        );
    });

    it('handles undefined or missing openDate values', () => {
        const tranches = [
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {},
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {
                servicingAccounts: [],
            },
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
        ] as unknown as MappedTranche[];
        const result = getEarliestTrancheOpenDate({
            accounts: [
                {
                    mainInfo: { openDate: '2021-09-15' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
                {
                    mainInfo: { openDate: '2021-09-27' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
                {
                    mainInfo: { openDate: '2021-09-04' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
            ],
            tranches,
        });

        expect(result).toEqual(
            toUnixEpoch({
                dateString: '2021-09-04',
            }),
        );
    });

    it('returns the same openDate when all openDates are the same', () => {
        const tranches = [
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {},
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
            {
                servicingAccounts: [{ openDate: { seconds: ********** } }],
            },
        ] as unknown as MappedTranche[];
        const result = getEarliestTrancheOpenDate({
            accounts: [
                {
                    mainInfo: { openDate: '2021-09-15' },
                    currency: {
                        code: undefined,
                        mnemonics: undefined,
                        minorUnitsNumber: undefined,
                    },
                    specConditions: [],
                    clientInfo: {
                        clientName: undefined,
                        isOwner: undefined,
                        ownerName: undefined,
                        ownerSurname: undefined,
                        ownerPatronymic: undefined,
                    },
                },
            ],
            tranches,
        });

        expect(result).toEqual(
            toUnixEpoch({
                dateString: '2021-09-15',
            }),
        );
    });
});

const formatDate = (dateStr: string) => getUnixTime(parse(dateStr, DATE_FORMAT, new Date()));

describe(hasActiveTrancheOutOfDateRange.name, () => {
    it('returns false if all active tranches are within the date range', () => {
        const data: TrancheOption[] = [
            {
                key: '1',
                value: {
                    isClosed: false,
                    docNumber: 'D1',
                    dealStatus: 'A',
                    fromDate: { seconds: formatDate('01.01.2024') },
                    toDate: { seconds: formatDate('31.01.2024') },
                },
            },
        ];

        const result = hasActiveTrancheOutOfDateRange(data, '01.01.2024', '31.01.2024');

        expect(result).toBe(false);
    });

    it('returns true if an active tranche ends before the range', () => {
        const data: TrancheOption[] = [
            {
                key: '2',
                value: {
                    isClosed: false,
                    docNumber: 'D2',
                    dealStatus: 'A',
                    fromDate: { seconds: formatDate('01.12.2023') },
                    toDate: { seconds: formatDate('31.12.2023') },
                },
            },
        ];

        const result = hasActiveTrancheOutOfDateRange(data, '01.01.2024', '31.01.2024');

        expect(result).toBe(true);
    });

    it('returns true if an active tranche starts after the range', () => {
        const data: TrancheOption[] = [
            {
                key: '3',
                value: {
                    isClosed: false,
                    docNumber: 'D3',
                    dealStatus: 'A',
                    fromDate: { seconds: formatDate('01.02.2024') },
                    toDate: { seconds: formatDate('28.02.2024') },
                },
            },
        ];

        const result = hasActiveTrancheOutOfDateRange(data, '01.01.2024', '31.01.2024');

        expect(result).toBe(true);
    });

    it('ignores tranches that are not active (dealStatus !== "A")', () => {
        const data: TrancheOption[] = [
            {
                key: '4',
                value: {
                    isClosed: false,
                    docNumber: 'D4',
                    dealStatus: 'C',
                    fromDate: { seconds: formatDate('01.12.2023') },
                    toDate: { seconds: formatDate('31.12.2023') },
                },
            },
        ];

        const result = hasActiveTrancheOutOfDateRange(data, '01.01.2024', '31.01.2024');

        expect(result).toBe(false);
    });

    it('returns true if at least one active tranche is out of date range', () => {
        const data: TrancheOption[] = [
            {
                key: '5',
                value: {
                    isClosed: false,
                    docNumber: 'D5',
                    dealStatus: 'A',
                    fromDate: { seconds: formatDate('01.01.2024') },
                    toDate: { seconds: formatDate('31.01.2024') },
                },
            },
            {
                key: '6',
                value: {
                    isClosed: false,
                    docNumber: 'D6',
                    dealStatus: 'A',
                    fromDate: { seconds: formatDate('01.03.2024') },
                    toDate: { seconds: formatDate('31.03.2024') },
                },
            },
        ];

        const result = hasActiveTrancheOutOfDateRange(data, '01.01.2024', '28.02.2024');

        expect(result).toBe(true);
    });

    it('returns false if data is empty', () => {
        const result = hasActiveTrancheOutOfDateRange([], '01.01.2024', '31.01.2024');

        expect(result).toBe(false);
    });
});
