import { ECreditOffers, ECreditOffersCampaignCode } from '#/src/constants/credit-offers';
import { EXTERNAL_PATHS } from '#/src/constants/routing';
import {
    goLanding,
    goToBusinessCreditCard,
    goToCreditFormsApp,
    goToOverformAgreement,
    initExternalRedirect,
} from '#/src/ducks/app/actions';

import { type TMappedCreditOffer } from '../credit-offers-mappers';
import { redirectForms } from '../redirect-forms-helper';

const mockDispatch = jest.fn();

describe(redirectForms.name, () => {
    it('should call goLanding with offer type and campaign code when offer type is EXPRESS_OVERDRAFT_OFFER and campaign code is OVR1', () => {
        const offer = {
            type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
            campaignCode: ECreditOffersCampaignCode.OVR1,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            goLanding({
                landingType: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
                campaignCode: ECreditOffersCampaignCode.OVR1,
            }),
        );
    });

    it('should call goToCreditFormsApp with offer type when offer type is EXPRESS_OVERDRAFT_OFFER and campaign code is LP_OVER_UO', () => {
        const offer = {
            type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
            campaignCode: ECreditOffersCampaignCode.LP_OVER_UO,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            goToCreditFormsApp({ offerType: ECreditOffers.EXPRESS_OVERDRAFT_OFFER }),
        );
    });

    it('should call goToOverformAgreement when offer type is EXPRESS_OVERDRAFT_OFFER and campaign code does not match any specific code', () => {
        const offer = {
            type: ECreditOffers.EXPRESS_OVERDRAFT_OFFER,
            campaignCode: 'OTHER_CAMPAIGN_CODE',
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(goToOverformAgreement({}));
    });

    it('should call goToCreditFormsApp with offer type when offer type is ALFA_BUSINESS_CREDIT_CARD', () => {
        const offer = {
            type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
            campaignCode: ECreditOffersCampaignCode.DEFAULT,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            goToCreditFormsApp({ offerType: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD }),
        );
    });

    it('should call goToCreditFormsApp with offer type when offer type is BUSINESS_CREDIT_CARD and campaign code matches specific codes', () => {
        const offer = {
            type: ECreditOffers.BUSINESS_CREDIT_CARD,
            campaignCode: ECreditOffersCampaignCode.BN_CC_SB,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            goToCreditFormsApp({ offerType: ECreditOffers.BUSINESS_CREDIT_CARD }),
        );
    });

    it('should call goToBusinessCreditCard when offer type is BUSINESS_CREDIT_CARD and campaign code does not match specific codes', () => {
        const offer = {
            type: ECreditOffers.BUSINESS_CREDIT_CARD,
            campaignCode: 'OTHER_CAMPAIGN_CODE',
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(goToBusinessCreditCard({}));
    });

    it('should call initExternalRedirect with the correct link when offer type is BUSINESS_CREDIT_WITH_STATE_SUPPORT', () => {
        const offer = {
            type: ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT,
            campaignCode: ECreditOffersCampaignCode.DEFAULT,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            initExternalRedirect({
                link: EXTERNAL_PATHS.GOSSUPPORT,
                addContextRoot: false,
            }),
        );
    });

    it('should call goLanding with offer type and campaign code when offer type is LEASING', () => {
        const offer = {
            type: ECreditOffers.LEASING,
            campaignCode: ECreditOffersCampaignCode.DEFAULT,
        } as TMappedCreditOffer;

        redirectForms({ offer, dispatch: mockDispatch });

        expect(mockDispatch).toHaveBeenCalledWith(
            goLanding({
                landingType: ECreditOffers.LEASING,
                campaignCode: ECreditOffersCampaignCode.DEFAULT,
            }),
        );
    });
});
