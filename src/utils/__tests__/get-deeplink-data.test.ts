import { CreditProduct } from 'thrift-services/services/credit_requests';

import { type DeeplinkData, getDeeplinkData } from '../get-deeplink-data';

describe(getDeeplinkData.name, () => {
    it('should return empty object if savedQueryParams is empty', () => {
        expect(getDeeplinkData('')).toEqual({});
    });

    it("should return empty object if savedQueryParams doesn't contain targetParams", () => {
        const queryParams = '?foo=123&buzz=456';

        expect(getDeeplinkData(queryParams)).toEqual({});
    });

    it('should return correct deeplinkData if savedQueryParams contain targetParams', () => {
        const queryParams = '?platformId=123&pin=456&advCode=789';

        const deeplinkData: DeeplinkData = {
            platformId: '123',
            pin: '456',
            advCode: '789',
        };

        expect(getDeeplinkData(queryParams)).toEqual(deeplinkData);
    });

    it('should return correct deeplinkData if savedQueryParams contain targetParams with productCode', () => {
        const queryParams = '?platformId=123&pin=456&advCode=789&productCode=UP03';

        const deeplinkData: DeeplinkData = {
            platformId: '123',
            pin: '456',
            advCode: '789',
            productCode: 'UP03',
            creditProduct: CreditProduct.OVERDRAFT,
        };

        expect(getDeeplinkData(queryParams)).toEqual(deeplinkData);
    });
});
