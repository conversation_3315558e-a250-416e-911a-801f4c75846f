import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';

import { ECreditProductsCodes, ECreditProductsDealStatuses } from '#/src/constants/credit-products';
import { EOverdueStatus } from '#/src/constants/overdue-status';
import { createCreditProduct } from '#/src/server/mocks/utils/create-credit-product';
import {
    mapBaseCreditProduct,
    mapCreditProductActions,
    type MappedTranche,
    mapServerCreditProducts,
    mapTranchesToSelectOptions,
} from '#/src/utils/credit-products-mappers';

import { isAmountLessOrEqual } from '../amount';

describe('credit-products-mappers', () => {
    describe('mapServerCreditProducts', () => {
        it('should group products by type', () => {
            const products = [
                ECreditProductsCodes.BUSINESS_CREDIT,
                ECreditProductsCodes.OVERDRAFT,
                ECreditProductsCodes.CREDIT_LINE,
                ECreditProductsCodes.GUARANTY,
                ECreditProductsCodes.GUARANTY_LINE,
                ECreditProductsCodes.SOPUK,
            ].map((code) => createCreditProduct({ productType: code }));

            expect(mapServerCreditProducts(products)).toEqual({
                credits: [
                    createCreditProduct({ productType: ECreditProductsCodes.BUSINESS_CREDIT }),
                ],
                overdrafts: [createCreditProduct({ productType: ECreditProductsCodes.OVERDRAFT })],
                creditCards: [],
                creditLines: [
                    createCreditProduct({ productType: ECreditProductsCodes.CREDIT_LINE }),
                ],
                sopukLine: [createCreditProduct({ productType: ECreditProductsCodes.SOPUK })],
                guaranties: [createCreditProduct({ productType: ECreditProductsCodes.GUARANTY })],
                guarantyLines: [
                    createCreditProduct({ productType: ECreditProductsCodes.GUARANTY_LINE }),
                ],
            });
        });

        it('should return fields with empty values if has not some products', () => {
            const products = [ECreditProductsCodes.OVERDRAFT, ECreditProductsCodes.GUARANTY].map(
                (code) => createCreditProduct({ productType: code }),
            );

            expect(mapServerCreditProducts(products)).toEqual({
                credits: [],
                overdrafts: [createCreditProduct({ productType: ECreditProductsCodes.OVERDRAFT })],
                creditCards: [],
                creditLines: [],
                guaranties: [createCreditProduct({ productType: ECreditProductsCodes.GUARANTY })],
                guarantyLines: [],
                sopukLine: [],
            });
        });

        it('should filter wrong values', () => {
            const products = [ECreditProductsCodes.OVERDRAFT, 'wrong type'].map((code) =>
                createCreditProduct({ productType: code }),
            );

            /* eslint-disable-next-line @typescript-eslint/no-explicit-any */
            products.push([undefined, null, {}, ''] as any);

            expect(mapServerCreditProducts(products)).toEqual({
                credits: [],
                overdrafts: [createCreditProduct({ productType: ECreditProductsCodes.OVERDRAFT })],
                creditCards: [],
                creditLines: [],
                guaranties: [],
                guarantyLines: [],
                sopukLine: [],
            });
        });
    });

    describe('mapTranchesToSelectOptions', () => {
        const buildTranche = (
            docNumber: string,
            amount: number,
            seconds: number,
            dealStatus: string,
        ) =>
            ({
                docNumber,
                requisites: {
                    sum: { amount },
                    fromDate: { seconds },
                    dealStatus,
                },
            }) as MappedTranche;

        const get2Tranches = (status1: string, status2: string) => [
            buildTranche('1', 100, 10, status1),
            buildTranche('1', 100, 20, status2),
        ];

        it('map tranches track different', () => {
            const actual = mapTranchesToSelectOptions(get2Tranches('L', 'A'));

            expect(actual.allInDifferentState).toBeTruthy();
            expect(actual.options[0].content).toContain(' - закрыт');
            expect(actual.options[1].content).not.toContain(' - закрыт');
        });

        it('map tranches track same opened', () => {
            const actual = mapTranchesToSelectOptions(get2Tranches('A', 'A'));

            expect(actual.allInDifferentState).toBeFalsy();
        });

        it('map tranches track same closed', () => {
            const actual = mapTranchesToSelectOptions(get2Tranches('L', 'L'));

            expect(actual.allInDifferentState).toBeFalsy();
        });
    });

    describe('base product isSignatureVisible', () => {
        type TMaybeNumber = number | undefined;

        type TDataSet = Array<[TMaybeNumber, TMaybeNumber, TMaybeNumber]>;

        const getProduct = (
            totalDebtAmount: TMaybeNumber,
            overdueInterestAmount: TMaybeNumber,
            overdueDebtAmount: TMaybeNumber,
        ) =>
            ({
                summary: { totalFine: { amount: totalDebtAmount } },
                debts: {
                    loan: { overdueDebt: { amount: overdueDebtAmount } },
                    interest: { overdueInterest: { amount: overdueInterestAmount } },
                },
            }) as CreditProduct;

        const falsyDataSet: TDataSet = [
            [1, 0, 0],
            [0, 1, 0],
            [0, 1, undefined],
            [1, 1, 1],
        ];

        it.each(falsyDataSet)(
            'falsy if at least one of amount > 0',
            (totalDebtAmount, overdueInterestAmount, overdueDebtAmount) => {
                const { isSignatureVisible } = mapBaseCreditProduct(
                    getProduct(totalDebtAmount, overdueInterestAmount, overdueDebtAmount),
                );

                expect(isSignatureVisible).toBeFalsy();
            },
        );

        const truthyDataSet: TDataSet = [
            [undefined, undefined, undefined],
            [0, 0, 0],
            [-1, -1, -1],
            [-1, 0, -1],
        ];

        it.each(truthyDataSet)(
            'truthy if all the amounts >= 0',
            (totalDebtAmount, overdueInterestAmount, overdueDebtAmount) => {
                const { isSignatureVisible } = mapBaseCreditProduct(
                    getProduct(totalDebtAmount, overdueInterestAmount, overdueDebtAmount),
                );

                expect(isSignatureVisible).toBeTruthy();
            },
        );
    });

    describe(mapBaseCreditProduct.name, () => {
        it('should map the creditProduct object correctly', () => {
            const product = createCreditProduct({
                productType: ECreditProductsCodes.BUSINESS_CREDIT,
                productCode: 'VKLRUVMS3',
            });

            const expectedMappedCreditProduct = {
                ...product,
                ...mapCreditProductActions(product?.actions || []),
                docNumber: product.docNumber,
                dealId: product.dealId || '',
                productCode: product.requisites?.productCode,
                totalToPay: product.summary?.totalToPay,
                debt: product.debts?.loan?.debt,
                interest: product.debts?.interest?.interest,
                debtRate: product.rate?.debtRate,
                debtRateDaily: product.rate?.debtRateDaily,
                overdueStatus:
                    (product.summary?.overdueDays || 0) > 0
                        ? EOverdueStatus.OVERDUE
                        : EOverdueStatus.AVAILABLE,
                overdueDays: product.summary?.overdueDays,
                shortAccountNumber: product.requisites?.account
                    ? product.requisites.account.slice(-4)
                    : '',
                accountNumber: product.requisites?.account || '',
                repaymentAccount: product.requisites?.repaymentAccount || '',
                payDebtTillDate: product.debts?.loan?.payDebtTillDate,
                payInterestTillDate: product.debts?.interest?.payInterestTillDate,
                hasCreditHolidaysFZ: product.frontParams?.hasCreditHolidaysFZ,
                hasPrepaymentAvailability: product.frontParams?.hasPrepaymentAvailability,
                isNotReadyScheduleOfPromoDeal: product.frontParams?.isNotReadyScheduleOfPromoDeal,
                dateDealClose: product.requisites?.dateDealClose,
                isClosedDeal: product.requisites?.dealStatus === ECreditProductsDealStatuses.L,
                fault: false as const,
                totalDebt: product.summary?.totalDebt,
                totalLoanSumToPay: product.summary?.totalLoanSumToPay,
                totalInterestSumToPay: product.summary?.totalInterestSumToPay,
                totalInterestOverdue: product.summary?.totalInterestOverdue,
                totalLoanSum: product.summary?.totalLoanSum,
                sum: product.requisites?.sum,
                overdueDebt: product.debts?.loan?.overdueDebt,
                overdueInterest: product.debts?.interest?.overdueInterest,
                interestToPay: product.debts?.interest?.interestToPay,
                debtToPay: product.debts?.loan?.debtToPay,
                fineDebt: product.debts?.loan?.fineDebt,
                fineInterest: product.debts?.interest?.fineInterest,
                totalFine: product.summary?.totalFine,
                dailyFine: product.summary?.dailyFine,
                availableAmount: product.availableAmount,
                totalOverdue: product.summary?.totalOverdue,
                totalOverdueAndFine: product.summary?.totalOverdueAndFine,
                fromDate: product.requisites?.fromDate,
                toDate: product.requisites?.toDate,
                limit: product.requisites?.limit,
                actualDate: product.actualDate,
                overdueDebtRate: product.rate?.overdueDebtRate,
                trancheDeadline: product.requisites?.trancheDeadline,
                clientLimit: product.requisites?.clientLimit,
                maxLimit: product.requisites?.maxLimit,
                hasClientLimit: product.frontParams?.hasClientLimit,
                hasDealDocs: product.frontParams?.hasDealDocs,
                beneficiary: product.requisites?.beneficiary,
                feeToPay: product.debts?.fee?.feeToPay,
                payFeeTillDate: product.debts?.fee?.payFeeTillDate,
                demandToPay: product.debts?.guaranteeRequirements?.demandToPay,
                payDemandTillDate: product.debts?.guaranteeRequirements?.payDemandTillDate,
                insuranceDate: product.requisites?.insuranceDate,
                feeRate: product.rate?.feeRate,
                daysBeforeAdvancedRepay: product.requisites?.daysBeforeAdvancedRepay,
                isAnnuityScheduleType: product.requisites?.isAnnuityScheduleType,
                suspensiveConditionsDeadlineInfo:
                    product.requisites?.suspensiveConditionsDeadlineInfo,
                isSignatureVisible: [
                    product?.summary?.totalFine,
                    product?.debts?.loan?.overdueDebt,
                    product?.debts?.interest?.overdueInterest,
                ].every(isAmountLessOrEqual(0)),
            };

            const mappedCreditProduct = mapBaseCreditProduct(product);

            expect(mappedCreditProduct).toEqual(expectedMappedCreditProduct);
        });
    });
});
