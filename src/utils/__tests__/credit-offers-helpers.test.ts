import { ECreditOffers } from '#/src/constants/credit-offers';
import {
    combineOffers,
    CREDIT_OFFERS,
    getSortedCreditOffers,
} from '#/src/utils/credit-offers-helpers';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

describe('credit offers helpers', () => {
    const offers: TMappedCreditOffer[] = [
        {
            titlesVariants: {
                long: 'long name business credit',
                short: 'short name business credit',
            },
            type: ECreditOffers.BUSINESS_CREDIT,
            expiryDate: {
                seconds: 100000,
            },
            preApproved: false,
        },
        {
            titlesVariants: {
                long: 'long name business credit with pledge',
                short: 'short name business credit with pledge',
            },
            type: ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE,
            expiryDate: {
                seconds: 100000,
            },
            preApproved: false,
        },
        {
            titlesVariants: {
                long: 'long name business credit card',
                short: 'short name business credit card',
            },
            type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
            expiryDate: {
                seconds: 100000,
            },
            preApproved: false,
        },
        {
            titlesVariants: {
                long: 'long name alfa business credit card',
                short: 'short name alfa business credit card',
            },
            type: ECreditOffers.BUSINESS_CREDIT_CARD,
            expiryDate: {
                seconds: 100000,
            },
            preApproved: false,
        },
    ];

    it('should return 1 combined offer between others', () => {
        const expected: TMappedCreditOffer[] = [
            {
                ...offers[0],
                additionalOffer: {
                    ...offers[2],
                },
            },
            { ...offers[1] },
            { ...offers[3] },
        ];

        expect(
            combineOffers(offers, [
                {
                    mainOffer: ECreditOffers.BUSINESS_CREDIT,
                    additionalOffer: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                },
            ]),
        ).toEqual(expected);
    });

    describe(getSortedCreditOffers.name, () => {
        it('should return an empty array if creditOffers is empty', () => {
            const result = getSortedCreditOffers([]);

            expect(result).toEqual([]);
        });

        it('should ignore credit offers with unknown types', () => {
            const creditOffers = [
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                },
                {
                    type: 'UNKNOWN_TYPE',
                },
            ];

            const expectedResult = [
                {
                    ...creditOffers[0],
                    ...CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT],
                },
            ];

            const result = getSortedCreditOffers(creditOffers as TMappedCreditOffer[]);

            expect(result).toEqual(expectedResult);
        });

        it('should handle an empty titlesVariants object in a credit offer', () => {
            const creditOffers = [
                {
                    type: ECreditOffers.OVERDRAFT,
                    titlesVariants: {},
                },
            ];

            const expectedOffers = [CREDIT_OFFERS[ECreditOffers.OVERDRAFT]];

            const expectedResult = creditOffers.map((offer, index) => ({
                ...offer,
                ...expectedOffers[index],
            }));

            const result = getSortedCreditOffers(creditOffers as TMappedCreditOffer[]);

            expect(result).toEqual(expectedResult);
        });

        it('should handle creditOffers with different types', () => {
            const creditOffers = [
                {
                    type: ECreditOffers.OVERDRAFT,
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                },
            ];

            const expectedOffers = [
                CREDIT_OFFERS[ECreditOffers.OVERDRAFT],
                CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT],
            ];

            const expectedResult = creditOffers.map((offer, index) => ({
                ...offer,
                ...expectedOffers[index],
            }));

            const result = getSortedCreditOffers(creditOffers as TMappedCreditOffer[]);

            expect(result).toEqual(expectedResult);
        });

        it('should handle creditOffers with various types and ordering', () => {
            const creditOffers = [
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                },
                {
                    type: ECreditOffers.REFILLABLE_CREDIT_LINE,
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT_CARD,
                },
                {
                    type: ECreditOffers.OVERDRAFT,
                },
                {
                    type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                },
            ];

            const expectedResult = [
                { type: ECreditOffers.OVERDRAFT, ...CREDIT_OFFERS[ECreditOffers.OVERDRAFT] },
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                    ...CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT],
                },
                {
                    type: ECreditOffers.REFILLABLE_CREDIT_LINE,
                    ...CREDIT_OFFERS[ECreditOffers.REFILLABLE_CREDIT_LINE],
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT_CARD,
                    ...CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT_CARD],
                },
                {
                    type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                    ...CREDIT_OFFERS[ECreditOffers.ALFA_BUSINESS_CREDIT_CARD],
                },
            ];

            const result = getSortedCreditOffers(creditOffers as TMappedCreditOffer[]);

            expect(result).toEqual(expectedResult);
        });
        it('should handle preapproved creditOffers with various types and ordering', () => {
            const creditOffers = [
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                    preApproved: true,
                },
                {
                    type: ECreditOffers.REFILLABLE_CREDIT_LINE,
                    preApproved: true,
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT_CARD,
                    preApproved: true,
                },
                {
                    type: ECreditOffers.OVERDRAFT,
                    preApproved: true,
                },
                {
                    type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                    preApproved: true,
                },
                {
                    type: ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS,
                    preApproved: true,
                },
            ];

            const expectedResult = [
                {
                    type: ECreditOffers.OVERDRAFT,
                    preApproved: true,
                    ...CREDIT_OFFERS[ECreditOffers.OVERDRAFT],
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT,
                    preApproved: true,
                    ...CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT],
                },
                {
                    type: ECreditOffers.REFILLABLE_CREDIT_LINE,
                    preApproved: true,
                    ...CREDIT_OFFERS[ECreditOffers.REFILLABLE_CREDIT_LINE],
                },
                {
                    type: ECreditOffers.BUSINESS_CREDIT_CARD,
                    preApproved: true,
                    ...CREDIT_OFFERS[ECreditOffers.BUSINESS_CREDIT_CARD],
                },
                {
                    type: ECreditOffers.ALFA_BUSINESS_CREDIT_CARD,
                    preApproved: true,
                    ...CREDIT_OFFERS[ECreditOffers.ALFA_BUSINESS_CREDIT_CARD],
                },
            ];

            const result = getSortedCreditOffers(creditOffers as TMappedCreditOffer[]);

            expect(result).toEqual(expectedResult);
        });
    });
});
