import { type FileUploadItemProps } from '@alfalab/core-components/file-upload-item';

import { type ActiveCreditCaseStage } from '#/src/types/client-documents';
import {
    ACTIVE_CREDIT_CASE_STAGE_MAP,
    getActiveCreditInfo,
    mapDocumentToFileUpload,
} from '#/src/utils/client-documents';

describe('client documents', () => {
    describe('mapDocumentToFileUpload', () => {
        const documentsMap = new Map();

        it('should return empty array if has not documents', () => {
            expect(mapDocumentToFileUpload(null)).toEqual([]);

            expect(mapDocumentToFileUpload(documentsMap)).toEqual([]);
        });

        it('should return array documents', () => {
            const documents: FileUploadItemProps[] = [
                {
                    id: '1',
                    size: 10000000000,
                    uploadStatus: 'SUCCESS',
                    showDelete: false,
                },
                {
                    id: '2',
                    size: 20000000000,
                    uploadStatus: 'ERROR',
                    showDelete: true,
                },
            ];

            documents.forEach((item) => documentsMap.set(`${item.id}`, item));

            expect(mapDocumentToFileUpload(documentsMap)).toEqual(documents);
        });
    });

    describe('getActiveCreditInfo', () => {
        it('should return null if stage is empty string', () => {
            expect(getActiveCreditInfo(undefined)).toEqual(null);
        });

        it('should return active credit info', () => {
            const keys = Object.keys(ACTIVE_CREDIT_CASE_STAGE_MAP) as ActiveCreditCaseStage[];

            expect(getActiveCreditInfo(keys[0])).toEqual({
                stage: keys[0],
                data: ACTIVE_CREDIT_CASE_STAGE_MAP[keys[0]],
            });
        });
    });
});
