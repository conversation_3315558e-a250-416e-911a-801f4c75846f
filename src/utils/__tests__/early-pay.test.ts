import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';

import { earlyRepaymentButtonStates } from '#/src/constants/credit-processing';

import { getEarlyRepaymentButtonState } from '../early-pay';

const dateSeconds = new Date().getTime() / 1000;

describe('getEarlyRepaymentButtonState', () => {
    it('should return "issueDate" when there is no interest', () => {
        const product = {
            requisites: {
                issueDate: {
                    seconds: dateSeconds + 100000,
                },
            },
        };

        const result = getEarlyRepaymentButtonState(product as CreditProduct);

        expect(result).toBe(earlyRepaymentButtonStates.disabled);
    });

    it('should return "fromDate" when there is no interest', () => {
        const product = {
            requisites: {
                fromDate: {
                    seconds: dateSeconds - 100000,
                },
            },
        };

        const result = getEarlyRepaymentButtonState(product as CreditProduct);

        expect(result).toBe(earlyRepaymentButtonStates.enabled);
    });

    it('should return "hasOverdue" when there is overdue debt, overdue interest, or total fine', () => {
        const product = {
            debts: {
                loan: {
                    overdueDebt: {
                        amount: 200,
                    },
                },
                interest: {
                    overdueInterest: {
                        amount: 300,
                    },
                },
            },
            summary: {
                totalFine: {
                    amount: 50,
                },
            },
        };

        const result = getEarlyRepaymentButtonState(product as CreditProduct);

        expect(result).toBe(earlyRepaymentButtonStates.hasOverdue);
    });

    it('should return "enabled" when none of the above conditions apply', () => {
        const product = {
            debts: {
                interest: {
                    interest: {
                        amount: 500,
                    },
                },
            },
            summary: {},
        };

        const result = getEarlyRepaymentButtonState(product as CreditProduct);

        expect(result).toBe(earlyRepaymentButtonStates.enabled);
    });
});
