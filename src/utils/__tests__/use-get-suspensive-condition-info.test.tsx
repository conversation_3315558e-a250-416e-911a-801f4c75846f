import React from 'react';
import { useSelector } from 'react-redux';
import {
    MonitoringOperationStatus,
    type SuspensiveCondition,
    SuspensiveConditionType,
} from 'corp-credit-products-api-typescript-services';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Status } from '@alfalab/core-components/status';

import { ECreditProducts } from '#/src/constants/credit-products';

import { type SomeMappedProduct } from '../credit-products-mappers';
import { useGetSuspensiveConditionInfo } from '../hooks/use-get-suspensive-condition-info';

jest.mock('@alfalab/core-components/mq', () => ({
    useMatchMedia: jest.fn(),
}));

jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
}));

const product = {
    type: ECreditProducts.BUSINESS_CREDIT,
    docNumber: '123',
};

describe('useGetSuspensiveConditionInfo helpers', () => {
    beforeEach(() => {
        (useSelector as jest.Mock).mockReturnValue(1741093529);
    });

    it('should return Violated status', () => {
        (useMatchMedia as jest.Mock).mockReturnValueOnce([true]);

        const condition: SuspensiveCondition = {
            status: MonitoringOperationStatus.Violated,
            planDate: 1741093529,
            description: 'description',
            expirationDays: 0,
            type: SuspensiveConditionType.Ou1,
        };

        expect(useGetSuspensiveConditionInfo(condition, product as SomeMappedProduct)).toEqual({
            completeLabelDate: 'Срок истёк 04.03.2025',
            description: 'description',
            status: 'VIOLATED',
            statusLabel: (
                <Status shape='rounded' view='soft' uppercase={true} color='red'>
                    Нарушено
                </Status>
            ),
            subTitleLabel: 'Кредит №123',
            typeLable: 'Оформление и регистрация залога',
        });
    });

    it('should return Executed status', () => {
        (useMatchMedia as jest.Mock).mockReturnValueOnce([true]);

        const condition: SuspensiveCondition = {
            status: MonitoringOperationStatus.Executed,
            planDate: 1741093529,
            description: 'description',
            expirationDays: 0,
            type: SuspensiveConditionType.Ou1,
        };

        expect(useGetSuspensiveConditionInfo(condition, product as SomeMappedProduct)).toEqual({
            completeLabelDate: 'Выполнено 04.03.2025',
            description: 'description',
            status: 'EXECUTED',
            statusLabel: (
                <Status shape='rounded' view='soft' uppercase={true} color='green'>
                    Выполнено
                </Status>
            ),
            subTitleLabel: 'Кредит №123',
            typeLable: 'Оформление и регистрация залога',
        });
    });

    it('should return Expiring status', () => {
        (useMatchMedia as jest.Mock).mockReturnValueOnce([true]);

        const condition: SuspensiveCondition = {
            status: MonitoringOperationStatus.Expiring,
            planDate: 1741093529,
            description: 'description',
            expirationDays: 0,
            type: SuspensiveConditionType.Ou1,
        };

        expect(useGetSuspensiveConditionInfo(condition, product as SomeMappedProduct)).toEqual({
            completeLabelDate: 'Выполнить до 04.03.2025',
            description: 'description',
            status: 'EXPIRING',
            statusLabel: (
                <Status shape='rounded' view='soft' uppercase={true} color='orange'>
                    Истекает сегодня
                </Status>
            ),
            subTitleLabel: 'Кредит №123',
            typeLable: 'Оформление и регистрация залога',
        });
    });

    it('should return Waiting status', () => {
        (useMatchMedia as jest.Mock).mockReturnValueOnce([true]);

        const condition: SuspensiveCondition = {
            status: MonitoringOperationStatus.Waiting,
            planDate: 1741093529,
            description: 'description',
            expirationDays: 0,
            type: SuspensiveConditionType.Ou1,
        };

        expect(useGetSuspensiveConditionInfo(condition, product as SomeMappedProduct)).toEqual({
            completeLabelDate: 'Выполнить до 04.03.2025',
            description: 'description',
            status: 'WAITING',
            statusLabel: (
                <Status shape='rounded' view='soft' uppercase={true} color='grey'>
                    Ожидает выполнения
                </Status>
            ),
            subTitleLabel: 'Кредит №123',
            typeLable: 'Оформление и регистрация залога',
        });
    });

    it('should return Waiting status in mobile', () => {
        (useMatchMedia as jest.Mock).mockReturnValueOnce([false]);

        const condition: SuspensiveCondition = {
            status: MonitoringOperationStatus.Waiting,
            planDate: 1741093529,
            description: 'description',
            expirationDays: 0,
            type: SuspensiveConditionType.Ou1,
        };

        expect(useGetSuspensiveConditionInfo(condition, product as SomeMappedProduct)).toEqual({
            completeLabelDate: 'Выполнить до 04.03.2025',
            description: 'description',
            status: 'WAITING',
            statusLabel: (
                <Status shape='rounded' view='contrast' uppercase={false} color='grey'>
                    Ожидает выполнения
                </Status>
            ),
            subTitleLabel: 'Кредит №123',
            typeLable: 'Оформление и регистрация залога',
        });
    });
});
