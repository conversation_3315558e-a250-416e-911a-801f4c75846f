import { type CreditOffer } from 'thrift-services/services/credit_offers';

import { ECreditOffers, ECreditOffersCampaignCode } from '#/src/constants/credit-offers';
import { EProductCodes } from '#/src/constants/credit-products';
import { NBSP } from '#/src/constants/unicode-symbols';
import { mapCreditOffers, type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

describe('credit offers mappers', () => {
    const generateCreditOffer = (offer: TMappedCreditOffer, withVariants = true): CreditOffer => ({
        type: {
            id: offer.type,
            preApproved: offer.preApproved,
            visibleForUser: offer.visibleForUser,
            campaignCode: offer.campaignCode,
        },
        ewsId: offer.ewsId,
        expiryDate: offer.expiryDate,
        variants: withVariants
            ? [
                  {
                      id: offer.id,
                      minimumAmount: offer.minimumAmount,
                      maximumAmount: offer.maximumAmount,
                      maximumTerm: offer.maximumTerm,
                      yearRatePct: offer.yearRatePct,
                      oneTimeComissionPct: offer.oneTimeComissionPct,
                      maximumLimit: offer.maximumLimit,
                      landingVersion: offer.landingVersion,
                      creditCardVariant: offer.creditCardVariant,
                      loanPurpose: offer.loanPurpose,
                  },
              ]
            : undefined,
    });

    const generalDataCreditOffer = {
        maximumAmount: {
            amount: 1000000,
            currency: {
                minorUnits: 1,
                unicodeSymbol: '₽',
            },
        },
        minimumAmount: {
            amount: 1000,
            currency: {
                minorUnits: 1,
                unicodeSymbol: '₽',
            },
        },
        maximumTerm: 12,
        yearRatePct: 11,
        id: undefined,
        landingVersion: 0,
        expiryDate: {
            seconds: 1000,
        },
        visibleForUser: true,
        preApproved: true,
        loanPurpose: ['1'],
        maximumLimit: undefined,
        campaignCode: 'CAMPAIGN CODE',
        code: undefined,
    };

    const preApprovedCreditOffers: TMappedCreditOffer[] = [
        {
            titlesVariants: {
                short: 'Кредит',
                long: 'Кредит для бизнеса',
            },
            type: ECreditOffers.BUSINESS_CREDIT,
            text: `Кредит одной суммой на пополнение оборотных средств и${NBSP}инвестиционные цели`,
            ...generalDataCreditOffer,
            productCode: EProductCodes.BP11,
            ewsId: 'HRG203222320UA9IWF',
        },
        {
            titlesVariants: {
                short: 'Карта Альфа-Бизнес Кредит',
                long: 'Карта Альфа-Бизнес Кредит',
            },
            type: ECreditOffers.BUSINESS_CREDIT_CARD,
            text: `Не ставьте бизнес на паузу${NBSP}- оплачивайте расходы кредитной картой`,
            ...generalDataCreditOffer,
            campaignCode: ECreditOffersCampaignCode.LP_CC1,
            creditCardVariant: undefined,
            ewsId: 'HRG203222320UA9IWF',
            productCode: EProductCodes.UC04,
        },
        {
            titlesVariants: {
                short: 'Кредит',
                long: 'Кредит для бизнеса',
            },
            type: ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE,
            text: `Кредит одной суммой на пополнение оборотных средств и${NBSP}инвестиционные цели`,
            ...generalDataCreditOffer,
            productCode: EProductCodes.UF99,
            ewsId: 'HRG203222320UA9IWF',
        },
        {
            type: ECreditOffers.OVERDRAFT,
            titlesVariants: {
                short: 'Овердрафт',
                long: 'Овердрафт',
            },
            text: `Возможность уходить в${NBSP}минус по${NBSP}счёту и${NBSP}пользоваться деньгами банка, когда нужно`,
            ...generalDataCreditOffer,
            campaignCode: ECreditOffersCampaignCode.LP_OVER,
            productCode: EProductCodes.UP03,
            ewsId: 'HRG203222320UA9IWF',
        },
        {
            type: ECreditOffers.REFILLABLE_CREDIT_LINE,
            titlesVariants: {
                long: 'Возобновляемая кредитная линия',
                short: 'Кредитная линия',
            },
            text: 'Возможность брать транши на любые суммы в пределах лимита. При погашении транша лимит восстанавливается',
            productCode: EProductCodes.VMS2,
            oneTimeComissionPct: 1111,
            ewsId: 'HRG203222320UA9IWF',
            ...generalDataCreditOffer,
        },
    ];

    const preUnApprovedCreditOffers: TMappedCreditOffer[] = [
        {
            type: ECreditOffers.GUARANTY,
            titlesVariants: {
                long: 'Банковская гарантия',
                short: 'Гарантия',
            },
            text: `Обеспечение исполнения${NBSP}обязательств`,
            oneTimeComissionPct: 111,
            ...generalDataCreditOffer,
            preApproved: false,
        },
        {
            type: ECreditOffers.BUSINESS_MORTGAGE,
            titlesVariants: {
                long: 'Бизнес-ипотека',
                short: 'Ипотека',
            },
            text: 'Покупка жилой или коммерческой недвижимости с аккредитивом',
            oneTimeComissionPct: 1111,
            productCode: EProductCodes.UF05,
            ...generalDataCreditOffer,
            preApproved: false,
        },
        {
            type: ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE,
            titlesVariants: {
                long: 'Возобновляемая кредитная линия c залогом',
                short: 'Кредитная линия',
            },
            text: 'На оплату периодических расходов. Лимит возвращается после погашения использованной части займа',
            oneTimeComissionPct: 11111,
            productCode: EProductCodes.VMS2,
            ...generalDataCreditOffer,
            preApproved: false,
        },
        {
            type: ECreditOffers.BUSINESS_FACTORING,
            titlesVariants: {
                long: 'Финансирование дебиторской задолженности',
                short: 'Финансирование дебиторской задолженности',
            },
            oneTimeComissionPct: 111,
            text: `Услуги для производителей и${NBSP}поставщиков`,
            ...generalDataCreditOffer,
            preApproved: false,
        },
        {
            type: ECreditOffers.LEASING,
            titlesVariants: {
                short: 'Лизинг',
                long: 'Лизинг',
            },
            text: 'Экспресс-решение для клиентов банка',
            ...generalDataCreditOffer,
            preApproved: false,
        },
        {
            type: ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT,
            titlesVariants: {
                short: 'Кредит для поддержки и развития бизнеса',
                long: 'Кредит для поддержки и развития бизнеса',
            },
            text: `Кредиты для${NBSP}бизнеса`,
            description: `Специальные программы для${NBSP}субъектов малого и${NBSP}среднего бизнеса, ИТ,${NBSP}агропрома, производителей и${NBSP}экспортеров. Получите деньги на${NBSP}развитие бизнеса или${NBSP}операционные расходы`,
            ...generalDataCreditOffer,
            campaignCode: ECreditOffersCampaignCode.PODDERJKA_SB,
            preApproved: false,
        },
    ];

    const creditOffers: CreditOffer[] = [
        ...preApprovedCreditOffers,
        ...preUnApprovedCreditOffers,
    ].map((offer) => generateCreditOffer(offer));

    const creditOffersWithoutVariants: CreditOffer[] = [
        ...preApprovedCreditOffers,
        ...preUnApprovedCreditOffers,
    ].map((offer) => generateCreditOffer(offer, false));

    it('should return empty array values if has not creditOffers', () => {
        expect(mapCreditOffers([])).toEqual({
            preApproved: [],
            offers: [],
        });
    });

    it('should return mapped credit offers', () => {
        expect(mapCreditOffers(creditOffers)).toEqual({
            preApproved: preApprovedCreditOffers,
            offers: preUnApprovedCreditOffers,
        });
    });

    it('should handle all variants fields as undefined (optional chaining)', () => {
        expect(mapCreditOffers(creditOffersWithoutVariants)).toBeDefined();
    });
});
