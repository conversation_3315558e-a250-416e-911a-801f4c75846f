import { type SignedDocument } from '#/src/types/signed-document';
import {
    downloadBinaryFile,
    downloadBinaryFileByExtension,
    downloadSignedBinaryFileByExtension,
} from '#/src/utils/binary-download';

describe('binary download tests', () => {
    let createObjectURLMock: jest.Mock;
    let revokeObjectURLMock: jest.Mock;
    let appendChildMock: jest.Mock;
    let removeChildMock: jest.Mock;
    let clickMock: jest.Mock;

    beforeEach(() => {
        createObjectURLMock = jest.fn().mockReturnValue('http://localhost/mock-url');
        revokeObjectURLMock = jest.fn();
        appendChildMock = jest.fn();
        removeChildMock = jest.fn();
        clickMock = jest.fn();

        global.URL.createObjectURL = createObjectURLMock;
        global.URL.revokeObjectURL = revokeObjectURLMock;
        document.body.appendChild = appendChildMock;
        document.body.removeChild = removeChildMock;
        HTMLAnchorElement.prototype.click = clickMock;
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should trigger download for binary file', () => {
        const mockBlob = new Blob(['file-content'], { type: 'application/octet-stream' });
        const fileName = 'file-name';

        downloadBinaryFile(mockBlob, fileName);

        expect(createObjectURLMock).toHaveBeenCalledWith(mockBlob);
        expect(document.body.appendChild).toHaveBeenCalled();
        expect(clickMock).toHaveBeenCalled();
        expect(removeChildMock).toHaveBeenCalled();
        expect(revokeObjectURLMock).toHaveBeenCalledWith(expect.stringContaining('mock-url'));
    });

    it('should trigger download for binary file with extension', () => {
        const mockBlob = new Blob(['file-content'], { type: 'application/octet-stream' });
        const fileName = 'file-name';
        const fileExtension = 'pdf';

        downloadBinaryFileByExtension(mockBlob, fileName, fileExtension);

        expect(createObjectURLMock).toHaveBeenCalledWith(mockBlob);
        expect(document.body.appendChild).toHaveBeenCalled();
        expect(clickMock).toHaveBeenCalled();
        expect(removeChildMock).toHaveBeenCalled();
        expect(revokeObjectURLMock).toHaveBeenCalledWith(expect.stringContaining('mock-url'));

        const link = document.createElement('a');

        link.download = 'file-name.pdf';
        expect(link.download).toBe('file-name.pdf');
    });

    it('should trigger download for signed binary file with extension', () => {
        const signedContent: SignedDocument['body'] = {
            type: 'signed-file-content',
            data: [1, 2, 3],
        };
        const fileName = 'signed-file-name';
        const fileExtension = 'xls';

        const bufferMock = Buffer.from(signedContent.data);
        const blobMock = new Blob([bufferMock.buffer], { type: 'application/vnd.ms-excel' });

        downloadSignedBinaryFileByExtension(signedContent, fileName, fileExtension);

        expect(createObjectURLMock).toHaveBeenCalledWith(blobMock);
        expect(document.body.appendChild).toHaveBeenCalled();
        expect(clickMock).toHaveBeenCalled();
        expect(removeChildMock).toHaveBeenCalled();
        expect(revokeObjectURLMock).toHaveBeenCalledWith(expect.stringContaining('mock-url'));

        const link = document.createElement('a');

        link.download = 'signed-file-name.xls';
        expect(link.download).toBe('signed-file-name.xls');
    });
});
