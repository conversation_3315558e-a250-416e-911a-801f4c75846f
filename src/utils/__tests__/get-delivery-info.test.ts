import { type TDeliveryInfo } from 'thrift-services/services/credit_application_processing';

import { DeliveryDocumentStatus } from '#/src/ducks/credit-processing/types';
import { getDeliveryInfoData } from '#/src/utils/client-documents';

describe('getDeliveryInfoData', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('Должен вернуть корректную информацию о доставке в статусе WAITING_FOR_DOCUMENTS_SIGNING', () => {
        const deliveryInfo: TDeliveryInfo = {
            status: DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
            dateOfSigning: { seconds: 1234567890 },
            address: 'ул. Примерная, д.1',
            slotFrom: '09:00',
            slotTo: '10:00',
        };

        const actual = getDeliveryInfoData(deliveryInfo);

        const expected = {
            description: 'Документы привезут 14 февраля 09:00–10:00 по адресу: ул. Примерная, д.1.',
            textButton: 'Отменить встречу',
            view: 'positive',
            isDelivery: true,
            isDeliveryInProgress: true,
            deliveryStatus: DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
        };

        expect(actual).toEqual(expected);
    });

    it('Должен вернуть корректную информацию о доставке в статусе CANCELLED', () => {
        const deliveryInfo: TDeliveryInfo = {
            status: DeliveryDocumentStatus.CANCELLED,
            dateOfSigning: { seconds: 1234567890 },
            address: 'ул. Примерная, д.1',
            slotFrom: '09:00',
            slotTo: '10:00',
        };

        const actual = getDeliveryInfoData(deliveryInfo);

        const expected = {
            description: 'Чтобы получить кредит, назначьте новую встречу и подпишите документы',
            textButton: 'Назначить новую встречу',
            view: 'negative',
            isDelivery: true,
            isDeliveryInProgress: false,
            deliveryStatus: DeliveryDocumentStatus.CANCELLED,
        };

        expect(actual).toEqual(expected);
    });

    it('Должен вернуть корректную информацию о доставке в статусе COMPLETED', () => {
        const deliveryInfo: TDeliveryInfo = {
            status: DeliveryDocumentStatus.COMPLETED,
            dateOfSigning: { seconds: 1234567890 },
            address: 'ул. Примерная, д.1',
            slotFrom: '09:00',
            slotTo: '10:00',
        };

        const actual = getDeliveryInfoData(deliveryInfo);

        const expected = {
            description:
                'Всё проверим за 1–2 рабочих дня. Как будет готово решение — деньги зачислятся на ваш счёт',
            textButton: undefined,
            view: 'positive',
            isDelivery: true,
            isDeliveryInProgress: false,
            deliveryStatus: DeliveryDocumentStatus.COMPLETED,
        };

        expect(actual).toEqual(expected);
    });

    it('Должен вернуть null, если статус не указан в статусах доставки', () => {
        const deliveryInfo: TDeliveryInfo = {
            status: 'UNKNOWN_STATUS',
            dateOfSigning: { seconds: 1234567890 },
            address: 'ул. Примерная, д.1',
            slotFrom: '09:00',
            slotTo: '10:00',
        };

        const actual = getDeliveryInfoData(deliveryInfo);

        const expected = null;

        expect(actual).toBe(expected);
    });
});
