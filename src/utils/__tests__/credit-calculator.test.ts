import { ECreditOffers } from '#/src/constants/credit-offers';
import { getIsCreditSumInsideCreditAmount } from '#/src/utils/credit-calculator';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

describe('credit calculator', () => {
    const amounts: TMappedCreditOffer = {
        titlesVariants: {
            long: '',
            short: '',
        },
        type: ECreditOffers.BUSINESS_CREDIT,
        expiryDate: {
            seconds: 0,
        },
        maximumAmount: {
            amount: 10,
        },
        minimumAmount: {
            amount: 1,
        },
    };

    it('should return true if  credit sum in the range amount', () => {
        expect(getIsCreditSumInsideCreditAmount(amounts, 2)).toBe(true);
    });

    it('should return false if credit sum out the range amount', () => {
        expect(getIsCreditSumInsideCreditAmount(amounts, 11)).toBe(false);
    });

    it('should return false if maximumAmount and minimumAmount have undefined', () => {
        amounts.maximumAmount = undefined;
        amounts.minimumAmount = undefined;

        expect(getIsCreditSumInsideCreditAmount(amounts, 11)).toBe(false);
    });
});
