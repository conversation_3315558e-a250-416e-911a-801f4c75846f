import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';

import { formatAmount } from 'arui-private/lib/formatters';

import { NBSP } from '#/src/constants/unicode-symbols';
import { getActiveCreditInfoFromSFA } from '#/src/utils/client-documents';

describe('getActiveCreditInfoFromSFA', () => {
    it('Returns the correct object when isOnlineSigningAvailable true', () => {
        const isOnlineSigningAvailable = true;
        const stage = 'choiseDecision';
        const productName = 'Кредит';
        const loanAmount = '10000000';

        const actual = getActiveCreditInfoFromSFA(
            isOnlineSigningAvailable,
            stage,
            productName,
            loanAmount,
        );

        const expected = {
            stage: 'choiseDecision',
            data: {
                title: (
                    <span>
                        Кредит{NBSP}на{NBSP}
                        <span style={{ whiteSpace: 'nowrap' }}>
                            {formatAmount(loanAmount)}
                            {NBSP}₽
                        </span>{' '}
                        одобрен
                    </span>
                ),
                description: 'Осталось только подписать документы',
                textButton: 'К подписанию',
            },
        };

        const renderNode = (node: React.ReactNode) =>
            renderToStaticMarkup(<React.Fragment>{node}</React.Fragment>)
                .replace(/\s+/g, ' ')
                .trim();

        expect(actual?.stage).toEqual(expected.stage);
        expect(actual?.data.description).toEqual(expected.data.description);
        expect(actual?.data.textButton).toEqual(expected.data.textButton);
        expect(renderNode(actual?.data.title)).toEqual(renderNode(expected.data.title));
    });

    it('Returns null when isOnlineSigningAvailable is false', () => {
        const isOnlineSigningAvailable = false;
        const stage = 'choiseDecision';
        const productName = 'Кредит для бизнеса';
        const loanAmount = '10000000';

        const actual = getActiveCreditInfoFromSFA(
            isOnlineSigningAvailable,
            stage,
            productName,
            loanAmount,
        );

        expect(actual).toBeNull();
    });
});
