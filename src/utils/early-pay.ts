import { type CreditProduct } from 'corp-core-credit-products-api-typescript-services';
import { type EmailToKMRequest } from 'corp-early-repayment-api-typescript-services';
import { addDays, format, getUnixTime, isSameDay, parse, startOfDay } from 'date-fns';
import { type TLoanPayment } from 'thrift-services/services/credit_products';
import { type UnixEpoch } from 'thrift-services/utils';

import { earlyRepaymentButtonStates } from '../constants/credit-processing';
import { DATE_FOR_SERVICES, DATE_FORMAT } from '../constants/date';
import { type TApplicationData } from '../ducks/credit-processing/types';
import { earlyPaymentTypes } from '../types/early-pay-sidebar';

import { type SomeMappedProduct } from './credit-products-mappers';
import { getMoscowDate, getMoscowTimeZone, parseDateFromAny } from './date';
import { convertStringToNumber } from './number-helpers';

export const getClosestFuturePaymentDate = (
    currentDate: Date,
    futurePayments: TLoanPayment[],
    fallbackDate: UnixEpoch,
): UnixEpoch => {
    const moscowDate = startOfDay(currentDate);
    const realFuturePayments = futurePayments.filter(
        (payment) => (payment?.paymentDate?.seconds ?? 0) * 1000 >= moscowDate.valueOf(),
    );

    return realFuturePayments.length
        ? (realFuturePayments[0].paymentDate ?? { seconds: 0 })
        : fallbackDate;
};

export const getIsTimeBeforeDeadline = (
    currentDate: Date,
    moscowTimeDeadline: { hours: number; minutes: number } | null,
) => {
    const isHoursEqualDeadline = currentDate.getHours() === (moscowTimeDeadline?.hours || 0);
    const isHoursLessThenDeadline = currentDate.getHours() < (moscowTimeDeadline?.hours || 0);
    const isMinutesLessThenDeadline = currentDate.getMinutes() < (moscowTimeDeadline?.minutes || 0);

    return isHoursLessThenDeadline || (isHoursEqualDeadline && isMinutesLessThenDeadline);
};

export const calculateMinDate = ({
    currentTime,
    maxDate,
    daysBeforeAdvancedRepay,
    isTodayTimeAfterDeadline,
    isAutomaticProcessingType,
    isMmbCategory,
    isOffDay,
}: {
    currentTime: Date;
    maxDate: number;
    daysBeforeAdvancedRepay: number;
    isTodayTimeAfterDeadline: boolean;
    isAutomaticProcessingType: boolean;
    isMmbCategory: boolean;
    isOffDay: (value: number) => boolean;
}) => {
    let minDate = currentTime.valueOf();
    let offsetDays = 0;

    if (daysBeforeAdvancedRepay === 0 && isTodayTimeAfterDeadline) {
        minDate = addDays(currentTime, 1).valueOf();
    }

    while (
        (isOffDay(minDate) && !isSameDay(maxDate, minDate)) ||
        (!isOffDay(minDate) && offsetDays < daysBeforeAdvancedRepay)
    ) {
        minDate = addDays(minDate, 1).valueOf();

        if (!isOffDay(minDate) && daysBeforeAdvancedRepay > 0) {
            offsetDays += 1;
        }
    }

    // add two working days to minDate if segment is MMB and earlyRepaymentProcessingTypes === "MANUAL"
    if (!isAutomaticProcessingType && isMmbCategory) {
        let remainingDaysToAdd = 2;

        while (remainingDaysToAdd > 0) {
            if (!isOffDay(minDate)) {
                remainingDaysToAdd -= 1;
            }
            minDate = addDays(minDate, 1).valueOf();
        }
    }

    return minDate;
};

export const calculateMaxDate = ({
    isAutomaticProcessingType,
    paymentType,
    closestFuturePaymentDate,
    currentTime,
    toDate,
}: {
    isAutomaticProcessingType: boolean;
    paymentType: earlyPaymentTypes;
    closestFuturePaymentDate: UnixEpoch;
    currentTime: Date;
    toDate: UnixEpoch;
}) => {
    const maxDate =
        isAutomaticProcessingType && paymentType === earlyPaymentTypes.PARTIAL
            ? (closestFuturePaymentDate?.seconds ?? 0) * 1000
            : parseDateFromAny(currentTime, toDate).valueOf();

    return getMoscowTimeZone(getMoscowDate(new Date(maxDate))).valueOf();
};

export const creditProductHasOverdue = (product: CreditProduct) =>
    !!product?.debts?.loan?.overdueDebt?.amount ||
    !!product?.debts?.interest?.overdueInterest?.amount ||
    !!product?.summary?.totalFine?.amount;

export const getEarlyRepaymentButtonState = (product: CreditProduct) => {
    if (product && creditProductHasOverdue(product)) {
        return earlyRepaymentButtonStates.hasOverdue;
    }

    const trancheDateSeconds =
        product.requisites?.issueDate?.seconds || product.requisites?.fromDate?.seconds || 0;
    const currentDateSeconds = startOfDay(getMoscowDate(new Date())).getTime() / 1000;

    if (trancheDateSeconds >= currentDateSeconds) {
        return earlyRepaymentButtonStates.disabled;
    }

    return earlyRepaymentButtonStates.enabled;
};

export const getBodyForMailToKM = ({
    docNumber,
    paymentType,
    paymentDate,
    interestToPay,
    debtToPay,
    total,
    currency,
    formatCurrentTime,
    name,
    eqId,
}: TApplicationData & {
    formatCurrentTime: Date;
    name: string;
    eqId: string;
}): EmailToKMRequest => ({
    actualDate: getUnixTime(formatCurrentTime),
    applicationInfo: {
        docNumber,
        customer: {
            id: eqId,
            name,
        },
        isFullEarlyRepaymentType: paymentType === 'full',
        earlyRepaymentDate: getUnixTime(parse(paymentDate, DATE_FORMAT, formatCurrentTime)),
        earlyRepaymentAmount: {
            loan: {
                amount: convertStringToNumber(debtToPay) * (currency?.minorUnits ?? 1),
                currency,
            },
            interest: {
                amount: convertStringToNumber(interestToPay) * (currency?.minorUnits ?? 1),
                currency,
            },
            totalSum: {
                amount: convertStringToNumber(total) * (currency?.minorUnits ?? 1),
                currency,
            },
        },
    },
});

export const getBodyForEarlyPay = ({
    docNumber,
    paymentType,
    paymentDate,
    interestToPay,
    total,
    currency,
    debtToPay,
    selectedAccountNumber,
    product,
    name,
    customerId,
    inn,
    lastName,
    firstName,
    middleName,
    formatCurrentTime,
    calculateFeeForEarlyRepayment,
}: TApplicationData & {
    product: SomeMappedProduct;
    name: string;
    customerId: string;
    inn: string;
    lastName: string;
    firstName: string;
    middleName: string;
    formatCurrentTime: Date;
    calculateFeeForEarlyRepayment: boolean;
}) => ({
    customerId,
    clientInfo: {
        fullName: name,
        inn,
    },
    responsiblePersonInfo: {
        lastName,
        firstName,
        middleName,
    },
    agreementNumber: docNumber,
    accountNumber: selectedAccountNumber,
    isFullEarlyRepayment: paymentType === 'full',
    baseDebtEarlyRepaymentDate: format(
        parse(paymentDate, DATE_FORMAT, formatCurrentTime),
        DATE_FOR_SERVICES,
    ),
    baseDebtEarlyRepaymentAmount: {
        amount: convertStringToNumber(debtToPay) * (currency?.minorUnits ?? 1),
        currency,
    },
    loanInterestEarlyRepaymentAmount: {
        amount: convertStringToNumber(interestToPay) * (currency?.minorUnits ?? 1),
        currency,
    },
    totalSumEarlyRepaymentAmount: {
        amount: convertStringToNumber(total) * (currency?.minorUnits ?? 1),
        currency,
    },
    product: product?.requisites?.product,
    calculateFeeForEarlyRepayment,
});
