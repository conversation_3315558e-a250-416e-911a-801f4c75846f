import {
    ECreditOffers,
    type ECreditOffersCampaignCode,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { ECreditOffersProductType, EProductCodes } from '#/src/constants/credit-products';

export const getIsShowContinueCaseModal = (
    activeProductCode: string,
    type: ECreditOffers | undefined,
    campaignCode: string | undefined,
    isActiveTotalOffer: boolean,
    preferredProduct?: string,
) => {
    const isClickedTotalOffer = TotalOffersCampaignCode.includes(
        campaignCode as ECreditOffersCampaignCode,
    );

    const finalProductCode =
        // Если скоринг L еще не пройден и preferredProduct равен UC04
        // то активным продуктом считается preferredProduct
        activeProductCode === EProductCodes.LT01 && preferredProduct === EProductCodes.UC04
            ? preferredProduct
            : activeProductCode;

    const activeProductType = ECreditOffersProductType[finalProductCode as EProductCodes];

    const finalType =
        // в offers есть только тип ECreditOffers.BUSINESS_CREDIT_CARD
        type === ECreditOffers.ALFA_BUSINESS_CREDIT_CARD
            ? ECreditOffers.BUSINESS_CREDIT_CARD
            : type;

    const isProductTypeMatches = activeProductType === finalType;
    const isTotalOfferMatches = isActiveTotalOffer === isClickedTotalOffer;

    const isShowModal =
        // Если есть активный продукт или выбран продукт из безусловного предодоба
        (activeProductType || isClickedTotalOffer) &&
        // и активный продукт не совпадает с выбранным или активный
        // безусловный предодоб не совпадает с выбранным
        (!isProductTypeMatches || !isTotalOfferMatches);

    return isShowModal;
};
