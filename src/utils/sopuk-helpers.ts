import { type Amount as TAmount } from 'thrift-services/entities';

import { type AmountProps } from '@alfalab/core-components/amount';

export type TSopukCurrencies = {
    [key in Extract<AmountProps['currency'], 'RUR' | 'USD' | 'HKD' | 'EUR' | 'CNY'>]: number;
};

const sopukCurrencyOrder: TSopukCurrencies = {
    RUR: 0,
    USD: 1,
    HKD: 2,
    EUR: 3,
    CNY: 4,
};

export const getSortedCreditAmountsByCurrency = (creditAmountsByCurrency: TAmount[]) =>
    creditAmountsByCurrency.reduce<TAmount[]>((sortedAmounts, amount) => {
        if (amount.currency?.mnemonicCode) {
            const mnemonicCode = amount.currency.mnemonicCode as keyof TSopukCurrencies;
            const order = sopukCurrencyOrder[mnemonicCode];

            sortedAmounts[order] = amount;
        }

        return sortedAmounts;
    }, []);
