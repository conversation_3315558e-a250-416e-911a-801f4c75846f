import { type Account } from 'corp-accounts-api-typescript-services';
import { getUnixTime, parse } from 'date-fns';
import { type UnixEpoch } from 'thrift-services/utils';

import { type MappedTranche } from '#/src/utils/credit-products-mappers';

import { type TrancheOption } from '../components/ui/tranches-select/types';
import { DATE_FORMAT } from '../constants/date';

import { toUnixEpoch } from './date';

export const getEarliestTrancheOpenDate = ({
    accounts,
    tranches,
    startDate,
    currentTime = new Date(),
}: {
    accounts: Account[];
    tranches: MappedTranche[];
    startDate?: UnixEpoch;
    currentTime?: Date;
}) => {
    const openDate = accounts?.[0]?.mainInfo?.openDate;
    const initialValue =
        openDate == null
            ? (startDate ?? { seconds: 0 })
            : toUnixEpoch({
                  currentTime,
                  dateString: openDate,
              });

    return tranches.reduce((currentDate, value) => {
        const formattedValue = toUnixEpoch({
            currentTime,
            dateString:
                accounts.find(
                    (account) =>
                        account?.mainInfo?.number === value?.servicingAccounts?.[0]?.number,
                )?.mainInfo?.openDate ?? '',
        });

        if (currentDate.seconds > formattedValue.seconds) {
            return formattedValue;
        }

        return currentDate;
    }, initialValue);
};

export const hasActiveTrancheOutOfDateRange = (
    data: TrancheOption[],
    fromDateStr: string,
    toDateStr: string,
): boolean => {
    const fromDate = getUnixTime(parse(fromDateStr, DATE_FORMAT, new Date()));
    const toDate = getUnixTime(parse(toDateStr, DATE_FORMAT, new Date()));

    return data.some((item) => {
        if (item.value.dealStatus !== 'A') return false;

        const docFrom = item.value.fromDate.seconds;
        const docTo = item.value.toDate.seconds;

        return docTo < fromDate || docFrom > toDate;
    });
};
