/* Для определения, что значение не равно null или undefined, возвращая правильный тип */
export const isDefinedGuard = <T>(
    value: NonNullable<T> | undefined | null,
): value is NonNullable<T> => value !== null && value !== undefined;

export const isEachItemDefinedGuard = <T>(
    value: Array<NonNullable<T> | undefined | null>,
): value is Array<NonNullable<T>> => value.every(isDefinedGuard);

/* Проверяет на число */
export const numberGuard = <T>(value: number | T): value is number =>
    typeof value === 'number' && Number.isFinite(value);
