import { pluralize } from 'arui-private/lib/formatters';

import { ETrancheTypes } from '#/src/sagas/workers/get-tranche-credit-products-worker';

const MILLION = 1000000;
const THOUSAND = 1000;

export function formatPhone(
    phone: string,
    separators: string | [string, string] | [string, string, string],
) {
    if (!/^\+\d{11}$/.test(phone)) {
        return phone;
    }

    let replaceValue: string;

    if (Array.isArray(separators)) {
        if (separators.length === 2) {
            replaceValue = `$1${separators[0]}$2${separators[0]}$3${separators[1]}$4${separators[1]}$5`;
        } else if (separators.length === 3) {
            replaceValue = `$1${separators[0]}$2${separators[1]}$3${separators[2]}$4${separators[2]}$5`;
        }
    } else {
        replaceValue = `$1${separators}$2${separators}$3${separators}$4${separators}$5`;
    }

    return phone.replace(/(\+\d)(\d{3})(\d{3})(\d{2})(\d{2})/, replaceValue);
}

// форматирование номера 89991234567 в +7 (999) 123-45-67
export const formatPhoneNumber = (number: string): string => {
    // убираем пробелы, скобки и тире, так как номер может быть уже форматирован, но не так как нам нужно
    const cleanNumber = number.replace(/\+|-|\s|\(|\)/g, '');
    // проверяем, что номер содержит код страны, оператора, и номер в формате 1112233
    const match = cleanNumber.match(/^(\d)(\d{3})(\d{3})(\d{2})(\d{2})$/);

    // и форматируем как нам нужно
    return match ? `+7 (${match[2]}) ${match[3]}-${match[4]}-${match[5]}` : '';
};

export function convertAmountToText(
    amount: number,
    {
        hasCurrencySymbol = true,
        isFloat = true,
    }: {
        hasCurrencySymbol?: boolean;
        isFloat?: boolean;
    } = {},
): string {
    const currencySymbol = hasCurrencySymbol ? ' ₽' : '';

    if (amount / MILLION >= 1) {
        const splitAmount = (amount / MILLION).toString().split('.');
        const first = splitAmount[0];
        const second = !splitAmount[1] || !isFloat ? '' : `.${splitAmount[1].slice(0, 2)}`;

        return `${first}${second} млн${currencySymbol}`;
    }

    if (amount / THOUSAND >= 1) {
        return `${(amount / THOUSAND).toString().split('.')[0]} тыс.${currencySymbol}`;
    }

    return `${amount}${currencySymbol}`;
}

export const formatMonthsToString = (
    numberOfMonths: number,
    options?: { isGenitive?: boolean },
) => {
    const declensionsMonth: [string, string, string] = options?.isGenitive
        ? ['месяца', 'месяцев', 'месяцев']
        : ['месяц', 'месяца', 'месяцев'];

    const formattedMonths = `${numberOfMonths} ${pluralize(numberOfMonths, declensionsMonth)}`;

    const result = numberOfMonths ? `${formattedMonths}` : '';

    return `${result}`;
};

export const getFormattedTextByProductType = (count: number, productType: ETrancheTypes) => {
    if (productType === ETrancheTypes.deal) {
        return `${count} ${pluralize(count, ['кредит', 'кредита', 'кредитов'])}`;
    }

    return `${count} ${pluralize(count, ['транш', 'транша', 'траншей'])}`;
};

export const convertKeysToLowerCase = (iterable: unknown) =>
    Object.fromEntries(
        Object.entries(iterable || {}).map(([key, value]) => [key.toLowerCase(), value]),
    );
