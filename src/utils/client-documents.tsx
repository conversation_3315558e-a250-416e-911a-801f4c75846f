import React from 'react';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { type TDeliveryInfo } from 'thrift-services/services/credit_application_processing';

import { type FileUpload } from 'arui-private/file-upload';
import { formatAmount, NBH, NBSP } from 'arui-private/lib/formatters';

import { DAY_MONTH_FORMAT } from '#/src/constants/date';
import { DeliveryDocumentStatus } from '#/src/ducks/credit-processing/types';
import {
    type ActiveCreditCaseInfo,
    type ActiveCreditCaseStage,
    type DocumentInfo,
} from '#/src/types/client-documents';
import { type DeliveryInfoData } from '#/src/types/delivery';

import { getIsLessThanDaysLeft, parseUnixEpochDate } from './date';
import { checkContainsWord } from './string-helpers';

export function mapDocumentToFileUpload(
    documents: Map<string, DocumentInfo> | null,
): React.ComponentProps<typeof FileUpload>['fileUploadItems'] {
    if (!documents?.size) {
        return [];
    }

    return Array.from(documents, (document) => ({
        id: document[1].id,
        name: document[1].name,
        size: document[1].size,
        uploadStatus: document[1].uploadStatus,
        error: document[1].error?.toString(),
        showDelete: document[1].showDelete,
    }));
}

export const ACTIVE_CREDIT_CASE_STAGE_MAP: Record<ActiveCreditCaseStage, ActiveCreditCaseInfo> = {
    needClientDocuments: {
        title: <span>Подготовьте документы для{NBSP}проверки</span>,
        description: `Кредитный аналитик изучит их и${NBSP}сообщит решение банка`,
        textButton: 'Прикрепить документы',
    },
    fillingForm: {
        title: <span>Сохранили заявку на&nbsp;кредитование</span>,
        description: 'Заполните её до конца',
        textButton: 'Перейти к заявке',
    },
    choiseDecision: {
        title: <span>Одобрен кредитный продукт</span>,
        description: `Мы рассмотрели вашу заявку и${NBSP}одобрили кредитный продукт. Ознакомьтесь с ним в${NBSP}интернет${NBH}банке`,
        textButton: 'Ознакомиться',
    },
    signingAgreement: {
        title: <span>Банк одобрил вашу кредитную заявку</span>,
        description: `Выберите счёт и${NBSP}подпишите документы в${NBSP}интернет${NBH}банке с${NBSP}помощью КЭП`,
        textButton: 'Перейти к оформлению',
    },
    processingCreditCard: {
        title: <span>Банк одобрил вашу кредитную заявку</span>,
        description: `Выберите тип карты и${NBSP}подпишите документы в${NBSP}интернет${NBH}банке с${NBSP}помощью КЭП`,
        textButton: 'Перейти к оформлению',
    },
};

export function getActiveCreditInfo(
    stage?: ActiveCreditCaseStage,
): { stage: string; data: ActiveCreditCaseInfo } | null {
    if (stage) {
        return {
            stage,
            data: ACTIVE_CREDIT_CASE_STAGE_MAP[stage],
        };
    }

    return null;
}

export function getDeliveryInfoData(deliveryInfo: TDeliveryInfo): DeliveryInfoData | null {
    if (!deliveryInfo) {
        return null;
    }
    const status: DeliveryDocumentStatus = deliveryInfo?.status as DeliveryDocumentStatus;

    const isDelivery = [
        DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
        DeliveryDocumentStatus.IN_PROGRESS,
        DeliveryDocumentStatus.CANCELLED,
        DeliveryDocumentStatus.COMPLETED,
        DeliveryDocumentStatus.AT_CLIENT,
    ].includes(status);

    if (isDelivery) {
        const isDeliveryInProgress = [
            DeliveryDocumentStatus.WAITING_FOR_DOCUMENTS_SIGNING,
            DeliveryDocumentStatus.IN_PROGRESS,
            DeliveryDocumentStatus.AT_CLIENT,
        ].includes(status);

        const nowDate = new Date();

        nowDate.setHours(0, 0, 0, 0);

        const parsedDate = parseUnixEpochDate(deliveryInfo.dateOfSigning);

        const isLessThanDaysLeft = getIsLessThanDaysLeft(
            nowDate.getTime(),
            deliveryInfo.dateOfSigning,
        );

        let resultData: DeliveryInfoData = {
            deliveryStatus: status,
            description: '',
            textButton: undefined,
            view: 'positive',
            isDelivery: true,
            isDeliveryInProgress,
        };

        if (isDeliveryInProgress && parsedDate) {
            const addressText = `по адресу: ${deliveryInfo.address}.`;
            const deliveryDate = format(parsedDate, DAY_MONTH_FORMAT, {
                locale: ru,
            });
            const deliveryTime = `${deliveryInfo.slotFrom}–${deliveryInfo.slotTo}`;

            const textButton = isLessThanDaysLeft ? 'Отменить встречу' : 'Перенести или отменить';

            resultData = {
                ...resultData,
                description: `Документы привезут ${deliveryDate} ${deliveryTime} ${addressText}`,
                textButton,
            };
        }

        if (status === DeliveryDocumentStatus.CANCELLED) {
            const textButton = 'Назначить новую встречу';

            resultData = {
                ...resultData,
                description: 'Чтобы получить кредит, назначьте новую встречу и подпишите документы',
                view: 'negative',
                textButton,
            };
        }

        if (status === DeliveryDocumentStatus.COMPLETED) {
            resultData = {
                ...resultData,
                description:
                    'Всё проверим за 1–2 рабочих дня. Как будет готово решение — деньги зачислятся на ваш счёт',
                textButton: undefined,
            };
        }

        return resultData;
    }

    return null;
}

export function getActiveCreditInfoFromSFA(
    isOnlineSigningAvailable: boolean,
    stage: string,
    productName: string,
    loanAmount: string,
): { stage: string; data: ActiveCreditCaseInfo } | null {
    if (isOnlineSigningAvailable) {
        return {
            stage,
            data: {
                title: (
                    <span>
                        {productName} на{NBSP}
                        <span style={{ whiteSpace: 'nowrap' }}>
                            {formatAmount(loanAmount)}
                            {NBSP}₽
                        </span>
                        {checkContainsWord(productName, ['карта', 'линия'])
                            ? ' одобрена'
                            : ' одобрен'}
                    </span>
                ),
                description: 'Осталось только подписать документы',
                textButton: 'К подписанию',
            },
        };
    }

    return null;
}
