/* eslint-disable @typescript-eslint/no-explicit-any */
import { differenceInDays } from 'date-fns';
import addHours from 'date-fns/addHours';
import formatDate from 'date-fns/format';
import isBefore from 'date-fns/isBefore';
import isValid from 'date-fns/isValid';
import ruLocale from 'date-fns/locale/ru';
import parse from 'date-fns/parse';
import startOfMonth from 'date-fns/startOfMonth';
import startOfQuarter from 'date-fns/startOfQuarter';
import startOfWeek from 'date-fns/startOfWeek';
import startOfYear from 'date-fns/startOfYear';
import subDays from 'date-fns/subDays';
import { formatInTimeZone, utcToZonedTime } from 'date-fns-tz';
import { type UnixEpoch } from 'thrift-services/utils';

import {
    DATE_FOR_SERVICES,
    DATE_FORMAT,
    MILLISECONDS_IN_SECOND,
    MONTHS_IN_YEAR,
    MOSCOW_TIMEZONE_OFFSET_IN_HOURS,
    TimePeriods,
} from '#/src/constants/date';

export const parseUnixEpochDate = (date?: UnixEpoch) => {
    if (!date?.seconds) {
        return null;
    }

    return parse(String(date.seconds * MILLISECONDS_IN_SECOND), 'T', new Date());
};

export function parseDateFromAny(currentDate: Date, date: any, formatString?: string): Date {
    if (!date) {
        return currentDate;
    }

    if (formatString) {
        let parsedDate = new Date();

        try {
            parsedDate = parse(date, formatString, currentDate);
        } catch (e) {}

        return parsedDate;
    }

    if (date.seconds) {
        return parseUnixEpochDate(date);
    }

    return date;
}

/**
 * Конвертировать в нативный объект `Date`
 * @param clientDate в формате `DD.MM.YYYY`
 */
export function toNativeDate(clientDate?: string): Date | null {
    if (!clientDate) {
        return null;
    }

    const date = parseDateFromAny(new Date(), clientDate, DATE_FORMAT);

    if (!date.getTime()) {
        return null;
    }

    return date;
}

export function millisecondsToUnixEpoch(milliseconds: number) {
    return {
        seconds: Math.floor(milliseconds / MILLISECONDS_IN_SECOND),
    };
}

export const unixEpochToMilliseconds = (unix: UnixEpoch) => unix.seconds * MILLISECONDS_IN_SECOND;

export function dateToCustomFormat(
    currentTime: Date | null,
    date: any,
    formatString: string,
    fromFormat?: string,
): string {
    if (!date) {
        return '';
    }

    const parsedDate = parseDateFromAny(currentTime || new Date(), date, fromFormat);

    let dnsFormat = 'Invalid date';

    try {
        dnsFormat = formatInTimeZone(parsedDate, 'Europe/Moscow', formatString, {
            locale: ruLocale,
        });
    } catch (e) {}

    return dnsFormat;
}

/* Функция сортирует даты в массиве, переданные в формате 'YYYY-MM-DD' */
export function sortByDate(dates: string[]) {
    return dates.sort();
}

export function convertFromMonthsToYears(numberOfMonths: number) {
    return Math.floor(numberOfMonths / MONTHS_IN_YEAR);
}

const BLOKING_END_DATE = new Date(1586149200000); // Apr 06 2020 08:00:00

export const isOverdraftFillingBlocked = (currentDate: Date) =>
    isBefore(currentDate, BLOKING_END_DATE);

export const getMoscowDate = (date: Date) => {
    const currentTimezoneOffsetWithMoscowDifference =
        MOSCOW_TIMEZONE_OFFSET_IN_HOURS - (date.getTimezoneOffset() * -1) / 60;
    const moscowDate = addHours(date, currentTimezoneOffsetWithMoscowDifference);

    return moscowDate;
};

export const getMoscowTimeZone = (date: Date) =>
    utcToZonedTime(date, 'Europe/Moscow', { locale: ruLocale });

export const getIsDatesEqual = (firstDate: Date, secondDate: Date) => {
    const currentFirstDate = firstDate;
    const currentSecondDate = secondDate;

    currentFirstDate.setHours(0, 0, 0, 0);
    currentSecondDate.setHours(0, 0, 0, 0);

    return currentFirstDate.valueOf() === currentSecondDate.valueOf();
};

export const getCurrentMoscowDate = () =>
    new Date().toLocaleString('en-US', { timeZone: 'Europe/Moscow' });

/**
 * Конвертировать в формат unix epoch
 * @param dateString в формате `YYYY-MM-DD`
 */
export function toUnixEpoch({
    dateString,
    currentTime = new Date(),
}: {
    dateString: string;
    currentTime?: Date;
}): UnixEpoch {
    const date = parseDateFromAny(currentTime, dateString, DATE_FOR_SERVICES);

    const seconds = date && isValid(date) ? date.getTime() / 1000 : 0;

    return {
        seconds,
    };
}

export const calculateDateByPeriod = (
    periodType: TimePeriods,
    currentTime: Date,
    closedDate?: UnixEpoch,
): Date => {
    const today = closedDate ? new Date(closedDate.seconds * 1000) : currentTime;
    const yesterday = closedDate ? today : subDays(today, 1);

    const periodsMap: Record<TimePeriods, Date> = {
        [TimePeriods.MONTH]: startOfMonth(yesterday),
        [TimePeriods.QUARTER]: startOfQuarter(yesterday),
        [TimePeriods.YEAR]: startOfYear(yesterday),
        [TimePeriods.WEEK]: startOfWeek(yesterday, { weekStartsOn: 1 }),
    };

    return periodsMap[periodType];
};

export const reformatDate = (dateString: string, formatFrom: string, formatTo: string): string =>
    formatDate(parse(dateString, formatFrom, new Date()), formatTo);

/**
 * Возвращает булево значение, если до необходимой даты осталось меньше N дней
 */
export function getIsLessThanDaysLeft(
    fromDateTimestamp: number,
    targetDate?: UnixEpoch,
    daysLeft = 1,
): boolean {
    if (!targetDate) {
        return false;
    }
    if ('seconds' in targetDate) {
        const targetDateTime = new Date(targetDate.seconds * 1000);

        return differenceInDays(targetDateTime, fromDateTimestamp) <= daysLeft;
    }

    return false;
}
