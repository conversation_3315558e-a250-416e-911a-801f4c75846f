import { CURRENCY } from 'corporate-services/lib/currency';
import { type Amount, type Currency } from 'thrift-services/entities';

export function getPercentage(nominator: Amount, denominator: Amount): number {
    return Math.round((nominator.amount / denominator.amount) * 100);
}

export function convertNumberToAmount(number: number, currency?: Currency): Amount {
    return {
        amount: number * 100,
        currency: currency || CURRENCY.RUR,
    };
}

export function convertAmountToNumber(amount: Amount): number {
    return Number((amount.amount / amount.currency.minorUnits).toFixed(2));
}

export function divideNumberByMinorUnits(number: number | null, minorUnits: number): number {
    return Number(((number || 0) / minorUnits).toFixed(2));
}

export function convertStringToNumber(string: string): number {
    return Number(string.split(' ').join('').replace(',', '.'));
}

export function convertStringToAmount(string: string, currency?: Currency): Amount {
    return convertNumberToAmount(convertStringToNumber(string), currency);
}

export function convertAmountToString(
    amount: Amount,
    thousandsSeparator = false,
    showCurrency = false,
): string {
    const currencyString =
        showCurrency && amount.currency ? ` ${amount.currency.unicodeSymbol}` : '';

    return (
        (thousandsSeparator
            ? (amount.amount / amount.currency.minorUnits).toLocaleString('ru-RU').replace(',', '.')
            : (amount.amount / amount.currency.minorUnits).toFixed(2)) + currencyString
    );
}
