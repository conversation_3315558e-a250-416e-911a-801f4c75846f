import {
    ECreditOffers,
    type ECreditOffersCampaignCode,
    OffersWithDigitalSignBadge,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import creditBusiness from '#/src/mobile/containers/credit-offers/images/credit-business.svg';
import creditCard from '#/src/mobile/containers/credit-offers/images/credit-card.svg';
import creditLine from '#/src/mobile/containers/credit-offers/images/credit-line.svg';
import overdraft from '#/src/mobile/containers/credit-offers/images/overdraft.svg';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';

import { NBSP } from '../constants/unicode-symbols';

type TOffers = Record<string, TMappedCreditOffer>;
export type TCombiningPair = {
    mainOffer: ECreditOffers;
    additionalOffer: ECreditOffers;
};

const isPreApprovedConditionMet = (offer1: TMappedCreditOffer, offer2: TMappedCreditOffer) =>
    offer1.preApproved === offer2.preApproved;

// Не объединяем карточки с разным признаком Безусловности
const isTotalOfferConditionMet = (offer1: TMappedCreditOffer, offer2: TMappedCreditOffer) =>
    TotalOffersCampaignCode.includes(offer1.campaignCode as ECreditOffersCampaignCode) ===
    TotalOffersCampaignCode.includes(offer2.campaignCode as ECreditOffersCampaignCode);

// Не объединяем карточки с разным признаком КЭП
const isShowDigitalSignBadgeConditionMet = (
    offer1: TMappedCreditOffer,
    offer2: TMappedCreditOffer,
) =>
    OffersWithDigitalSignBadge.includes(offer1.type) ===
    OffersWithDigitalSignBadge.includes(offer2.type);

export function combineOffers(
    mappedOffers: TMappedCreditOffer[],
    combiningPairs: TCombiningPair[],
) {
    const offers: TOffers = mappedOffers.reduce((memo, offer) => {
        memo[offer?.type] = { ...offer };

        return memo;
    }, {} as TOffers);

    combiningPairs.forEach(({ mainOffer, additionalOffer }) => {
        const mainOfferData = offers[mainOffer];
        const additionalOfferData = offers[additionalOffer];

        if (!mainOfferData || !additionalOfferData) return;

        if (
            isPreApprovedConditionMet(mainOfferData, additionalOfferData) &&
            isTotalOfferConditionMet(mainOfferData, additionalOfferData) &&
            isShowDigitalSignBadgeConditionMet(mainOfferData, additionalOfferData)
        ) {
            mainOfferData.additionalOffer = additionalOfferData;
            delete offers[additionalOffer];
        }
    });

    return Object.values(offers);
}

// под этим ключом лежат данные по заявке на кредит в приложении Заявка. Кредитовать-по-новому
const LOCAL_STORAGE_KEY_NEWCREDIT = 'sd';

export const writeDataToLocalStorageCreditLimit = ({
    isPreApproved = false,
    offerId,
}: {
    isPreApproved?: boolean;
    offerId?: string;
}) => {
    const previousLocalStorageData = localStorage.getItem(LOCAL_STORAGE_KEY_NEWCREDIT)
        ? JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY_NEWCREDIT) || '')
        : {};
    const newState = {
        ...previousLocalStorageData,
        isPreApproved,
        ...(isPreApproved && { offerId }),
    };

    localStorage.setItem('sd', JSON.stringify(newState));
};

export const CREDIT_OFFERS = {
    [ECreditOffers.OVERDRAFT]: {
        titlesVariants: {
            long: 'Овердрафт',
            short: 'Овердрафт',
        },
        text: 'Совершайте платежи, даже если на счёте недостаточно средств',
        image: overdraft,
    },
    [ECreditOffers.EXPRESS_OVERDRAFT_OFFER]: {
        titlesVariants: {
            long: 'Овердрафт',
            short: 'Овердрафт',
        },
        text: 'Совершайте платежи, даже если на счёте недостаточно средств',
        image: overdraft,
    },
    [ECreditOffers.BUSINESS_CREDIT]: {
        titlesVariants: {
            long: 'Кредит для бизнеса',
            short: 'Кредит для бизнеса',
        },
        text: `Увеличьте оборот или вложите в${NBSP}развитие когда точно знаете, сколько вам нужно`,
        image: creditBusiness,
    },
    [ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE]: {
        titlesVariants: {
            long: 'Кредит для бизнеса',
            short: 'Кредит для бизнеса',
        },
        text: `Увеличьте оборот или вложите в${NBSP}развитие когда точно знаете, сколько вам нужно`,
        image: creditBusiness,
    },
    [ECreditOffers.REFILLABLE_CREDIT_LINE]: {
        titlesVariants: {
            long: 'Кредитная линия',
            short: 'Кредитная линия',
        },
        text: `Берите деньги частями в${NBSP}рамках лимита. Возвращайте и${NBSP}берите снова`,
        image: creditLine,
    },
    [ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE]: {
        titlesVariants: {
            long: 'Кредитная линия',
            short: 'Кредитная линия',
        },
        text: `Берите деньги частями в${NBSP}рамках лимита. Возвращайте и${NBSP}берите снова`,
        image: creditLine,
    },
    [ECreditOffers.BUSINESS_CREDIT_CARD]: {
        titlesVariants: {
            long: 'Кредитная карта',
            short: 'Кредитная карта',
        },
        text: `Оплачивайте картой покупки в${NBSP}интернете и${NBSP}магазинах`,
        image: creditCard,
    },
    [ECreditOffers.ALFA_BUSINESS_CREDIT_CARD]: {
        titlesVariants: {
            long: 'Кредитная карта',
            short: 'Кредитная карта',
        },
        text: `Оплачивайте картой покупки в${NBSP}интернете и${NBSP}магазинах`,
        image: creditCard,
    },
};

type TOverdraftsList = Array<(typeof CREDIT_OFFERS)[ECreditOffers.OVERDRAFT] & TMappedCreditOffer>;
type TBusinessCreditsList = Array<
    (typeof CREDIT_OFFERS)[ECreditOffers.BUSINESS_CREDIT] & TMappedCreditOffer
>;
type TCreditLinesList = Array<
    (typeof CREDIT_OFFERS)[ECreditOffers.REFILLABLE_CREDIT_LINE] & TMappedCreditOffer
>;
type TCreditCardsList = Array<
    (typeof CREDIT_OFFERS)[ECreditOffers.BUSINESS_CREDIT_CARD] & TMappedCreditOffer
>;

export const getSortedCreditOffers = (creditOffers: TMappedCreditOffer[]) => {
    const overdraftsList: TOverdraftsList = [];
    const businessCreditsList: TBusinessCreditsList = [];
    const creditLinesList: TCreditLinesList = [];
    const creditCardsList: TCreditCardsList = [];
    const preApprovedOverdraftsList: TOverdraftsList = [];
    const preApprovedBusinessCreditsList: TBusinessCreditsList = [];
    const preApprovedCreditLinesList: TCreditLinesList = [];
    const preApprovedCreditCardsList: TCreditCardsList = [];

    creditOffers.forEach((creditOffer) => {
        if (CREDIT_OFFERS[creditOffer?.type as keyof typeof CREDIT_OFFERS]) {
            const offer = {
                ...creditOffer,
                ...CREDIT_OFFERS[creditOffer.type as keyof typeof CREDIT_OFFERS],
            };

            switch (creditOffer.type) {
                case ECreditOffers.OVERDRAFT:
                case ECreditOffers.EXPRESS_OVERDRAFT_OFFER:
                    if (offer.preApproved) {
                        preApprovedOverdraftsList.push(offer);
                    } else {
                        overdraftsList.push(offer);
                    }
                    break;
                case ECreditOffers.BUSINESS_CREDIT:
                case ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE:
                    if (offer.preApproved) {
                        preApprovedBusinessCreditsList.push(offer);
                    } else {
                        businessCreditsList.push(offer);
                    }
                    break;
                case ECreditOffers.REFILLABLE_CREDIT_LINE:
                case ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE:
                    if (offer.preApproved) {
                        preApprovedCreditLinesList.push(offer);
                    } else {
                        creditLinesList.push(offer);
                    }
                    break;
                case ECreditOffers.BUSINESS_CREDIT_CARD:
                case ECreditOffers.ALFA_BUSINESS_CREDIT_CARD:
                    if (offer.preApproved) {
                        preApprovedCreditCardsList.push(offer);
                    } else {
                        creditCardsList.push(offer);
                    }
                    break;
            }
        }
    });

    return [
        ...preApprovedOverdraftsList,
        ...preApprovedBusinessCreditsList,
        ...preApprovedCreditLinesList,
        ...preApprovedCreditCardsList,
        ...overdraftsList,
        ...businessCreditsList,
        ...creditLinesList,
        ...creditCardsList,
    ];
};
