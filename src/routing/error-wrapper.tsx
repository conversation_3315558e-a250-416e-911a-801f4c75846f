import React, { type PropsWithChildren } from 'react';
import { connect } from 'react-redux';
import { type History } from 'history';

import ErrorPage from 'arui-private/error-page';

import { type ApplicationState } from '../ducks/application-state';

export type ErrorWrapperOwnProps = {
    history: History;
};

type TMapStateToProps = ReturnType<typeof mapStateToProps>;

export type ErrorWrapperProps = TMapStateToProps & ErrorWrapperOwnProps;

function mapStateToProps(state: ApplicationState) {
    return {
        authPage: state.settings.authPage,
        traceId: state.settings.traceId,
        isError: state.app.error,
    };
}

/**
 * Компонент верхнего уровня для роутера, который хендлит рендер 404х и 500х экранов
 * Работает только если роутинг осуществлен браузером.
 */
class ErrorWrapper extends React.Component<PropsWithChildren<ErrorWrapperProps>> {
    render() {
        if (this.isNotFound) {
            return <ErrorPage errorCode={404} returnUrl={this.props.authPage} />;
        }

        if (this.isError) {
            return (
                <ErrorPage
                    errorCode={500}
                    traceId={this.props.traceId}
                    returnUrl={this.props.authPage}
                />
            );
        }

        return this.props.children;
    }

    get isNotFound() {
        return (this.props.history.location?.state as any)?.notFoundError;
    }

    get isError() {
        return this.props.isError;
    }
}

export default connect(mapStateToProps)(ErrorWrapper);
