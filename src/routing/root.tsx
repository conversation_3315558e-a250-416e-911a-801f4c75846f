import * as React from 'react';
import { Route, Switch } from 'react-router';
import { type History } from 'history';

import App from '../containers/app/app';
import { type TStaticContext } from '../types/routing';
import PAGES_ACCORDING_TO_PATHS from '../view-utils/routing';

import NotFound from './not-found';

type TOwnProps = {
    history: History;
    staticContext?: TStaticContext;
};

const Root: React.FC<TOwnProps> = ({ history, staticContext }) => (
    <App>
        <Switch>
            {Object.keys(PAGES_ACCORDING_TO_PATHS).map((path) => (
                <Route
                    path={path}
                    component={
                        PAGES_ACCORDING_TO_PATHS[path as keyof typeof PAGES_ACCORDING_TO_PATHS]
                    }
                    key={path}
                />
            ))}
            <NotFound history={history} staticContext={staticContext} />
        </Switch>
    </App>
);

export default Root;
