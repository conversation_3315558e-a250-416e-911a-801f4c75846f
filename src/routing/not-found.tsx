import * as React from 'react';
import { Redirect, Route } from 'react-router';
import { type History } from 'history';

import { type TStaticContext } from '../types/routing';

export type NotFoundOwnProps = {
    history: History;
    staticContext?: TStaticContext;
};

/**
 * Компонент, который необходимо использовать в Switch роутера, если в случае отсутствия
 * сматчившегося роута нужно показать 404.
 */
const NotFound: React.FC<NotFoundOwnProps> = ({ staticContext, history }) => {
    const renderRoute = () => {
        if (staticContext) {
            /* staticContext используется при серверном роутинге */
            staticContext.notFoundError = true;

            return null;
        }
        /* location используется при браузерном роутинге */
        const location = { ...history.location, state: { notFoundError: true } };

        return <Redirect to={location} />;
    };

    return <Route render={renderRoute} />;
};

export default NotFound;
