import { type OpenApiConnectionConfig } from 'corporate-services/lib/openapi/create-openapi-service';
import { type OpenApiServiceConfig } from 'corporate-services/lib/openapi/method-config';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { type DeepPartial } from 'arui-private/types';

export type TOpenApiServiceConfig<T extends OpenApiServiceConfig> = {
    config: OpenApiConnectionConfig &
        DeepPartial<{
            projectId: string;
            channelId: string;
            clientType: string;
            mksAuthConfig: {
                clientId: string;
                clientSecret: string;
                gatewayUrl: string;
            };
        }>;
    serviceConfig: T;
    mockConfig?: Partial<OpenApiRestService<T>>;
    withAuth?: boolean;
};
