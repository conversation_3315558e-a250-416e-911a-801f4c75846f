import globalConfig from 'config';
import { type OpenApiConnectionConfig } from 'corporate-services/lib/openapi/create-openapi-service';
import { type OpenApiServiceConfig } from 'corporate-services/lib/openapi/method-config';
import {
    createMksHeadersGetter,
    createOpenApiSystemHeadersGetter,
    type MksAuthConfig,
    type OpenApiProjectConfig,
} from 'corporate-services/lib/openapi/open-api-system-headers';
import { makeGenericOpenApiService } from 'corporate-services/lib/service-manager/generic-openapi-service';

import { createMockMksHeadersGetter } from '#/src/server/mocks/utils/mock-oauth';

import { type TOpenApiServiceConfig } from './types';

type ConfigInput = TOpenApiServiceConfig<OpenApiServiceConfig>['config'];

const DEFAULT_AUTH_CONFIG: MksAuthConfig = {
    clientId: globalConfig.get('reactive-clients.oauth.client-id') ?? '',
    clientSecret: globalConfig.get('reactive-clients.oauth.client-secret') ?? '',
    gatewayUrl: globalConfig.get('reactive-clients.oauth.token-uri') ?? '',
};

const shouldUseMocks = !!globalConfig.get<boolean>('server.shouldUseMocks');

const createConnectionConfig = (config: ConfigInput): OpenApiConnectionConfig => ({
    host: config.host,
    port: config.port,
    endpoint: config.endpoint?.replace(/(http|https):/g, ''),
    protocol: config.protocol,
});

const createProjectConfig = (config: ConfigInput): OpenApiProjectConfig => ({
    projectId: config.projectId ?? globalConfig.get('app.projectName'),
    channelId: config.channelId ?? 'nib',
    clientType: config.clientType ?? 'FRONT',
});

const createAuthConfig = (config: ConfigInput): MksAuthConfig => {
    const { clientId, clientSecret, gatewayUrl } = config.mksAuthConfig ?? {};

    return {
        clientId: clientId ?? DEFAULT_AUTH_CONFIG.clientId,
        clientSecret: clientSecret ?? DEFAULT_AUTH_CONFIG.clientSecret,
        gatewayUrl: gatewayUrl ?? DEFAULT_AUTH_CONFIG.gatewayUrl,
    };
};

const createHeadersGetter = (
    projectConfig: OpenApiProjectConfig,
    authConfig: MksAuthConfig,
    withAuth?: boolean,
) => {
    if (!withAuth) {
        return createOpenApiSystemHeadersGetter(projectConfig);
    }

    return shouldUseMocks
        ? createMockMksHeadersGetter(projectConfig, authConfig)
        : createMksHeadersGetter({ projectConfig, mksAuthConfig: authConfig });
};

export function createOpenApiService<T extends OpenApiServiceConfig>({
    config,
    serviceConfig,
    mockConfig,
    withAuth,
}: TOpenApiServiceConfig<T>) {
    const connectionConfig = createConnectionConfig(config);
    const projectConfig = createProjectConfig(config);
    const headersGetter = createHeadersGetter(projectConfig, createAuthConfig(config), withAuth);

    return makeGenericOpenApiService<T>({
        serviceConfig,
        mockConfig,
        defaultConnectionConfig: {
            ...connectionConfig,
            headersGetter,
        },
    })(connectionConfig);
}
