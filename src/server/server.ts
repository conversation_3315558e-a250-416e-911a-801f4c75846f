/* eslint-disable @typescript-eslint/no-explicit-any */
import { type ServerRegisterPluginObject } from '@hapi/hapi';
import * as config from 'config';
import { type LoggerConfig } from 'corporate-logger';
import { createOpenApiHapiPlugins } from 'corporate-services/lib/openapi/create-openapi-hapi-plugins';
import alboRedirect from 'corporate-services/server/api/albo/albo';
import createCorpLoanWatchlistApis from 'corporate-services/server/api/corp-loan-watchlist';
import sendLetterGroupToSign from 'corporate-services/server/api/correspondence/sendLetterGroupToSign';
import sendLetterToBranch from 'corporate-services/server/api/correspondence/sendLetterToBranch';
import { createCreditApplicationProcessingApi } from 'corporate-services/server/api/credit-application-processing';
import getDocList from 'corporate-services/server/api/credit-document-circulation/getDocList';
import createCreditOffersApis from 'corporate-services/server/api/credit-offers';
import createCreditProductsApi from 'corporate-services/server/api/credit-products';
import getTrancheCreditProducts from 'corporate-services/server/api/credit-products/getTrancheCreditProducts';
import getTrancheCreditProductsCount from 'corporate-services/server/api/credit-products/getTrancheCreditProductsCount';
import createCreditProductsApiV2 from 'corporate-services/server/api/credit-products-v2';
import createCreditRequestDocumentsApi from 'corporate-services/server/api/credit-request-documents';
import sendUnsafeFileRest from 'corporate-services/server/api/credit-request-documents/send-unsafe-file-rest';
import createCallbackRequest from 'corporate-services/server/api/credits/createCallbackRequest';
import { createCSIApi } from 'corporate-services/server/api/customer-satisfaction-index';
import searchOkvedSuggestions from 'corporate-services/server/api/dadata/searchOkvedSuggestions';
import externalRedirect from 'corporate-services/server/api/externalRedirect';
import healthmonitor from 'corporate-services/server/api/healthmonitor';
import logout from 'corporate-services/server/api/logout';
import createOverdraftApi from 'corporate-services/server/api/overdraft';
import getNonBusinessDays from 'corporate-services/server/api/production-calendar/getNonBusinessDays';
import getBlockingFromCorpShared from 'corporate-services/server/api/shared/getSharedBlocking';
import findRequestsInProgressStatistics from 'corporate-services/server/api/ufr-ak-corp-gateway-rest/findRequestsInProgressStatistics';
import dnscachePlugin from 'corporate-services/server/dnscache';
import jwt from 'corporate-services/server/jwt-auth';
import corporateLoggerPlugin from 'corporate-services/server/plugins/corporate-logger-v2';
import csrfPlugin from 'corporate-services/server/plugins/csrf-plugin';
import { methodsListPlugin } from 'corporate-services/server/plugins/methods-list';
import promPlugin from 'corporate-services/server/plugins/prometheus';

import { createOAuthMockMiddleware } from './mocks/middleware/oauth-mock-middleware';
import { createServerRoot } from './plugins/pages';
import serviceManager from './services';

const CONTEXT_ROOT = config.get<string>('app.contextRoot');
const AUTHORIZED_APPLICATION_ID = config.get<string>('app.authorizedApplicationId');

export default () =>
    serviceManager.createServer(
        {
            host: config.get<string>('server.host'),
            port: config.get<string>('server.port'),
            shouldUseMocks: config.get<boolean>('server.shouldUseMocks'),
            addDefaultPlugins: false,
            disableMockStdout: true,
            useDomains: false,
        },
        ({ services, configs, server }) => {
            const {
                getActiveCreditCase,
                getInfoForClientDocuments,
                getDocument,
                closeCase,
                moveCaseToStage,
                getAgreement,
            } = createCreditApplicationProcessingApi(
                services.creditApplication,
                AUTHORIZED_APPLICATION_ID,
            );

            const {
                getCreditProductsV2,
                getTrancheCreditProductsV2,
                getDealCreditProductsV2,
                getTrancheCreditProductsCountV2,
                getDealCreditProductsCountV2,
                getPaymentScheduleV2,
                getGuarantyPaymentScheduleV2,
                getPaymentScheduleFileV2,
                getProductDocumentsV2,
            } = createCreditProductsApiV2(
                services.creditProductsV2,
                config.get('externalSystemCode'),
            );

            const {
                getChangeRepaymentStatusV2,
                getClientLimitSettingPropertiesV2,
                getClosePaymentAmountV2,
                setClientLimitV2,
                getOverTurnV2,
            } = createOverdraftApi(services.overdraft, config.get('externalSystemCode'));

            const { acceptAttachedFiles, getRequestStatus, getAttachedFiles, removeAttachedFile } =
                createCreditRequestDocumentsApi(services.creditRequestDocuments);

            const { getCreditOffers } = createCreditOffersApis(
                services.creditOffers,
                config.get('externalSystemCode'),
            );

            const { checkSubjectZone } = createCorpLoanWatchlistApis(
                services.corpLoanWatchlist,
                config.get('externalSystemCode'),
            );

            const { getDealCreditProducts } = createCreditProductsApi(
                services.creditProducts,
                config.get('externalSystemCode'),
            );

            const { getCategory, getManagers } = createOpenApiHapiPlugins({
                service: services.mksCustomers,
            });

            const { getClientLimit, getCreditProducts } = createOpenApiHapiPlugins({
                service: services.coreCreditProductsRestV2,
            });

            const { createEarlyRepaymentAppl, getEarlyRepaymentProcessingType, sendEmailToKM } =
                createOpenApiHapiPlugins({
                    service: services.earlyRepaymentRest,
                    withFullError: true,
                });

            const { getCreditRequests } = createOpenApiHapiPlugins({
                service: services.corpCreditRequestRest,
            });

            const {
                getSuspensiveConditions,
                getCreditProductsMainMenu,
                getCountCreditProductsByType,
            } = createOpenApiHapiPlugins({
                service: services.creditProductsRestV2,
            });

            const { getByCustomerId, getByAccountNumbers } = createOpenApiHapiPlugins({
                service: services.mksAccounts,
            });

            const { runDecisionMakerCheck } = createOpenApiHapiPlugins({
                service: services.mksPermissions,
            });

            const {
                createStatementRequest,
                getFileById,
                getStatementRequests,
                updateStatementRequestsView,
            } = createOpenApiHapiPlugins({
                service: services.corpLoanStatements,
            });

            const { getOrConnectAlfaCredit } = createOpenApiHapiPlugins({
                service: services.corpCreditDocumentCirculation,
            });

            const {
                getCreditRequestList,
                getWidgetStatus,
                getCreditRequestByCreditRequestId,
                deleteWelcomeDealById,
            } = createOpenApiHapiPlugins({
                service: services.creditRequest,
            });

            const { getApplicationProgressStages } = createOpenApiHapiPlugins({
                service: services.statusModel,
            });
            const { sendOccuredEventInformation } = createCSIApi(services.csi);

            const plugins: Array<ServerRegisterPluginObject<any>> = [
                { plugin: corporateLoggerPlugin, options: config.get<LoggerConfig>('logger') },
                { plugin: promPlugin },
                { plugin: methodsListPlugin },
                { plugin: dnscachePlugin },
                { plugin: csrfPlugin, options: { contextRoot: CONTEXT_ROOT } },
                { plugin: healthmonitor({ services: configs, serviceCheckTimeout: 6000 }) },
                { plugin: createServerRoot() },
                {
                    plugin: jwt,
                    options: {
                        serviceUrl: config.get('auth.jwt'),
                        tokenCookie: config.get('auth.tokenCookie'),
                        tokenParam: config.get('auth.tokenParam'),
                        devAccess: config.get('auth.devAccess'),
                        devAccessParam: config.get('auth.devAccessParam'),
                        devAccessCookie: config.get('auth.devAccessCookie'),
                        externalSystemCode: config.get('externalSystemCode'),
                        contextRoot: CONTEXT_ROOT,
                        logger: server,
                    },
                },
                {
                    plugin: externalRedirect(config.get('auth'), services.token),
                },
                { plugin: logout(CONTEXT_ROOT) },
                { plugin: alboRedirect(services.albo, config.get('app.authPage')) },
                // APIS
                { plugin: getCreditOffers },
                { plugin: createCallbackRequest(services.credits) },
                { plugin: getTrancheCreditProducts(services.creditProducts) },
                { plugin: getTrancheCreditProductsCount(services.creditProducts) },
                { plugin: getDealCreditProducts },
                { plugin: getChangeRepaymentStatusV2 },
                { plugin: getClientLimitSettingPropertiesV2 },
                { plugin: setClientLimitV2 },
                { plugin: getOverTurnV2 },
                {
                    plugin: getDocList(
                        services.creditDocumentCirculation,
                        config.get('externalSystemCode'),
                    ),
                },
                { plugin: getBlockingFromCorpShared(services.sharedUI) },
                { plugin: sendLetterToBranch(services.correspondence) },
                { plugin: sendLetterGroupToSign(services.correspondence) },
                {
                    plugin: findRequestsInProgressStatistics(services.ufrAkCorpGatewayRest),
                },
                { plugin: getNonBusinessDays(services.productionCalendar) },
                { plugin: getActiveCreditCase },
                { plugin: getInfoForClientDocuments },
                { plugin: sendUnsafeFileRest(services.creditRequestDocumentsRest) },
                { plugin: acceptAttachedFiles },
                { plugin: getRequestStatus },
                { plugin: getAttachedFiles },
                { plugin: removeAttachedFile },
                { plugin: checkSubjectZone },
                { plugin: getAgreement },
                { plugin: getClosePaymentAmountV2 },
                { plugin: searchOkvedSuggestions(services.dadata, AUTHORIZED_APPLICATION_ID) },
                { plugin: getDocument },
                { plugin: getClientLimit },
                { plugin: createEarlyRepaymentAppl },
                { plugin: getEarlyRepaymentProcessingType },
                { plugin: getCreditRequestList },
                {
                    plugin: getCreditRequestByCreditRequestId,
                },
                { plugin: closeCase },
                { plugin: moveCaseToStage },
                { plugin: getManagers },
                { plugin: getCategory },
                { plugin: getCreditRequests },
                { plugin: getSuspensiveConditions },
                { plugin: sendEmailToKM },
                { plugin: getByCustomerId },
                { plugin: runDecisionMakerCheck },
                { plugin: getCreditProductsMainMenu },
                { plugin: sendOccuredEventInformation },
                { plugin: getByAccountNumbers },
                { plugin: getCreditProductsV2 },
                { plugin: getTrancheCreditProductsV2 },
                { plugin: getDealCreditProductsV2 },
                { plugin: getTrancheCreditProductsCountV2 },
                { plugin: getDealCreditProductsCountV2 },
                { plugin: getPaymentScheduleV2 },
                { plugin: getGuarantyPaymentScheduleV2 },
                { plugin: getPaymentScheduleFileV2 },
                { plugin: getProductDocumentsV2 },
                { plugin: getOrConnectAlfaCredit },
                { plugin: getWidgetStatus },
                { plugin: deleteWelcomeDealById },
                { plugin: getCreditProducts },
                { plugin: createStatementRequest },
                { plugin: getFileById },
                { plugin: getStatementRequests },
                { plugin: updateStatementRequestsView },
                { plugin: getCountCreditProductsByType },
                { plugin: getApplicationProgressStages },
            ];

            if (config.get<boolean>('server.shouldUseMocks')) {
                plugins.push({ plugin: createOAuthMockMiddleware() });
            }

            return plugins;
        },
    );
