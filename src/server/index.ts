/* eslint no-console: [1, { allow: ["log", "error"] }] */
import config from 'config';
import type ServerManager from 'corporate-services/lib/service-manager/server-manager';

import { pathConfig } from '../utils/cloud-config';

import { type TServices } from './services';

let currentServer: ServerManager<TServices>;

async function startServer() {
    try {
        await pathConfig(config);

        const createServer = (await import('./server')).default;

        currentServer = await createServer();

        console.log('[CONFIG]: ', JSON.stringify(config, null, '\t'));

        await currentServer.start();
    } catch (error) {
        console.error('Failed to start server', error);
        process.exit(1);
    }
}

startServer();

if (module.hot) {
    module.hot.accept(['./server'], async () => {
        try {
            const createServer = (await import('./server')).default;

            await currentServer.stop();

            currentServer = await createServer();
            await currentServer.start();
        } catch (error) {
            console.log('Failed to update server. You probably need to restart application', error);
        }
    });
}
