import * as vm from 'vm';

import type Hapi from '@hapi/hapi';
import * as config from 'config';
import {
    type ApiGetSubjectFeatureV2Response,
    SubjectType,
} from 'corp-global-features-api-typescript-services';
import {
    type Checks,
    type RunMandatoryChecksV2Response,
} from 'corp-role-model-mks-permissions-api-typescript-services';
import { type ApiGetByUserIdResponse } from 'corp-roles-api-typescript-services';
import { type ApiGetByIdsResponse } from 'corp-users-api-typescript-services';
import { nodeJsLogger } from 'corporate-logger';
import * as HEADERS from 'corporate-services/lib/constants/headers';
import { factoryError } from 'corporate-services/lib/error-factory';
import { type CommonOpenApiUserHeaders } from 'corporate-services/lib/openapi/open-api-types';
import { openApiUserHeadersFromRequest } from 'corporate-services/lib/openapi/open-api-user-headers-from-request';
import tokenFactory from 'corporate-services/lib/token-factory';
import { type ErrorInfo } from 'thrift-services/errors';

import { APP_NAME } from '#/src/constants/credit-products';
import { USER_RIGHTS } from '#/src/constants/rights';
import { addressInitialState } from '#/src/ducks/addresses/reducer';
import { appDefaultState, type ApplicationState } from '#/src/ducks/application-state';
import { categoryInitialState } from '#/src/ducks/organization/reducer';
import { type TCustomCustomer } from '#/src/types/organization';
import { EErrorMessages } from '#/src/utils/errors/error-messages';
import { getErrorMessage } from '#/src/utils/errors/get-error-message';
import { type DeepWritable } from '#/src/utils/generic-types';

import { REQUEST_ID_HEADER_NAME } from '../../constants';
import serviceManager from '../../services';

type HeaderRequest = CommonOpenApiUserHeaders & {
    [REQUEST_ID_HEADER_NAME]: string;
    [HEADERS.TRACE_ID]: string;
    [HEADERS.CUSTOMER_ID]: string;
    [HEADERS.COMPANY_ID]?: string;
};

type TRights = {
    [key: string]: Checks['rightChecksResults'];
};

const tokenWrapper = tokenFactory({
    serviceUrl: config.get('auth.jwt'),
    externalSystemCode: config.get('externalSystemCode'),
});

function sharedUILoadedCodeCallback(sources: string[]) {
    /* eslint-disable */
    const sandbox = {
        react: require('react'),
        reactDOM: require('react-dom'),
        __CorporateAppHeader: null,
    };

    vm.createContext(sandbox);
    sources.forEach((code) => {
        vm.runInContext(code, sandbox, { filename: 'shared-ui.js' });
    });

    global['__CorporateAppHeader'] = sandbox.__CorporateAppHeader;
    /* eslint-enable */
}

export default async function prepareState(
    request: Hapi.Request,
    defaultState: typeof appDefaultState,
): Promise<ApplicationState> {
    const services = serviceManager.serviceInstances;
    let preparedState = { ...defaultState } as DeepWritable<ApplicationState>;

    preparedState.app.error = false;
    preparedState.settings = config.get('app');

    if (!request.auth.credentials.profileId) {
        preparedState.app.error = true;

        return preparedState as unknown as ApplicationState;
    }

    try {
        const userData = {
            id: request.auth.credentials.profileId as string,
            authorizedApplicationId: config.get<string>('externalSystemCode'),
        };

        const headers: HeaderRequest = {
            ...openApiUserHeadersFromRequest(request),
            [REQUEST_ID_HEADER_NAME]: request.info.id,
            [HEADERS.TRACE_ID]: request.headers['X-B3-TraceId'] as string,
            [HEADERS.CUSTOMER_ID]: userData.id,
        };

        const { validCustomerIds } = await services.mksCustomersUI.internalGetValidCustomerIds({
            headers: {
                ...headers,
                // Так как в этот момент мы еще не всегда знаем id пользователя - мы подставляем его сюда сами, просто чтобы он соответствовал формату
                [HEADERS.OPENAPI_COMPANY_ID]: ['UNDEFINED'],
            },
        });

        if (validCustomerIds.length === 0) {
            throw new Error('No available companies');
        }

        let organizationId = '';
        let organizationIdFromToken;
        const { holdingGroupId } = request.state;
        const organizationIdFromCookie = request.state.organizationId;
        const firstOrganizationIdInList = validCustomerIds[0];

        if (request.query.dataToken) {
            try {
                const params = await tokenWrapper.unwrap(request.query.dataToken);

                if (typeof params === 'object') {
                    organizationIdFromToken = params.cus;
                }
            } catch (error) {
                console.log('dataToken error', getErrorMessage(error));
            }
        }

        if (organizationIdFromToken) {
            organizationId = organizationIdFromToken;
        } else if (organizationIdFromCookie) {
            organizationId = organizationIdFromCookie;
        } else {
            organizationId = firstOrganizationIdInList;
        }

        if (!validCustomerIds.some((id) => id === organizationId)) {
            organizationId = firstOrganizationIdInList;
        }

        headers['x-company-id'] = organizationId;

        const sharedUI = await services.sharedUI.getSharedResources(
            {
                profileId: userData.id,
                organizationId,
                appName: APP_NAME,
                organizationsIds: validCustomerIds,
                loadedCodeCallback: sharedUILoadedCodeCallback,
                userAgent: request.headers[HEADERS.USER_AGENT],
                holdingGroupId,
            },
            {
                traceId: request.headers[HEADERS.TRACE_ID],
            },
        );

        const firstOrganizationIdInGroup = sharedUI?.state?.holdingGroup?.group?.organizations?.[0];

        if (firstOrganizationIdInGroup) {
            organizationId = firstOrganizationIdInGroup;
        }

        const [
            mksUsers,
            mksRoles,
            mksAddresses,
            mksCategory,
            mksFeaturesCompanies,
            mksFeaturesUsers,
            mksPermissions,
            mksCustomers,
        ] = await Promise.all([
            services.mksUsers
                .getByIds({
                    urlParams: {
                        userIds: userData.id as unknown as Set<string>,
                    },
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                })
                .catch(() => [] as ApiGetByIdsResponse),
            services.mksRoles
                .getByUserId({
                    urlParams: {
                        userId: userData.id,
                    },
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                })
                .catch(() => [] as ApiGetByUserIdResponse),
            services.mksCustomersUI
                .getAddressesV2({
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                    urlParams: { customerIds: validCustomerIds },
                })
                .catch(() => addressInitialState),
            services.mksCustomers
                .getCategory({
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: [organizationId],
                    },
                    urlParams: { customerId: organizationId },
                })
                .catch(() => categoryInitialState),
            services.mksGlobalFeatures
                .getSubjectFeatureV2({
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                    query: { type: SubjectType.Customer },
                    urlParams: { ids: validCustomerIds },
                })
                .catch(() => [] as ApiGetSubjectFeatureV2Response),
            services.mksGlobalFeatures
                .getSubjectFeatureV2({
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                    query: {
                        type: SubjectType.User,
                        project: appDefaultState.settings.projectName,
                    },
                    urlParams: { ids: [userData.id] },
                })
                .catch(() => [] as ApiGetSubjectFeatureV2Response),
            services.mksPermissions
                .runMandatoryChecksV2({
                    body: {
                        rights: USER_RIGHTS,
                    },
                    headers: {
                        ...headers,
                        [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                    },
                })
                .catch(() => ({}) as RunMandatoryChecksV2Response),
            services.mksCustomers.getByIds({
                headers: {
                    ...headers,
                    [HEADERS.OPENAPI_COMPANY_ID]: validCustomerIds,
                },
                urlParams: { customerIds: validCustomerIds as unknown as Set<string> },
            }),
        ]);

        const user = mksUsers.find((u) => u?.id === userData.id);
        const role = mksRoles.find((r) => r?.userId === userData.id)?.name;

        const categoryError = mksCategory.sksCode ? null : EErrorMessages.CATEGORY_ERROR;

        const rights =
            mksPermissions?.checks?.reduce((acc, check) => {
                if (check.customerId) {
                    acc[check.customerId] = check.rightChecksResults;
                }

                return acc;
            }, {} as TRights) ?? {};

        const organizationsList = mksCustomers.map(
            (organization): TCustomCustomer => ({
                ...organization,
                name: organization?.name.fullNameWithOpf,
                organizationName: {
                    shortName: organization?.name.shortNameWithOpf,
                    fullName: organization?.name.fullNameWithOpf,
                },
                rights: rights[organization.eqId],
                features: {
                    ...mksFeaturesCompanies
                        .filter((feature) => feature.subjectId === organization.eqId)
                        .reduce((list, feature) => ({ ...list, [feature.name]: true }), {}),
                    ...mksFeaturesUsers.reduce(
                        (list, feature) => ({ ...list, [feature.name]: true }),
                        {},
                    ),
                },
            }),
        );

        preparedState = {
            ...preparedState,
            creditProductsHeader: {
                ...preparedState.creditProductsHeader,
                category: {
                    ...preparedState.creditProductsHeader.category,
                    category: mksCategory,
                    error: categoryError,
                },
            },
            creditProductsMainMenu: {
                ...preparedState.creditProductsMainMenu,
                category: {
                    ...preparedState.creditProductsMainMenu.category,
                    category: mksCategory,
                    error: categoryError,
                },
            },
            sharedUI,
            holdingGroup: {
                groupOrganizationsIds: sharedUI?.state?.holdingGroup?.group?.organizations,
                groupInfo: {
                    groupName: sharedUI?.state?.holdingGroup?.group?.name,
                    groupId: sharedUI?.state?.holdingGroup?.group?.id,
                },
            },
            organization: {
                ...preparedState.organization,
                list: organizationsList,
                current: organizationId,
                category: mksCategory,
                categoryError,
            },
            blockings: sharedUI?.state?.blockings,
            user: {
                lastName: user?.russianName?.first,
                firstName: user?.russianName?.last,
                middleName: user?.russianName?.middle,
                phoneNumber: user?.phoneNumber,
                id: userData.id,
                role,
            },
            router: {
                action: 'POP',
                location: {
                    pathname: request.url.pathname || '',
                    search: request.url.search || '',
                    hash: request.url.hash || '',
                    query: {},
                    key: '',
                    state: null,
                },
            },
            addresses: mksAddresses,
        };
    } catch (error) {
        const nodeJsError = factoryError(500, error as ErrorInfo);

        nodeJsLogger.logNodeJsError(nodeJsError, request.headers);

        preparedState.app.error = true;
        preparedState.settings.traceId = request.headers[HEADERS.TRACE_ID];
    }

    return preparedState as unknown as ApplicationState;
}
