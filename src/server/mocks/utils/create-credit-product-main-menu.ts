import { type CreditProduct, type Customer } from 'corp-credit-products-api-typescript-services';
import { Action } from 'corp-credit-products-api-typescript-services/dist/action';
import { SuspensiveConditionsDeadlineStatus } from 'corp-credit-products-api-typescript-services/dist/suspensiveConditionsDeadlineStatus';

import { type ECreditProductsCodes } from '#/src/constants/credit-products';
import {
    CURRENCY_CNY,
    CURRENCY_EUR,
    CURRENCY_RUR,
    CURRENCY_USD,
} from '#/src/server/mocks/data/constants';

interface CreditProductParams {
    customer: Customer;
    docNumber: string;
    productType: ECreditProductsCodes;
    productCode?: string;
    actions?: Action[];
    suspensiveCondition?: SuspensiveConditionsDeadlineStatus;
}

export const createCreditProductMainMenu = ({
    customer,
    docNumber,
    productType,
    productCode = '',
    actions = [Action.Details],
    suspensiveCondition = SuspensiveConditionsDeadlineStatus.Undefined,
}: CreditProductParams): CreditProduct => ({
    customer,
    requisites: {
        docNumber,
        dealId: 180582220,
        productType,
        productCode,
        isActive: true,
        fromDate: 1666645200,
        issueDate: 1786635300,
        toDate: 1830200400,
        dateDealClose: 1910200400,
        beneficiary: 'OOO "Компания"',
        currency: 'RUR',
        serviceType: 2,
    },
    summary: {
        sum: { amount: 15379200000, currency: CURRENCY_RUR },
        availableAmount: { amount: 1402000, currency: CURRENCY_RUR },
        limit: { amount: 15379200000, currency: CURRENCY_RUR },
        sopukAmounts: [
            { amount: 15379200000, currency: CURRENCY_RUR },
            { amount: 15379200000, currency: CURRENCY_USD },
            { amount: 15379200000, currency: CURRENCY_EUR },
            { amount: 15379200000, currency: CURRENCY_CNY },
        ],
        totalOverdueAndFine: { amount: 567000, currency: CURRENCY_RUR },
        totalDebt: { amount: 6635144575, currency: CURRENCY_RUR },
        totalOverdue: { amount: 335144575, currency: CURRENCY_RUR },
    },
    editDate: 1720007180,
    debts: {
        loan: {
            debt: { amount: 6529212953, currency: CURRENCY_RUR },
            debtToPay: { amount: 6529212953, currency: CURRENCY_RUR },
            payDebtTillDate: 1830200400,
            overdueDebt: { amount: 6529212953, currency: CURRENCY_RUR },
            fineDebt: { amount: 6529212953, currency: CURRENCY_RUR },
        },
        interest: {
            interest: { amount: 105931622, currency: CURRENCY_RUR },
            interestToPay: { amount: 105931622, currency: CURRENCY_RUR },
            payInterestTillDate: 1830200400,
            overdueInterest: { amount: 6529212953, currency: CURRENCY_RUR },
            fineInterest: { amount: 6529212953, currency: CURRENCY_RUR },
        },
        fee: {
            feeToPay: { amount: 105931622, currency: CURRENCY_RUR },
            overdueFee: { amount: 105931622, currency: CURRENCY_RUR },
            fineFee: { amount: 105931622, currency: CURRENCY_RUR },
        },
    },
    actions,
    suspensiveConditionsStatus: suspensiveCondition,
});
