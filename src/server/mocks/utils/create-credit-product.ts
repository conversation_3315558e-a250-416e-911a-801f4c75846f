import {
    type CreditProduct,
    SuspensiveConditionsDeadlineStatus,
} from 'corp-core-credit-products-api-typescript-services';
import { PaymentStatus } from 'corp-core-credit-products-api-typescript-services/dist/paymentStatus';
import { TrancheStatus } from 'corp-core-credit-products-api-typescript-services/dist/trancheStatus';
import { type TSopukSummaryV2 } from 'thrift-services/services/credit_products_v2';

import { ECreditProductActions } from '#/src/constants/credit-products';
import {
    CURRENCY_CNY,
    CURRENCY_EUR,
    CURRENCY_RUR,
    CURRENCY_USD,
} from '#/src/server/mocks/data/constants';

const MILLISECONDS = 1000;
const ONE_YEAR_SHIFT = 60 * 60 * 24 * 365;
const CURRENT_DATE = new Date().valueOf() / MILLISECONDS;

enum CreditDebtStatus {
    MIN_DEBT_PAYMENT_IN_CALCULATION = 'MIN_DEBT_PAYMENT_IN_CALCULATION',
    MIN_DEBT_PAYMENT_CALCULATED = 'MIN_DEBT_PAYMENT_CALCULATED',
    FIVE_DAYS_TILL_MIN_PAYMENT_DAY = 'FIVE_DAYS_TILL_MIN_PAYMENT_DAY',
    FIRST_MIN_DEBT_PAYMENT_OVERDUE = 'FIRST_MIN_DEBT_PAYMENT_OVERDUE',
    TWO_OR_MORE_MIN_DEBT_PAYMENTS_OVERDUE = 'TWO_OR_MORE_MIN_DEBT_PAYMENTS_OVERDUE',
}

const sopukSummary: TSopukSummaryV2 = {
    totalDebtByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalFinesByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalInterestsByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalLoansByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalOverdueDebtsByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalOverdueInterestsByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
    totalOverdueLoansByCurrency: [
        { amount: 40040000, currency: CURRENCY_RUR },
        { amount: 40040000, currency: CURRENCY_USD },
        { amount: 40040000, currency: CURRENCY_EUR },
        { amount: 40040000, currency: CURRENCY_CNY },
    ],
};

export const createCreditProduct = ({
    docNumber = '',
    productType = '',
    productCode = '',
    withDebt = false,
    withFine = false,
    withSopukSummary = false,
    withDifferentDates = false,
}): CreditProduct => ({
    docNumber,
    requisites: {
        sum: { amount: ********, currency: CURRENCY_RUR },
        product: productType,
        productCode,
        limit: { amount: **************, currency: CURRENCY_RUR },
        fromDate: { seconds: CURRENT_DATE },
        issueDate: { seconds: CURRENT_DATE },
        toDate: { seconds: CURRENT_DATE + ONE_YEAR_SHIFT },
        insuranceDate: { seconds: CURRENT_DATE },
        beneficiary: 'ООО Кампания',
        account: '40802810838090004842',
        trancheDeadline: 0,
        clientLimit: { amount: ********, currency: CURRENCY_RUR },
        dealStatus: 'A',
        maxLimit: { amount: ********0000, currency: CURRENCY_RUR },
        trancheGettingDeadline: { seconds: ********** },
        repaymentAccount: '40802810838090004842',
        dateDealClose: { seconds: ********** },
        trancheStatus: TrancheStatus.Active,
        businessBlock: 'Block',
        commissions: [
            {
                percentType: 'PercentType_CommSubscriptionMB',
                rate: 1,
            },
        ],
        creditAmountsByCurrency: [
            { amount: ********, currency: CURRENCY_RUR },
            { amount: ********, currency: CURRENCY_USD },
            { amount: ********, currency: CURRENCY_EUR },
            { amount: ********, currency: CURRENCY_CNY },
        ],
        mainDebtShare: 10,
        repaymentType: 'repaymentType',
        daysBeforeAdvancedRepay: 5,
        suspensiveConditionsDeadlineInfo: {
            suspensiveConditionsDeadlineStatus: SuspensiveConditionsDeadlineStatus.Approaching,
            notificationsCount: 2,
        },
        notifyAboutAdvancedRepay: true,
        trancheBegin: { seconds: ********** },
        trancheEnd: { seconds: 1538321600 },
        paymentStatus: PaymentStatus.Active,
    },
    frontParams: {
        errorMessage: '',
        fault: false,
        hasPaymentSchedule: false,
        hasClientLimit: true,
        hasCreditHolidaysFZ: false,
        isNotReadyScheduleOfPromoDeal: false,
        debtStatus: CreditDebtStatus.MIN_DEBT_PAYMENT_CALCULATED,
        hasDealDocs: false,
        hasPrepaymentAvailability: false,
    },
    gracePeriod: {
        gracePeriodDeadLine: 0,
        gracePeriodToDate: { seconds: -62135605817 },
        gracePeriodInterestAccrued: 0,
        gracePeriodActualEndDate: { seconds: -62135605817 },
        gracePeriodInterestAccruedAmount: { amount: 0, currency: CURRENCY_RUR },
    },
    rate: {
        debtRate: 16.3,
        debtRateDaily: 0.23,
        feeRate: 0,
        withdrawalRate: 10,
        overdueDebtRate: 5,
        overdueFeeRate: 5,
        overdueInterestRate: 5,
        overdueIncreasedRate: 5,
    },
    availableAmount: { amount: 150000000, currency: CURRENCY_RUR },
    debts: {
        loan: {
            blockLimitDate: { seconds: ******** },
            overdueMinPayDate: { seconds: 1491944400 },
            minDebtToPay: { amount: 40000000, currency: CURRENCY_RUR },
            minPayDebtCalculationDate: { seconds: 1491944400 },
            minPayDebtTillDate: { seconds: 1491944400 },
            daysOverdue: 60,
            debt: { amount: withDebt ? 40000000 : 0, currency: CURRENCY_RUR },
            debtToPay: { amount: 40000000, currency: CURRENCY_RUR },
            payDebtTillDate: { seconds: 1491944400 },
            overdueDebt: { amount: 0, currency: CURRENCY_RUR },
            fineDebt: { amount: 0, currency: CURRENCY_RUR },
        },
        interest: {
            minInterestToPay: { amount: 0, currency: CURRENCY_RUR },
            overdueInterestDate: { seconds: 1491944400 },
            interest: { amount: 40000, currency: CURRENCY_RUR },
            interestToPay: { amount: 40000, currency: CURRENCY_RUR },
            payInterestTillDate: { seconds: withDifferentDates ? 1591944400 : 1491944400 },
            overdueInterest: { amount: withDebt ? 40000000 : 0, currency: CURRENCY_RUR },
            fineInterest: { amount: 0, currency: CURRENCY_RUR },
        },
        fee: {
            feeToPay: { amount: 0, currency: CURRENCY_RUR },
            payFeeTillDate: { seconds: 1491944400 },
            overdueFee: { amount: 0, currency: CURRENCY_RUR },
            fineFee: { amount: 0, currency: CURRENCY_RUR },
        },
        guaranteeRequirements: {
            demandToPay: { amount: 0, currency: CURRENCY_RUR },
            payDemandTillDate: { seconds: ******** },
            overdueDemand: { amount: 0, currency: CURRENCY_RUR },
            fineDemand: { amount: 0, currency: CURRENCY_RUR },
        },
    },
    summary: {
        sopukSummary: withSopukSummary ? sopukSummary : undefined,
        dailyFine: { amount: 0, currency: CURRENCY_RUR },
        overdueDays: 0,
        totalFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalInterestSumToPay: { amount: 0, currency: CURRENCY_RUR },
        totalToPay: { amount: 40040000, currency: CURRENCY_RUR },
        totalDebt: { amount: 40040000, currency: CURRENCY_RUR },
        totalLoanSumToPay: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalLoanSum: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalInterestOverdue: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalInterestAndFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalInterestToPayAndFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalLoanAndFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalLoanToPayAndFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalOverdue: { amount: 0, currency: CURRENCY_RUR },
        totalOverdueAndFine: { amount: withFine ? 40040000 : 0, currency: CURRENCY_RUR },
        totalToPayWithoutOverdueAndFine: { amount: 40040000, currency: CURRENCY_RUR },
        totalTrancheSum: { amount: 0, currency: CURRENCY_RUR },
    },
    actions: [
        { action: ECreditProductActions.GET_CREDIT },
        { action: ECreditProductActions.GET_DETAILS },
        { action: ECreditProductActions.GET_STATEMENT },
        { action: ECreditProductActions.GET_TRANCHE },
        { action: ECreditProductActions.GET_TRANCHE_MMB },
        { action: ECreditProductActions.GET_GUARANTEE },
    ],
    actualDate: { seconds: ********** },
    servicingAccounts: [{ code: '**********', number: '**********' }],
    dealId: '123',
    productDocs: [
        {
            uuid: '123',
            docType: 'testType',
            requestId: '123',
        },
    ],
});
