import { type FetcherWithOnProgress } from 'corporate-services/lib/fetch-factory';
import {
    type OpenApiServiceMethod,
    type OpenApiServiceMethodParams,
} from 'corporate-services/lib/openapi/open-api-types';

/**
 * Метод создания промиса с имитацией задержки сетевого запроса.
 *
 * @param data Данные которые требуется разрешить.
 * @param delay Задержка в миллисекундах (по умолчанию 100-200мс для имитации реального запроса).
 */
export const remoteServerImitation = <TData>(
    data: TData,
    delay?: number,
): FetcherWithOnProgress<TData> => {
    let abort;

    // Случайная задержка от 100 до 200мс для имитации реального сетевого запроса
    const requestDelay = delay ?? Math.floor(Math.random() * 100) + 100;

    const abortedPromise = new Promise<TData>((resolve, reject) => {
        abort = reject;

        setTimeout(() => {
            resolve(data);
        }, requestDelay);
    });

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    abortedPromise.abort = abort;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return abortedPromise;
};

/**
 * @param mockData Данные которые требуется отдать клиенту.
 * @param [mockCallbackServiceSimulator] Callback симулятора для runtime генерации моков.
 */
interface CreateMockFetchMethodParams<
    QueryParams,
    UrlParams,
    HeaderParams,
    BodyParams,
    ReturnType,
> {
    mockData: ReturnType;
    mockCallbackServiceSimulator?: (
        response: ReturnType,
        requestData?: OpenApiServiceMethodParams<QueryParams, UrlParams, HeaderParams, BodyParams>,
    ) => ReturnType;
}

/**
 * Метод имитации ответа сервера.
 *
 * @param params Параметры метода имитации ответа сервера.
 */
export const createMockFetchMethod =
    <QueryParams, UrlParams, HeaderParams, BodyParams, ReturnType>({
        mockData,
        mockCallbackServiceSimulator,
    }: CreateMockFetchMethodParams<
        QueryParams,
        UrlParams,
        HeaderParams,
        BodyParams,
        ReturnType
    >): OpenApiServiceMethod<QueryParams, UrlParams, HeaderParams, BodyParams, ReturnType> =>
    (input) => {
        const resolveData = mockCallbackServiceSimulator
            ? mockCallbackServiceSimulator(mockData, input)
            : mockData;

        return remoteServerImitation(resolveData);
    };
