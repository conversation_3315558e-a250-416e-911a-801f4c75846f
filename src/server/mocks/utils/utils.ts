import {
    type ApiGetCreditProductsMainMenuRequestBody,
    type CreditProduct,
} from 'corp-credit-products-api-typescript-services';
import { type Customer } from 'corp-customers-api-typescript-services';
import { type ApiGetSubjectFeatureResponse } from 'corp-global-features-api-typescript-services';
import {
    type ApiRunMandatoryChecksV2HeadersParams,
    type ApiRunMandatoryChecksV2RequestBody,
    type Checks,
} from 'corp-role-model-mks-permissions-api-typescript-services';
import { OPENAPI_COMPANY_ID } from 'corporate-services/lib/constants/headers';

import {
    type CustomerShortData,
    PERMISSIONS_CHECK_RESULTS_MOCK,
    PERMISSIONS_OTHER_CHECKS_RESULTS_MOCK,
} from '#/src/server/mocks/data/data';

export const createSubjectFeatureResponse = (
    companyIds: string[],
    featuresNames: string[],
): ApiGetSubjectFeatureResponse =>
    companyIds.flatMap((companyId) =>
        featuresNames.map((featureName) => ({
            subjectId: companyId,
            featureId: 'featureId',
            project: 'corp-support',
            channel: 'NIB',
            name: featureName,
            description: 'description',
        })),
    );

export const createCustomersResponse = (customersNames: CustomerShortData) =>
    customersNames.map<Customer>((customer) => ({
        ...customer,
        type: 'legal',
        ogrnDate: new Date().toISOString(),
        registrationDateInRP: new Date().toISOString(),
        registrationNumberInRP: '*********',
        okpo: '*********',
        okved: '*********',
        contacts: {},
        inn: '*********',
        ogrn: '*********',
        kppList: ['*********'],
    }));

export const filterProductsMainMenu = (
    products: CreditProduct[],
    requestBody: ApiGetCreditProductsMainMenuRequestBody,
) => {
    const { filter, sort, page } = requestBody;
    let result = products;

    if (filter) {
        result = result?.filter((product) => {
            const matchesCustomer = filter.customers.some(
                (customer) => customer.customerId === product.customer.customerId,
            );
            const matchesProductType = filter.productTypes.includes(product.requisites.productType);
            const matchesIsActive = filter.isActive === product.requisites.isActive;
            const matchesDocNumber =
                !filter.docNumber || product.requisites.docNumber.includes(filter.docNumber);

            return matchesCustomer && matchesProductType && matchesIsActive && matchesDocNumber;
        });
    }

    if (sort.sortByCustomerName) {
        result?.sort((a, b) => a.customer.name.localeCompare(b.customer.name));
    }

    if (page?.pageNumber === 1) {
        result = result.slice(0, 10);
    } else if (page?.pageNumber === 2) {
        result = result.slice(10);
    }

    return result;
};

const generateRightChecksResults = (rights: string[]) =>
    rights.reduce(
        (acc, item) => ({
            ...acc,
            [item]: {
                checkResults: [...PERMISSIONS_CHECK_RESULTS_MOCK],
                successful: true,
            },
        }),
        {},
    );

export const createRunMandatoryChecksV2Response = (
    headers: ApiRunMandatoryChecksV2HeadersParams,
    body: ApiRunMandatoryChecksV2RequestBody,
) => ({
    generalSuccessful: true,
    checks: (
        headers[OPENAPI_COMPANY_ID.toLowerCase() as typeof OPENAPI_COMPANY_ID] as unknown as string
    )
        .split(',')
        .map<Checks>((customerId) => ({
            customerId: customerId.trimStart(),
            rightChecksResults: generateRightChecksResults(body?.rights ?? []),
            otherChecksResults: [...PERMISSIONS_OTHER_CHECKS_RESULTS_MOCK],
            successful: true,
        })),
});
