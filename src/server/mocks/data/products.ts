import { SuspensiveConditionsDeadlineStatus } from 'corp-credit-products-api-typescript-services';
import { Action } from 'corp-credit-products-api-typescript-services/dist/action';

import { ECreditProductsCodes } from '#/src/constants/credit-products';
import {
    ECompanyNamesMock,
    ECreditProductsDocNumbersMock,
    ECustomersIdsMock,
} from '#/src/server/mocks/data/constants';
import { createCreditProduct } from '#/src/server/mocks/utils/create-credit-product';
import { createCreditProductMainMenu } from '#/src/server/mocks/utils/create-credit-product-main-menu';

// CREDIT PRODUCTS MAIN MENU
const BUSINESS_CREDIT_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    actions: [Action.Details, Action.Credit, Action.Tranche],
    suspensiveCondition: SuspensiveConditionsDeadlineStatus.Violated,
});
const BUSINESS_CREDIT2_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT2,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    actions: [Action.Details, Action.Credit, Action.Tranche],
    suspensiveCondition: SuspensiveConditionsDeadlineStatus.Violated,
});
const BUSINESS_CREDIT3_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT3,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    actions: [Action.Details, Action.Credit, Action.Tranche],
    suspensiveCondition: SuspensiveConditionsDeadlineStatus.Violated,
});

const BUSINESS_CREDIT_DIFFERENT_DATES_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT_DIFFERENT_DATES,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    actions: [Action.Details, Action.Statement],
    suspensiveCondition: SuspensiveConditionsDeadlineStatus.Approaching,
});

const BUSINESS_CREDIT_DIFFERENT_DATES2_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT_DIFFERENT_DATES2,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    actions: [Action.Details, Action.Statement],
    suspensiveCondition: SuspensiveConditionsDeadlineStatus.Approaching,
});

const CREDIT_LINE_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE,
    productType: ECreditProductsCodes.CREDIT_LINE,
    actions: [Action.Details, Action.Tranche],
});

const CREDIT_LINE_MAIN_MENU_MOCK2 = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE2,
    productType: ECreditProductsCodes.CREDIT_LINE,
    actions: [Action.Details, Action.Tranche],
});

const CREDIT_LINE_MAIN_MENU_MOCK3 = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE3,
    productType: ECreditProductsCodes.CREDIT_LINE,
    actions: [Action.Details, Action.Tranche],
});

const OVERDRAFT_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.GOOGLE, name: ECompanyNamesMock.GOOGLE },
    docNumber: ECreditProductsDocNumbersMock.OVERDRAFT,
    productType: ECreditProductsCodes.OVERDRAFT,
    productCode: 'OVERNZ',
    actions: [Action.Details, Action.Tranche, Action.Statement],
});

const OVERDRAFT_WITH_TRANCHE_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.EMPTY, name: ECompanyNamesMock.EMPTY },
    docNumber: ECreditProductsDocNumbersMock.OVERDRAFT_WITH_TRANCHE,
    productType: ECreditProductsCodes.OVERDRAFT,
    productCode: 'OVER',
    actions: [Action.Details, Action.Tranche, Action.Statement],
});

const GUARANTY_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.GUARANTY,
    productType: ECreditProductsCodes.GUARANTY,
    actions: [Action.Details, Action.Guarantee],
});

const GUARANTY_LINE_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.GUARANTY_LINE,
    productType: ECreditProductsCodes.GUARANTY_LINE,
    actions: [Action.Details, Action.Guarantee, Action.Statement],
});

const SOPUK_MAIN_MENU_MOCK = createCreditProductMainMenu({
    customer: { customerId: ECustomersIdsMock.ALFA_LEASING, name: ECompanyNamesMock.ALFA_LEASING },
    docNumber: ECreditProductsDocNumbersMock.SOPUK,
    productType: ECreditProductsCodes.SOPUK,
    actions: [Action.Details, Action.Credit],
});

// CREDIT PRODUCTS
const OVERDRAFT_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.OVERDRAFT,
    productType: ECreditProductsCodes.OVERDRAFT,
    withFine: true,
});

const OVERDRAFT_TRANCHE_MOCK = createCreditProduct({
    docNumber: 'M38AIV',
    productType: ECreditProductsCodes.OVERDRAFT,
    withFine: true,
    productCode: 'OVER',
});

const CREDIT_CARD_MOCK = createCreditProduct({
    docNumber: '28BO34',
    productType: ECreditProductsCodes.OVERDRAFT,
    withFine: true,
    productCode: 'OVERNZUC',
});

const BUSINESS_CREDIT_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    withFine: true,
});

const BUSINESS_CREDIT_DIFFERENT_DATES_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.BUSINESS_CREDIT_DIFFERENT_DATES,
    productType: ECreditProductsCodes.BUSINESS_CREDIT,
    withFine: true,
    withDifferentDates: true,
});

const CREDIT_LINE_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.CREDIT_LINE,
    productType: ECreditProductsCodes.CREDIT_LINE,
    productCode: 'VKLRUVMS1',
});

const GUARANTY_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.GUARANTY,
    productType: ECreditProductsCodes.GUARANTY,
});

const GUARANTY_LINE_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.GUARANTY_LINE,
    productType: ECreditProductsCodes.GUARANTY_LINE,
});

export const SOPUK_MOCK = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.SOPUK,
    productType: ECreditProductsCodes.SOPUK,
    withSopukSummary: true,
});

export const SOPUK_MOCK_WITHOUT_SUMMARY = createCreditProduct({
    docNumber: ECreditProductsDocNumbersMock.SOPUK,
    productType: ECreditProductsCodes.SOPUK,
    withSopukSummary: false,
});

export const SOPUK_MOCK_WITH_OVERDUE_SUMMARY_RUB_YUAN: ReturnType<typeof createCreditProduct> = {
    ...SOPUK_MOCK,
    summary: {
        ...SOPUK_MOCK.summary,
        sopukSummary: {
            ...SOPUK_MOCK.summary.sopukSummary,
            totalOverdueDebtsByCurrency: [
                {
                    amount: 40040000,
                    currency: {
                        code: 810,
                        mnemonicCode: 'RUR',
                        minorUnits: 100,
                        unicodeSymbol: '₽',
                        fullName: 'Российский рубль',
                    },
                },
                {
                    amount: 40040000,
                    currency: {
                        code: 156,
                        mnemonicCode: 'CNY',
                        minorUnits: 100,
                        unicodeSymbol: '¥',
                        fullName: 'Юань',
                    },
                },
            ],
        },
    },
};

export const SOPUK_MOCK_WITH_OVERDUE_SUMMARY_ONLY_YUAN: ReturnType<typeof createCreditProduct> = {
    ...SOPUK_MOCK,
    summary: {
        ...SOPUK_MOCK.summary,
        sopukSummary: {
            ...SOPUK_MOCK.summary.sopukSummary,
            totalOverdueDebtsByCurrency: [
                {
                    amount: 40040000,
                    currency: {
                        code: 156,
                        mnemonicCode: 'CNY',
                        minorUnits: 100,
                        unicodeSymbol: '¥',
                        fullName: 'Юань',
                    },
                },
            ],
        },
    },
};

export const CREDIT_PRODUCTS_MAIN_MENU_MOCK = {
    BUSINESS_CREDIT_MAIN_MENU_MOCK,
    BUSINESS_CREDIT2_MAIN_MENU_MOCK,
    BUSINESS_CREDIT3_MAIN_MENU_MOCK,
    BUSINESS_CREDIT_DIFFERENT_DATES_MAIN_MENU_MOCK,
    BUSINESS_CREDIT_DIFFERENT_DATES2_MAIN_MENU_MOCK,
    CREDIT_LINE_MAIN_MENU_MOCK,
    CREDIT_LINE_MAIN_MENU_MOCK2,
    CREDIT_LINE_MAIN_MENU_MOCK3,
    OVERDRAFT_MAIN_MENU_MOCK,
    OVERDRAFT_WITH_TRANCHE_MAIN_MENU_MOCK,
    GUARANTY_MAIN_MENU_MOCK,
    GUARANTY_LINE_MAIN_MENU_MOCK,
    SOPUK_MAIN_MENU_MOCK,
};

export const CREDIT_PRODUCTS_MOCK = {
    OVERDRAFT_MOCK,
    OVERDRAFT_TRANCHE_MOCK,
    CREDIT_CARD_MOCK,
    BUSINESS_CREDIT_MOCK,
    BUSINESS_CREDIT_DIFFERENT_DATES_MOCK,
    CREDIT_LINE_MOCK,
    GUARANTY_MOCK,
    GUARANTY_LINE_MOCK,
    SOPUK_MOCK,
};
