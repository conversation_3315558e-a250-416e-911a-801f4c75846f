import { CURRENCY } from 'corporate-services/lib/currency';

export enum ECompanyNamesMock {
    EMPTY = 'ООО "Пустышка"',
    GOOGLE = 'ООО "Google Ltd."',
    ALFA_LEASING = 'ООО "Альфа-Лизинг"',
    ACE_MILK = 'ООО "ЭЙС МИЛК"',
}

export enum ECustomersIdsMock {
    EMPTY = 'UCI0WP',
    GOOGLE = 'KMFJYH',
    ALFA_LEASING = 'U11923',
    ACE_MILK = 'M176UX',
}

export enum ECreditProductsDocNumbersMock {
    OVERDRAFT = '037A4V',
    OVERDRAFT2 = '137A4V',
    OVERDRAFT_WITH_TRANCHE = 'M38AIV',
    BUSINESS_CREDIT = '064Y4K',
    BUSINESS_CREDIT2 = '164Y4K',
    BUSINESS_CREDIT3 = '264Y4K',
    BUSINESS_CREDIT_DIFFERENT_DATES = 'L2N0SF',
    BUSINESS_CREDIT_DIFFERENT_DATES2 = 'L3N0SF',
    CREDIT_LINE = '07F78L',
    CREDIT_LINE2 = '17F78L',
    CREDIT_LINE3 = '27F78L',
    GUARANTY = '03BW4Q',
    GUARANTY2 = '13BW4Q',
    GUARANTY_LINE = '0RGZ6R',
    GUARANTY_LINE2 = '1RGZ6R',
    SOPUK = '1543',
}

export const CURRENCY_RUR = CURRENCY.RUR;
export const CURRENCY_USD = CURRENCY.USD;
export const CURRENCY_EUR = CURRENCY.EUR;
export const CURRENCY_CNY = CURRENCY.CNY;
