import { type ApiGetByCustomerIdResponse } from 'corp-accounts-api-typescript-services';
import {
    type ApiGetClientLimitResponse,
    type ApiGetCreditProductsResponse,
} from 'corp-core-credit-products-api-typescript-services';
import { type ApiGetOrConnectAlfaCreditResponse } from 'corp-credit-document-circulation-api-typescript-services';
import {
    type ApiGetCreditProductsMainMenuResponse,
    type ApiGetSuspensiveConditionsResponse,
    MonitoringOperationStatus,
    SuspensiveConditionType,
} from 'corp-credit-products-api-typescript-services';
import {
    type ApiGetManagersResponse,
    type Customer,
    type CustomerAddressesV2,
} from 'corp-customers-api-typescript-services';
import { type ApiGetEarlyRepaymentProcessingTypeResponse } from 'corp-early-repayment-api-typescript-services';
import {
    type GetStatementRequestsResponse,
    ProductType,
    StatementStatus,
} from 'corp-loan-statements-api-typescript-services';
import { type ApiGetWidgetStatusResponse } from 'corp-new-credit-api-typescript-services';
import { type ApiInternalGetByUserIdResponse } from 'corp-proxies-api-typescript-services';
import { type RunDecisionMakerCheckResponse } from 'corp-role-model-mks-permissions-api-typescript-services';
import { ResultCode } from 'corp-role-model-mks-permissions-api-typescript-services/dist/resultCode';
import { type ApiGetByUserIdResponse } from 'corp-roles-api-typescript-services';
import { type ApiGetByIdsResponse } from 'corp-users-api-typescript-services';

import { earlyRepaymentProcessingTypes } from '#/src/constants/credit-processing';
import { DECISION_COMMITTEE_UTK } from '#/src/containers/main-page/main-page-header/main-page-smart-limits/utils/constants';
import { type TCreditRequestProgressStagesResponse } from '#/src/ducks/credit-requests/types';
import {
    CURRENCY_RUR,
    ECompanyNamesMock,
    ECustomersIdsMock,
} from '#/src/server/mocks/data/constants';
import {
    CREDIT_PRODUCTS_MAIN_MENU_MOCK,
    CREDIT_PRODUCTS_MOCK,
} from '#/src/server/mocks/data/products';

export type CustomerShortData = Array<Pick<Customer, 'eqId' | 'name'>>;

export const ALFA_CREDIT_RESPONSE_MOCK: ApiGetOrConnectAlfaCreditResponse = {
    checks: [
        {
            customerId: 'U11923',
            code: 'sad',
            check: 'OK',
            claims: ['claims1', 'claims2', 'claims3'],
        },
    ],
    generalSuccessful: true,
};

export const USERS_RESPONSE_MOCK: ApiGetByIdsResponse = [
    {
        id: 'XB7H4T',
        registrationDate: '28.12.1970',
        russianName: {
            first: 'Аркадий',
            middle: 'Натанович',
            last: 'Стругацкий',
        },
        phoneNumber: '+71098103981',
        email: '<EMAIL>',
    },
];

export const CREDIT_RESPONSE_MOCK = {
    commonCreditRequestList: [
        {
            id: '30d64bf8-8e35-4e1c-a5de-14dbf1791899',
            type: 'LIMIT',
            lmLimitId: null,
            createDt: 1731311556,
            description: 'Сохранили заявку — заполните её до конца',
            clientStatus: 'Черновик',
            isAvailableForCurrentChannel: null,
            productName: 'Заявка на кредит',
            limit: {
                amount: 15000000000,
                currency: CURRENCY_RUR,
            },
            redirect: [
                {
                    redirectLink:
                        '/creditsb/30d64bf8-8e35-4e1c-a5de-14dbf1791899&utm_source=button5',
                    linkText: null,
                    type: 'CARD',
                },
            ],
        },
        {
            id: '3184056b-6793-4641-924c-ea311da22ef3',
            type: 'WELCOME',
            lmLimitId: null,
            createDt: 1742970916,
            description: 'Овердрафт подключён к счёту',
            clientStatus: 'Отклонена',
            isAvailableForCurrentChannel: null,
            productName: 'Альфа-Старт — овердрафт',
            limit: {
                amount: 1999994200,
                currency: CURRENCY_RUR,
            },
            redirect: [
                {
                    redirectLink:
                        '/creditsb/welcome/3184056b-6793-4641-924c-ea311da22ef3?utm_source=button5',
                    linkText: null,
                    type: 'CARD',
                },
            ],
        },
    ],
};

export const CREDIT_REQUEST_PROGRESS_STAGES_MOCK: TCreditRequestProgressStagesResponse = [
    {
        stageOrder: 100,
        stageName: 'ЗАЯВКА НА КРЕДИТ',
        progressStage: 'DONE',
        sla: undefined,
        status: undefined,
    },
    {
        stageOrder: 200,
        stageName: 'РАССМОТРЕНИЕ ЗАЯВКИ',
        progressStage: 'DONE',
        sla: undefined,
        status: undefined,
    },
    {
        stageOrder: 300,
        stageName: 'КРЕДИТНОЕ ПРЕДЛОЖЕНИЕ',
        progressStage: 'DONE',
        sla: undefined,
        status: undefined,
    },
    {
        stageOrder: 400,
        stageName: 'ПОДТВЕРЖДЕНИЕ УСЛОВИЙ',
        progressStage: 'CURRENT',
        sla: new Date('2025-01-29T13:06:06.677182Z'),
        status: {
            clientDescription:
                'Заявка принята – вам позвонят из банка. Чтобы узнать решение сразу, заполните данные сами',
            clientStatusName: 'В РАБОТЕ',
        },
    },
    {
        stageOrder: 500,
        stageName: 'ПОДПИСАНИЕ',
        progressStage: 'TODO',
        sla: undefined,
        status: undefined,
    },
    {
        stageOrder: 600,
        stageName: 'КРЕДИТ ОФОРМЛЕН',
        progressStage: 'TODO',
        sla: undefined,
        status: undefined,
    },
];

export const CUSTOMERS_ADDRESSES_RESPONSE_MOCK: CustomerAddressesV2 = {
    customerId: 'customer-id',
    address: {
        fullAddress:
            '236022, РОССИЯ, Калининградская область, КАЛИНИНГРАД, ул, ГЕНЕРАЛ-ЛЕЙТЕНАНТА ОЗЕРОВА, 17Б, 17',
        country: {
            title: 'Russian Federation',
            charCode: 'RU',
            numericCode: '643',
            titleInRussian: 'РОССИЯ',
        },
        region: {
            code: '39',
            fullName: 'Калининградская область',
            shortName: 'Калинингр. обл.',
        },
        district: '',
        city: 'КАЛИНИНГРАД',
        streetInfo: {
            name: 'ГЕНЕРАЛ-ЛЕЙТЕНАНТА ОЗЕРОВА',
            fullTypeName: 'улица',
            shortTypeName: 'ул',
        },
        house: '17Б',
        building: '',
        flat: '17',
        postcode: '236022',
    },
    swiftAddress: {
        name: 'B2B PROMOTION.LTD',
        localAddress: 'GENERAL-LEITENANTA OZEROVA, d. 17B,',
        cityAndCountry: 'KALININGRAD, RUSSIA',
    },
};

export const CUSTOMERS_MANAGERS_RESPONSE_MOCK: ApiGetManagersResponse = [
    {
        fullName: 'Симанова Наталья Александровна',
        mobilePhone: '79168853398',
        mail: '<EMAIL>',
        roleCode: 'SubjectRoleType_KMUL',
        role: 'Менеджер развития ЮЛ',
    },
    {
        fullName: 'Симанова Наталья Александровна',
        mobilePhone: '79168853398',
        mail: '<EMAIL>',
        roleCode: 'SubjectRoleType_KMUL_300',
        role: 'Клиентский менеджер ЮЛ',
    },
];

export const ROLES_RESPONSE_MOCK: ApiGetByUserIdResponse = [
    {
        customerId: 'UAAAR9',
        userId: 'XCC0VI',
        name: 'Руководитель',
        code: 'MANAGER',
    },
    {
        customerId: 'UAABQ7',
        userId: 'XCC0VI',
        name: 'Руководитель',
        code: 'MANAGER',
    },
    {
        customerId: 'UAAFXJ',
        userId: 'XCC0VI',
        name: 'Руководитель',
        code: 'MANAGER',
    },
];

export const ACCOUNTS_GET_BY_CUSTOMER_ID_RESPONSE_MOCK: ApiGetByCustomerIdResponse = [
    {
        mainInfo: {
            accountType: 'Test Account Type',
        },
        currency: CURRENCY_RUR,
        specConditions: [
            {
                code: 'Test Code',
                description: 'Test Description',
                value: true,
            },
        ],
        clientInfo: {
            clientName: 'Test Client Name',
            isOwner: true,
            ownerName: 'Test Owner Name',
            ownerPatronymic: 'Test Owner Patronymic',
            ownerSurname: 'Test Owner Surname',
        },
        balance: {
            balance: 100,
            total: 1000,
            holds: 10000,
        },
    },
];

export const MKS_PROXIES_RESPONSE_MOCK: ApiInternalGetByUserIdResponse = [
    {
        proxyType: '',
        customerId: ECustomersIdsMock.ALFA_LEASING,
        userId: '',
    },
    {
        proxyType: '',
        customerId: ECustomersIdsMock.EMPTY,
        userId: '',
    },
    {
        proxyType: '',
        customerId: ECustomersIdsMock.GOOGLE,
        userId: '',
    },
    {
        proxyType: '',
        customerId: ECustomersIdsMock.ACE_MILK,
        userId: '',
    },
];

export const INTERNAL_GET_VALID_CUSTOMER_IDS_RESPONSE_MOCK = {
    validCustomerIds: [
        ECustomersIdsMock.ALFA_LEASING,
        ECustomersIdsMock.EMPTY,
        ECustomersIdsMock.GOOGLE,
        ECustomersIdsMock.ACE_MILK,
    ],
};

export const CUSTOMERS_MOCK: CustomerShortData = [
    {
        eqId: ECustomersIdsMock.EMPTY,
        name: {
            fullNameWithOpf: ECompanyNamesMock.EMPTY,
            shortNameWithOpf: ECompanyNamesMock.EMPTY,
            fullName: ECompanyNamesMock.EMPTY,
        },
    },
    {
        eqId: ECustomersIdsMock.GOOGLE,
        name: {
            fullNameWithOpf: ECompanyNamesMock.GOOGLE,
            shortNameWithOpf: ECompanyNamesMock.GOOGLE,
            fullName: ECompanyNamesMock.GOOGLE,
        },
    },
    {
        eqId: ECustomersIdsMock.ALFA_LEASING,
        name: {
            fullNameWithOpf: ECompanyNamesMock.ALFA_LEASING,
            shortNameWithOpf: ECompanyNamesMock.ALFA_LEASING,
            fullName: ECompanyNamesMock.ALFA_LEASING,
        },
    },
    {
        eqId: ECustomersIdsMock.ACE_MILK,
        name: {
            fullNameWithOpf: ECompanyNamesMock.ACE_MILK,
            shortNameWithOpf: ECompanyNamesMock.ACE_MILK,
            fullName: ECompanyNamesMock.ACE_MILK,
        },
    },
];

export const CREDIT_PRODUCTS_MAIN_MENU_RESPONSE_MOCK: ApiGetCreditProductsMainMenuResponse = {
    creditProducts: [
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.BUSINESS_CREDIT_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.BUSINESS_CREDIT_DIFFERENT_DATES_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.CREDIT_LINE_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.OVERDRAFT_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.OVERDRAFT_WITH_TRANCHE_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.GUARANTY_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.GUARANTY_LINE_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.SOPUK_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.BUSINESS_CREDIT2_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.BUSINESS_CREDIT3_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.BUSINESS_CREDIT_DIFFERENT_DATES2_MAIN_MENU_MOCK,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.CREDIT_LINE_MAIN_MENU_MOCK2,
        CREDIT_PRODUCTS_MAIN_MENU_MOCK.CREDIT_LINE_MAIN_MENU_MOCK3,
    ],
    pagination: { pageNumber: 1, pageSize: 10, totalElements: 13, totalPages: 2 },
};

export const CREDIT_PRODUCTS_RESPONSE_MOCK: ApiGetCreditProductsResponse = [
    CREDIT_PRODUCTS_MOCK.BUSINESS_CREDIT_MOCK,
    CREDIT_PRODUCTS_MOCK.BUSINESS_CREDIT_DIFFERENT_DATES_MOCK,
    CREDIT_PRODUCTS_MOCK.CREDIT_LINE_MOCK,
    CREDIT_PRODUCTS_MOCK.OVERDRAFT_MOCK,
    CREDIT_PRODUCTS_MOCK.OVERDRAFT_TRANCHE_MOCK,
    CREDIT_PRODUCTS_MOCK.GUARANTY_MOCK,
    CREDIT_PRODUCTS_MOCK.GUARANTY_LINE_MOCK,
    CREDIT_PRODUCTS_MOCK.SOPUK_MOCK,
    CREDIT_PRODUCTS_MOCK.CREDIT_CARD_MOCK,
];

export const CREDIT_PRODUCTS_SUSPENSIVE_CONDITIONS_RESPONSE_MOCK: ApiGetSuspensiveConditionsResponse =
    {
        suspensiveConditions: [
            {
                suspensiveConditionId: 120885,
                monitoringOperationId: 664648,
                dealId: 200196091,
                type: SuspensiveConditionType.Ou4,
                description: 'Оформить поручительство с ООО "УРАЛ ЛОГИСТИКА"',
                planDate: 1724630400,
                expirationDays: 10,
                status: MonitoringOperationStatus.Violated,
            },
        ],
        pagination: { pageNumber: 1, pageSize: 10, totalElements: 1, totalPages: 1 },
    };

export const PERMISSIONS_DECISION_MAKER_CHECK_RESPONSE_MOCK: RunDecisionMakerCheckResponse = {
    resultCode: ResultCode.Ok,
    successful: true,
};

export const EARLY_REPAYMENT_PROCESSING_TYPE_MOCK: ApiGetEarlyRepaymentProcessingTypeResponse =
    earlyRepaymentProcessingTypes.AUTO;

export const GLOBAL_FEATURES_NAMES_MOCK = [];

export const PERMISSIONS_CHECK_RESULTS_MOCK = [
    {
        checkName: 'RoleModelRightsCheck',
        resultCode: 'OK',
        successful: true,
        details: null,
    },
    {
        checkName: 'ComplianceBlockingsCheck',
        resultCode: 'OK',
        successful: true,
        details: null,
    },
];

export const PERMISSIONS_OTHER_CHECKS_RESULTS_MOCK = [
    {
        checkName: 'DBOProxyCheck',
        resultCode: 'OK',
        successful: true,
    },
    {
        checkName: 'ALBOBlockingsCheck',
        resultCode: 'OK',
        successful: true,
    },
];

export const CORE_CREDIT_PRODUCTS_LIMITS_RESPONSE_MOCK: ApiGetClientLimitResponse = {
    clientId: 27018,
    limit: {
        id: 9478351,
        decisionCommittee: DECISION_COMMITTEE_UTK,
        temporaryBlocked: false,
        decisionDate: 1719954000,
        amount: {
            amount: 31931600000000,
            currency: CURRENCY_RUR,
        },
        restReserveAmount: {
            amount: *************,
            currency: CURRENCY_RUR,
        },
        suspensiveConditions: [
            {
                id: 353881,
                type: 'OUType_Turn',
                turnBase: 'TurnTypeLimit_AvgSum',
                restrictionKind: 'CompareType_More',
                restrictionRate: 50.0,
                dayPeriodCode: 'DayPeriod_month',
            },
            {
                id: 353882,
                type: 'OUType_Turn_WSRM',
                turnBase: 'TurnTypeLimit_Over',
                restrictionKind: 'CompareType_NotLess',
                restrictionRate: 35.0,
                dayPeriodCode: 'DayPeriod_quarter',
            },
            {
                id: 353883,
                type: 'OUType_ND_1',
                turnBase: 'TurnTypeLimit_AvgSum_Blank',
                restrictionKind: 'CompareType_NotMore',
                restrictionRate: 2.1,
                dayPeriodCode: 'DayPeriod_month',
            },
            {
                id: 353884,
                type: 'OUType_OU27',
                turnBase: 'TurnTypeLimit_PortfolioPart',
                restrictionKind: 'CompareType_NotEqually',
                restrictionRate: 3.4,
                dayPeriodCode: 'DayPeriod_quarter',
            },
        ],
        sublimits: [
            {
                id: 9479042,
                type: 'OUType_Turn',
                kind: 'Гарантии',
                products: ['Кредитные линии', 'Кредиты'],
                amount: {
                    amount: 66750000032,
                    currency: CURRENCY_RUR,
                },
                restReserveAmount: {
                    amount: 66750000054,
                    currency: CURRENCY_RUR,
                },
                period: 100,
                compareKindCode: 'TEST',
            },
            {
                id: 94790465,
                type: 'OUType_OU27',
                kind: 'Оборотные средства',
                products: ['Кредитные линии', 'Кредиты'],
                amount: {
                    amount: 6675000035,
                    currency: CURRENCY_RUR,
                },
                restReserveAmount: {
                    amount: 6675000075,
                    currency: CURRENCY_RUR,
                },
                period: 100,
                compareKindCode: 'TEST',
            },
            {
                id: 63773416,
                type: 'OUType_OU27',
                kind: 'Инвестиции',
                products: ['Кредитные линии', 'Овердрафты'],
                amount: {
                    amount: 7012000023,
                    currency: CURRENCY_RUR,
                },
                restReserveAmount: {
                    amount: ***********,
                    currency: CURRENCY_RUR,
                },
                period: 100,
                compareKindCode: 'TEST',
            },
            {
                id: 33233519,
                type: 'OUType_ND_1',
                kind: 'Факторинг',
                products: ['Банковские гарантии', 'Овердрафты'],
                amount: {
                    amount: 7012000023,
                    currency: CURRENCY_RUR,
                },
                restReserveAmount: {
                    amount: ***********,
                    currency: CURRENCY_RUR,
                },
                period: 100,
                compareKindCode: 'TEST',
            },
        ],
    },
    availableActions: ['newTranche', 'newWarranty'],
};

export const WIDGET_STATUS_DEAL_MOCK: ApiGetWidgetStatusResponse = {
    request: {
        id: 'id-123',
        type: 'DEAL',
        clientStatus: 'ACCEPT_BY_BANK',
    },
    action: 'CONTACT_MANAGER',
};

export const WIDGET_STATUS_LIMIT_MOCK: ApiGetWidgetStatusResponse = {
    request: {
        id: 'id-123',
        type: 'LIMIT',
        clientStatus: 'CONTACT_BANK',
    },
    action: 'CREATE_NEW_LIMIT_REQUEST',
};

export const STATEMENT_REQUESTS_MOCK: GetStatementRequestsResponse = {
    statementRequests: [
        {
            id: '67e68d96511fe55651abf8ec',
            productType: ProductType.CreditLine,
            docNumber: '07XW2L',
            subsetProductsInfo: {
                numbers: ['07XW2T005', '07XW2T006'],
            },
            period: {
                fromDate: '2025-03-24',
                toDate: '2025-03-27',
            },
            status: StatementStatus.Success,
            isViewed: true,
            createDate: **********,
        },
        {
            id: '67e68d8e511fe55651abf8eb',
            productType: ProductType.CreditLine,
            docNumber: '07XW2L',
            subsetProductsInfo: {
                numbers: ['07XW2T006'],
            },
            period: {
                fromDate: '2025-03-24',
                toDate: '2025-03-27',
            },
            status: StatementStatus.Success,
            isViewed: false,
            createDate: 1743162766,
        },
        {
            id: '67e68cb3511fe55651abf8ea',
            productType: ProductType.CreditLine,
            docNumber: '07XW2L',
            subsetProductsInfo: {
                numbers: [
                    '07XW2T001',
                    '07XW2T002',
                    '07XW2T003',
                    '07XW2T004',
                    '07XW2T005',
                    '07XW2T006',
                ],
            },
            period: {
                fromDate: '2025-03-24',
                toDate: '2025-03-27',
            },
            status: StatementStatus.Success,
            isViewed: false,
            createDate: 1743162547,
        },
        {
            id: '67e68c92511fe55651abf8e4',
            productType: ProductType.CreditLine,
            docNumber: '07XW2L',
            subsetProductsInfo: {
                numbers: [
                    '07XW2T001',
                    '07XW2T002',
                    '07XW2T003',
                    '07XW2T004',
                    '07XW2T005',
                    '07XW2T006',
                ],
            },
            period: {
                fromDate: '2025-03-24',
                toDate: '2025-03-27',
            },
            status: StatementStatus.InProgress,
            isViewed: true,
            createDate: 1743162514,
        },
        {
            id: '67e68c90511fe55651abf8e3',
            productType: ProductType.CreditLine,
            docNumber: '07XW2L',
            subsetProductsInfo: {
                numbers: [
                    '07XW2T001',
                    '07XW2T002',
                    '07XW2T003',
                    '07XW2T004',
                    '07XW2T005',
                    '07XW2T006',
                ],
            },
            period: {
                fromDate: '2025-03-24',
                toDate: '2025-03-27',
            },
            status: StatementStatus.Failed,
            isViewed: true,
            createDate: 1743162512,
        },
    ],
    pagination: {
        pageNumber: 1,
        pageSize: 10,
        totalElements: 5,
        totalPages: 1,
    },
    newStatementsCount: 2,
};
