import { type apiConfig } from 'corp-early-repayment-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { EARLY_REPAYMENT_PROCESSING_TYPE_MOCK } from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';

export const mockConfigCorpEarlyRepayment: Partial<OpenApiRestService<typeof apiConfig>> = {
    getEarlyRepaymentProcessingType: createMockFetchMethod({
        mockData: EARLY_REPAYMENT_PROCESSING_TYPE_MOCK,
    }),
};
