import { type apiConfig } from 'corp-accounts-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ACCOUNTS_GET_BY_CUSTOMER_ID_RESPONSE_MOCK } from '#/src/server/mocks/data/data';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigMksAccounts: Partial<OpenApiRestService<typeof apiConfig>> = {
    getByCustomerId: createMockFetchMethod({ mockData: ACCOUNTS_GET_BY_CUSTOMER_ID_RESPONSE_MOCK }),
    getBalanceByCustomerId: createMockFetchMethod({ mockData: [] }),
    getByAccountNumbers: createMockFetchMethod({ mockData: [] }),
};
