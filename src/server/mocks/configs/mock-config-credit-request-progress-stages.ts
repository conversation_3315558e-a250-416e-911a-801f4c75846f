import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';
import { type apiConfig } from 'crmmb-status-model-core-api-typescript-services';

import { CREDIT_REQUEST_PROGRESS_STAGES_MOCK } from '#/src/server/mocks/data/data';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigStatusModel: Partial<OpenApiRestService<typeof apiConfig>> = {
    getApplicationProgressStages: createMockFetchMethod({
        mockData: CREDIT_REQUEST_PROGRESS_STAGES_MOCK,
    }),
};
