import { type apiConfig } from 'corp-roles-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ROLES_RESPONSE_MOCK } from '#/src/server/mocks/data/data';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigMksRoles: Partial<OpenApiRestService<typeof apiConfig>> = {
    getByUserId: createMockFetchMethod({ mockData: ROLES_RESPONSE_MOCK }),
};
