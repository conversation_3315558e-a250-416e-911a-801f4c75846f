import {
    type apiConfig,
    type ApiGetClientLimitResponse,
} from 'corp-core-credit-products-api-typescript-services';
import { type GetCreditProductsRequest } from 'corp-core-credit-products-api-typescript-services/dist/getCreditProductsRequest';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ECustomersIdsMock } from '#/src/server/mocks/data/constants';
import {
    CORE_CREDIT_PRODUCTS_LIMITS_RESPONSE_MOCK,
    CREDIT_PRODUCTS_RESPONSE_MOCK,
} from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';
import { convertKeysToLowerCase as headersToLowerCase } from '#/src/utils/formatters';

export const mockConfigCoreCreditProductsRestV2: Partial<OpenApiRestService<typeof apiConfig>> = {
    getClientLimit: createMockFetchMethod({
        mockData: CORE_CREDIT_PRODUCTS_LIMITS_RESPONSE_MOCK,
        mockCallbackServiceSimulator: (response, requestData) => {
            const headers = headersToLowerCase(requestData?.headers);

            const customerId = headers['a-customerid'];

            if (customerId === ECustomersIdsMock.GOOGLE) {
                return null as unknown as ApiGetClientLimitResponse;
            }
            if (customerId === ECustomersIdsMock.ACE_MILK) {
                return {
                    ...CORE_CREDIT_PRODUCTS_LIMITS_RESPONSE_MOCK,
                    limit: {
                        ...CORE_CREDIT_PRODUCTS_LIMITS_RESPONSE_MOCK.limit,
                        decisionCommittee: 'Committee_Wh20',
                    },
                };
            }

            return response;
        },
    }),
    getCreditProducts: createMockFetchMethod({
        mockData: CREDIT_PRODUCTS_RESPONSE_MOCK,
        mockCallbackServiceSimulator: (response, requestData) => {
            const body = requestData?.body as GetCreditProductsRequest;
            const docNumber = body.filter?.docNumber;

            if (docNumber) {
                response = response.filter((product) => product.docNumber === docNumber);
            }

            return response;
        },
    }),
};
