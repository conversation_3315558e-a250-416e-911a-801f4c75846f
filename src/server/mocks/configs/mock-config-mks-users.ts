import { type apiConfig } from 'corp-users-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { USERS_RESPONSE_MOCK } from '#/src/server/mocks/data/data';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigMksUsers: Partial<OpenApiRestService<typeof apiConfig>> = {
    getByIds: createMockFetchMethod({ mockData: USERS_RESPONSE_MOCK }),
};
