import { type apiConfig } from 'corp-global-features-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ECustomersIdsMock } from '#/src/server/mocks/data/constants';
import { GLOBAL_FEATURES_NAMES_MOCK } from '#/src/server/mocks/data/data';
import { createSubjectFeatureResponse } from '#/src/server/mocks/utils/utils';

import { createMockFetchMethod } from '../utils/mock-request';

const companyIds = Object.values(ECustomersIdsMock);

export const mockConfigMksGlobalFeatures: Partial<OpenApiRestService<typeof apiConfig>> = {
    getSubjectFeatureV2: createMockFetchMethod({
        mockData: createSubjectFeatureResponse(companyIds, GLOBAL_FEATURES_NAMES_MOCK),
    }),
};
