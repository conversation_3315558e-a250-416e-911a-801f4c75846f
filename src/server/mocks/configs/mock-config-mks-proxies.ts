import { type apiConfig } from 'corp-proxies-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { MKS_PROXIES_RESPONSE_MOCK } from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';

export const mockConfigMksProxies: Partial<OpenApiRestService<typeof apiConfig>> = {
    internalGetByUserId: createMockFetchMethod({ mockData: MKS_PROXIES_RESPONSE_MOCK }),
};
