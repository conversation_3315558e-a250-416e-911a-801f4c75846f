import {
    type apiConfig,
    type ApiGetCreditRequestsResponse,
} from 'corp-credit-request-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { CREDIT_RESPONSE_MOCK } from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';

export const mockConfigCorpCreditRequest: Partial<OpenApiRestService<typeof apiConfig>> = {
    getCreditRequests: createMockFetchMethod({
        mockData: CREDIT_RESPONSE_MOCK as unknown as ApiGetCreditRequestsResponse,
    }),
};
