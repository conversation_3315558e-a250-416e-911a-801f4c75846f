import {
    type apiConfig,
    type ApiRunMandatoryChecksV2HeadersParams,
    type ApiRunMandatoryChecksV2RequestBody,
} from 'corp-role-model-mks-permissions-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { PERMISSIONS_DECISION_MAKER_CHECK_RESPONSE_MOCK } from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';
import { createRunMandatoryChecksV2Response } from '#/src/server/mocks/utils/utils';

export const mockConfigMksPermissions: Partial<OpenApiRestService<typeof apiConfig>> = {
    runMandatoryChecksV2: createMockFetchMethod({
        mockData: {},
        mockCallbackServiceSimulator: (_, requestData) =>
            createRunMandatoryChecksV2Response(
                requestData?.headers as ApiRunMandatoryChecksV2HeadersParams,
                requestData?.body as ApiRunMandatoryChecksV2RequestBody,
            ),
    }),
    runDecisionMakerCheck: createMockFetchMethod({
        mockData: PERMISSIONS_DECISION_MAKER_CHECK_RESPONSE_MOCK,
    }),
};
