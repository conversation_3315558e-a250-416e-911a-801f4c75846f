import { type apiConfig } from 'corp-credit-document-circulation-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ALFA_CREDIT_RESPONSE_MOCK } from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';

export const mockConfigCorpCreditDocumentCirculation: Partial<
    OpenApiRestService<typeof apiConfig>
> = {
    getOrConnectAlfaCredit: createMockFetchMethod({ mockData: ALFA_CREDIT_RESPONSE_MOCK }),
};
