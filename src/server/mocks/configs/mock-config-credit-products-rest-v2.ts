import {
    type apiConfig,
    type ApiGetCreditProductsMainMenuRequestBody,
    type ApiGetCreditProductsMainMenuResponse,
} from 'corp-credit-products-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import {
    CREDIT_PRODUCTS_MAIN_MENU_RESPONSE_MOCK,
    CREDIT_PRODUCTS_SUSPENSIVE_CONDITIONS_RESPONSE_MOCK,
} from '#/src/server/mocks/data/data';
import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';
import { filterProductsMainMenu } from '#/src/server/mocks/utils/utils';

export const mockConfigCreditProductsRestV2: Partial<OpenApiRestService<typeof apiConfig>> = {
    getCreditProductsMainMenu: createMockFetchMethod({
        mockData: CREDIT_PRODUCTS_MAIN_MENU_RESPONSE_MOCK,
        mockCallbackServiceSimulator: (
            response: ApiGetCreditProductsMainMenuResponse,
            requestData,
        ) => {
            const filteredProducts = filterProductsMainMenu(
                response.creditProducts || [],
                requestData?.body as ApiGetCreditProductsMainMenuRequestBody,
            );
            const { page } = requestData?.body as ApiGetCreditProductsMainMenuRequestBody;

            return {
                creditProducts: filteredProducts,
                pagination: {
                    ...response.pagination,
                    pageNumber: page.pageNumber,
                    pageSize: 10,
                },
            };
        },
    }),
    getSuspensiveConditions: createMockFetchMethod({
        mockData: CREDIT_PRODUCTS_SUSPENSIVE_CONDITIONS_RESPONSE_MOCK,
    }),
    getCountCreditProductsByType: createMockFetchMethod({
        mockData: {
            productCounts: [
                { productType: 2, count: 5 },
                { productType: 5, count: 3 },
            ],
        },
    }),
};
