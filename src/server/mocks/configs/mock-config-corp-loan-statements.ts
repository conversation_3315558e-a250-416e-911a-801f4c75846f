import { type apiConfig } from 'corp-loan-statements-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { createMockFetchMethod } from '#/src/server/mocks/utils/mock-request';

import { STATEMENT_REQUESTS_MOCK } from '../data/data';

export const mockConfigCorpLoanStatements: Partial<OpenApiRestService<typeof apiConfig>> = {
    getStatementRequests: createMockFetchMethod({
        mockData: STATEMENT_REQUESTS_MOCK,
    }),
};
