import { type apiConfig } from 'corp-customers-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { OrganizationCategoryCodes } from '#/src/constants/organization-category-codes';
import { ECustomersIdsMock } from '#/src/server/mocks/data/constants';
import {
    CUSTOMERS_ADDRESSES_RESPONSE_MOCK,
    CUSTOMERS_MANAGERS_RESPONSE_MOCK,
    CUSTOMERS_MOCK,
    INTERNAL_GET_VALID_CUSTOMER_IDS_RESPONSE_MOCK,
} from '#/src/server/mocks/data/data';
import { createCustomersResponse } from '#/src/server/mocks/utils/utils';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigMksCustomers: Partial<OpenApiRestService<typeof apiConfig>> = {
    getByIds: createMockFetchMethod({
        mockData: createCustomersResponse(CUSTOMERS_MOCK),
    }),
    getAddressesV2: createMockFetchMethod({ mockData: [CUSTOMERS_ADDRESSES_RESPONSE_MOCK] }),
    getCategory: createMockFetchMethod({
        mockData: { sksCode: '' },
        mockCallbackServiceSimulator: (response, requestData) => {
            const customerId = requestData?.urlParams?.customerId;

            if (customerId === ECustomersIdsMock.ALFA_LEASING) {
                response.sksCode = OrganizationCategoryCodes.ClientCategorySKS8;
            }
            if (customerId === ECustomersIdsMock.GOOGLE) {
                response.sksCode = OrganizationCategoryCodes.ClientCategorySKS2;
            }
            if (customerId === ECustomersIdsMock.ACE_MILK) {
                response.sksCode = OrganizationCategoryCodes.ClientCategorySKS5;
            }
            if (customerId === ECustomersIdsMock.EMPTY) {
                response.sksCode = OrganizationCategoryCodes.ClientCategorySKS1;
            }

            return response;
        },
    }),
    getManagers: createMockFetchMethod({
        mockData: CUSTOMERS_MANAGERS_RESPONSE_MOCK,
    }),
    internalGetValidCustomerIds: createMockFetchMethod({
        mockData: INTERNAL_GET_VALID_CUSTOMER_IDS_RESPONSE_MOCK,
    }),
};
