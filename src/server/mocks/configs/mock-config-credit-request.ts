import { type apiConfig } from 'corp-new-credit-api-typescript-services';
import { type OpenApiRestService } from 'corporate-services/lib/openapi/open-api-types';

import { ECustomersIdsMock } from '#/src/server/mocks/data/constants';
import { WIDGET_STATUS_DEAL_MOCK, WIDGET_STATUS_LIMIT_MOCK } from '#/src/server/mocks/data/data';
import { convertKeysToLowerCase as headersToLowerCase } from '#/src/utils/formatters';

import { createMockFetchMethod } from '../utils/mock-request';

export const mockConfigCreditRequest: Partial<OpenApiRestService<typeof apiConfig>> = {
    getWidgetStatus: createMockFetchMethod({
        mockData: {},
        mockCallbackServiceSimulator: (response, requestData) => {
            const headers = headersToLowerCase(requestData?.headers);

            const customerId = headers['a-customerid'];

            if (customerId === ECustomersIdsMock.ALFA_LEASING) return WIDGET_STATUS_DEAL_MOCK;
            if (customerId === ECustomersIdsMock.GOOGLE) return WIDGET_STATUS_LIMIT_MOCK;

            return response;
        },
    }),
};
