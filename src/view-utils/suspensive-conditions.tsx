import React from 'react';
import { MonitoringOperationStatus } from 'corp-credit-products-api-typescript-services';

import { Status } from '@alfalab/core-components/status';
import { pluralize } from '@alfalab/utils';

import { ECreditProducts, ECreditProductsNames } from '../constants/credit-products';
import { PATHS } from '../constants/routing';
import { ETrancheTypes } from '../sagas/workers/get-tranche-credit-products-worker';

export const getSuspensiveConditionStatus = ({
    status,
    expirationDays,
    isTablet,
}: {
    status: MonitoringOperationStatus;
    expirationDays: number;
    isTablet: boolean;
}) => {
    switch (status) {
        case MonitoringOperationStatus.Violated:
            return (
                <Status
                    shape='rounded'
                    view={isTablet ? 'soft' : 'contrast'}
                    uppercase={isTablet}
                    color='red'
                >
                    Нарушено
                </Status>
            );
        case MonitoringOperationStatus.Expiring:
            if (expirationDays === 0) {
                return (
                    <Status
                        shape='rounded'
                        view={isTablet ? 'soft' : 'contrast'}
                        uppercase={isTablet}
                        color='orange'
                    >
                        Истекает сегодня
                    </Status>
                );
            }

            return (
                <Status
                    shape='rounded'
                    view={isTablet ? 'soft' : 'contrast'}
                    uppercase={isTablet}
                    color='orange'
                >
                    {`Истекает через ${expirationDays} ${pluralize(
                        expirationDays,
                        'день',
                        'дня',
                        'дней',
                    )}`}
                </Status>
            );

        case MonitoringOperationStatus.Executed:
            return (
                <Status
                    shape='rounded'
                    view={isTablet ? 'soft' : 'contrast'}
                    uppercase={isTablet}
                    color='green'
                >
                    Выполнено
                </Status>
            );
        case MonitoringOperationStatus.Waiting:
            return (
                <Status
                    shape='rounded'
                    view={isTablet ? 'soft' : 'contrast'}
                    uppercase={isTablet}
                    color='grey'
                >
                    Ожидает выполнения
                </Status>
            );
        default:
            return null;
    }
};

export const getLabelByProductType = (productType: ETrancheTypes | ECreditProducts) => {
    switch (productType) {
        case ECreditProducts.BUSINESS_CREDIT:
            return ECreditProductsNames.BUSINESS_CREDIT;
        case ECreditProducts.CREDIT_LINE:
            return ECreditProductsNames.CREDIT_LINE;
        case ECreditProducts.SOPUK:
            return ECreditProductsNames.SOPUK;
        case ETrancheTypes.deal:
            return 'Кредит';
        case ETrancheTypes.tranche:
            return 'Транш';
        default:
            return '';
    }
};

export const getPathByProductType = ({
    productType,
    trancheType,
}: {
    productType: ECreditProducts;
    trancheType?: ETrancheTypes;
}) => {
    switch (productType) {
        case ECreditProducts.CREDIT_LINE:
            if (trancheType === ETrancheTypes.tranche) {
                return PATHS.CREDIT_LINE_TRANCHE;
            }

            return PATHS.CREDIT_LINE;
        case ECreditProducts.CREDIT_CARD:
        case ECreditProducts.BUSINESS_CREDIT:
            return PATHS.CREDIT;
        case ECreditProducts.GUARANTY:
            return PATHS.GUARANTY;
        case ECreditProducts.GUARANTY_LINE:
            if (trancheType === ETrancheTypes.tranche) {
                return PATHS.GUARANTY_LINE_TRANCHE;
            }

            return PATHS.GUARANTY_LINE;
        case ECreditProducts.SOPUK:
            if (trancheType === ETrancheTypes.deal) {
                return PATHS.SOPUK_LINE_DEAL;
            }

            return PATHS.SOPUK_LINE;
        case ECreditProducts.OVERDRAFT:
        case ECreditProducts.EXPRESS_OVERDRAFT_OFFER:
            if (trancheType === ETrancheTypes.tranche) {
                return PATHS.OVERDRAFT_TRANCHE;
            }

            return PATHS.OVERDRAFT;
        default:
            return null;
    }
};
