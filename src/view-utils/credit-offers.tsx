import React from 'react';

import { formatAmount } from '@alfalab/utils';
import { pluralize } from 'arui-private/lib/formatters';

import { DigitalLandingsLink } from '#/src/components/digital-landings-link';
import { ECreditOffers } from '#/src/constants/credit-offers';
import { EAdditionalDigitalSalesLandingsKeys } from '#/src/constants/credit-products';
import { EOL, NBSP } from '#/src/constants/unicode-symbols';
import {
    type TCreditOfferFeatures,
    type TFeatureColumn,
    type TGetCreditOffersFeaturesParams,
} from '#/src/types/credit-offers';
import { convertAmountToText } from '#/src/utils/amount';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';
import { formatMonthsToString } from '#/src/utils/formatters';

export function getOfferPaneTitle(
    titlesVariants: TMappedCreditOffer['titlesVariants'],
    isCreditProductsExist: boolean,
    isDesktop: boolean,
    offerType: ECreditOffers,
) {
    const BASE_OFFER_PANE_TITLE =
        !isCreditProductsExist && isDesktop ? titlesVariants.long : titlesVariants.short;

    return {
        [ECreditOffers.BUSINESS_CREDIT]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.ALFA_BUSINESS_CREDIT_CARD]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_CREDIT_CARD]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE]: isCreditProductsExist
            ? titlesVariants.short
            : titlesVariants.long,
        [ECreditOffers.GUARANTY]:
            isCreditProductsExist && isDesktop ? titlesVariants.short : titlesVariants.long,
        [ECreditOffers.OVERDRAFT]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.EXPRESS_OVERDRAFT_OFFER]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_MORTGAGE]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_FACTORING]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.REFILLABLE_CREDIT_LINE]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.LEASING]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.CREDIT_LIMIT]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.FACTORING_X5]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.CREDIT_DOCUMENT_CIRCULATION]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_CREDIT_REFINANCING]: BASE_OFFER_PANE_TITLE,
        [ECreditOffers.BUSINESS_AUTO_CREDIT]: BASE_OFFER_PANE_TITLE,
    }[offerType];
}

// NOTE: Во втором элементе TFeatureColumn мы отслеживаем наличие переноса строки. Он будет виден на планшете.
// Нельзя допускать случайных переносов строки внутри Template literals (апострофы)

// eslint-disable-next-line complexity
export function getCreditOffersFeatures({
    offerType,
    maximumAmount,
    yearRatePct = 0,
    finalRate,
    maximumTerm,
    oneTimeComissionPct = 0,
    landingVersion,
    maximumLimit,
    creditCardVariant,
    preApproved,
}: TGetCreditOffersFeaturesParams): TCreditOfferFeatures {
    let creditOffersFeatures: TFeatureColumn[] = [];

    const sumValue = `${preApproved ? '' : 'до '}${convertAmountToText(maximumAmount)}`;

    const rateWithFinal: TFeatureColumn = [
        'ставка (% годовых)',
        finalRate
            ? `${finalRate.toLocaleString('ru-RU')}%`
            : `от ${yearRatePct.toLocaleString('ru-RU')}%`,
    ];

    switch (offerType) {
        case ECreditOffers.BUSINESS_CREDIT_WITH_STATE_SUPPORT_MMB:
            creditOffersFeatures = [
                ['сумма', `до${NBSP}${convertAmountToText(maximumAmount)}`],
                ['ставка', `от${NBSP}${yearRatePct.toLocaleString('ru-RU')}%`],
                [
                    'срок кредита',
                    `до${NBSP}${formatMonthsToString(Number(maximumTerm), {
                        isGenitive: true,
                    })}`,
                ],
                ['обеспечение', `размер поручительства до${NBSP}50% от${NBSP}суммы кредита`],
            ];

            break;
        case ECreditOffers.BUSINESS_AUTO_CREDIT:
        case ECreditOffers.BUSINESS_CREDIT_REFINANCING:
            creditOffersFeatures = [
                ['сумма', `до${NBSP}${convertAmountToText(maximumAmount)}`],
                ['ставка', `от${NBSP}${yearRatePct.toLocaleString('ru-RU')}%`],
                [
                    'срок кредита',
                    `до${NBSP}${formatMonthsToString(Number(maximumTerm), {
                        isGenitive: true,
                    })}`,
                ],
            ];
            break;
        case ECreditOffers.LEASING:
            creditOffersFeatures = [['сумма', sumValue], rateWithFinal];
            break;
        case ECreditOffers.CREDIT_LIMIT:
            creditOffersFeatures = [
                ['Одобрено', convertAmountToText(maximumAmount)],
                ['Средняя ставка', `${yearRatePct.toLocaleString('ru-RU')}%`],
                ['Господдержка', 'Возможна'],
                ['Обеспечение', 'Без залога'],
            ];
            break;
        case ECreditOffers.BUSINESS_MORTGAGE:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                [
                    'срок кредита',
                    `до ${formatMonthsToString(Number(maximumTerm), {
                        isGenitive: true,
                    })}`,
                ],
                ['обеспечение', `Недвижимость${EOL}в залог`],
            ];
            break;
        case ECreditOffers.BUSINESS_FACTORING:
            creditOffersFeatures = [['возможность увеличения лимита', `Без${NBSP}ограничений`]];
            break;
        case ECreditOffers.ALFA_BUSINESS_CREDIT_CARD:
        case ECreditOffers.BUSINESS_CREDIT_CARD:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                ['срок кредита', formatMonthsToString(Number(maximumTerm))],
                [
                    'беспроцентный период',
                    `${creditCardVariant?.gracePeriodDays || 0}${NBSP}${pluralize(
                        creditCardVariant?.gracePeriodDays || 0,
                        ['дня', 'дней', 'дней'],
                    )}`,
                ],
            ];
            break;
        case ECreditOffers.REFILLABLE_CREDIT_LINE:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                [
                    'срок',
                    'не ограничен',
                    <React.Fragment>
                        Договор на 18 месяцев,
                        <br />
                        продлевается автоматически
                        <br />
                        каждый год.{' '}
                        <DigitalLandingsLink
                            landingKey={
                                EAdditionalDigitalSalesLandingsKeys.RefillableCreditLineWithProlongation
                            }
                        >
                            Подробнее
                        </DigitalLandingsLink>
                    </React.Fragment>,
                ],
                ['обеспечение', 'Без залога'],
            ];
            break;
        case ECreditOffers.BUSINESS_CREDIT:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                ['срок кредита', formatMonthsToString(Number(maximumTerm))],
                ['обеспечение', 'Без залога'],
            ];
            break;
        case ECreditOffers.REFILLABLE_CREDIT_LINE_WITH_PLEDGE:
        case ECreditOffers.BUSINESS_CREDIT_WITH_PLEDGE:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                ['срок кредита', formatMonthsToString(Number(maximumTerm))],
                ['обеспечение', 'Нужен залог'],
            ];
            break;
        case ECreditOffers.EXPRESS_OVERDRAFT_OFFER:
        case ECreditOffers.OVERDRAFT:
            creditOffersFeatures = [
                ['сумма', sumValue],
                rateWithFinal,
                [
                    'срок',
                    'не ограничен',
                    <React.Fragment>
                        Договор на 15 месяцев,
                        <br />
                        продлевается автоматически
                        <br />
                        каждый год.{' '}
                        <DigitalLandingsLink
                            landingKey={
                                EAdditionalDigitalSalesLandingsKeys.OverdraftWithProlongation
                            }
                        >
                            Подробнее
                        </DigitalLandingsLink>
                    </React.Fragment>,
                ],
                ['обеспечение', 'Без залога'],
            ];
            break;
        case ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS:
            creditOffersFeatures = [
                ['лимит', `до${NBSP}300 млн${NBSP}₽`],
                ['сумма', `до${NBSP}300 млн${NBSP}₽`],
                ['срок гарантии', `до${NBSP}1${NBSP}года`],
            ];
            break;
        case ECreditOffers.FACTORING_X5:
            creditOffersFeatures = [['возможность увеличения лимита', 'Без ограничений']];
            break;
        case ECreditOffers.GUARANTY: {
            if (landingVersion === 2 && maximumLimit) {
                creditOffersFeatures = [
                    ['лимит', `до ${convertAmountToText(maximumLimit)}`],
                    ['сумма', sumValue],
                    ['срок кредита', formatMonthsToString(Number(maximumTerm))],
                    [
                        'комиссия',
                        `от${NBSP}${
                            formatAmount({
                                value: oneTimeComissionPct,
                                currency: 'RUR',
                                minority: 1,
                            }).formattedWithCurrency
                        }`,
                    ],
                ];
            } else {
                creditOffersFeatures = [
                    ['сумма', sumValue],
                    ['срок кредита', formatMonthsToString(Number(maximumTerm))],
                    [
                        'комиссия за выдачу',
                        `от${NBSP}${
                            formatAmount({
                                value: oneTimeComissionPct,
                                currency: 'RUR',
                                minority: 1,
                            }).formattedWithCurrency
                        }`,
                    ],
                ];
            }
            break;
        }
    }

    return creditOffersFeatures.map(([title, text, tooltipText]) => ({ title, text, tooltipText }));
}

export function getCreditOffersFeaturesRow(
    productInfo: TMappedCreditOffer,
    currentTime: Date,
): TCreditOfferFeatures {
    const {
        type,
        maximumTerm,
        yearRatePct,
        maximumAmount,
        expiryDate,
        landingVersion,
        maximumLimit,
        oneTimeComissionPct,
        creditCardVariant,
        campaignCode,
        preApproved,
        finalRate,
    } = productInfo;

    return getCreditOffersFeatures({
        offerType: type,
        maximumAmount,
        yearRatePct,
        maximumTerm,
        expiryDate,
        oneTimeComissionPct,
        landingVersion,
        maximumLimit,
        creditCardVariant,
        campaignCode,
        currentTime,
        preApproved,
        finalRate,
    });
}
