import {
    ETabs,
    ETabsTexts,
    type TGetOverdraftTabsDataArgs,
    type TOverdraftPageTabsInfo,
    type TOverdraftTabsData,
} from '../types/overdraft';

const OVERDRAFT_PAGE_TABS: TOverdraftPageTabsInfo = {
    [ETabs.conditions]: {
        text: ETabsTexts.conditions,
        value: ETabs.conditions,
    },
    [ETabs.tranches]: {
        text: ETabsTexts.tranches,
        value: ETabs.tranches,
    },
    [ETabs.settings]: {
        text: ETabsTexts.settings,
        value: ETabs.settings,
    },
};

export function getOverdraftTabsData({
    withTranches,
    hasClientLimit,
    canChangeRepaymentType,
}: TGetOverdraftTabsDataArgs): TOverdraftTabsData {
    return withTranches
        ? {
              currentTab: ETabs.tranches,
              tabsList: [
                  OVERDRAFT_PAGE_TABS[ETabs.tranches],
                  OVERDRAFT_PAGE_TABS[ETabs.conditions],
                  (!!hasClientLimit || canChangeRepaymentType) &&
                      OVERDRAFT_PAGE_TABS[ETabs.settings],
              ].filter(Boolean),
          }
        : {
              currentTab: ETabs.conditions,
              tabsList: [
                  OVERDRAFT_PAGE_TABS[ETabs.conditions],
                  (!!hasClientLimit || canChangeRepaymentType) &&
                      OVERDRAFT_PAGE_TABS[ETabs.settings],
              ].filter(Boolean),
          };
}
