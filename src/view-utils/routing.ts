import { CreditPageWrapper } from '#/src/containers/credit/credit-page-wrapper';
import { CreditLine } from '#/src/containers/credit-line/credit-line';
import { Guaranty } from '#/src/containers/guaranty/guaranty';
import { GuarantyLine } from '#/src/containers/guaranty-line/guaranty-line';
import { Overdraft } from '#/src/containers/overdraft/overdraft';

import { PATHS } from '../constants/routing';
import AttachDocuments from '../containers/attach-documents';
import CreditLineTranche from '../containers/credit-line-tranche';
import { CreditOffers } from '../containers/credit-offers/credit-offers';
import GuarantyLineTranche from '../containers/guaranty-line-tranche';
import { MainPage } from '../containers/main-page';
import { SopukLine } from '../containers/sopuk-line';
import { SopukLineDeal } from '../containers/sopuk-line-deal';
import { type TPagesAccordingToPaths } from '../types/routing';

const PAGES_ACCORDING_TO_PATHS: TPagesAccordingToPaths = {
    [PATHS.CREDIT]: CreditPageWrapper,
    [PATHS.CREDIT_LINE]: CreditLine,
    [PATHS.CREDIT_LINE_TRANCHE]: CreditLineTranche,
    [PATHS.GUARANTY]: Guaranty,
    [PATHS.GUARANTY_LINE]: GuarantyLine,
    [PATHS.GUARANTY_LINE_TRANCHE]: GuarantyLineTranche,
    [PATHS.OVERDRAFT]: Overdraft,
    [PATHS.ATTACH_DOCUMENTS]: AttachDocuments,
    [PATHS.SOPUK_LINE]: SopukLine,
    [PATHS.SOPUK_LINE_DEAL]: SopukLineDeal,
    [PATHS.CREDIT_OFFERS]: CreditOffers,
    [PATHS.MAIN_PAGE]: MainPage, // path вида '/' всегда должен идти последним
};

export default PAGES_ACCORDING_TO_PATHS;
