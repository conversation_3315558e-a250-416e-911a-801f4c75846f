import * as React from 'react';
import { CURRENCY } from 'corporate-services/lib/currency';
import { type Amount } from 'thrift-services/entities';

import { SuperEllipse } from '@alfalab/core-components/icon-view/super-ellipse';
import { Typography } from '@alfalab/core-components/typography';
import CorporateAmount from 'arui-private/corporate-amount';

import AmountPure from '../components/ui/amount-pure';

export const DEFAULT_AMOUNT: Amount = {
    amount: 0,
    currency: CURRENCY.RUR,
};

type TPaneAmount = {
    value?: Amount;
    view?: React.ComponentProps<typeof Typography.Text>['view'];
    dataTestId?: string;
} & React.ComponentProps<typeof Typography.Text>;

export const PaneIcon = ({
    Icon,
    className,
}: {
    Icon: React.FC<React.SVGProps<SVGSVGElement>>;
    className?: string;
}) => (
    <SuperEllipse backgroundColor='#f3f4f5' size={40}>
        <Icon className={className} />
    </SuperEllipse>
);

export const PaneAmount = ({
    value = DEFAULT_AMOUNT,
    view = 'primary-large',
    dataTestId,
    ...props
}: TPaneAmount) => (
    <Typography.Text view={view} {...props}>
        <CorporateAmount
            transparentMinor={false}
            dataTestId={dataTestId}
            view='withZeroMinorPart'
            amount={value}
        />
    </Typography.Text>
);

export const PanePopupAmount = ({
    text,
    value,
    className,
}: {
    text?: string;
    value?: Amount;
    className: string;
}) => (
    <div className={className}>
        {!!value && <AmountPure value={value} />}
        &nbsp;— {text}
    </div>
);
