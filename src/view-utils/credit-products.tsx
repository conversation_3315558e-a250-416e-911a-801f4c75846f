import React from 'react';
import isEqual from 'date-fns/isEqual';
import startOfDay from 'date-fns/startOfDay';
import { TPaymentStatus } from 'thrift-services/services/credit_products';

import { Status } from '@alfalab/core-components/status';

import {
    ECreditCardPaymentStatus,
    ECreditCardUniversalCode,
    ECreditProducts,
    ECreditProductsCodes,
} from '../constants/credit-products';
import { type MappedCredit, type MappedOverdraft } from '../utils/credit-products-mappers';
import { parseDateFromAny } from '../utils/date';

export const interestTooltipText = {
    [ECreditProducts.CREDIT_LINE]: (
        <React.Fragment>
            Сумма&nbsp;начисленных&nbsp;процентов на&nbsp;основной&nbsp;долг активных&nbsp;траншей
            на&nbsp;конец&nbsp;дня.
        </React.Fragment>
    ),
    [ECreditProducts.OVERDRAFT]: (
        <React.Fragment>
            Проценты&nbsp;начисляются&nbsp;ежедневно на&nbsp;сумму&nbsp;основного&nbsp;долга
            каждого&nbsp;транша по&nbsp;состоянию на&nbsp;конец&nbsp;дня.
        </React.Fragment>
    ),
};

export const getCreditProductStatus = ({
    productData,
    currentTime,
}: {
    productData: MappedOverdraft & MappedCredit;
    currentTime: Date;
}) => {
    const paymentStatus = productData?.requisites?.paymentStatus;
    const product = productData?.requisites?.product;
    const productCode = productData?.requisites?.productCode;
    const debtStatus = productData?.frontParams?.debtStatus;
    const minPayDebtTillDate = productData?.debts?.loan?.minPayDebtTillDate;

    if (
        !!product &&
        [
            ECreditProductsCodes.CREDIT_LINE,
            ECreditProductsCodes.BUSINESS_CREDIT,
            ECreditProductsCodes.OVERDRAFT,
        ].includes(product as ECreditProductsCodes)
    ) {
        const isCreditCard = productCode?.includes(ECreditCardUniversalCode.OVERNZUC);

        switch (isCreditCard ? debtStatus : paymentStatus) {
            case TPaymentStatus.CHECK_PAYMENT:
                return <Status color='grey'>ПРОВЕРЬТЕ ПЛАТЕЖИ</Status>;
            case TPaymentStatus.UPCOMING_PAYMENT:
                return <Status color='orange'>СКОРО ПЛАТЁЖ</Status>;
            case ECreditCardPaymentStatus.TWO_OR_MORE_MIN_DEBT_PAYMENTS_OVERDUE:
            case ECreditCardPaymentStatus.SECOND_MIN_DEBT_PAYMENT_OVERDUE:
            case TPaymentStatus.PAYMENT_OVERDUE:
                return <Status color='red'>ПРОСРОЧЕНО</Status>;
            case ECreditCardPaymentStatus.FIVE_DAYS_TILL_MIN_PAYMENT_DAY:
                return <Status color='blue'>К ОПЛАТЕ</Status>;
            default:
                if (
                    !!minPayDebtTillDate &&
                    isEqual(
                        startOfDay(currentTime),
                        startOfDay(parseDateFromAny(currentTime, minPayDebtTillDate)),
                    )
                ) {
                    return <Status color='blue'>К ОПЛАТЕ</Status>;
                }

                return null;
        }
    }

    return null;
};
