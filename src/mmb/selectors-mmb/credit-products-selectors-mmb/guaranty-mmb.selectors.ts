import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const guarantiesSelector = (state: ApplicationState) => state.creditProducts.guaranties;

export const guarantiesAsArraySelector = createSelector(guarantiesSelector, (guaranties) => {
    const guarantieslist = mapObjectToArray(guaranties);

    return guarantieslist.length ? guarantieslist : [];
});
