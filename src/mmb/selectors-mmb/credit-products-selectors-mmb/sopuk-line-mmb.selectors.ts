import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';

export const sopukLineSelector = (state: ApplicationState) => state.creditProducts.sopukLine;

export const sopukLineAsArraySelector = createSelector(sopukLineSelector, (sopukLine) => {
    const sopukLinelist = Object.values(sopukLine);

    return sopukLinelist.length ? sopukLinelist : [];
});
