import qs from 'qs';
import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { searchParamsSelector } from '#/src/ducks/router/selectors';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const creditLinesSelector = (state: ApplicationState) => state.creditProducts.creditLines;

export const creditLinesAsArraySelector = createSelector(creditLinesSelector, (creditLines) => {
    const creditLineslist = mapObjectToArray(creditLines);

    return creditLineslist.length ? creditLineslist : [];
});

export const parentCreditLineSelector = createSelector(
    creditLinesSelector,
    searchParamsSelector,
    (creditLines, search) =>
        creditLines[`${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`],
);
