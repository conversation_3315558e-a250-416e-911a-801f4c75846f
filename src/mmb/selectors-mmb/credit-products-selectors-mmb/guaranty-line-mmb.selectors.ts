import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const guarantyLinesSelector = (state: ApplicationState) => state.creditProducts.guarantyLines;

export const guarantyLinesAsArraySelector = createSelector(
    guarantyLinesSelector,
    (guarantyLines) => {
        const guarantyLineslist = mapObjectToArray(guarantyLines);

        return guarantyLineslist.length ? guarantyLineslist : [];
    },
);
