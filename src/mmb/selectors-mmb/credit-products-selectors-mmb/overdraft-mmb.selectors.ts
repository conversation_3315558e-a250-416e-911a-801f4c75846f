import qs from 'qs';
import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { searchParamsSelector } from '#/src/ducks/router/selectors';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const overdraftsSelector = (state: ApplicationState) => state.creditProducts.overdrafts;

export const overdraftsAsArraySelector = createSelector(overdraftsSelector, (overdrafts) => {
    const overdraftsList = mapObjectToArray(overdrafts);

    return overdraftsList.length ? overdraftsList : [];
});

export const parentOverdraftSelector = createSelector(
    overdraftsSelector,
    searchParamsSelector,
    (overdrafts, search) =>
        overdrafts[`${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`],
);
