import qs from 'qs';
import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { searchParamsSelector } from '#/src/ducks/router/selectors';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const creditCardsSelector = (state: ApplicationState) => state.creditProducts.creditCards;

export const creditCardsAsArraySelector = createSelector(creditCardsSelector, (cards) => {
    const cardsList = mapObjectToArray(cards);

    return cardsList.length ? cardsList : [];
});

export const parentCreditCardSelector = createSelector(
    creditCardsSelector,
    searchParamsSelector,
    (cards, search) => cards[`${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`],
);
