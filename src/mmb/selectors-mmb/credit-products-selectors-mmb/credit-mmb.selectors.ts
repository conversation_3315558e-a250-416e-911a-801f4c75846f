import qs from 'qs';
import { createSelector } from 'reselect';

import { type ApplicationState } from '#/src/ducks/application-state';
import { searchParamsSelector } from '#/src/ducks/router/selectors';
import { mapObjectToArray } from '#/src/utils/object-helpers';

const creditsSelector = (state: ApplicationState) => state.creditProducts.credits;

export const creditsAsArraySelector = createSelector(creditsSelector, (credits) => {
    const creditsList = mapObjectToArray(credits);

    return creditsList.length ? creditsList : [];
});

export const parentCreditSelector = createSelector(
    creditsSelector,
    searchParamsSelector,
    (credits, search) =>
        credits[`${qs.parse(search, { ignoreQueryPrefix: true }).docNumber ?? ''}`],
);
