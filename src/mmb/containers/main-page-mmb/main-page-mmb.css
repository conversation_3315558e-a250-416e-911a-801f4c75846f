@import '@alfalab/core-components/vars';

.main-page-mmb {
    width: 100%;

    &__skeleton {
        &_wrapper {
            display: flex;
        }

        &_divider {
            margin-bottom: var(--gap-20);
        }

        &_tab1 {
            height: 16px;
            border-radius: var(--border-radius-16);
            margin-right: var(--gap-24);
            margin-bottom: var(--gap-20);
        }

        &_tab2 {
            height: 32px;
            border-radius: var(--border-radius-16);
            margin-right: var(--gap-8);
            margin-bottom: var(--gap-24);
        }

        &_title1 {
            display: flex;
            height: 40px;
            width: 414px;
            border-radius: var(--border-radius-24);
            margin: var(--gap-32) var(--gap-0) var(--gap-32) var(--gap-0);
        }

        &_title2 {
            display: flex;
            height: 8px;
            width: 177px;
            margin-bottom: var(--gap-24);
        }
    }

    &__tabs {
        margin-bottom: var(--gap-20);
    }

    &__header {
        margin-top: var(--gap-24);
        margin-bottom: var(--gap-24);
    }

    &__heading {
        &_type {
            &_credit-products {
                margin-bottom: var(--gap-24);
            }
        }
    }

    &__subheading {
        display: block;
        max-width: 450px;
        margin: var(--gap-16) var(--gap-0) var(--gap-0) var(--gap-0);
    }

    &__credit-offers-container {
        margin-bottom: var(--gap-48);
    }

    &__credit-offer {
        margin-bottom: var(--gap-24);
    }

    &__credit-product-title {
        display: block;
        margin-bottom: var(--gap-8);
    }

    &__heading-amount {
        display: flex;
    }

    &__heading-amount-prefix,
    &__heading-amount-value {
        margin: var(--gap-0);
    }

    &__offer {
        margin-bottom: var(--gap-20);
    }

    &__offers {
        margin-bottom: var(--gap-24);
    }

    &__credit-calculator-section {
        margin-bottom: var(--gap-24);
    }

    &__credit-calculator {
        margin-bottom: var(--gap-24);
    }

    &__credit-calculator-button {
        margin-left: auto;
        background-color: transparent;
        border: 1px solid var(--color-light-border-tertiary-inverted);
    }

    &__credit-calculator-button-wrapper {
        margin-bottom: var(--gap-24);
        display: flex;
    }

    &__credit-tabs-container {
        margin: var(--gap-0) var(--gap-0) var(--gap-20) var(--gap-0);
    }
}
