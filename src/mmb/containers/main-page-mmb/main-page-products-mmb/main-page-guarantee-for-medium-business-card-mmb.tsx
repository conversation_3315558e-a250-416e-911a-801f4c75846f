import React from 'react';

import { CreditOfferItem } from '#/src/components/credit-offer-item';
import { ECreditOffers } from '#/src/constants/credit-offers';
import { goLanding } from '#/src/ducks/app/actions';
import { BANK_GUARANTEE_FOR_MEDIUM_BUSINESS_OFFER } from '#/src/utils/credit-offers-mappers';
import { useDispatchedAction } from '#/src/utils/hooks/use-dispatched-action';

export const MainPageGuaranteeForMediumBusinessCardMMB = React.memo(() => {
    const goGuaranteeForMediumBusinessLanding = useDispatchedAction(() =>
        goLanding({ landingType: ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS }),
    );

    return (
        <CreditOfferItem
            productInfo={BANK_GUARANTEE_FOR_MEDIUM_BUSINESS_OFFER}
            title={BANK_GUARANTEE_FOR_MEDIUM_BUSINESS_OFFER.titlesVariants.long}
            key={ECreditOffers.BANK_GUARANTEE_FOR_MEDIUM_BUSINESS}
            onDetailedButtonClick={goGuaranteeForMediumBusinessLanding}
        />
    );
});
