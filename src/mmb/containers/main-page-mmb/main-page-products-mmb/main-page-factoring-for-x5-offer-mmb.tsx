import React from 'react';
import { useSelector } from 'react-redux';

import { CreditOfferItem } from '#/src/components/credit-offer-item';
import { ECreditOffers } from '#/src/constants/credit-offers';
import { goLanding } from '#/src/ducks/app/actions';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { FACTORING_X5_OFFER } from '#/src/utils/credit-offers-mappers';
import { useDispatchedAction } from '#/src/utils/hooks/use-dispatched-action';

export const MainPageFactoringForX5OfferMMB = React.memo(() => {
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const goFactoringForX5Landing = useDispatchedAction(() =>
        goLanding({ landingType: ECreditOffers.FACTORING_X5, organizationId }),
    );

    return (
        <CreditOfferItem
            productInfo={FACTORING_X5_OFFER}
            title={FACTORING_X5_OFFER.titlesVariants.long}
            key={ECreditOffers.FACTORING_X5}
            onDetailedButtonClick={goFactoringForX5Landing}
        />
    );
});
