import React, { useEffect, useRef } from 'react';
import { shallowEqual, useSelector } from 'react-redux';

import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';

import { CreditCalculatorProductsNumber } from '#/src/components/credit-calculator/credit-calculator-products-number';
import { ANCHORS } from '#/src/constants/routing';
import { CreditCalculator } from '#/src/containers/credit-calculator';
import { useTrackCreditCalculator } from '#/src/containers/credit-calculator/use-track-credit-calculator';
import { mainPageCn } from '#/src/containers/main-page/main-page-cn';
import { type ApplicationState } from '#/src/ducks/application-state';
import { activeCreditCalculator } from '#/src/ducks/credit-calculator/actions';
import { isCreditCalculatorActiveSelector } from '#/src/ducks/credit-calculator/selectors';
import {
    allCreditOffersSelector,
    creditOffersWithoutLoanPurposeSelector,
    filteredByCalculatorCreditOffersSelector,
    isGetCreditOffersAccessErrorSelector,
} from '#/src/ducks/credit-offers/selectors';
import {
    hasOffersViewRightsForHeaderSelector,
    isKIBCategoryHeaderSelector,
    isMmbCategoryHeaderSelector,
    isNibCreditOffersForHeaderSelector,
    isNibCreditShowcaseSliderForHeaderSelector,
    isSBCategoryHeaderSelector,
} from '#/src/ducks/credit-products-header/selectors';
import { useDispatchedAction } from '#/src/utils/hooks/use-dispatched-action';
import { useScrollToRefWithAnchor } from '#/src/utils/hooks/use-scroll-to-ref-with-anchor';

import { MainPageCreditOfferMMB } from './main-page-credit-offer-mmb';

type TProps = { onlyOldOffers?: boolean };

const mapStateToProps = (state: ApplicationState) => ({
    creditOffers: allCreditOffersSelector(state),
    filteredByCalculatorCreditOffers: filteredByCalculatorCreditOffersSelector(state),
    creditOffersWithoutLoanPurpose: creditOffersWithoutLoanPurposeSelector(state),
    isCreditCalculatorActive: isCreditCalculatorActiveSelector(state),
    isGetCreditOffersAccessError: isGetCreditOffersAccessErrorSelector(state),
    hasOffersViewRightsForHeader: hasOffersViewRightsForHeaderSelector(state),
    isNibCreditShowcaseSliderForHeader: isNibCreditShowcaseSliderForHeaderSelector(state),
    isNibCreditOffersForHeader: isNibCreditOffersForHeaderSelector(state),
    isMMBCategoryHeader: isMmbCategoryHeaderSelector(state),
    isSBCategoryHeader: isSBCategoryHeaderSelector(state),
    isKIBCategoryHeader: isKIBCategoryHeaderSelector(state),
});

export const MainPageCreditOffersWithCreditCalculatorMMB = ({ onlyOldOffers = false }: TProps) => {
    const track = useTrackCreditCalculator();
    const [isDesktop] = useMatchMedia('--tablet-m');
    const {
        creditOffers,
        filteredByCalculatorCreditOffers,
        isCreditCalculatorActive,
        creditOffersWithoutLoanPurpose,
        isGetCreditOffersAccessError,
        hasOffersViewRightsForHeader,
        isNibCreditShowcaseSliderForHeader,
        isNibCreditOffersForHeader,
        isMMBCategoryHeader,
        isSBCategoryHeader,
        isKIBCategoryHeader,
    } = useSelector<ApplicationState, ReturnType<typeof mapStateToProps>>(
        mapStateToProps,
        shallowEqual,
    );

    const allProducts = useRef<null | HTMLDivElement>(null);

    const scrollToRefWithUrlHash = useScrollToRefWithAnchor(allProducts, ANCHORS.ALL_PRODUCTS);

    const isMMBCondition =
        (isNibCreditShowcaseSliderForHeader || isNibCreditOffersForHeader) && isMMBCategoryHeader;
    const isShowDigitalSalesSpecialOffersCreditSlider =
        (isSBCategoryHeader || isKIBCategoryHeader || isMMBCondition) && !onlyOldOffers;

    useEffect(() => {
        scrollToRefWithUrlHash();
    }, [scrollToRefWithUrlHash]);

    const handleActiveCreditCalculator = useDispatchedAction(activeCreditCalculator, {
        callback: (isActive) => track.changeVisibility(isActive ? 'visible' : 'hidden'),
    });

    let actualCreditOffers;

    if (
        !hasOffersViewRightsForHeader ||
        isGetCreditOffersAccessError ||
        isShowDigitalSalesSpecialOffersCreditSlider
    )
        return null;

    if (isCreditCalculatorActive && filteredByCalculatorCreditOffers.length) {
        actualCreditOffers = (
            <div className={mainPageCn('credit-offers-container')}>
                {filteredByCalculatorCreditOffers.map((offer) => (
                    <div key={`${offer?.id}_${offer?.type}`} className={mainPageCn('credit-offer')}>
                        <MainPageCreditOfferMMB offer={offer} isDesktop={isDesktop} />
                    </div>
                ))}
            </div>
        );
    } else if (!isCreditCalculatorActive) {
        actualCreditOffers = (
            <div className={mainPageCn('credit-offers-container')}>
                {creditOffers.map((offer) => (
                    <div key={`${offer?.id}_${offer?.type}`} className={mainPageCn('credit-offer')}>
                        <MainPageCreditOfferMMB offer={offer} isDesktop={isDesktop} />
                    </div>
                ))}
            </div>
        );
    }

    return (
        <React.Fragment>
            {!onlyOldOffers && (
                <div className={mainPageCn('credit-calculator-button-wrapper')}>
                    <Typography.Title view='small' weight='bold' tag='div' font='system'>
                        Кредитные предложения
                    </Typography.Title>
                </div>
            )}
            <CreditCalculator
                className={mainPageCn('credit-calculator')}
                activeCreditCalculator={handleActiveCreditCalculator}
            />
            {isCreditCalculatorActive && (
                <CreditCalculatorProductsNumber
                    productsNumber={filteredByCalculatorCreditOffers.length}
                    className={mainPageCn('credit-calculator-section')}
                />
            )}
            {actualCreditOffers}
            {isCreditCalculatorActive && creditOffersWithoutLoanPurpose.length > 0 && (
                <React.Fragment>
                    <Typography.Title
                        view='small'
                        weight='bold'
                        tag='div'
                        font='system'
                        className={mainPageCn('credit-calculator-section')}
                    >
                        Также ознакомьтесь
                    </Typography.Title>
                    <div className={mainPageCn('credit-offers-container')}>
                        {creditOffersWithoutLoanPurpose.map((offer) => (
                            <div
                                key={`${offer?.id}_${offer?.type}`}
                                className={mainPageCn('credit-offer')}
                            >
                                <MainPageCreditOfferMMB offer={offer} isDesktop={isDesktop} />
                            </div>
                        ))}
                    </div>
                </React.Fragment>
            )}
        </React.Fragment>
    );
};
