import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { redirectForms } from 'src/utils/redirect-forms-helper';

import { CreditOfferItem } from '#/src/components/credit-offer-item';
import {
    ECreditOffersCampaignCode,
    MustSendDefaultCampaignCodeToLanding,
    TotalOffersCampaignCode,
} from '#/src/constants/credit-offers';
import { goLanding } from '#/src/ducks/app/actions';
import { isCommonCheckBlockedOverdraftSelector } from '#/src/ducks/app/selectors';
import {
    isOnlineSigningAvailableSelector,
    isTotalOfferSelector,
    preferredProductSelector,
    productCodeSelector,
    stageActiveCreditCaseSelector,
} from '#/src/ducks/attach-documents/selectors';
import {
    setClickedOffer,
    setContinueActiveCaseModalVisible,
    setContinueSFAActiveCaseModalVisible,
    setTotalOfferNeedQESModalVisible,
} from '#/src/ducks/credit-processing/actions';
import { hasCreditOffersSmallSizeSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { currentHeaderOrganizationEqIdSelector } from '#/src/ducks/credit-products-header/selectors';
import { getIsShowContinueCaseModal } from '#/src/utils/active-credit-case';
import { type TMappedCreditOffer } from '#/src/utils/credit-offers-mappers';
import { getOfferPaneTitle } from '#/src/view-utils/credit-offers';

type Props = {
    offer: TMappedCreditOffer;
    isDesktop: boolean;
};

export const MainPageCreditOfferMMB = React.memo(({ offer, isDesktop }: Props) => {
    const hasCreditOffersSmallSize = useSelector(hasCreditOffersSmallSizeSelector);
    const isCommonCheckBlockedOverdraft = useSelector(isCommonCheckBlockedOverdraftSelector);
    const activeProductCode = useSelector(productCodeSelector);
    const preferredProduct = useSelector(preferredProductSelector);
    const isActiveTotalOffer = useSelector(isTotalOfferSelector);
    const stage = useSelector(stageActiveCreditCaseSelector);
    const isOnlineSigningAvailable = useSelector(isOnlineSigningAvailableSelector);
    const organizationId = useSelector(currentHeaderOrganizationEqIdSelector);

    const dispatch = useDispatch();

    const getCreditOfferPaneClickHandler = useCallback(() => {
        if (offer) {
            const campaignCode =
                !offer.campaignCode || MustSendDefaultCampaignCodeToLanding.includes(offer.type)
                    ? ECreditOffersCampaignCode.DEFAULT
                    : offer.campaignCode;

            dispatch(
                goLanding({
                    landingType: offer.type,
                    campaignCode: campaignCode as ECreditOffersCampaignCode,
                    organizationId,
                }),
            );
        }
    }, [dispatch, offer, organizationId]);

    const goToQuestionnaireClickHandler = useCallback(
        (clickedOffer: TMappedCreditOffer) => {
            dispatch(setClickedOffer(clickedOffer));
            const { campaignCode, type } = clickedOffer;
            const isClickedTotalOffer = TotalOffersCampaignCode.includes(
                campaignCode as ECreditOffersCampaignCode,
            );

            if (isOnlineSigningAvailable) {
                dispatch(setContinueSFAActiveCaseModalVisible(true));

                return;
            }

            if (isClickedTotalOffer) {
                dispatch(setTotalOfferNeedQESModalVisible(true));

                return;
            }

            if (stage) {
                const isShowContinueCaseModal = getIsShowContinueCaseModal(
                    activeProductCode,
                    type,
                    campaignCode,
                    isActiveTotalOffer,
                    preferredProduct,
                );

                if (isShowContinueCaseModal) {
                    dispatch(setContinueActiveCaseModalVisible(true));

                    return;
                }
            }

            redirectForms({ offer: clickedOffer, dispatch, organizationId });
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [isOnlineSigningAvailable, stage, activeProductCode, isActiveTotalOffer, preferredProduct],
    );

    const offerTitle = useMemo(
        () =>
            offer
                ? getOfferPaneTitle(
                      offer.titlesVariants,
                      hasCreditOffersSmallSize,
                      isDesktop,
                      offer.type,
                  )
                : '',
        [offer, hasCreditOffersSmallSize, isDesktop],
    );

    return (
        <CreditOfferItem
            productInfo={offer}
            title={offerTitle}
            key={offer?.type}
            onDetailedButtonClick={getCreditOfferPaneClickHandler}
            onGoToQuestionnaireButtonClick={goToQuestionnaireClickHandler}
            isCommonCheckBlockedOverdraft={isCommonCheckBlockedOverdraft}
        />
    );
});
