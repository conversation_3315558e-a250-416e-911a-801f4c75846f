import React from 'react';
import { useSelector } from 'react-redux';
import { createCn } from 'bem-react-classname';

import { Grid } from '@alfalab/core-components/grid';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';

import { ECreditOffersCampaignCode } from '#/src/constants/credit-offers';
import {
    allCreditOffersSelector,
    isGetCreditOffersAccessErrorSelector,
} from '#/src/ducks/credit-offers/selectors';
import { hasCreditOffersSmallSizeSelector } from '#/src/ducks/credit-products/selectors/common-credit-products.selectors';
import { hasOffersViewRightsForHeaderSelector } from '#/src/ducks/credit-products-header/selectors';

import { MainPageCreditOfferMMB } from './main-page-credit-offer-mmb';

import './main-page-credit-offers-without-credit-calculator-mmb.css';

const cn = createCn('main-page-credit-offers-without-credit-calculator-mmb');

type TProps = {
    offersMaxCount?: number;
};

const SMALL_SIZE_WIDTH = { desktop: { s: 6, m: 4 }, tablet: 6, mobile: 12 };

export const MainPageCreditOffersWithoutCreditCalculatorMMB = ({ offersMaxCount }: TProps) => {
    const [isDesktop] = useMatchMedia('--tablet-m');

    const hasCreditOffersSmallSize = useSelector(hasCreditOffersSmallSizeSelector);
    const creditOffers = useSelector(allCreditOffersSelector);

    const formattedCreditOffers = offersMaxCount
        ? creditOffers.slice(0, offersMaxCount)
        : creditOffers;

    const isGetCreditOffersAccessError = useSelector(isGetCreditOffersAccessErrorSelector);
    const hasOffersViewRightsForHeader = useSelector(hasOffersViewRightsForHeaderSelector);
    const isVisibleTitle =
        !!formattedCreditOffers.find(
            (offer) => offer.campaignCode === ECreditOffersCampaignCode.LP_LOAN_GS,
        ) && formattedCreditOffers.length > 1;

    if (!hasOffersViewRightsForHeader || isGetCreditOffersAccessError) return null;

    return formattedCreditOffers.length ? (
        <div>
            {isVisibleTitle && (
                <Typography.Title view='small' font='styrene' tag='h2' className={cn('title')}>
                    Предложения от банка
                </Typography.Title>
            )}
            <Grid.Row justify='left'>
                {formattedCreditOffers.map((offer, index) => {
                    if (offer.campaignCode === ECreditOffersCampaignCode.LP_LOAN_GS) return null;

                    return (
                        <Grid.Col
                            className={cn('col')}
                            width={hasCreditOffersSmallSize ? SMALL_SIZE_WIDTH : 12}
                            key={offer.id || index}
                        >
                            <MainPageCreditOfferMMB offer={offer} isDesktop={isDesktop} />
                        </Grid.Col>
                    );
                })}
            </Grid.Row>
        </div>
    ) : null;
};
