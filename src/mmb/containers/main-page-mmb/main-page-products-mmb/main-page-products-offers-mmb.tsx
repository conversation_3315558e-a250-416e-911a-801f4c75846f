import React from 'react';

import { mainPageCnMMB } from '../main-page-cn-mmb';

export const MainPageProductsOffersMMB = ({ children }: { children?: React.ReactNode }) => {
    const childrenArray = React.Children.toArray(children);

    if (!childrenArray.length) {
        return null;
    }

    return (
        <div className={mainPageCnMMB('offers')}>
            {childrenArray.map((child, index) => (
                <div className={mainPageCnMMB('offer')} key={`offer_${index}`}>
                    {child}
                </div>
            ))}
        </div>
    );
};
