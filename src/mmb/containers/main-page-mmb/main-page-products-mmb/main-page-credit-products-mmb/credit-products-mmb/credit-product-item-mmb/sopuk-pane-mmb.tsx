import React from 'react';
import { createCn } from 'bem-react-classname';
import { type Amount as TAmount } from 'thrift-services/entities';

import { Amount, type AmountProps } from '@alfalab/core-components/amount';
import { Divider } from '@alfalab/core-components/divider';
import { useMatchMedia } from '@alfalab/core-components/mq';
import { Typography } from '@alfalab/core-components/typography';

import { getSortedCreditAmountsByCurrency } from '#/src/utils/sopuk-helpers';

import './sopuk-pane-mmb.css';

type TOwnProps = {
    creditAmountsByCurrency: TAmount[];
    dataTestId?: string;
};

const cn = createCn('sopuk-pane-mmb');

export const SopukPaneMmb = ({ creditAmountsByCurrency, dataTestId }: TOwnProps) => {
    const [isTablet] = useMatchMedia('--tablet-m');
    const sortedCreditAmountsByCurrency = getSortedCreditAmountsByCurrency(creditAmountsByCurrency);

    if (sortedCreditAmountsByCurrency.length === 0) {
        return null;
    }

    return (
        <React.Fragment>
            <Typography.Text
                view='primary-medium'
                color='secondary'
                className={cn('title')}
                tag='div'
            >
                Использовано
            </Typography.Text>
            <div className={cn()} data-test-id={dataTestId}>
                {sortedCreditAmountsByCurrency.map(({ amount, currency }) => (
                    <Typography.Title
                        key={currency?.mnemonicCode}
                        view='small'
                        font='system'
                        color='primary'
                        tag='div'
                    >
                        <Amount
                            value={amount || 0}
                            currency={currency?.mnemonicCode as AmountProps['currency']}
                            minority={100}
                            transparentMinor={false}
                            view='withZeroMinorPart'
                        />
                    </Typography.Title>
                ))}
            </div>
            {isTablet && <Divider className={cn('divider')} />}
        </React.Fragment>
    );
};
