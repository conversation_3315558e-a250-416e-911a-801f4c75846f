import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, type ButtonProps } from '@alfalab/core-components/button';

import { trackAlfaMetrics } from '#/src/ducks/alfa-metrics/actions';
import { initExternalRedirect } from '#/src/ducks/app/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { externalRedirectAlfaCreditRequestGuaranteeSelector } from '#/src/ducks/settings/selectors';
import { GUARANTY_LINE_IN_USE_METRICS } from '#/src/metrics';

type TProps = ButtonProps & {
    dealId: string;
    docNumber: string;
    customerId?: string;
    onClick?: () => void;
};

export const GetGuarantyButton = ({
    docNumber,
    dealId,
    customerId,
    onClick,
    ...buttonProps
}: TProps) => {
    const dispatch = useDispatch();
    const currentCustomerId = useSelector(currentOrganizationEqIdSelector);

    const externalRedirectAlfaCreditRequestGuarantee = useSelector(
        externalRedirectAlfaCreditRequestGuaranteeSelector,
    );

    const handleOnGetGuaranteeButtonClick = () => {
        const parameters = {
            dealDocNumber: docNumber,
            dealId,
            customerId: customerId ?? currentCustomerId,
        };

        dispatch(
            trackAlfaMetrics(GUARANTY_LINE_IN_USE_METRICS.guarantyArrangeButtonClick, {
                agreementNumber: dealId,
            }),
        );

        dispatch(
            initExternalRedirect({
                link: externalRedirectAlfaCreditRequestGuarantee,
                addContextRoot: false,
                withOrganizationId: true,
                organizationId: customerId ?? currentCustomerId,
                parameters,
            }),
        );

        onClick?.();
    };

    return (
        <Button
            onClick={handleOnGetGuaranteeButtonClick}
            view='primary'
            size='xs'
            nowrap={true}
            {...buttonProps}
        >
            Новая гарантия
        </Button>
    );
};
