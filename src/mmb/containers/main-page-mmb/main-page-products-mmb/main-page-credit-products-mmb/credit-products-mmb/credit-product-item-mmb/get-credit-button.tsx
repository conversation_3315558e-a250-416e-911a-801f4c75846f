import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, type ButtonProps } from '@alfalab/core-components/button';

import { initExternalRedirect } from '#/src/ducks/app/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { externalRedirectAlfaCreditRequestCreditSelector } from '#/src/ducks/settings/selectors';

type TProps = ButtonProps & {
    docNumber: string;
    dealId: string;
    customerId?: string;
    onClick?: () => void;
};

export const GetCreditButton = ({
    docNumber,
    dealId,
    customerId,
    onClick,
    ...buttonProps
}: TProps) => {
    const dispatch = useDispatch();
    const link = useSelector(externalRedirectAlfaCreditRequestCreditSelector);
    const currentCustomerId = useSelector(currentOrganizationEqIdSelector);

    const handleOnClick = useCallback(() => {
        dispatch(
            initExternalRedirect({
                link,
                addContextRoot: false,
                withOrganizationId: true,
                organizationId: customerId ?? currentCustomerId,
                parameters: {
                    dealDocNumber: docNumber,
                    dealId,
                    customerId: customerId ?? currentCustomerId,
                },
            }),
        );

        onClick?.();
    }, [dispatch, link, customerId, currentCustomerId, docNumber, dealId, onClick]);

    return (
        <Button view='primary' size='xs' nowrap={true} onClick={handleOnClick} {...buttonProps}>
            Новый кредит
        </Button>
    );
};
