import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button, type ButtonProps } from '@alfalab/core-components/button';

import { goCreditTrancheApp, initExternalRedirect } from '#/src/ducks/app/actions';
import { currentOrganizationEqIdSelector } from '#/src/ducks/organization/selectors';
import { externalRedirectAlfaCreditRequestCreditSelector } from '#/src/ducks/settings/selectors';

type TProps = ButtonProps & {
    docNumber: string;
    dealId: string;
    isMmb?: boolean;
    customerId?: string;
    onClick?: () => void;
};

export const GetTrancheButton = ({
    docNumber,
    dealId,
    isMmb,
    customerId,
    onClick,
    ...buttonProps
}: TProps) => {
    const dispatch = useDispatch();
    const link = useSelector(externalRedirectAlfaCreditRequestCreditSelector);
    const currentCustomerId = useSelector(currentOrganizationEqIdSelector);

    const handleOnClick = useCallback(() => {
        const action = isMmb
            ? goCreditTrancheApp({ organizationId: customerId ?? currentCustomerId, docNumber })
            : initExternalRedirect({
                  link,
                  addContextRoot: false,
                  withOrganizationId: true,
                  organizationId: customerId ?? currentCustomerId,
                  parameters: {
                      dealDocNumber: docNumber,
                      dealId,
                      customerId: customerId ?? currentCustomerId,
                  },
              });

        dispatch(action);

        onClick?.();
    }, [isMmb, customerId, currentCustomerId, docNumber, link, dealId, dispatch, onClick]);

    return (
        <Button view='primary' size='xs' nowrap={true} onClick={handleOnClick} {...buttonProps}>
            Новый транш
        </Button>
    );
};
