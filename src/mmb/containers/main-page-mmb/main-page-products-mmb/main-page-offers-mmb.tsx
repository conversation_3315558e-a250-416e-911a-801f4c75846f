import React from 'react';
import { useSelector } from 'react-redux';

import { creditOffersWithLoanPurposeSelector } from '#/src/ducks/credit-offers/selectors';

import { MainPageCreditOffersWithoutCreditCalculatorMMB } from './main-page-credit-offers-mmb';
import { MainPageFactoringForX5OfferMMB } from './main-page-factoring-for-x5-offer-mmb';
import { MainPageGuaranteeForMediumBusinessCardMMB } from './main-page-guarantee-for-medium-business-card-mmb';
import { MainPageProductsOffersMMB } from './main-page-products-offers-mmb';

export const MainPageOffersMMB = () => {
    const creditOffersWithLoanPurpose = useSelector(creditOffersWithLoanPurposeSelector);

    if (creditOffersWithLoanPurpose.length > 0) {
        return (
            <MainPageProductsOffersMMB>
                <MainPageGuaranteeForMediumBusinessCardMMB />
                <MainPageFactoringForX5OfferMMB />
            </MainPageProductsOffersMMB>
        );
    }

    return (
        <React.Fragment>
            <MainPageCreditOffersWithoutCreditCalculatorMMB />
            <MainPageProductsOffersMMB>
                <MainPageGuaranteeForMediumBusinessCardMMB />
                <MainPageFactoringForX5OfferMMB />
            </MainPageProductsOffersMMB>
        </React.Fragment>
    );
};
