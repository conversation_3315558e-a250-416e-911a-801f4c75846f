import { type FileUploadItemProps } from '@alfalab/core-components/file-upload-item';

export type DocumentInfo = {
    id: string;
    fileId?: string;
    name: File['name'];
    size: File['size'];
    type?: File['type'];
    uploadStatus: FileUploadItemProps['uploadStatus'];
    error?: FileUploadItemProps['error'];
    showDelete?: FileUploadItemProps['showDelete'];
};

export type DocumentInfoWithFile = DocumentInfo & {
    file?: File;
};

export enum EClientDocumentsRequestStatuses {
    // Прикладывание документов доступно
    INIT = 'INIT',
    // Документы на проверке
    CHECK = 'CHECK',
}

export type ActiveCreditCaseStage =
    | 'needClientDocuments'
    | 'fillingForm'
    | 'choiseDecision'
    | 'signingAgreement'
    | 'processingCreditCard';

export type ActiveCreditCaseInfo = {
    title: React.ReactNode;
    description: string;
    textButton?: string;
    date?: string | null;
};
