import { type Customer } from 'corp-customers-api-typescript-services';
import { type Checks } from 'corp-role-model-mks-permissions-api-typescript-services';
import { type OrganizationName } from 'thrift-services/entities';

export type TCustomCustomer = Omit<Customer, 'name'> & {
    name: string;
    organizationName: OrganizationName;
    rights: Checks['rightChecksResults'];
    features: Record<string, boolean>;
};
