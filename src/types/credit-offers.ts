import { type ECreditOffers } from '../constants/credit-offers';
import { type TMappedCreditOffer } from '../utils/credit-offers-mappers';

type TCreditOfferFeature = {
    title: string;
    text: React.ReactNode;
    tooltipText?: React.ReactNode;
};
export type TCreditOfferFeatures = TCreditOfferFeature[];
export type TCreditOfferText = JSX.Element;
export type TCreditOffersFeatures = {
    [ECreditOffers.BUSINESS_CREDIT]: TCreditOfferFeatures;
    [ECreditOffers.BUSINESS_CREDIT_CARD]: TCreditOfferFeatures;
    [ECreditOffers.OVERDRAFT]: TCreditOfferFeatures;
    [ECreditOffers.GUARANTY]: TCreditOfferFeatures;
    [ECreditOffers.EXPRESS_OVERDRAFT_OFFER]: TCreditOfferFeatures;
    [ECreditOffers.CREDIT_LIMIT]: TCreditOfferFeatures;
};

export type TGetCreditOffersTextsParams = Partial<TMappedCreditOffer> & {
    isSmall?: boolean;
    text?: string;
    title?: string | React.ReactNode;
    isBusinessCreditCard?: boolean;
};
export type TGetCreditOffersFeaturesParams = Partial<TMappedCreditOffer> & {
    offerType: ECreditOffers;
    currentTime?: Date;
};

export type TFeatureColumn =
    | [string, JSX.Element | string]
    | [string, JSX.Element | string, JSX.Element | string];
