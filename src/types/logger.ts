import { type ERROR_ACTION_TYPES_TO_WATCH } from '../sagas/logger-watcher';
import {
    type CreditDocumentCirculationErrors,
    type CreditOffersErrors,
    type CreditProcessingErrors,
    type CreditProductErrors,
} from '../utils/errors';

type TErrorActionTypes = (typeof ERROR_ACTION_TYPES_TO_WATCH)[0];

export enum LOG_LEVEL {
    ERROR = 'ERROR',
    WARN = 'WARN',
    INFO = 'INFO',
}

export type TErrorAction = {
    type: TErrorActionTypes;
    error:
        | CreditProductErrors
        | CreditOffersErrors
        | CreditProcessingErrors
        | CreditDocumentCirculationErrors;
    logLevel: LOG_LEVEL;
};
