import { type Currency } from 'thrift-services/entities';

export type EarlyPayData = {
    docNumber: string;
    fromDate?: string;
    paymentType: earlyPaymentTypes;
    paymentDate: string;
    debtToPay: string;
    interestToPay: string;
    total: string;
    selectedAccountNumber: string;
    currency: Currency;
    branchNumber: string;
    currentOrganizationEqId?: string;
};

export enum earlyPaymentTypes {
    FULL = 'full',
    PARTIAL = 'partial',
}
