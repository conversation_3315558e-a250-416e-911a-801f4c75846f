import { type Amount } from 'thrift-services/entities';
import { type TRepaymentResult } from 'thrift-services/services/credit_products';

export type TOverdraftStatusConfig = {
    isOverdue: boolean;
    isDebtExists: boolean;
    isFineExists: boolean;
    withTranches: boolean;
    isFromMMB: boolean;
};

export enum ETabs {
    tranches = 'tranches',
    conditions = 'conditions',
    settings = 'settings',
    documents = 'documents',
    suspensiveConditions = 'suspensiveConditions',
    statementRequests = 'statementRequests',
}

export enum ETabsTexts {
    tranches = 'Активные транши',
    conditions = 'Условия овердрафта',
    settings = 'Настройки',
}

export type TOverdraftPageTabInfo = {
    text: ETabsTexts;
    value: ETabs;
};

export type TTabsList = TOverdraftPageTabInfo[];

export type TOverdraftPageTabsInfo = {
    [ETabs.conditions]: TOverdraftPageTabInfo;
    [ETabs.settings]: TOverdraftPageTabInfo;
    [ETabs.tranches]: TOverdraftPageTabInfo;
};

export type TGetOverdraftTabsDataArgs = {
    withTranches: boolean;
    hasClientLimit: boolean;
    canChangeRepaymentType: boolean;
};

export type TOverdraftTabsData = {
    currentTab: ETabs;
    tabsList: TTabsList;
};

export type TOverdraftLimit = {
    maxClientLimit?: Amount;
    minClientLimit?: Amount;
    hasClientLimitSetting?: boolean;
    repaymentResult?: TRepaymentResult | null;
    overdraftLimitRulesUrl?: string;
};

export enum ERepaymentType {
    EOD = 'EOD',
    Online = 'Online',
}

export enum ERepaymentStatus {
    NOT_CHANGING = 'NOT_CHANGING',
    IN_PROGRESS = 'IN_PROGRESS',
    ERROR = 'ERROR',
    SET_SUCCESSFUL = 'SET_SUCCESSFUL',
}

export enum ELimitStatus {
    NOT_CHANGING = 'NOT_CHANGING',
    IN_PROGRESS = 'IN_PROGRESS',
    ERROR = 'ERROR',
    SET_SUCCESSFUL = 'SET_SUCCESSFUL',
}

export enum EOverBlock {
    BlockCB = 'BlockCB',
    BlockSME = 'BlockSME',
    BlockIB = 'BlockIB',
    BlockRB = 'BlockRB',
    BlockSB = 'BlockSB',
    BlockKazn = 'BlockKazn',
    BlockTB = 'BlockTB',
    BlockUKCK = 'BlockUKCK',
}
