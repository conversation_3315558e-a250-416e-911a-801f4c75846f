import memoize from 'lodash/memoize';

import { MDASH, NBSP } from '#/src/constants/unicode-symbols';

export const EARLY_REPAYMENT_TEXTS = {
    errors: {
        title: memoize(
            (docNumber) =>
                `Вам отказано в досрочном погашении кредита по договору №${NBSP}${docNumber}`,
        ),
        onlyEIOCanRepay: 'Отсутсвие наличие доверенности типа ЕИО',
        noCeo: 'Погасить досрочно может только ЕИО (Единоличный исполнительный орган)',
        hasOverdue: 'Сумма штрафов больше 0',
    },
    aboutTheProcedureForDebitingPayments: `Если у${NBSP}вас на${NBSP}одном счету есть несколько кредитных продуктов, то${NBSP}досрочное погашение будет списываться${NBSP}в${NBSP}последнюю очередь. Приоритет${NBSP}${MDASH} у${NBSP}плановых платежей по${NBSP}кредитам и${NBSP}овердрафта`,
    requestAlreadyCreated: `По${NBSP}этому договору уже создана заявка${NBSP}${MDASH} дождитесь, пока она обработается, или${NBSP}обратитесь к${NBSP}персональному менеджеру для отмены`,
    timeBeforeDeadline: `Погашение текущей датой возможно в${NBSP}рабочие дни до${NBSP}19:00 по${NBSP}московскому времени`,
};
