import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    clickDownloadButton: {
        category: EventCategory.statements,
        action: 'Click -> Statement download',
        label: 'Клик на кнопку скачивания выписки',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    сlickCreateButton: {
        category: EventCategory.statements,
        action: 'Click -> Statement create',
        label: 'Клик на кнопку "Создать выписку"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    clickOpenButton: {
        category: EventCategory.statements,
        action: 'Click -> Statement open',
        label: 'Клик на кнопку открытия формы по выписке',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    successfullyDownloaded: {
        category: EventCategory.statements,
        action: 'Download -> Statement',
        label: 'Успешное скачивание выписки',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    downloadError: {
        category: EventCategory.statements,
        action: 'Download -> Error Statement',
        label: 'Ошибка при скачивании выписки',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            error: '3',
        },
    },
    successfullyCreated: {
        category: EventCategory.statements,
        action: 'Create -> Statement',
        label: 'Успешное создание выписки',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            product: '4',
        },
    },
} as Record<string, TMetric>;
