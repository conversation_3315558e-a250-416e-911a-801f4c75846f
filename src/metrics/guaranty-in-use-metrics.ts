import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderGuarantyPage: {
        category: EventCategory.guarantyInUse,
        action: 'Render page',
        label: 'Загружена главная страница гарантии',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    tabClick: {
        category: EventCategory.guarantyInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками - условия договора, график платежей',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            tabName: '4',
        },
    },
} as Record<string, TMetric>;
