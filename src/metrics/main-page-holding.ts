import type TMetric from '#/src/metrics/metric';
import { EventCategory } from '#/src/metrics/types';

export const FILTER_NAME = {
    COMPANY_FILTER: 'COMPANY_FILTER',
    PRODUCTS_FILTER: 'PRODUCTS_FILTER',
    DOC_NUMBER_FILTER: 'DOC_NUMBER_FILTER',
    STATUS_FILTER: 'STATUS_FILTER',
    SORTING: 'SORTING',
};

export default {
    openFiltersHolding: {
        category: EventCategory.holding,
        action: 'Click -> Open filter holding',
        label: 'Клик на кнопку открытия модалки фильтра',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    filterInteraction: {
        category: EventCategory.holding,
        action: 'Interaction -> Holding filter Interaction',
        label: 'Взаимодействие с фильтрами на главной странице холдинга',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            filterName: '3',
            filterValues: '4',
        },
    },
    submitFilterModal: {
        category: EventCategory.holding,
        action: 'Click -> Submit filter modal',
        label: 'Клик на кнопку применения фильтров в модалке',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
} as Record<string, TMetric>;
