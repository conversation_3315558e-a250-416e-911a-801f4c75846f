import type TMetric from './metric';
import { EventCategory } from './types';

export const changeCreditCalculatorCreditPurpose: TMetric = {
    category: EventCategory.creditCalculator,
    action: 'Click > Change Purpose',
    label: 'Выбор цели кредита',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        purpose: '3',
    },
};

export const changeCreditCalculatorVisibility: TMetric = {
    category: EventCategory.creditCalculator,
    action: 'Click > Toggle calculator visible',
    label: 'Переключение видимости калькулятора',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        visibility: '3',
    },
};

export const changeCreditCalculatorCreditSum: TMetric = {
    category: EventCategory.creditCalculator,
    action: 'DragAndDrop > Change Amount',
    label: 'Выбор суммы кредита',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        amountValue: '3',
    },
};

export const resetCreditCalculator: TMetric = {
    category: EventCategory.creditCalculator,
    action: 'Click > Reset',
    label: 'Нажатие на кнопку "Сбросить фильтр"',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        userRole: '3',
    },
};

export const clickOfferByCreditCalculator: TMetric = {
    category: EventCategory.creditCalculator,
    action: 'Click > Offer By Calculator',
    label: 'Нажатие по предложению с использованием калькулятора',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
    },
};
