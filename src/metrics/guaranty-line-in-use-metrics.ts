import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderGuarantyLinePage: {
        category: EventCategory.guarantyLineInUse,
        action: 'Render page',
        label: 'Загружена главная страница гарантийной линии',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    tabClick: {
        category: EventCategory.guarantyLineInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками - гарантии, условия договора',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            tabName: '4',
        },
    },
    trancheDetailedInfoButtonClick: {
        category: EventCategory.guarantyLineInUse,
        action: 'Click > TrancheDetailsButton',
        label: 'Переход на страницу транша - нажатие на кнопку "Подробнее"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    guarantyArrangeButtonClick: {
        category: EventCategory.guarantyLineInUse,
        action: 'Click > GuarantyArrangeButton',
        label: 'Нажатие на кнопку "Оформить гарантию"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
} as Record<string, TMetric>;
