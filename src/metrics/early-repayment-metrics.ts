import type TMetric from './metric';
import { EventCategory } from './types';

export const mountEarlyRepaymentButton: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Mount > Early repayment button',
    label: 'Открытие страницы с кнопкой на ДП',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        earlyRepaymentButtonState: '4',
    },
};

export const clickEarlyRepaymentButton: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Click > Early repayment button',
    label: 'Нажатие на кнопку, открывающую форму ДП',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
    },
};

export const renderEarlyRepaymentSidebar: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Render > Early repayment form',
    label: 'Открытие формы ДП',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
    },
};

export const submitEarlyRepaymentSidebar: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Click > Submit early repayment form',
    label: 'Нажатие на кнопку отправки формы ДП',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
        paymentType: '5',
    },
};

export const submitValidationErrorEarlyRepaymentSidebar: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Submit > Early repayment form validation error',
    label: 'Ошибка валидации формы ДП',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
        errorsMessages: '5',
    },
};

export const showSuccessfullySignedModal: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Show modal > Successfully signed',
    label: 'Открытие окна об успешном подписании',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
        paymentType: '5',
    },
};

export const showSigningErrorModal: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Show modal > Signing error',
    label: 'Открытие окна об ошибке при подписании',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
    },
};

export const showSignModal: TMetric = {
    category: EventCategory.earlyRepayment,
    action: 'Show modal > Sign modal',
    label: 'Открытие модального окна с подписанием',
    property: null,
    value: null,
    dimensionsMapping: {
        userId: '2',
        agreementNumber: '3',
        processingType: '4',
        paymentType: '5',
    },
};
