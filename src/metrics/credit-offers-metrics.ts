import type TMetric from '#/src/metrics/metric';
import { EventCategory } from '#/src/metrics/types';

export default {
    showStandardCreditOfferPane: {
        category: EventCategory.offers,
        action: 'Impression -> Offer pane -> Standard',
        label: 'Просмотр стандартной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            campaignCode: '4',
            type: '5',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    clickStandardToLandingMetric: {
        category: EventCategory.offers,
        action: 'Click -> Standard detailed button',
        label: 'Нажатие на кнопку "Все условия и лимиты" стандартной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
            typeOffer: '4',
            preApprovedOffer: '5',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    clickStandardToFormsMetric: {
        category: EventCategory.offers,
        action: 'Click -> Standard to forms button',
        label: 'Нажатие на кнопку "Оформить кредит" стандартной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
            typeOffer: '4',
            amount: '5',
            preApprovedOffer: '6',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    showPreApprovedCreditOfferPane: {
        category: EventCategory.offers,
        action: 'Impression -> Offer pane -> Pre Approved',
        label: 'Просмотр предодобренной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            campaignCode: '4',
            type: '5',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    clickPreApprovedCreditToFormsMetric: {
        category: EventCategory.offers,
        action: 'Click -> Offer to forms button',
        label: 'Нажатие на кнопку "Оформить кредит" предодобренной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
            typeOffer: '4',
            preApprovedOffer: '5',
            amount: '6',
            isTotalOffer: '10',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    clickPreApprovedCreditPaneDetailedButton: {
        category: EventCategory.offers,
        action: 'Click -> Offer detailed button',
        label: 'Нажатие на кнопку "Все условия и лимиты" предодобренной карточки какого-либо предложения',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
            typeOffer: '4',
            preApprovedOffer: '5',
            isTotalOffer: '10',
            campaignCodeCommon: '9',
            productIdCommon: '10',
        },
    },
    clickAllCreditOffersButton: {
        category: EventCategory.offers,
        action: 'Click -> All credit offers button',
        label: 'Нажатие на кнопку "Все кредитные предложения"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
        },
    },
    showAllCreditOffers: {
        category: EventCategory.offers,
        action: 'Show -> All credit offers',
        label: 'Показ всех кредитных предложений на главной странице',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
        },
    },
} as Record<string, TMetric>;
