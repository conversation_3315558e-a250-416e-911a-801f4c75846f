import type TMetric from '#/src/metrics/metric';
import { EventCategory } from '#/src/metrics/types';

export default {
    showCreditProductPane: {
        category: EventCategory.products,
        action: 'Impression -> Product pane',
        label: 'Просмотр карточки действующего продукта',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            status: '5',
            productType: '6',
            availableAmount: '7',
            limit: '8',
        },
    },
    clickCreditProductPane: {
        category: EventCategory.products,
        action: 'Click -> Product pane',
        label: 'Нажатие на плашку с действующим продуктом',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productType: '3',
        },
    },
    clickCreditProductPaneDetailedButton: {
        category: EventCategory.products,
        action: 'Click -> Product detailed button',
        label: 'Нажатие на кнопку "Подробнее" на плашке с действующим продуктом',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productType: '3',
        },
    },
    clickCreditProductPaneGetTrancheButton: {
        category: EventCategory.products,
        action: 'Click -> Product get tranche button',
        label: 'Нажатие на кнопку "Новый транш" на плашке с действующим продуктом',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            isMmb: '4',
        },
    },
    clickCreditProductPaneGetCreditButton: {
        category: EventCategory.products,
        action: 'Click -> Product get credit button',
        label: 'Нажатие на кнопку "Получить кредит" на плашке с действующим продуктом',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
        },
    },
    clickCreditProductPaneGetGuarantyButton: {
        category: EventCategory.products,
        action: 'Click -> Product get guaranty button',
        label: 'Нажатие на кнопку "Новая гарантию" на плашке с действующим продуктом',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
        },
    },
} as Record<string, TMetric>;
