import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderOverdraftPage: {
        category: EventCategory.overdraftInUse,
        action: 'Render page',
        label: 'Загружена главная страница овердрафта',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            overType: '4',
        },
    },
    fineForOverdueTooltip: {
        category: EventCategory.overdraftInUse,
        action: 'Click > Hint Fine For Overdue',
        label: 'Подсказка "Неустойка за просрочку"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            overType: '4',
        },
    },
    tabClick: {
        category: EventCategory.overdraftInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            overType: '4',
            tabName: '5',
        },
    },
    trancheDetailsButtonClick: {
        category: EventCategory.overdraftInUse,
        action: 'Click > TrancheDetailsButton',
        label: 'Подробности транша - подробнее, свернуть',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    limitEdit: {
        category: EventCategory.overdraftInUse,
        action: 'Click > Limit edit button',
        label: 'Нажатие на кнопку "Изменить" в настройках лимита',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    limitCancel: {
        category: EventCategory.overdraftInUse,
        action: 'Click > Limit cancel edit button',
        label: 'Нажатие на кнопку "Отменить" в настройках лимита',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
        },
    },
    limitSave: {
        category: EventCategory.overdraftInUse,
        action: 'Click > Limit save button',
        label: 'Нажатие на кнопку "Сохранить" в настройках лимита',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            limit: '3',
        },
    },
} as Record<string, TMetric>;
