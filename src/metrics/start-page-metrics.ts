import type TMetric from './metric';
import { EventCategory } from './types';

export enum EWebVersion {
    MOBILE_WEB = 'mobile-web',
    WEB = 'web',
}

export default {
    renderStartPage: {
        category: EventCategory.startPage,
        action: 'Render page',
        label: 'Загружена главная страница витрины',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            userRole: '3',
            fromPage: '4',
            webVersion: '5',
        },
    },
    clickContinueActiveCaseOnModal: {
        category: EventCategory.startPage,
        action: 'Click -> Continue active case on modal',
        label: 'Нажатие на "К оформлению" на модалке с активной заявкой',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            stage: '4',
            productCode: '5',
            loanAmount: '6',
            isOnlineSigningAvailable: '7',
            scoringExpiryDate: '8',
            isTotalOffer: '10',
            borrowerType: '11',
        },
    },
    tabClick: {
        category: EventCategory.startPage,
        action: 'Click > Tab',
        label: 'Переключение между табами',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            tabName: '3',
        },
    },
} as Record<string, TMetric>;
