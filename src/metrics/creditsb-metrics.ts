import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    showRequestsDocumentsTab: {
        category: EventCategory.creditsb,
        action: 'Show',
        label: 'Show CreditSBLists Tab',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            creditSBLists: '10',
        },
    },
    showRequestsDocumentsList: {
        category: EventCategory.creditsb,
        action: 'Show',
        label: 'Show CreditSBLists',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            creditSBLists: '10',
            count: '16',
        },
    },
    clickNewRequestDocument: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'CreditSB Request Link',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            creditSBLists: '10',
            url: '12',
            urlName: '13',
            status: '15',
            fromName: '17',
        },
    },
    showCreditLimitBanner: {
        category: EventCategory.creditsb,
        action: 'Impression',
        label: 'Show Banner PA',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            offerType: '3',
            deviceScreenName: '17',
        },
    },
    clickAnotherTermsBanner: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'Another Terms',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            offerType: '3',
            deviceScreenName: '17',
        },
    },
    clickDetailedButtonBanner: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'More Button',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            offerType: '3',
            deviceScreenName: '17',
        },
    },
    clickRedirectButtonBanner: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'Go To Request',
        property: null,
        value: null,
        dimensionsMapping: {
            offerType: '2',
            deviceScreenName: '17',
        },
    },
    clickRequestDocument: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'CreditSB Request Item',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
        },
    },
    showRequestModalWindow: {
        category: EventCategory.creditsb,
        action: 'Show',
        label: 'Show Credit Request Modal Window',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
            sla: '7',
        },
    },
    closeRequestModalWindow: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'Close Credit Request Modal Window',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
        },
    },
    clickActionRequestModalWindow: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'Action on Request Modal Window',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
        },
    },
    clickRequestActiveFilter: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'Credit Request Active Filter',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
        },
    },
    clickRequestDescription: {
        category: EventCategory.creditsb,
        action: 'Click',
        label: 'CreditSB Request Item',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            description: '4',
            creditSBLists: '10',
            url: '12',
            urlName: '13',
            sum: '14',
            status: '15',
        },
    },
    showProgressBarInfo: {
        category: EventCategory.creditsb,
        action: 'Show',
        label: 'Show Progress Bar Content',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            clientStatus: '3',
            description: '4',
            isAvailableForCurrentChannel: '5',
            type: '6',
            stageName: '7',
            sla: '9',
        },
    },
} as Record<string, TMetric>;
