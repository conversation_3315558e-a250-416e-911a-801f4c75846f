import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    showStatusPlate: {
        category: EventCategory.suspensiveConditions,
        action: 'Impression -> Plate',
        label: 'Показ статуса ОУ',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            docNumber: '4',
        },
    },
    clickShowConditions: {
        category: EventCategory.suspensiveConditions,
        action: 'Click -> Show Conditions',
        label: 'Нажатие на кнопку "Посмотреть условия"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            docNumber: '4',
        },
    },
    clickSendDocuments: {
        category: EventCategory.suspensiveConditions,
        action: 'Click -> Send Documents',
        label: 'Нажатие на кнопку "Отправить документы"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            productCode: '3',
            docNumber: '4',
        },
    },
} as Record<string, TMetric>;
