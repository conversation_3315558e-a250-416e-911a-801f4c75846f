import * as EARLY_REPAYMENT from './early-repayment-metrics';

export { EventCategory } from './types';
export * as TMetric from './metric';
export { default as START_PAGE_METRICS } from './start-page-metrics';
export { default as OVER_IN_USE_METRICS } from './over-in-use-metrics';
export { default as CREDIT_IN_USE_METRICS } from './credit-in-use-metrics';
export { default as GUARANTY_IN_USE_METRICS } from './guaranty-in-use-metrics';
export { default as CREDIT_LINE_IN_USE_METRICS } from './credit-line-in-use-metrics';
export { default as SOPUK_LINE_IN_USE_METRICS } from './sopuk-line-in-use-metrics';
export { default as GUARANTY_LINE_IN_USE_METRICS } from './guaranty-line-in-use-metrics';
export { default as CLIENT_DOCUMENTS_ATTACH_METRICS } from './client-documents-attach';
export { default as CREDIT_BANNER_METRICS } from './credit-banner';
export { default as STATEMENTS_METRICS } from './statements';
export { default as CREDITSB_METRICS } from './creditsb-metrics';
export { default as CREDIT_OFFERS_METRICS } from './credit-offers-metrics';
export { default as CREDIT_PRODUCTS_METRICS } from './credit-products-metrics';
export { default as SMART_LIMITS_METRICS } from './smart-limits-metrics';
export { default as SUSPENSIVE_CONDITIONS_METRICS } from './suspensive-conditions';
export { default as HOLDING_MAIN_PAGE_METRICS } from './main-page-holding';
export { default as DIGITAL_SALES_METRICS } from './digital-sales-metrics';
export const EARLY_REPAYMENT_METRICS = EARLY_REPAYMENT;
