import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderCreditPage: {
        category: EventCategory.creditInUse,
        action: 'Render page',
        label: 'Загружена главная страница кредита',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    tabClick: {
        category: EventCategory.creditInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками - о кредиты, график платежей',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            tabName: '4',
        },
    },
    downloadScheduleButtonClick: {
        category: EventCategory.creditInUse,
        action: 'Click > Btn Download Schedule',
        label: 'Нажатие на кнопку "Скачать график"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            fileType: '4',
        },
    },
} as Record<string, TMetric>;
