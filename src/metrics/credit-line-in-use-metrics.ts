import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderCreditLinePage: {
        category: EventCategory.creditLineInUse,
        action: 'Render page',
        label: 'Загружена главная страница кредитной линии',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    tabClick: {
        category: EventCategory.creditLineInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками - общие условия, активные транши',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            tabName: '4',
        },
    },
    trancheDetailedInfoButtonClick: {
        category: EventCategory.creditLineInUse,
        action: 'Click > TrancheDetailsButton',
        label: 'Переход на страницу транша - нажатие на кнопку "Подробнее"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
    getTrancheButtonClick: {
        category: EventCategory.creditLineInUse,
        action: 'Click > Get Tranche Button',
        label: 'Нажатие на кнопку "Новый транш"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
            isMmb: '4',
        },
    },
} as Record<string, TMetric>;
