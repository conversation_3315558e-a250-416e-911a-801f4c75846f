import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderSmartLimitsStatus: {
        category: EventCategory.smartLimits,
        action: 'Render smart limits status',
        label: 'Показ статуса в умном виджете лимитов',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            type: '3',
            clientStatus: '4',
            decisionCommittee: '5',
        },
    },
    clickActionButton: {
        category: EventCategory.smartLimits,
        action: 'Click -> Action Button',
        label: 'Нажатие на кнопку действия',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            type: '3',
            clientStatus: '4',
            action: '5',
            decisionCommittee: '6',
        },
    },
} as Record<string, TMetric>;
