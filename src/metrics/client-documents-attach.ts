import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    attachClientDocumentsPageRender: {
        category: EventCategory.clientDocumentsAttach,
        action: 'Render > Page',
        label: 'Отрисовка страницы',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            borrowerCode: '4',
            currentPath: '5',
        },
    },
    clientDocumentAttach: {
        category: EventCategory.clientDocumentsAttach,
        action: 'Send > File',
        label: 'Прикреление документа',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            borrowerCode: '4',
            mimeType: '5',
            fileSize: '6',
        },
    },
    getClientDocumentsStatus: {
        category: EventCategory.clientDocumentsAttach,
        action: 'Get > Status',
        label: 'Получение статуса заявки',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            borrowerCode: '4',
            status: '5',
        },
    },
    sendClientDocuments: {
        category: EventCategory.clientDocumentsAttach,
        action: 'Send > Application',
        label: 'Отправка заявки на кредитного аналитика',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            borrowerCode: '4',
            totalFilesSize: '5',
        },
    },
} as Record<string, TMetric>;
