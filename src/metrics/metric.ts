import { type EventCategory } from './types';

export default interface TMetric {
    category: EventCategory;
    action: string;
    label?: string;
    property?: string | null;
    value?: any;
    dimensionsMapping: Record<
        string,
        | '2'
        | '3'
        | '4'
        | '5'
        | '6'
        | '7'
        | '8'
        | '9'
        | '10'
        | '11'
        | '12'
        | '13'
        | '14'
        | '15'
        | '16'
        | '17'
        | '18'
        | '19'
        | '20'
    >;
}
