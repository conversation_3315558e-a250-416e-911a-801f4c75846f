import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    creditCaseInfoPaneRender: {
        category: EventCategory.creditBanner,
        action: 'Show banner',
        label: 'Показ баннера по активной кредитной заявке',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            stage: '4',
            isOnlineSigningAvailable: '7',
            isTotalOffer: '10',
        },
    },
    clickCreditCaseInfoPaneContinueButton: {
        category: EventCategory.creditBanner,
        action: 'Click → Banner button',
        label: 'Нажатие на кнопку перехода в активную кредитную заявку с баннера',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            stage: '4',
            productCode: '5',
            isOnlineSigningAvailable: '7',
            isTotalOffer: '10',
            borrowerType: '11',
        },
    },
    clickCreditCaseInfoPaneCloseButton: {
        category: EventCategory.creditBanner,
        action: 'Click → CloseCase Banner button',
        label: 'Нажатие на кнопку "Закрыть заявку" на баннере',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            loanMBId: '3',
            stage: '4',
            productCode: '5',
            isTotalOffer: '6',
        },
    },
} as Record<string, TMetric>;
