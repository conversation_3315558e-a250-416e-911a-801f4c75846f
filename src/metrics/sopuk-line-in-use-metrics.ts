import type TMetric from './metric';
import { EventCategory } from './types';

export default {
    renderSopukPage: {
        category: EventCategory.sopukLineInUse,
        action: 'Render page',
        label: 'Загружена главная страница СОПУКа',
        property: null,
        value: null,
        dimensionsMapping: {
            agreementNumber: '2',
        },
    },
    renderSopukDealPage: {
        category: EventCategory.sopukLineInUse,
        action: 'Render page',
        label: 'Загружена главная страница сделки СОПУКа',
        property: null,
        value: null,
        dimensionsMapping: {
            agreementNumber: '2',
        },
    },
    tabClick: {
        category: EventCategory.sopukLineInUse,
        action: 'Click > Info Tab',
        label: 'Переключение между вкладками - общие условия, активные кредиты',
        property: null,
        value: null,
        dimensionsMapping: {
            agreementNumber: '2',
            tabName: '3',
        },
    },
    sopukDealDetailedInfoButtonClick: {
        category: EventCategory.sopukLineInUse,
        action: 'Click > SopukDealDetailsButton',
        label: 'Переход на страницу сделки СОПУК - нажатие на кнопку "Подробнее"',
        property: null,
        value: null,
        dimensionsMapping: {
            agreementNumber: '2',
        },
    },
    getCreditButtonClick: {
        category: EventCategory.sopukLineInUse,
        action: 'Click > Get Credit Button',
        label: 'Нажатие на кнопку "Новый кредит"',
        property: null,
        value: null,
        dimensionsMapping: {
            userId: '2',
            agreementNumber: '3',
        },
    },
} as Record<string, TMetric>;
